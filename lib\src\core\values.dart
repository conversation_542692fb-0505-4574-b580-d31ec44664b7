/// [Values] will be used for defining sizing all over the app
/// If you need to add a new value please add it in sorted order
/// Ex. `v11.0` should be added after `v10.0`


class Values {
  static const double v0 = 0.0;
  static const double v0_019 = 0.019;
  static const double v0_038 = 0.038;
  static const double v0_05 = 0.05;
  static const double v0_098 = 0.098;
  static const double v0_1 = 0.1;
  static const double v0_15 = 0.15;
  static const double v0_2 = 0.2;
  static const double v0_25 = 0.25;
  static const double v0_3 = 0.3;
  static const double v0_33 = 0.33;
  static const double v0_4 = 0.4;
  static const double v0_5 = 0.5;
  static const double v0_6 = 0.6;
  static const double v0_7 = 0.7;
  static const double v0_75 = 0.75;
  static const double v0_8 = 0.8;
  static const double v0_87 = 0.87;
  static const double v0_9 = 0.9;
  static const double v1 = 1;
  static const double v2 = 1;
  static const double v1_5 = 1.5;
  static const double v3 = 3.0;
  static const double v4 = 4.0;
  static const double v5 = 5.0;
  static const double v6 = 6.0;
  static const double v7 = 7.0;
  static const double v8 = 8.0;
  static const double v9 = 9.0;
  static const double v10 = 10.0;
  static const double v11 = 10.0;
  static const double v12 = 12.0;
  static const double v13 = 13.0;
  static const double v14 = 14.0;
  static const double v15 = 15.0;
  static const double v16 = 16.0;
  static const double v17 = 17.0;
  static const double v18 = 18.0;
  static const double v19 = 19.0;
  static const double v20 = 20.0;
  static const double v21 = 21.0;
  static const double v22 = 22.0;
  static const double v23 = 23.0;
  static const double v24 = 24.0;
  static const double v25 = 25.0;
  static const double v26 = 26.0;
  static const double v28 = 28.0;
  static const double v30 = 30.0;
  static const double v32 = 32.0;
  static const double v33 = 33.0;
  static const double v34 = 34.0;
  static const double v35 = 35.0;
  static const double v36 = 36.0;
  static const double v37 = 37.0;
  static const double v38 = 38.0;
  static const double v39 = 39.0;
  static const double v40 = 40.0;
  static const double v42 = 42.0;
  static const double v43 = 43.0;
  static const double v44 = 44.0;
  static const double v45 = 45.0;
  static const double v46 = 46.0;
  static const double v48 = 48.0;
  static const double v50 = 50.0;
  static const double v52 = 52.0;
  static const double v54 = 54.0;
  static const double v55 = 55.0;
  static const double v56 = 56.0;
  static const double v57 = 57.0;
  static const double v58 = 58.0;
  static const double v60 = 60.0;
  static const double v64 = 64.0;
  static const double v66 = 66.0;
  static const double v67 = 67.0;
  static const double v70 = 70.0;
  static const double v72 = 72.0;
  static const double v75 = 75.0;
  static const double v76 = 76.0;
  static const double v78 = 78.0;
  static const double v80 = 80.0;
  static const double v82 = 82.0;
  static const double v83 = 83.0;
  static const double v85 = 85.0;
  static const double v88 = 88.0;
  static const double v90 = 90.0;
  static const double v91 = 91.0;
  static const double v92 = 92.0;
  static const double v100 = 100.0;
  static const double v105 = 105.0;
  static const double v107 = 107.0;
  static const double v110 = 110.0;
  static const double v112 = 112.0;
  static const double v117 = 117.0;
  static const double v120 = 120.0;
  static const double v128 = 128.0;
  static const double v130 = 130.0;
  static const double v131 = 131.0;
  static const double v135 = 135.0;
  static const double v140 = 140.0;
  static const double v141 = 141.0;
  static const double v148 = 148.0;
  static const double v150 = 150.0;
  static const double v160 = 160.0;
  static const double v163 = 163.0;
  static const double v175 = 175.0;
  static const double v173 = 173.0;
  static const double v180 = 180.0;
  static const double v182 = 182.0;
  static const double v184 = 184.0;
  static const double v187 = 187.0;
  static const double v190 = 190.0;
  static const double v192 = 192.0;
  static const double v207 = 207.0;
  static const double v200 = 200.0;
  static const double v215 = 215.0;
  static const double v220 = 220.0;
  static const double v230 = 230.0;
  static const double v240 = 240.0;
  static const double v242 = 242.0;
  static const double v257 = 257.0;
  static const double v276 = 276.0;
  static const double v285 = 285.0;
  static const double v288 = 288.0;
  static const double v300 = 300.0;
  static const double v375 = 375.0;
  static const double v302 = 302.0;
  static const double v304 = 304.0;
  static const double v324 = 324.0;
  static const double v333 = 333.0;
  static const double v350 = 359.0;
  static const double v400 = 400.0;
  static const double v430 = 430.0;
  static const double v450 = 450.0;
  static const double v478 = 478.0;
  static const double v500 = 500.0;
  static const double v540 = 540.0;
  static const double v560 = 560.0;
  static const double v580 = 580.0;
  static const double v600 = 600.0;
  static const double defaultWidth = 428.0;
  static const double defaultHeight = 926.0;
}

class IntValue{
  static const int v10 = 10;
  static const int v15 = 15;
  static const int v20 = 20;
  static const int v60 = 60;
  static const int v100 = 100;
  static const int v200 = 200;
  static const int v201 = 201;
}


class DurationConstant {
  static const int d300 = 300;
  static const int d400 = 400;
  static const verificationCountDownTime = 60;
}

class ExceptionConstant {
  static const int exc480 = 480;
  static const int exc408 = 408;
  static const int exc400 = 400;
  static const int exc403 = 403;
  static const int exc401 = 401;
  static const int exc404 = 404;
  static const int exc409 = 409;
  static const int exc500 = 500;
}

class ResponseCode{
  static const int res200 = 200;
  static const int res400 = 400;
}
