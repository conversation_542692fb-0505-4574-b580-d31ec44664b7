import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class CoachProgramDataSource {
  Future<Response> createCoachProgram({required String coachId, required Map<String, dynamic> data});
  Future<Response> updateCoachProgram({required String coachId, required String programId, required Map<String, dynamic> data});
  Future<Response> getCoachProgramById({required String coachId, required String programId});
  Future<Response> getCoachPrograms({required String coachId});
  Future<Response> deleteCoachProgramById({required String coachId, required String programId});
  Future<Response> subscribeProgram({required String coachId, required String programId});
  Future<Response> paySubscriptionFee({required String subscriptionId, required Map<String, dynamic> data});
}

class CoachProgramDataSourceImpl extends CoachProgramDataSource{
  final RestClient restClient;

  CoachProgramDataSourceImpl({required this.restClient});
  
  @override
  Future<Response> createCoachProgram({required String coachId, required Map<String, dynamic> data}) async {
    final response = await restClient.post(APIType.PROTECTED, API.coachProgramCreate(coachId), data);

    return response;
  }
  
  @override
  Future<Response> updateCoachProgram({required String coachId, required String programId, required Map<String, dynamic> data}) async {
    final path = '${API.coachProgramUpdate(coachId)}/$programId';
    final response = await restClient.patch(APIType.PROTECTED, path, data);

    return response;
  }
  
  @override
  Future<Response> getCoachProgramById({required String coachId, required String programId}) async {
    final path = '${API.coachSingleProgram(coachId)}/$programId';
    final response = await restClient.get(APIType.PROTECTED, path);

    return response;
  }
  
  @override
  Future<Response> getCoachPrograms({required String coachId}) async {
    final response = await restClient.get(APIType.PROTECTED, API.coachPrograms(coachId));

    return response;
  }
  
  @override
  Future<Response> deleteCoachProgramById({required String coachId, required String programId}) async {
    final path = '${API.coachProgramDelete(coachId)}/$programId';
    final response = await restClient.delete(APIType.PROTECTED, path);

    return response;
  }
  
  @override
  Future<Response> paySubscriptionFee({required String subscriptionId, required Map<String, dynamic> data}) async {
    final path = API.subscriptionPaymentByBkash(subscriptionId);
    final response = await restClient.post(APIType.PROTECTED, path, data);

    return response;
  }
  
  @override
  Future<Response> subscribeProgram({required String coachId, required String programId}) async {
    final path = API.subscriptionToProgram(coachId, programId);
    final response = await restClient.post(APIType.PROTECTED, path, {});

    return response;
  }

}