import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/exception/network_exception.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/auth/root/data/data_sources/auth_data_source.dart';
import 'package:fitsomnia_app/src/features/auth/root/data/model/user_model.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/entities/user.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/repositories/auth_repository.dart';

class AuthRepositoryImp implements AuthRepository {
  const AuthRepositoryImp({required this.dataSource});

  final AuthRemoteDataSource dataSource;

  @override
  Future<Either<ErrorModel, User>> login(
    Map<String, dynamic> data,
  ) async {
    try {
      final Response response =
          await dataSource.loginWithEmailAndPassword(data);

      final token = response.data['data']['token'];

      return Right(User(token: token));
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel.error!);
    }
  }

  @override
  Future<Either<ErrorModel, User>> google() async {
    try {
      final Response response = await dataSource.loginWithGoogle();
      UserModel userModel = UserModel.fromJson(response.data);
      Log.debug('User token: ${userModel.token}');

      return Right(userModel);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel.error!);
    }
  }

  @override
  Future<Either<ErrorModel, User>> facebook() async {
    try {
      final Response response = await dataSource.loginWithFacebook();

      return Right(UserModel.fromJson(response.data));
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel.error!);
    }
  }

  @override
  Future<Either<ErrorModel, User>> apple() async {
    try {
      final Response response = await dataSource.loginWithApple();

      return Right(UserModel.fromJson(response.data));
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel.error!);
    }
  }

  @override
  Future<Either<ErrorModel, String>> registration(
    Map<String, dynamic> requestBody,
  ) async {
    try {
      Response response = await dataSource.registration(requestBody);
      String message = response.data['data']['message'];

      return Right(message);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel.error!);
    }
  }

  @override
  Future<Either<ErrorModel, String>> verification(
    Map<String, dynamic> map,
  ) async {
    late dynamic response;
    try {
      response = await dataSource.verification(map);
      final token = response.data['data']['token'];

      return Right(token);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel.error!);
    }
  }

  @override
  Future<Either<ErrorModel, bool>> resetPassword(
    Map<String, dynamic> map,
  ) async {
    try {
      await dataSource.resetPassword(map);

      return const Right(true);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel.error!);
    }
  }

  @override
  Future<Either<ErrorModel, bool>> sendOTP(
    Map<String, dynamic> requestBody,
  ) async {
    try {
      await dataSource.sendOTP(requestBody);

      return const Right(true);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel.error!);
    }
  }

  @override
  Future<Either<ErrorModel, bool>> identityVerification(
    Map<String, dynamic> requestBody,
  ) async {
    try {
      await dataSource.identityVerification(requestBody);

      return const Right(true);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel.error!);
    }
  }
}
