part of 'injection_container.dart';

Future<void> _initDataSources() async {
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImp(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<NotificationRemoteDataSource>(
    () => NotificationRemoteDataSourceImp(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<FeedRemoteDataSource>(
    () => FeedRemoteDataSourceImp(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<ProfileRemoteDataSource>(
    () => ProfileRemoteDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<PlannerRemoteDataSource>(
    () => PlannerRemoteDataSourceImp(),
  );

  sl.registerLazySingleton<SpotNotDataSource>(
    () => SpotNotRemoteDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<SharedRemoteDataSource>(
    () => SharedRemoteDataSourceImp(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<SpotNotRequestDataSource>(
    () => SpotNotRequestDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<ClubRemoteDataSource>(
    () => ClubRemoteDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<CreatePostRemoteDatasource>(
    () => CreatePostRemoteDatasourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<FileUploadRemoteDatasource>(
    () => FileUploadDatasourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<ScanQRCodeDataSource>(
    () => ScanQRCodeDataSourceImpl(),
  );

  /// Story
  sl.registerLazySingleton<StoryRemoteDatasource>(
    () => StoryRemoteDatasourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<StoryViewersRemoteDataSource>(
    () => StoryViewersRemoteDataSourceImp(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<AllUsersDataSource>(
    () => AllUsersDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<RecentConversationDataSource>(
    () => RecentConversationDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<GroupDataSource>(
    () => GroupDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<DeleteMessageDataSource>(
    () => DeleteMessageDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<OneToOneChatDataSource>(
    () => OneToOneChatDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<ActiveUserDataSource>(
    () => ActiveUserDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<CommentRemoteDataSource>(
    () => CommentRemoteDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<TrainingRemoteDataSource>(
    () => TrainingRemoteDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<ExerciseCategoryRemoteDataSource>(
    () => ExerciseCategoryRemoteDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<ProfileFeedDataSource>(
    () => ProfileFeedDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<MonthlyChallengeDataSource>(
    () => MonthlyChallengeDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<PointsDataSource>(
    () => PointsDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<ChangePasswordRemoteDataSource>(
    () => ChangePasswordRemoteDataSourceImp(
      client: sl.call(),
    ),
  );

  sl.registerLazySingleton<PreferenceRemoteDataSource>(
    () => PreferenceRemoteDataSourceImp(
      client: sl.call(),
    ),
  );

  sl.registerLazySingleton<ChangeEmailDataSource>(
    () => ChangeEmailDataSourceImp(
      client: sl.call(),
    ),
  );

  sl.registerLazySingleton<BlockRemoteDataSource>(
    () => BlockRemoteDataSourceImpl(
      client: sl.call(),
    ),
  );

  sl.registerLazySingleton<DietRemoteDataSource>(
    () => DietRemoteDataSourceImpl(
      client: sl.call(),
    ),
  );

  sl.registerLazySingleton<FoodRemoteDataSource>(
    () => FoodRemoteDataSourceImpl(
      client: sl.call(),
    ),
  );

  sl.registerLazySingleton<MuscleGroupDataSource>(
    () => MuscleGroupDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<BodyBuildingProgramDataSource>(
    () => BodyBuildingProgramDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<FitMarketRemoteDataSource>(
    () => FitMarketRemoteDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerLazySingleton<CartRemoteDataSource>(
    () => CartRemoteDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<ShippingAddressRemoteDataSource>(
    () => ShippingAddressRemoteDataSourceImp(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<SearchRemoteDataSource>(
    () => SearchRemoteDataSourceImp(
      client: sl.call(),
    ),
  );

  sl.registerFactory<ProductDetailsDataSource>(
    () => ProductDetailsDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<SpotMeDataSource>(
    () => SpotMeDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<WishlistDataSource>(
    () => WishlistDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<BillingAddressDataSource>(
    () => BillingAddressDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<OrderDataSource>(
    () => OrderHistoryDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<OrderSummaryDataSource>(
    () => OrderSummaryDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<PaymentDataSource>(
    () => PaymentDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<ReviewDataSource>(
    () => ReviewDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<SpotBackDataSource>(
    () => SpotBackDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<PostDetailsDataSource>(
    () => PostDetailsDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<DeleteSpotProfileDataSource>(
    () => DeleteSpotProfileDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  /// Likers Info
  sl.registerFactory<LikersInfoRemoteDataSource>(
    () => LikersInfoRemoteDataSourceImp(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<LogoutDataSource>(
    () => LogoutDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  ///Subscription
  sl.registerFactory<SubscriptionRemoteDataSource>(
    () => SubscriptionRemoteDataSourceImp(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<ValidateAddressDataSource>(
    () => ValidateAddressDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<DeleteAccountDataSource>(
    () => DeleteAccountDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<FitbotChatDataSource>(
    () => FitbotChatDataSoureceImp(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<PollDataSource>(
    () => PollDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<EventDataSource>(
    () => EventDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  /// Coach
  sl.registerFactory<CoachDataSource>(
    () => CoachDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<CoachProfileDataSource>(
    () => CoachProfileDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<CoachProgramDataSource>(
    () => CoachProgramDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<CoachPersonalDataSource>(
    () => CoachPersonalDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<UserProgramSubscriptionDataSource>(
    () => UserProgramSubscriptionDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<CoachNewsfeedDataSource>(
    () => CoachNewsfeedDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<CoachProgramReviewDataSource>(
    () => CoachProgramReviewDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<CoachWithdrawPaymentDatasource>(
    () => CoachWithdrawPaymentDatasourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<RewardPointDataSource>(
    () => RewardPointDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<RewardLeaderboardDataSource>(
    () => RewardLeaderboardDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<ReferralDataSource>(
    () => ReferralDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<DailyTaskDataSource>(
    () => DailyTaskDataSourceImpl(
      restClient: sl.call(),
    ),
  );

  sl.registerFactory<FoodScanDatasource>(
    () => FoodScanDatasourceImpl(
      restClient: sl.call(),
    ),
  );

}
