part of 'delete_message_bloc.dart';

class DeleteMessageState extends Equatable {
  @override
  List<Object?> get props => [];
}

class DeleteMessageInitial extends DeleteMessageState {}

class DeleteMessageLoading extends DeleteMessageState {}

class DeleteMessageSuccess extends DeleteMessageState {
  final String message;

  DeleteMessageSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class DeleteMessageFailure extends DeleteMessageState {
  final String errorMessage;

  DeleteMessageFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}
