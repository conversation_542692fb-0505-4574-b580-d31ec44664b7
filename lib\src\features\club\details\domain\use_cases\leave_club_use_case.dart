import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/repository/club_repository.dart';

class LeaveClubUseCase {
  const LeaveClubUseCase({required this.clubRepository});

  final ClubRepository clubRepository;

  Future<Either<ErrorResponseModel, String>> call(String clubId) async {
    return clubRepository.leaveClub(clubId);
  }
}
