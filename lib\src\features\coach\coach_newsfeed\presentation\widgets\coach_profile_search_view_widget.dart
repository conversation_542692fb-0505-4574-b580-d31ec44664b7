import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_profile_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/page/coach_profile_details_page.dart';
import 'package:fitsomnia_app/src/features/coach/root/domain/entities/coach_program_category_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CoachProfileSearchViewWidget extends StatefulWidget {
  const CoachProfileSearchViewWidget(
      {super.key, required this.coachProfileEntity});
  final CoachProfileEntity coachProfileEntity;

  @override
  State<CoachProfileSearchViewWidget> createState() =>
      _CoachProfileSearchViewWidgetState();
}

class _CoachProfileSearchViewWidgetState
    extends State<CoachProfileSearchViewWidget> {
  CoachEntity coachEntity = testCoachProfileEntity;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Log.debug('coach profile tap');
        Navigator.of(context).pushNamed(Routes.coachProfileDetailsPage,
            arguments: [null, widget.coachProfileEntity.coachId, true]);
      },
      child: _buildCoachPreviewWidget(),
    );
  }

  _buildCoachPreviewWidget() {
    return Container(
      color: AppColors.white,
      child: Stack(
        alignment: Alignment.center,
        children: [
          _buildProfileImage(),
          _buildProfileInformation(),
        ],
      ),
    );
  }

  _buildProfileImage() {
    // return Container(
    //   child: Image.network(
    //     widget.profileImage,
    //     fit: BoxFit.fitHeight,
    //     // width: double.infinity,
    //     // height: double.infinity,
    //   ),
    // );

    if (widget.coachProfileEntity.profilePictures.isEmpty) {
      return Image.asset(
        Assets.spotMeNoImage,
        width: double.infinity,
        height: double.infinity,
        // height: 250,
        // width: 190,
      );
    }

    // return ClipRRect(
    //   borderRadius: BorderRadius.circular(Values.v12.r),
    //   child: Image.network(
    //     widget.coachProfileEntity.profilePictures.first.url,
    //     height: 250,
    //     width: 190,
    //     cacheHeight: 650,
    //     cacheWidth: 500,
    //     fit: BoxFit.cover,
    //   ),
    // );

    return ImageContainer.rectangularImage(
      cornerRadius: Values.v10,
      image: widget.coachProfileEntity.profilePictures.first.url,
      width: double.infinity,
      height: double.infinity,
      // width: 190,
      // height: 250,
      fit: BoxFit.fill,
      useOriginal: false,
      useMedium: true,
      hideLoadingIndicator: true,
      errorWidget: Container(
        height: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Values.v10),
          image: DecorationImage(
            image: const Image(
              image: AssetImage(
                Assets.spotMeNoImage,
              ),
            ).image,
            fit: BoxFit.fill,
          ),
        ),
      ),
    );
  }

  _buildProfileInformation() {
    return Container(
      height: double.infinity,
      width: double.infinity,
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(Values.v18),
        gradient: _buildLinearGradientWhiteToBlack(),
        // color: UIColors.black.withOpacity(0.30),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCoachRatingSubscriberSection(),
          Spacer(),
          Text(
            widget.coachProfileEntity.legalName,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: AppTypography.poppinsSemiBold16(color: AppColors.white),
          ),
          Text(
            '${widget.coachProfileEntity.expertise}',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: AppTypography.poppinsRegular12(color: AppColors.white),
          )
        ],
      ),
    );
  }

  LinearGradient _buildLinearGradientWhiteToBlack() {
    return LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        AppColors.transparent,
        UIColors.black.withOpacity(0.3),
      ],
    );
  }

  _buildCoachRatingSubscriberSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildCoachRatingInfo(),
        Spacer(),
        _buildCoachSubscriberInfo(),
      ],
    );
  }

  _buildCoachRatingInfo() {
    return Container(
      padding:
          EdgeInsets.symmetric(horizontal: Values.v8.w, vertical: Values.v4),
      decoration: BoxDecoration(
        color: AppColors.black.withOpacity(0.5),
        borderRadius: BorderRadius.circular(Values.v15),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            Assets.coachRateIcon,
          ),
          SizedBox(
            width: 3,
          ),
          Text(
            '${widget.coachProfileEntity.currentRating} ',
            style: AppTypography.poppinsRegular12(color: AppColors.white),
          ),
        ],
      ),
    );
  }

  _buildCoachSubscriberInfo() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: Values.v8.h),
      padding:
          EdgeInsets.symmetric(horizontal: Values.v8.w, vertical: Values.v4),
      decoration: BoxDecoration(
        color: AppColors.black.withOpacity(0.5),
        borderRadius: BorderRadius.circular(Values.v15),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon(
          //   Icons.star,
          //   color: UIColors.purple500,
          //   size: Values.v16,
          // ),
          SvgPicture.asset(
            Assets.coachSubscribeIcon,
          ),
          SizedBox(
            width: 3,
          ),
          Text(
            '${widget.coachProfileEntity.subscriptionCount}',
            // '${30} Subscriber',
            style: AppTypography.poppinsRegular12(color: AppColors.white),
          )
        ],
      ),
    );
  }
}
