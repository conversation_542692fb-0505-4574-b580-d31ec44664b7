import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:camera/camera.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/camera/app_camera_service.dart';
import 'package:fitsomnia_app/src/core/services/camera/bloc/camera_bloc.dart';
import 'package:fitsomnia_app/src/core/services/camera/bloc/camera_cubit.dart';
import 'package:fitsomnia_app/src/core/services/media/ui/widgets/trimmer_view.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/challenge_details/presentation/page/preview_recorded_video_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart' as path_provider;
import 'package:image/image.dart' as img;

class FoodCameraFooterWidget extends StatefulWidget {
  const FoodCameraFooterWidget({
    Key? key,
    this.showMediaIcon = true,
    this.onlyRecord = false,
  }) : super(key: key);

  final bool showMediaIcon;
  final bool onlyRecord;

  @override
  State<FoodCameraFooterWidget> createState() => _FoodCameraFooterWidgetState();
}

class _FoodCameraFooterWidgetState extends State<FoodCameraFooterWidget> {
  final ImagePicker _picker = ImagePicker();
  bool isRecording = false;
  double transform = 0;
  Timer? _timer;
  late GestureDetector captureIcon;

  @override
  void initState() {
    super.initState();
    captureIcon = _buildCaptureIcon(null);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black,
      padding: EdgeInsets.only(top: Values.v5.h, bottom: Values.v5.h),
      width: MediaQuery.of(context).size.width,
      child: Column(
        children: [
          BlocBuilder<CameraBloc, CameraState>(
            builder: (context, state) {
              if (state.cameraController != null) {
                captureIcon = _buildCaptureIcon(state.cameraController);
              }

              return state.cameraController == null
                  ? const Center(
                      child: CircularProgressIndicator(),
                    )
                  : Column(
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Opacity(
                              opacity: widget.showMediaIcon ? 1 : 0,
                              child: _buildGalleryIcon(),
                            ),
                            captureIcon,
                            Opacity(
                              opacity: 1,
                              child: _buildFlipCameraIcon(
                                state.cameraController!,
                                doNothing: isRecording,
                              ),
                            ),
                          ],
                        ),
                      ],
                    );
            },
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    if (_timer != null) {
      _timer!.cancel();
    }
    super.dispose();
  }

  bool _isPickingImage = false;

  Widget _buildGalleryIcon() {
    return Opacity(
      opacity: 1,
      child: IconButton(
        icon: Icon(Icons.image, color: Colors.white, size: Values.v28.r),
        onPressed: (isRecording || _isPickingImage)
            ? null
            : () async {
                try {
                  setState(() {
                    _isPickingImage = true;
                  });
                  await _pickImage(context);
                } catch (e) {
                  Log.error('Error picking image: $e');
                } finally {
                  setState(() {
                    _isPickingImage = false;
                  });
                }
              },
      ),
    );
  }

  Future<void> _pickImage(BuildContext context) async {
    try {
      final XFile? image = await _picker.pickImage(
          source: ImageSource.gallery, imageQuality: 80);
      if (image != null && mounted) {
        _navigateToScanDetailsPageFroGalleryImage(context, image.path);
      }
    } catch (e) {
      Log.error('Error picking image: $e');
      AppToast.showToast(message: 'Failed to pick image from gallery.');
    }
  }

  //ignore: long-method
  GestureDetector _buildCaptureIcon(CameraController? cameraController) {
    if (cameraController == null) return GestureDetector();

    return GestureDetector(
      onTap: () async {
        Log.debug('Capture icon tapped');

        try {
          if (!cameraController.value.isInitialized) {
            await cameraController.initialize();
          }

          if (cameraController.value.isTakingPicture) {
            Log.debug('Already recording, stopping...');

            return;
          }

          XFile file = await cameraController.takePicture();
          if (mounted) {
            _navigateToScanDetailsPage(context, file.path);
          }
        } catch (e) {
          Log.error('Error capturing image: $e');
        }
      },
      child: Icon(
        Icons.panorama_fish_eye,
        color: Colors.white,
        size: Values.v70.r,
      ),
    );
  }

  Widget _buildFlipCameraIcon(CameraController cameraController,
      {bool doNothing = false}) {
    return IconButton(
        icon: Transform.rotate(
          angle: transform,
          child: Icon(
            Icons.loop,
            color: Colors.white,
            size: Values.v28.r,
          ),
        ),
        onPressed: !doNothing
            ? () async {
                CameraLensDirection cameraLensDirection =
                    CameraLensDirection.back;
                if (cameraController.description.lensDirection ==
                    CameraLensDirection.back) {
                  cameraLensDirection = CameraLensDirection.front;
                }
                CameraController newController = AppCameraService.instance
                    .selectNewCamera(cameraLensDirection);

                setState(() {
                  transform = transform + pi;
                });
                context
                    .read<CameraBloc>()
                    .add(ChangeCameraEvent(newController));
              }
            : null);
  }

  Future<String> _compressImage(File file) async {
    try {
      final dir = await path_provider.getTemporaryDirectory();
      final targetPath =
          "${dir.absolute.path}/compressed_${DateTime.now().millisecondsSinceEpoch}.jpg";

      final result = await FlutterImageCompress.compressWithList(
        await file.readAsBytes(),
        minWidth: 1024,
        minHeight: 1024,
        quality: 50,
        rotate: 0,
      );

      // Write the compressed data to a file
      final compressedFile = File(targetPath);
      await compressedFile.writeAsBytes(result);

      Log.debug('Compressed image saved to: ${compressedFile.path}');
      Log.debug('Original size: ${file.lengthSync() / (1024 * 1024)} MB');
      Log.debug(
          'Compressed size: ${compressedFile.lengthSync() / (1024 * 1024)} MB');

      return compressedFile.path;
    } catch (e) {
      Log.error('Error compressing image: $e');
    }

    return file.path; // Return original path if compression fails
  }

  Future<String> _cropImage({required File file}) async {
    try {
      final dir = await path_provider.getTemporaryDirectory();
      final targetPath =
          "${dir.absolute.path}/crop_${DateTime.now().millisecondsSinceEpoch}.jpg";

      final bytes = await file.readAsBytes();

      img.Image? currentImage = img.decodeImage(bytes);

      if (currentImage == null) {
        Log.debug('failed to decode image');

        return file.path;
      }

      int imageWidth = currentImage.width;
      int imageHeight = currentImage.height;
      double scaleX = imageWidth / Values.defaultWidth;
      double scaleY = imageHeight / Values.defaultHeight;

      double screenX = 45;
      double screenY = 220;
      double screenCropWidth = 350;
      double screenCropHeight = 450;

      int cropX = (screenX * scaleX).toInt();
      int cropY = (screenY * scaleY).toInt();
      int cropWidth = (screenCropWidth * scaleX).toInt();
      int cropHeight = (screenCropHeight * scaleY).toInt();

      img.Image croppedImage = img.copyCrop(currentImage,
          x: cropX, y: cropY, height: cropHeight, width: cropWidth);

      final croppedBytes = img.encodeJpg(croppedImage);
      await File(targetPath).writeAsBytes(croppedBytes);

      return targetPath;
    } catch (e) {
      Log.debug('image crop failed');

      return file.path;
    }
  }

  _navigateToScanDetailsPage(
    BuildContext context,
    String imagePath,
  ) async {
    // String croppedImagePath = await _cropImage(file: File(imagePath));

    String compressedImagePath = await _compressImage(File(imagePath));
    Navigator.pushNamed(
      context,
      Routes.scanFoodDetailsPage,
      arguments: [compressedImagePath],
    );
  }

  _navigateToScanDetailsPageFroGalleryImage(
    BuildContext context,
    String imagePath,
  ) async {
    String compressedImagePath = await _compressImage(File(imagePath));
    Navigator.pushNamed(
      context,
      Routes.scanFoodDetailsPage,
      arguments: [compressedImagePath],
    );
  }
}
