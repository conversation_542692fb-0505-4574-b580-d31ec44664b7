import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';

abstract class CoachProfileRepository {
  Future<Either<ErrorModel, CoachEntity>> createCoachProfile({ required CoachEntity registrationEntity});
  Future<Either<ErrorModel, CoachEntity>> updateCoachProfile({required String coachId, required CoachEntity coachEntity});
}