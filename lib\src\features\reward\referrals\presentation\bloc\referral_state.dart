part of 'referral_bloc.dart';

sealed class ReferralState extends Equatable {
  const ReferralState();
  
  @override
  List<Object> get props => [];
}

final class ReferralInitial extends ReferralState {}
final class ReferralLoading extends ReferralState {}

class GetReferralsSuccess extends ReferralState {
  final List<ReferralDataEntity> referrals;

  GetReferralsSuccess({required this.referrals});

  @override
  List<Object> get props => [referrals];

}

class GetReferralsFail<T> extends ReferralState {
  final T? data;

  GetReferralsFail({required this.data});

}

class GetSingleReferralSuccess extends ReferralState {
  final ReferralDataEntity referralDataEntity;

  GetSingleReferralSuccess({required this.referralDataEntity});

  @override
  List<Object> get props => [referralDataEntity];

}

class GetSingleReferralFail<T> extends ReferralState {
  final T? data;

  GetSingleReferralFail({required this.data});

}

class GetReferralCodeSuccess extends ReferralState {
  final ReferralCodeEntity referralCodeEntity;

  GetReferralCodeSuccess({required this.referralCodeEntity});

  @override
  List<Object> get props => [referralCodeEntity];

}

class GetReferralCodeFail<T> extends ReferralState {
final T? data;

  GetReferralCodeFail({required this.data});
}

class UserReferralCodeSuccess extends ReferralState {
  final ReferralDataEntity referralDataEntity;

  UserReferralCodeSuccess({required this.referralDataEntity});

  @override
  List<Object> get props => [referralDataEntity];

}

class UserReferralCodeFail<T> extends ReferralState {
  final T? data;

  UserReferralCodeFail({required this.data});

}
