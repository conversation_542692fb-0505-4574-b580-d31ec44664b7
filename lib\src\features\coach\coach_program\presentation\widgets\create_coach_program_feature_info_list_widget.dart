import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/validators/input_validators.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/pages/create_coach_program_page.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CreateCoachProgramFeatureInfoListWidget extends StatefulWidget {
  const CreateCoachProgramFeatureInfoListWidget({
    Key? key,
    required this.features,
    required this.callback,
  }) : super(key: key);
  final List<CoachProgramFeature> features;
  final Function(List<CoachProgramFeature>)? callback;

  @override
  State<CreateCoachProgramFeatureInfoListWidget> createState() =>
      CreateCoachProgramFeatureInfoListWidgetState();
}

class CreateCoachProgramFeatureInfoListWidgetState
    extends State<CreateCoachProgramFeatureInfoListWidget> with AutomaticKeepAliveClientMixin<CreateCoachProgramFeatureInfoListWidget>{
  final int minFeatureCount = 1;
  final int maxFeatureCount = 5;

  List<CoachProgramFeature> programFeatures = [
    CoachProgramFeature(featureTitle: '', featureDescription: '')
  ];

  @override
  void initState() {
    if (widget.features.isNotEmpty) {
      programFeatures = widget.features;
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPageHeader(),
          _buildProgramFeatureSection(),
          _buildAddFeatureButton(),
          const SizedBox(height: 50,)
        ],
      ),
    );
  }

  _buildPageHeader() {
    return Text(
      'Program Features',
      style: AppTypography.poppinsBold20(color: UIColors.primaryGreen950),
    );
  }

  _buildProgramFeatureSection() {
    return Form(
      key: programFeaturesFormValidationKey,
      child: Column(
        children: programFeatures
            .asMap()
            .map((index, featureEntity) {
              return MapEntry(
                  index, _buildInputFeatureSection(index: index) as Widget);
            })
            .values
            .toList(),
      ),
    );
  }

  _buildInputFeatureSection({required int index}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      margin: const EdgeInsets.only(top: 10, bottom: 10),
      decoration: BoxDecoration(
          border: Border.all(color: UIColors.fitGrey),
          borderRadius: BorderRadius.circular(10)),
      child: Column(
        children: [
          _buildFeatureDeleteIconSection(index: index),
          _buildFeatureTitleSection(index: index),
          const SizedBox(
            height: 20,
          ),
          _buildFeatureDescriptioSection(index: index),
          const SizedBox(
            height: 5,
          ),
        ],
      ),
    );
  }

  _buildFeatureTitleSection({required int index}) {
    return _buildEditTextInfo(
      name: 'Feature Title ( 0${index + 1} )',
      text: programFeatures[index].featureTitle,
      controller: TextEditingController(),
      validator: InputValidators.name,
      onChangeCallback: (String? title) {
        programFeatures[index].featureTitle = title ?? '';
        Log.debug('$title');
        // widget.callback(programFeatures);
      },
    );
  }

  _buildFeatureDescriptioSection({required int index}) {
    return _buildEditTextInfo(
      name: 'Description',
      text: programFeatures[index].featureDescription,
      controller: TextEditingController(),
      validator: InputValidators.name,
      maxLine: 5,
      onChangeCallback: (String? desc) {
        programFeatures[index].featureDescription = desc ?? '';
        Log.debug('$desc');
        // widget.callback(programFeatures);
      },
    );
  }

  _buildFeatureDeleteIconSection({required int index}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        IconButton(
          onPressed: () {
            Log.debug('feature delete button pressed');
            if (programFeatures.length == 1) {
              AppToast.showToast(
                  message: 'must have at least one feature',
                  gravity: ToastGravity.BOTTOM);
            } else {
              programFeatures.removeAt(index);
              setState(() {
                '';
              });
            }
          },
          icon: const Icon(
            Icons.close_rounded,
            size: 24,
          ),
        )
      ],
    );
  }

  _buildEditTextInfo({
    required String name,
    required String? text,
    required TextEditingController controller,
    required String? Function(String?)? validator,
    int maxLine = 1,
    TextInputType? keyboardType = null,
    Function(String?)? onChangeCallback,
  }) {
    if (text != null) controller.text = text;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "$name",
          style:
              AppTypography.poppinsRegular16(color: UIColors.primaryGreen950),
        ),
        SizedBox(
          height: Values.v5,
        ),
        TextFormField(
          cursorColor: AppColors.black,
          controller: controller,
          onChanged: onChangeCallback,
          maxLines: maxLine,
          minLines: maxLine,
          autofocus: false,
          autocorrect: false,
          decoration: _buildInputDecoration(),
          validator: validator,
          keyboardType: keyboardType,
          autovalidateMode: AutovalidateMode.onUserInteraction,
        )
      ],
    );
  }

  InputDecoration _buildInputDecoration() {
    var inputBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(Values.v10),
      borderSide: BorderSide(color: AppColors.greyscale50, width: Values.v2),
    );

    return InputDecoration(
      enabledBorder: inputBorder,
      focusedBorder: inputBorder,
      // hintText: 'Write a short bio?',
      hintStyle: AppTypography.regular18(
        color: AppColors.silver,
      ),
      contentPadding: EdgeInsets.all(Values.v16),
      // filled: true,
      // fillColor: AppColors.alto.withOpacity(0.2),
    );
  }

  _buildAddFeatureButton() {
    return Button.filled(
      label: 'Add Feature',
      onPressed: () {
        Log.debug('add feature button pressed');
        if (programFeatures.length >= maxFeatureCount) {
          AppToast.showToast(message: 'Can not add more than 5 features');
        } else {
          programFeatures.add(
              CoachProgramFeature(featureTitle: '', featureDescription: ''));
          setState(() {
            '';
          });
        }
      },
      width: 200,
      background: AppColors.greyscale500,
      borderColor: AppColors.white,
    );
  }
  
  @override
  bool get wantKeepAlive => true;
}
