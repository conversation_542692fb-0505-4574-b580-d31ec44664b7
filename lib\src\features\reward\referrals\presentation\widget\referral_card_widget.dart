import 'dart:ui';

import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/entity/referral_data_entity.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/presentation/widget/referral_details_info_widget.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';

class ReferralCardWidget extends StatefulWidget {
  const ReferralCardWidget({
    super.key,
    required this.referralDataEntity,
  });
  final ReferralDataEntity referralDataEntity;

  @override
  State<ReferralCardWidget> createState() => _ReferralCardWidgetState();
}

class _ReferralCardWidgetState extends State<ReferralCardWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: ExactAssetImage(Assets.referralBackgroundImg),
          fit: BoxFit.cover,
          opacity: 0.15,
        ),
        color: UIColors.primaryGreen950,
        // gradient: RadialGradient(colors: [UIColors.primaryGreen900, Colors.transparent], radius: 1.0),
        borderRadius: BorderRadius.circular(Values.v10),
      ),
      child: Stack(
        alignment: AlignmentDirectional.center,
        children: [
          // _buildBackgroundImageSection(),
          _buildInformationSection(),
        ],
      ),
    );
  }

  _buildBackgroundImageSection() {
    return Container(
      height: 200,
      width: 400,
      decoration: BoxDecoration(
        image: DecorationImage(
            image: ExactAssetImage(Assets.eventBackgroundGroup)),
        color: UIColors.transparent.withValues(alpha: 0.4),
        // gradient: RadialGradient(colors: [UIColors.primaryGreen900, Colors.transparent], radius: 1.0),
        borderRadius: BorderRadius.circular(Values.v10),
      ),
      // height: 150,
      // width: double.maxFinite,
      // child: BackdropFilter(
      //   filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
      //   child: Container(
      //     decoration: BoxDecoration(color: Colors.white.withOpacity(0.0)),
      //   ),
      // ),
    );
  }

  _buildInformationSection() {
    return Container(
      padding: EdgeInsets.all(Values.v30),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTitleSection(),
                _buildPointSection(),
                _buildAmountSection(),
                SizedBox(
                  height: Values.v10,
                ),
                _buildViewRules(),
                // SizedBox(
                //   height: Values.v10,
                // ),
                // _buildShareCode()
              ],
            ),
          )
        ],
      ),
    );
  }

  _buildTitleSection() {
    return Flexible(
      child: Text(
        '${widget.referralDataEntity.referralTitle}',
        style: AppTypography.poppinsRegular14(color: UIColors.white),
        overflow: TextOverflow.ellipsis,
        maxLines: 2,
        textAlign: TextAlign.center,
      ),
    );
  }

  _buildPointSection() {
    // return Text(
    //   '${widget.referralDataEntity.totalPoint} Points',
    //   style: AppTypography.poppinsBold40(color: UIColors.white),
    // );

    var textStyle = TextStyle(
      fontSize: FontSize.s40,
      fontFamily: FontConstants.poppinsFontFamily,
      fontWeight: FontWeight.bold,
      color: UIColors.white,
      height: 1.4,
    );

    var textStyle2 = TextStyle(
      fontSize: FontSize.s40,
      fontFamily: FontConstants.poppinsFontFamily,
      fontWeight: FontWeight.bold,
      color: UIColors.primaryGreen400,
      height: 1.4,
    );

    return RichText(
      text: TextSpan(
        text: '${widget.referralDataEntity.totalPoint}',
        style: textStyle,
        children: <TextSpan>[
          TextSpan(
            text: ' Points',
            style: textStyle2,
          ),
        ],
      ),
    );
  }

  _buildAmountSection() {
    return Text(
      'Get Rewarded Upto ${widget.referralDataEntity.totalAmount}tk !',
      style: AppTypography.poppinsRegular14(color: UIColors.white),
    );
  }

  _buildViewRules() {
    return Button.filled(
      label: 'View Rules',
      onPressed: () async {
        Log.debug('view rules pressed');
        _showReferralRulesAlert();
      },
      width: Values.v150,
    );
  }

  _buildShareCode() {
    return Button.filled(
      label: 'ShareCode',
      onPressed: () async {
        Log.debug('view rules pressed');
        ShareResult result = await SharePlus.instance.share(
          ShareParams(
              text:
                  '${widget.referralDataEntity.referralTitle} \n Earn Points: ${widget.referralDataEntity.totalPoint} \n Code: ${widget.referralDataEntity.referralCode}'),
        );

        Log.debug('Share result: ${result.status}');
      },
      width: Values.v150,
    );
  }

  _showReferralRulesAlert() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(Values.v10))),
          contentPadding: EdgeInsets.all(1),
          // titlePadding: EdgeInsets.all(1),
          // actionsPadding: EdgeInsets.all(1),
          // buttonPadding: EdgeInsets.all(1),
          // insetPadding: EdgeInsets.all(1),
          content: ReferralDetailsInfoWidget(
            referralId: widget.referralDataEntity.referralId,
          ),
        );
      },
      barrierDismissible: true,
    );
  }
}
