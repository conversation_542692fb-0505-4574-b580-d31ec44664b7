import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:outline_gradient_button/outline_gradient_button.dart';

class AppOutlineGradientButton extends StatelessWidget {
  const AppOutlineGradientButton({
    Key? key,
    required this.onTap,
    required this.title,
    this.weight,
    this.height,
    this.titleColor,
    this.gradientStartColor,
    this.gradientEndColor,
    this.radius,
    this.strokeWidth,
    this.backgroundColor,
    this.textStyle,
  }) : super(key: key);

  final VoidCallback onTap;
  final String title;
  final double? weight;
  final double? height;
  final Color? titleColor;
  final Color? gradientStartColor;
  final Color? gradientEndColor;
  final double? radius;
  final double? strokeWidth;
  final Color? backgroundColor;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: weight ?? double.infinity,
      height: height ?? Values.v40.h,
      child: OutlineGradientButton(
        onTap: onTap,
        inkWell: true,
        strokeWidth: strokeWidth ?? 2.r,
        radius: Radius.circular(radius ?? Values.v3.r),
        gradient: LinearGradient(
          colors: [
            gradientEndColor ?? AppColors.primaryGreen,
            gradientStartColor ?? AppColors.primaryGreen,
          ],
        ),
        backgroundColor: backgroundColor ?? UIColors.secondary.withOpacity(0.6),
        child: Center(
          child: Text(
            title,
            style: textStyle ??
                AppTypography.semiBold18(
                  color: titleColor ?? AppColors.black,
                ),
          ),
        ),
      ),
    );
  }
}
