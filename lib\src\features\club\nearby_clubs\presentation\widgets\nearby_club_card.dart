import 'package:cached_network_image/cached_network_image.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/services/firebase/firebase_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/club/dashboard/presentation/bloc/club_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/club/nearby_clubs/presentation/bloc/nearby_clubs_bloc.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/entities/club_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NearbyClubCard extends StatefulWidget {
  const NearbyClubCard({
    Key? key,
    required this.club,
  }) : super(key: key);

  final ClubEntity club;

  @override
  State<NearbyClubCard> createState() => _NearbyClubCardState();
}

class _NearbyClubCardState extends State<NearbyClubCard> {
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<NearbyClubsBloc, BaseState>(
      listener: (context, state) {
        if (state is JoinClubSuccessState) {
          setState(() => isLoading = false);
          context.read<ClubDashboardBloc>().add(ClubMembershipCheckEvent());
        }
      },
      builder: (context, state) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 8.h),
          decoration: _boxDecoration(),
          child: ListTile(
            horizontalTitleGap: 10.w,
            leading: _buildClubLogo(),
            title: Text(
              widget.club.name,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.black,
              ),
            ),
            trailing: _buildClubJoinButton(),
          ),
        );
      },
    );
  }

  BoxDecoration _boxDecoration() {
    return BoxDecoration(
      color: AppColors.white,
      border: Border(
        bottom: BorderSide(
          color: AppColors.alto_08,
        ),
      ),
    );
  }

  Widget _buildClubLogo() {
    return Container(
      width: 52.w,
      height: 52.h,
      decoration: BoxDecoration(
        color: AppColors.transparent,
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.primaryGreen,
          width: 1.w,
        ),
        image: DecorationImage(
          image: CachedNetworkImageProvider(
            widget.club.image.logo.contains('/original/')
                ? widget.club.image.logo
                    .replaceFirst('/original/', '/thumbnail/original/')
                : widget.club.image.logo,
            cacheKey: widget.club.name,
          ),
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  Widget _buildClubJoinButton() {
    return Button.outlined(
      onPressed: () {
        _showDisclaimerAgreementAlert();
        FirebaseService().logFeatureUsage('club', 'club_card', '');
      },
      isLoading: isLoading,
      width: 110.w,
      height: 30.h,
      scale: 0.8,
      label: "Join",
      textStyle: const TextStyle(
        fontWeight: FontWeight.w400,
        color: AppColors.black,
      ),
    );
  }

  //ignore: long-method
  _showDisclaimerAgreementAlert() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text("Disclaimer"),
          content: SizedBox(
            height: MediaQuery.of(context).size.height * 0.4,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const Text(
                    TextConstants.clubDisclaimer1,
                    textAlign: TextAlign.start,
                  ),
                  SizedBox(height: 16.h),
                  const Text(
                    TextConstants.clubDisclaimer2,
                    textAlign: TextAlign.start,
                  ),
                  SizedBox(height: 16.h),
                  const Text(
                    TextConstants.clubDisclaimer3,
                    textAlign: TextAlign.start,
                  ),
                  SizedBox(height: 16.h),
                  const Text(
                    TextConstants.clubDisclaimer4,
                    textAlign: TextAlign.start,
                  ),
                  SizedBox(height: 16.h),
                  const Text(
                    TextConstants.clubDisclaimer5,
                    textAlign: TextAlign.start,
                  ),
                  SizedBox(height: 16.h),
                  const Text(
                    TextConstants.clubDisclaimer6,
                    textAlign: TextAlign.start,
                  ),
                  SizedBox(height: 16.h),
                ],
              ),
            ),
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Button.outlined(
                    label: 'Cancel',
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  flex: 2,
                  child: Button(
                    label: 'Agree',
                    onPressed: () {
                      Navigator.of(context).pop();
                      setState(() => isLoading = true);
                      context.read<NearbyClubsBloc>().add(
                            JoinClubEvent(
                              clubId: widget.club.id,
                            ),
                          );
                    },
                  ),
                ),
              ],
            )
          ],
        );
      },
    );
  }
}
