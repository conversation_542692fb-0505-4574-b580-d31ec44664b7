import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/spot_not_request/domain/use_cases/cancel_spot_request_use_case.dart';
import 'package:fitsomnia_app/src/features/spot_not_request/domain/use_cases/not_request_use_case.dart';
import 'package:fitsomnia_app/src/features/spot_not_request/domain/use_cases/send_spot_request_use_case.dart';
import 'package:fitsomnia_app/src/features/spot_not_request/domain/use_cases/update_request_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'spot_not_state.dart';

class SpotNotCubit extends Cubit<SpotNotState> {
  SpotNotCubit({
    required this.sendRequestUseCase,
    required this.cancelRequestUseCase,
    required this.updateRequestUseCase,
    required this.notRequestUseCase,
  }) : super(const SpotNotState());

  final SendSpotRequestUseCase sendRequestUseCase;
  final CancelSpotRequestUseCase cancelRequestUseCase;
  final UpdateRequestUseCase updateRequestUseCase;
  final NotRequestUseCase notRequestUseCase;

  Future<void> sendRequest({
    required String id,
  }) async {
    emit(state.copyWith(
      status: SpotNotStatus.loading,
      spotRequestStatus: SpotRequestStatus.SENT,
      id: id,
    ));

    try {
      final result = await sendRequestUseCase.sendRequest(
        recipientId: id,
      );

      result.fold(
        (l) => emit(
          state.copyWith(
            status: SpotNotStatus.error,
            spotRequestStatus: SpotRequestStatus.SENT,
            error: l.message,
          ),
        ),
        (r) => emit(
          state.copyWith(
            status: SpotNotStatus.success,
            spotRequestStatus: SpotRequestStatus.SENT,
            data: r,
          ),
        ),
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      emit(
        state.copyWith(
          status: SpotNotStatus.error,
          spotRequestStatus: SpotRequestStatus.SENT,
          error: TextConstants.defaultErrorMessage,
        ),
      );
    }
  }

  Future<void> cancelRequest({
    required String id,
  }) async {
    emit(state.copyWith(
      status: SpotNotStatus.loading,
      spotRequestStatus: SpotRequestStatus.CANCELLED,
      id: id,
    ));

    try {
      final result = await cancelRequestUseCase.cancelRequest(
        id: id,
      );

      result.fold(
        (l) => emit(
          state.copyWith(
            status: SpotNotStatus.error,
            spotRequestStatus: SpotRequestStatus.CANCELLED,
            error: l.message,
          ),
        ),
        (r) => emit(
          state.copyWith(
            status: SpotNotStatus.success,
            spotRequestStatus: SpotRequestStatus.CANCELLED,
            data: r,
          ),
        ),
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      emit(
        state.copyWith(
          status: SpotNotStatus.error,
          spotRequestStatus: SpotRequestStatus.CANCELLED,
          error: TextConstants.defaultErrorMessage,
        ),
      );
    }
  }

  Future<void> updateRequest({
    required String requesterId,
    required bool isAccepted,
  }) async {
    emit(
      state.copyWith(
        status: SpotNotStatus.loading,
        spotRequestStatus: isAccepted
            ? SpotRequestStatus.ACCEPTED
            : SpotRequestStatus.REJECTED,
        id: requesterId,
      ),
    );

    try {
      final result = await updateRequestUseCase.call(
        id: requesterId,
        requestBody: {
          'isAccepted': isAccepted,
        },
      );

      result.fold(
        (l) => emit(
          state.copyWith(
            status: SpotNotStatus.error,
            spotRequestStatus: isAccepted
                ? SpotRequestStatus.ACCEPTED
                : SpotRequestStatus.REJECTED,
            error: l,
          ),
        ),
        (r) => emit(
          state.copyWith(
            status: SpotNotStatus.success,
            spotRequestStatus: isAccepted
                ? SpotRequestStatus.ACCEPTED
                : SpotRequestStatus.REJECTED,
            data: r,
          ),
        ),
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      emit(
        state.copyWith(
          status: SpotNotStatus.error,
          spotRequestStatus: isAccepted
              ? SpotRequestStatus.ACCEPTED
              : SpotRequestStatus.REJECTED,
          error: TextConstants.defaultErrorMessage,
        ),
      );
    }
  }

  Future<void> notRequest({
    required String userId,
  }) async {
    emit(
      state.copyWith(
        status: SpotNotStatus.loading,
        spotRequestStatus: SpotRequestStatus.REJECTED,
        id: userId,
      ),
    );

    try {
      final result = await notRequestUseCase.call(
        userId: userId,
      );

      result.fold(
        (l) => emit(
          state.copyWith(
            status: SpotNotStatus.error,
            spotRequestStatus: SpotRequestStatus.REJECTED,
            error: l.message,
          ),
        ),
        (r) => emit(
          state.copyWith(
            status: SpotNotStatus.success,
            spotRequestStatus: SpotRequestStatus.REJECTED,
            data: r,
          ),
        ),
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      emit(
        state.copyWith(
          status: SpotNotStatus.error,
          spotRequestStatus: SpotRequestStatus.REJECTED,
          error: TextConstants.defaultErrorMessage,
        ),
      );
    }
  }
}
