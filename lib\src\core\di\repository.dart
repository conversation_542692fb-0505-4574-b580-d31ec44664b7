part of 'injection_container.dart';

Future<void> _initRepositories() async {
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImp(
      dataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<NotificationRepository>(
    () => NotificationRepositoryImp(
      dataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<FeedRepository>(
    () => FeedRepositoryImp(
      feedRemoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<ProfileRepository>(
    () => ProfileRepositoryImpl(
      profileRemoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<PlannerRepository>(
    () => PlannerRepositoryImp(),
  );

  sl.registerLazySingleton<SpotNotRepository>(
    () => SpotNotRepositoryImpl(
      spotNotDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<SharedRepository>(
    () => SharedRepositoryImp(
      sharedRemoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<SpotNotRequestRepository>(
    () => SpotNotRequestRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<ClubRepository>(
    () => ClubRepositoryImpl(
      clubRemoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<CreatePostRepository>(
    () => CreatePostRepositoryImpl(
      createPostRemoteDatasource: sl.call(),
    ),
  );

  sl.registerLazySingleton<FileUploadRepository>(
    () => FileUploadRepositoryImpl(
      fileUploadDatasource: sl.call(),
    ),
  );

  sl.registerLazySingleton<QRCodeRepository>(
    () => QRCodeRepositoryImpl(
      scanQRDataSource: sl.call(),
    ),
  );

  /// Story
  sl.registerLazySingleton<StoryRepository>(
    () => StoryRepositoryImpl(
      storyRemoteDatasource: sl.call(),
    ),
  );

  sl.registerLazySingleton<StoryViewersRepository>(
    () => StoryViewersRepositoryImp(
      storyViewersRemoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<AllUsersRepository>(
    () => AllUsersRepositoryImpl(
      allUsersDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<ChatListRepository>(
    () => ChatListRepositoryImpl(
      recentConversationDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<GroupRepository>(
    () => GroupRepositoryImpl(
      groupDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<DeleteMessageRepository>(
    () => DeleteMessageRepositoryImpl(
      deleteMessageDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<OneToOneChatRepository>(
    () => OneToOneChatRepositoryImpl(
      oneToOneChatDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<ActiveUserRepository>(
    () => ActiveUserRepositoryImpl(
      activeUserDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<CommentRepository>(
    () => CommentRepositoryImpl(
      commentRemoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<MonthlyChallengeRepository>(
    () => MonthlyChallengeRepositoryImpl(
      monthlyChallengeDataSource: sl.call(),
      fileUploadDatasource: sl.call(),
    ),
  );

  sl.registerLazySingleton<ImagePickingRepository>(
    () => ImagePickingRepositoryImpl(),
  );

  sl.registerLazySingleton<ImageUploadRepository>(
    () => ImageUploadRepositoryImpl(
      sl.call(),
    ),
  );

  sl.registerLazySingleton<ProfileFeedRepository>(
    () => ProfileFeedRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<PointsRepository>(
    () => PointsRepositoryImpl(
      pointsDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<ChangePasswordRepository>(
    () => ChangePasswordRepositoryImpl(
      remoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<PreferenceRepository>(
    () => PreferenceRepositoryImpl(
      remoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<ChangeEmailRepository>(
    () => ChangeEmailRepositoryImpl(
      remoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<BlockRepository>(
    () => BlockRepositoryImpl(
      remoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<DietRepository>(
    () => DietRepositoryImpl(
      remoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<FoodRepository>(
    () => FoodRepositoryImpl(
      remoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<TrainingRepository>(
    () => TrainingRepositoryImpl(
      trainingRemoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<ExerciseCategoryRepository>(
    () => ExerciseCategoryRepositoryImpl(
      exerciseCategoryRemoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<MuscleGroupRepository>(
    () => MuscleGroupRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<BodyBuildingProgramRepository>(
    () => BodyBuildingProgramRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<FitMarketDashboardRepository>(
    () => FitMarketDashboardRepositoryImp(
      remoteDataSource: sl.call(),
    ),
  );

  sl.registerLazySingleton<CartRepository>(
    () => CartRepositoryImpl(
      cartRemoteDataSource: sl.call(),
    ),
  );

  sl.registerFactory<ShippingAddressRepository>(
    () => ShippingAddressRepositoryImp(
      shippingAddressRemoteDataSource: sl.call(),
    ),
  );

  sl.registerFactory<SearchRepository>(
    () => SearchRepositoryImp(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<ProductDetailsRepository>(
    () => ProductDetailsRepositoryImpl(
      productDetailsDataSource: sl.call(),
    ),
  );

  sl.registerFactory<SpotMeRepository>(
    () => SpotMeRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<WishlistRepository>(
    () => WishlistRepositoryImpl(
      wishlistDataSource: sl.call(),
    ),
  );

  sl.registerFactory<BillingAddressRepository>(
    () => BillingAddressRepositoryImpl(
      billingAddressDataSource: sl.call(),
    ),
  );

  sl.registerFactory<OrderRepository>(
    () => OrderRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<PaymentRepository>(
    () => PaymentRepositoryImpl(
      paymentDataSource: sl.call(),
    ),
  );

  sl.registerFactory<OrderSummaryRepository>(
    () => OrderSummaryRepositoryImpl(
      orderSummaryDataSource: sl.call(),
    ),
  );

  sl.registerFactory<ReviewRepository>(
    () => ReviewRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<SpotBackRepository>(
    () => SpotBackRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<PostDetailsRepository>(
    () => PostDetailsRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<DeleteSpotProfileRepository>(
    () => DeleteSpotProfileRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  /// Likers Info
  sl.registerFactory<LikersInfoRepository>(
    () => LikersInfoRepositoryImp(
      likersInfoRemoteDataSource: sl.call(),
    ),
  );

  sl.registerFactory<LogoutRepository>(
    () => LogoutRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  ///Subscriptions
  sl.registerFactory<SubscriptionRepository>(
    () => SubscriptionRepositoryImp(
      subscriptionRemoteDataSource: sl.call(),
    ),
  );

  sl.registerFactory<ValidateAddressRepository>(
    () => ValidateAddressRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<DeleteAccountRepository>(
    () => DeleteAccountRepositoryImpl(
      deleteAccountDataSource: sl.call(),
      logoutDataSource: sl.call(),
    ),
  );

  sl.registerFactory<FitbotChatRepository>(
    () => FitbotChatRepositoryImp(
      fitbotChatDatasource: sl.call(),
    ),
  );

  sl.registerFactory<TeamPollRepository>(
    () => TeamPollRepositoryImpl(
      pollDataSource: sl.call(),
    ),
  );

  sl.registerFactory<EventRepository>(
    () => EventRepositoryImpl(
      eventDataSource: sl.call(),
    ),
  );

  /// coach
  sl.registerFactory<CoachRepository>(
    () => CoachRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<CoachProfileRepository>(
    () => CoachProfileRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<CoachProgramRepository>(
    () => CoachProgramRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<CoachPersonalRepository>(
    () => CoachPersonalRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<UserProgramSubscriptionRepository>(
    () => UserProgramSubscriptionRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<CoachNewsfeedRepository>(
    () => CoachNewsfeedRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<CoachProgramReviewRepository>(
    () => CoachRatingRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<CoachWithdrawPaymentRepository>(
    () => CoachWithdrawPaymentRepositoryImpl(
      datasource: sl.call(),
    ),
  );


  sl.registerFactory<RewardPointRepository>(
    () => RewardPointRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<RewardLeaderboardRepository>(
    () => RewardLeaderboardRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<ReferralRepository>(
    () => ReferralRepositoryImpl(
      dataSource: sl.call(),
    ),
  );

  sl.registerFactory<DailyTaskRepository>(
    () => DailyTaskRepositoryImpl(
      dataSource: sl.call(),
    ),
  );
  sl.registerFactory<FoodScanRepository>(
    () => FoodScanRepositoryImpl(
      datasource: sl.call(),
    ),
  );
}
