<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>841300673733-vd2fbupp6g6p5r53echnuvh1p6clkink.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.841300673733-vd2fbupp6g6p5r53echnuvh1p6clkink</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>841300673733-cpktdcomc5n9rhvqliorgq4gfci028pc.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyBcMSohJT1P5-7cxENXmXX9VzKthiaCHcY</string>
	<key>GCM_SENDER_ID</key>
	<string>841300673733</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.fitsomnia.fitsomniaApp</string>
	<key>PROJECT_ID</key>
	<string>fitsomnia-client-web</string>
	<key>STORAGE_BUCKET</key>
	<string>fitsomnia-client-web.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:841300673733:ios:3e8be10539ad7ea198dcac</string>
</dict>
</plist>