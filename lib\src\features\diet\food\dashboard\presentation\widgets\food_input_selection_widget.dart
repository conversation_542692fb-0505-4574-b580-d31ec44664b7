import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/bloc/food_consumption/food_consumption_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

class FoodInputSelectionWidget extends StatelessWidget {
  const FoodInputSelectionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Stack(
        children: [
          _buildCancelPopupOption(context),
          Container(
            padding: EdgeInsets.only(top: 30, bottom: 20, left: 20, right: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeaderSection(context),
                SizedBox(height: Values.v20,),
                _buildInputOptionSection(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  _buildCancelPopupOption(BuildContext context) {
    return Container(
      // decoration: BoxDecoration(border: Border.all(color: Colors.red)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          IconButton(
            // iconSize: Values.v18,
            onPressed: () {
              Navigator.of(context).pop();
            },
            icon: Icon(Icons.clear_sharp),
          )
        ],
      ),
    );
  }

  _buildHeaderSection(BuildContext context) {
    return Container(
        // decoration: BoxDecoration(border: Border.all(color: Colors.red)),
        child: Column(
      children: [
        Text(
          'Add ${_mealTypeName(context)}',
          style: AppTypography.poppinsSemiBold24(),
        ),

        Text(
          'Update your ${_mealTypeName(context).toLowerCase()} food intake',
          style: AppTypography.poppinsRegular14(color: AppColors.greyscale400),
        ),
      ],
    ));
  }

  String _mealTypeName(BuildContext context) {
    int mealTypeIndex =
        context.read<FoodConsumptionCubit>().selectedTabIndex.value;

    switch (mealTypeIndex) {
      case 0:
        return 'Breakfast';
      case 1:
        return 'Lunch';
      case 2:
        return 'Dinner';
      default:
        return 'Snacks';
    }
  }

  _buildInputOptionSection(BuildContext context) {
    return Row(
      children: [
        InkWell(
          onTap: () {
            _navigateToFoodScanner(context);
          },
          child: SizedBox(
            height: Values.v120,
            width: Values.v120,
            child: SvgPicture.asset(Assets.mealArCameraImg),
          ),
        ),
        SizedBox(
          width: Values.v10,
        ),
        InkWell(
          onTap: () {
            _navigateToFoodSearchDashboard(context);
          },
          child: SizedBox(
            height: Values.v120,
            width: Values.v120,
            child: SvgPicture.asset(Assets.mealFoodSearchImg),
          ),
        ),
      ],
    );
  }

  _navigateToFoodSearchDashboard(BuildContext context) {
    Log.debug('go to food search page');
    Navigator.of(context).pop();
    Navigator.of(context).pushNamed(Routes.foodSearchPage);
  }

  _navigateToFoodScanner(BuildContext context) {
    Log.debug('go to food scanner page');
    Navigator.of(context).pop();
    Navigator.of(context).pushNamed(Routes.foodScannerPage);
  }
}
