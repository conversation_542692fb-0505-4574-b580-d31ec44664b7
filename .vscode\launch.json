{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    
    {
      "name": "fitsomnia_app",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "fitsomnia_app (profile mode)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "fitsomnia_app (release mode)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "dropdown_form_field",
      "cwd": "packages/dropdown_form_field",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "dropdown_form_field (profile mode)",
      "cwd": "packages/dropdown_form_field",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "dropdown_form_field (release mode)",
      "cwd": "packages/dropdown_form_field",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "input_form_field",
      "cwd": "packages/input_form_field",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "input_form_field (profile mode)",
      "cwd": "packages/input_form_field",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "input_form_field (release mode)",
      "cwd": "packages/input_form_field",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
  ]
}