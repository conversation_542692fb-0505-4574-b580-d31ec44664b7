import 'package:fitsomnia_app/src/features/chat/group_messages/domain/entity/group_chat_entity.dart';

class GroupConversationModel extends GroupChatEntity {
  const GroupConversationModel({
    required super.id,
    required super.senderId,
    required super.groupId,
    required super.type,
    required super.content,
    required super.createdAt,
    required super.senderName,
    required super.senderImage,
  });

  factory GroupConversationModel.fromJson(Map<String, dynamic> json) => GroupConversationModel(
    id: json["id"],
    senderId: json["senderId"],
    groupId: json["groupId"],
    type: json["type"],
    content: json["content"],
    createdAt: DateTime.parse(json["createdAt"]),
    senderName: json["senderName"],
    senderImage: json["senderImage"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "senderId": senderId,
    "groupId": groupId,
    "type": type,
    "content": content,
    "createdAt": createdAt.toIso8601String(),
    "senderName": senderName,
    "senderImage": senderImage,
  };
}
