import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/firebase/firebase_service.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';

class RewardOptionWidget extends StatefulWidget {
  const RewardOptionWidget({super.key});

  @override
  State<RewardOptionWidget> createState() => _RewardOptionWidgetState();
}

class _RewardOptionWidgetState extends State<RewardOptionWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Flexible(child: _buildTaskCard()),
          SizedBox(
            width: Values.v10,
          ),
          Flexible(child: _buildReferralCard()),
          SizedBox(
            width: Values.v10,
          ),
          Flexible(child: _buildChallengeCard()),
        ],
      ),
    );
  }

  _buildTaskCard() {
    return InkWell(
      onTap: () {
        // AppToast.showToast(message: 'Upcomming feature', gravity: ToastGravity.BOTTOM, backgroundColor: AppColors.primaryGreen);
        FirebaseService().logFeatureUsage('rewards', 'reward_task', '');
        Navigator.of(context).pushNamed(Routes.dailyTaskPage);
      },
      child: Container(
        width: 130,
        padding: EdgeInsets.all(Values.v10),
        decoration: BoxDecoration(
            border: Border.all(color: AppColors.greyscale100),
            borderRadius: BorderRadius.circular(Values.v10)),
        child: Column(
          children: [
            _buildCountSection(),
            Image.asset(Assets.dailyTaskOptionImg),
            const SizedBox(
              height: Values.v10,
            ),
            Text('Daily Tasks',
                style: AppTypography.poppinsMedium14(
                    color: AppColors.greyscale700))
          ],
        ),
      ),
    );
  }

  _buildReferralCard() {
    return InkWell(
      onTap: () {
        // AppToast.showToast(message: 'Upcomming feature', gravity: ToastGravity.BOTTOM, backgroundColor: AppColors.primaryGreen);
        FirebaseService().logFeatureUsage('rewards', 'reward_referral', '');
        Navigator.of(context).pushNamed(Routes.referralListPage);
      },
      child: Container(
        width: 130,
        padding: EdgeInsets.all(Values.v10),
        decoration: BoxDecoration(
            border: Border.all(color: AppColors.greyscale100),
            borderRadius: BorderRadius.circular(Values.v10)),
        child: Column(
          children: [
            _buildCountSection(),
            Image.asset(Assets.referralImg),
            const SizedBox(
              height: Values.v10,
            ),
            Text('Referrals',
                style: AppTypography.poppinsMedium14(
                    color: AppColors.greyscale700))
          ],
        ),
      ),
    );
  }

  _buildChallengeCard() {
    return InkWell(
      onTap: () {
        // AppToast.showToast(message: 'Upcomming feature', gravity: ToastGravity.BOTTOM, backgroundColor: AppColors.primaryGreen);
        FirebaseService().logFeatureUsage('rewards', 'reward_challenge', '');
        Navigator.of(context).pushNamed(Routes.allMonthlyChallenges);
      },
      child: Container(
        width: 130,
        padding: EdgeInsets.all(Values.v10),
        decoration: BoxDecoration(
            border: Border.all(color: AppColors.greyscale100),
            borderRadius: BorderRadius.circular(Values.v10)),
        child: Column(
          children: [
            _buildCountSection(),
            Image.asset(Assets.challengeImg),
            const SizedBox(
              height: Values.v10,
            ),
            Text(
              'Challenges',
              style:
                  AppTypography.poppinsMedium14(color: AppColors.greyscale700),
            )
          ],
        ),
      ),
    );
  }

  _buildCountSection() {
    return SizedBox.shrink();

    // return Row(
    //   mainAxisAlignment: MainAxisAlignment.end,
    //   children: [
    //     CircleAvatar(
    //       maxRadius: Values.v12,
    //       backgroundColor: UIColors.red600,
    //         child: Center(child: Text('12', style: AppTypography.poppinsMedium12(color: UIColors.white),)),
    //       ),

    //   ],
    // );
  }
}

// class RewewardSingleOptionWidget extends StatefulWidget {
//   const RewewardSingleOptionWidget({super.key});
//   @override
//   State<RewewardSingleOptionWidget> createState() => _RewewardSingleOptionWidgetState();
// }

// class _RewewardSingleOptionWidgetState extends State<RewewardSingleOptionWidget> {
//   @override
//   Widget build(BuildContext context) {
//     return const Placeholder();
//   }
// }
