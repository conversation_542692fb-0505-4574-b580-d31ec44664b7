import 'dart:io';

import 'package:camera/camera.dart';
import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/entities/feed_entity.dart';
import 'package:http_parser/http_parser.dart';
import 'package:intl/intl.dart';
import 'package:mime/mime.dart';

extension EmailValidator on String {
  bool isValidEmail() {
    String pattern =
        r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
    RegExp regExp = RegExp(pattern);

    return regExp.hasMatch(this);
  }
}

extension PasswordValidator on String {
  bool isValidatePassword() {
    String pattern =
        r'^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[!@#\$&*~]).{8,}$';
    RegExp regExp = RegExp(pattern);

    return regExp.hasMatch(this);
  }
}

extension MultipartFileConverter on File {
  Future<MultipartFile> toMultiPartFile() async {
    final mimeTypeData = lookupMimeType(path)!.split('/');

    return await MultipartFile.fromFile(
      path,
      filename: path.split('/').last,
      contentType: MediaType(
        mimeTypeData[0],
        mimeTypeData[1],
      ),
    );
  }
}

extension MediaTypeDetector on XFile {
  String getMediaType() {
    final mimeTypeData = lookupMimeType(path)!.split('/'); //mimeType: [video, mp4]
    Log.debug('mimeType: ${mimeTypeData}'); 

    return mimeTypeData.first;
  }
}

extension DateTimeFormat on DateTime {
  String asFormat(String format) {
    var formatter = DateFormat(format);
    String formattedDate = formatter.format(this);

    return formattedDate;
  }
}

extension DateTimeExtension on DateTime {
  String timeAgo({bool numericDates = true}) {
    final date2 = DateTime.now();
    final difference = date2.difference(this);

    if ((difference.inDays / 365).floor() >= 2) {
      return '${((difference.inDays / 365).floor())} years ago';
    } else if ((difference.inDays / 365).floor() >= 1) {
      return (numericDates) ? '1 year ago' : 'Last year';
    } else if ((difference.inDays / 30).floor() >= 2) {
      return '${((difference.inDays / 30).floor())} months ago';
    } else if ((difference.inDays / 30).floor() >= 1) {
      return (numericDates) ? '1 month ago' : 'Last month';
    } else if ((difference.inDays / 7).floor() >= 2) {
      return '${((difference.inDays / 7).floor())} weeks ago';
    } else if ((difference.inDays / 7).floor() >= 1) {
      return (numericDates) ? '1 week ago' : 'Last week';
    } else if (difference.inDays >= 2) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays >= 1) {
      return (numericDates) ? '1 day ago' : 'Yesterday';
    } else if (difference.inHours >= 2) {
      return '${difference.inHours} hours ago';
    } else if (difference.inHours >= 1) {
      return (numericDates) ? '1 hour ago' : 'An hour ago';
    } else if (difference.inMinutes >= 2) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inMinutes >= 1) {
      return (numericDates) ? '1 minute ago' : 'A minute ago';
    } else if (difference.inSeconds >= 3) {
      return '${difference.inSeconds} seconds ago';
    } else {
      return 'Just now';
    }
  }
}

extension DurationToMinutesExtension on Duration {
  String get toMinutes {
    var components = <String>[];
    var hours = inHours % 24;
    if (hours != 0) {
      components.add('${hours}h');
    }

    var minutes = inMinutes % 60;
    if (minutes != 0) {
      components.add('${minutes}m');
    }

    var seconds = inSeconds % 60;
    var centiseconds = (inMilliseconds % 1000) ~/ 10;

    if (components.isEmpty || seconds != 0 || centiseconds != 0) {
      components.add('$seconds');
      if (centiseconds != 0) {
        components.add('.');
        components.add(centiseconds.toString().padLeft(2, '0'));
      }
      components.add('s');
    }

    return components.join();
  }
}

extension FeedPostTypeExtension on FeedEntity {
  String get getPostType {
    if ((images != null && images!.isNotEmpty) &&
        (videos != null && videos!.isNotEmpty)) {
      return 'new album with photo and video';
    } else if (images != null && images!.isNotEmpty) {
      return images!.length > 1 ? 'new photo album' : 'new photo';
    } else if (videos != null && videos!.isNotEmpty) {
      return videos!.length > 1 ? 'new video album' : 'new video';
    } else {
      return 'new status';
    }
  }
}

extension CapitalFirstExtension on String {
  String toCapitalFirst() {
    return isEmpty ? '' : this[0].toUpperCase() + substring(1);
  }
}

extension TruncateStringExtension on String {
  String truncate({int maxLength = 35}) {
    return length <= maxLength ? this : "${substring(0, maxLength)}...";
  }
}
