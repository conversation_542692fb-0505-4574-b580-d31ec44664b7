import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/entity/referral_code_entity.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/entity/referral_data_entity.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/repositories/referral_repository.dart';

class ReferralUseCase {
  final ReferralRepository repository;

  ReferralUseCase({required this.repository});

  Future<Either<ErrorModel, List<ReferralDataEntity>>> getReferrals() async {
    return await repository.getReferrals();
  }

  Future<Either<ErrorModel, ReferralDataEntity>> getReferralById(
      {required String referralId}) async {
    return await repository.getReferralById(referralId: referralId);
  }

  Future<Either<ErrorModel, ReferralCodeEntity>> getReferralCode(
      {required String userId, required String referralId}) async {
    return await repository.getReferralCode(
        userId: userId, referralId: referralId);
  }

  Future<Either<ErrorModel, ReferralDataEntity>> useReferralCode(
      {required String userId, required String referralCode}) async {
    return await repository.useReferralCode(
        userId: userId, referralCode: referralCode);
  }
}
