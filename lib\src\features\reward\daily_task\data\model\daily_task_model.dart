import 'package:fitsomnia_app/src/features/reward/daily_task/domain/entities/daily_task.dart';

class DailyTaskModel extends DailyTask {
  DailyTaskModel({
    required super.id,
    required super.title,
    required super.totalRewardPoint,
    required super.taskProgress,
    required super.isCompleted,
  });

  factory DailyTaskModel.fromJson(Map<String, dynamic> json) {
    return DailyTaskModel(
      id: json['id'],
      title: json['title'],
      totalRewardPoint: json['points'] ?? 0,
      taskProgress: (json['progress'] == null)
          ? 0.0
          : (json['progress'] is int)
              ? json['progress'].toDouble()
              : json['progress'],
      isCompleted: (json['completionStatus'] == null)
          ? false
          : (json['completionStatus'] == 'completed'),
    );
  }

  DailyTask toEntity() {
    return DailyTask(
        id: id,
        title: title,
        totalRewardPoint: totalRewardPoint,
        taskProgress: taskProgress,
        isCompleted: isCompleted);
  }
}
