import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/services/firebase/firebase_service.dart';
import 'package:fitsomnia_app/src/core/services/local_storage/cache_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/login_use_case.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/login_with_apple_use_case.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/login_with_facebook_use_case.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/login_with_google_use_case.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'login_event.dart';
part 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  LoginBloc({
    required this.loginUseCase,
    required this.loginWithGoogleUseCase,
    required this.loginWithFacebookUseCase,
    required this.loginWithAppleUseCase,
  }) : super(LoginInitialState()) {
    on<LoginInitialEvent>(_onLoginInitialEvent);
    on<LoginWithEmailAndPasswordEvent>(_onLoginWithEmailAndPasswordEvent);
    on<LoginWithGoogleEvent>(_onLoginWithGoogleEvent);
    on<LoginWithFacebookEvent>(_onLoginWithFacebookEvent);
    on<LoginWithAppleEvent>(_onLoginWithAppleEvent);
    on<CheckLoginMethodEvent>(_onCheckLoginMethodEvent);
  }

  final LoginUseCase loginUseCase;
  final LoginWithGoogleUseCase loginWithGoogleUseCase;
  final LoginWithFacebookUseCase loginWithFacebookUseCase;
  final LoginWithAppleUseCase loginWithAppleUseCase;

  // ignore: long-method
  void _onLoginWithEmailAndPasswordEvent(
    LoginWithEmailAndPasswordEvent event,
    Emitter<LoginState> emit,
  ) async {
    emit(const LoginLoadingState(type: LoginType.EMAIL_PASSWORD));

    try {
      /// Retrieves the FCM token from the cache. If the token is not found in the cache,
      /// it retrieves the token from Firebase and updates the cache with the new token.
      String? fcmToken;
      try {
        fcmToken = await CacheService.instance.retrieveFcmToken();
        if (fcmToken == null) {
          await FirebaseService().retrieveFCMToken();
          fcmToken = await CacheService.instance.retrieveFcmToken();
        }
      } catch (_) {
        Log.error('Failed to retrieve FCM token');
      }

      bool isDigit = false;
      if (emailOrPhoneField.text.startsWith('+')) {
        isDigit = true;
      } else if (int.tryParse(emailOrPhoneField.text) != null) {
        isDigit = true;
      }

      Map<String, dynamic> data = {
        if (isDigit) "phone": emailOrPhoneField.text.trim(),
        if (!isDigit) "email": emailOrPhoneField.text.trim(),
        'password': passwordField.text.trim(),
        if (fcmToken != null) 'fcmToken': fcmToken,
      };

      final response = await loginUseCase.call(data);

      await response.fold(
        (l) async => emit(LoginErrorState(message: l.message!)),
        (r) async {
          if (isRememberMeChecked.value) {
            await CacheService.instance.storeCredentials({
              "emailOrPhone": emailOrPhoneField.text.trim(),
              'password': passwordField.text.trim(),
            });
          } else {
            await CacheService.instance.clearCredentials();
          }

          await CacheService.instance.storeBearerToken(r.token);
          emit(LoginSuccessState(token: r.token));
        },
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      emit(const LoginErrorState(message: TextConstants.somethingWentWrong));
    }
  }

  Future<void> _onLoginWithGoogleEvent(
    LoginWithGoogleEvent event,
    Emitter<LoginState> emit,
  ) async {
    emit(const LoginLoadingState(type: LoginType.GOOGLE));
    final response = await loginWithGoogleUseCase();
    
    await response.fold(
      (l) async => emit(LoginErrorState(message: l.message!)),
      (r) async {
        await CacheService.instance.storeBearerToken(r.token);
        emit(LoginSuccessState(token: r.token));
      },
    );
  }

  Future<void> _onLoginWithFacebookEvent(
    LoginWithFacebookEvent event,
    Emitter<LoginState> emit,
  ) async {
    emit(const LoginLoadingState(type: LoginType.FACEBOOK));
    final response = await loginWithFacebookUseCase();
    
    await response.fold(
      (l) async => emit(LoginErrorState(message: l.message!)),
      (r) async {
        await CacheService.instance.storeBearerToken(r.token);
        emit(LoginSuccessState(token: r.token));
      },
    );
  }

  Future<void> _onLoginWithAppleEvent(
    LoginWithAppleEvent event,
    Emitter<LoginState> emit,
  ) async {
    emit(const LoginLoadingState(type: LoginType.APPLE));
    final response = await loginWithAppleUseCase();
    
    await response.fold(
      (l) async => emit(LoginErrorState(message: l.message!)),
      (r) async {
        await CacheService.instance.storeBearerToken(r.token);
        emit(LoginSuccessState(token: r.token));
      },
    );
  }

  Future<void> _onLoginInitialEvent(
    LoginInitialEvent event,
    Emitter<LoginState> emit,
  ) async {
    Map<String, dynamic>? credentials =
        await CacheService.instance.retrieveCredentials();

    if (credentials == null) return;

    isRememberMeChecked.value = true;
    emailOrPhoneField.value =
        TextEditingValue(text: credentials['emailOrPhone']);
    passwordField.value = TextEditingValue(text: credentials['password']);

    emit(const RememberedCredentialState());
  }

  ValueNotifier<bool> isRememberMeChecked = ValueNotifier(false);

  /// Login Form
  // final GlobalKey<FormState> key = GlobalKey<FormState>();

  TextEditingController emailOrPhoneField = TextEditingController();
  TextEditingController passwordField = TextEditingController();

  FutureOr<void> _onCheckLoginMethodEvent(
    CheckLoginMethodEvent event,
    Emitter<LoginState> emit,
  ) {
    emit(const LoginLoadingState(type: LoginType.EMAIL_PASSWORD));
    bool isDigit = false;
    if (emailOrPhoneField.text.startsWith('+')) {
      isDigit = true;
    } else if (int.tryParse(emailOrPhoneField.text) != null) {
      isDigit = true;
    }

    if (isDigit) {
      emit(LoginWithPhoneNumberState());
    } else {
      emit(LoginWithEmailState());
    }
  }
}
