class LiveMemberModel {
  String id;
  String userId;
  String name;
  int age;
  String gender;
  String workoutType;
  String bodyType;
  List<ImageElement> images;
  double maxBench;
  double maxSquat;
  Location location;
  bool ghostModeOn;
  String datumId;
  double distance;
  Club? club;

  LiveMemberModel({
    required this.id,
    required this.userId,
    required this.name,
    required this.age,
    required this.gender,
    required this.workoutType,
    required this.bodyType,
    required this.images,
    required this.maxBench,
    required this.maxSquat,
    required this.location,
    required this.ghostModeOn,
    required this.datumId,
    required this.distance,
    this.club,
  });

  factory LiveMemberModel.fromJson(Map<String, dynamic> json) =>
      LiveMemberModel(
        id: json["id"] ?? '',
        userId: json["userId"],
        name: json["name"],
        age: num.tryParse(json["age"].toString())?.toInt() ?? 0,
        gender: json["gender"],
        workoutType: json["workoutType"],
        bodyType: json["bodyType"],
        images: List<ImageElement>.from(
            json["images"].map((x) => ImageElement.fromJson(x))),
        maxBench: double.tryParse(json["maxBench"].toString()) ?? 0.0,
        maxSquat: double.tryParse(json["maxSquat"].toString()) ?? 0.0,
        location: Location.fromJson(json["location"]),
        ghostModeOn: json["ghostModeOn"],
        datumId: json["id"],
        distance: json["distance"]?.toDouble(),
        club: json["club"] == null ? null : Club.fromJson(json["club"]),
      );
}

class Club {
  String name;
  ClubImage image;
  Location location;
  Address address;
  String description;
  String clubId;
  DateTime createdAt;
  DateTime updatedAt;
  String? placeId;

  Club({
    required this.name,
    required this.image,
    required this.location,
    required this.address,
    required this.description,
    required this.clubId,
    required this.createdAt,
    required this.updatedAt,
    this.placeId,
  });

  factory Club.fromJson(Map<String, dynamic> json) => Club(
        name: json["name"],
        image: ClubImage.fromJson(json["image"]),
        location: Location.fromJson(json["location"]),
        address: Address.fromJson(json["address"]),
        description: json["description"],
        clubId: json["id"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        placeId: json["placeId"],
      );
}

class Address {
  String id;
  String addressLine1;
  String addressLine2;
  String postCode;
  String city;
  String country;
  String? state;

  Address({
    required this.id,
    required this.addressLine1,
    required this.addressLine2,
    required this.postCode,
    required this.city,
    required this.country,
    this.state,
  });

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        id: json["id"],
        addressLine1: json["addressLine1"],
        addressLine2: json["addressLine2"],
        postCode: json["postCode"],
        city: json["city"],
        country: json["country"],
        state: json["state"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "addressLine1": addressLine1,
        "addressLine2": addressLine2,
        "postCode": postCode,
        "city": city,
        "country": country,
        "state": state,
      };
}

class ClubImage {
  String cover;
  String logo;

  ClubImage({
    required this.cover,
    required this.logo,
  });

  factory ClubImage.fromJson(Map<String, dynamic> json) => ClubImage(
        cover: json["cover"],
        logo: json["logo"],
      );

  Map<String, dynamic> toJson() => {
        "cover": cover,
        "logo": logo,
      };
}

class Location {
  String type;
  List<double> coordinates;

  Location({
    required this.type,
    required this.coordinates,
  });

  factory Location.fromJson(Map<String, dynamic> json) => Location(
        type: json["type"],
        coordinates: List<double>.from(
          json["coordinates"].map((x) => x?.toDouble()),
        ),
      );
}

class ImageElement {
  String url;
  String id;
  DateTime createdAt;
  DateTime updatedAt;

  ImageElement({
    required this.url,
    required this.id,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ImageElement.fromJson(Map<String, dynamic> json) => ImageElement(
        url: json["url"],
        id: json["id"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "url": url,
        "id": id,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
      };
}
