part of 'coach_newsfeed_bloc.dart';

sealed class Coach<PERSON><PERSON>feedState extends Equatable {
  const CoachNewsfeedState();
  
  @override
  List<Object> get props => [];
}

final class CoachNewsfeedInitial extends CoachNewsfeedState {}

class CoachProfilesLoading extends CoachNewsfeedState {}

class GetCoachProfilesSuccess extends CoachNewsfeedState {
  final List<CoachProfileEntity> coachProfiles;

  const GetCoachProfilesSuccess({required this.coachProfiles});

  @override
  List<Object> get props => [coachProfiles];
}

class GetCoachProfilesFail<T> extends CoachNewsfeedState {
  final T? data;
  const GetCoachProfilesFail({required this.data});
}


class GetSingleCoachProfileSuccess extends CoachNewsfeedState {
  final CoachEntity coachProfile;

  const GetSingleCoachProfileSuccess({required this.coachProfile});

  @override
  List<Object> get props => [coachProfile];
}

class GetSingleCoachProfileFail<T> extends CoachNewsfeedState {
  final T? data;
  const GetSingleCoachProfileFail({required this.data});
}

class GetBestCoachProfilesSuccess extends CoachNewsfeedState {
  final List<CoachProfileEntity> coachProfiles;

  const GetBestCoachProfilesSuccess({required this.coachProfiles});

  @override
  List<Object> get props => [coachProfiles];
}

class GetBestCoachProfilesFail<T> extends CoachNewsfeedState {
  final T? data;
  const GetBestCoachProfilesFail({required this.data});
}


class CoachProgramsLoading extends CoachNewsfeedState {}

class GetCoachProgramsSuccess extends CoachNewsfeedState {
  final List<CoachProgramSearchEntity> programs;

  const GetCoachProgramsSuccess({required this.programs});

  @override
  List<Object> get props => [programs];
}

class GetCoachProgramsFail<T> extends CoachNewsfeedState {
  final T? data;
  const GetCoachProgramsFail({required this.data});
}