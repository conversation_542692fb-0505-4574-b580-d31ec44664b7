// import 'package:dropdown_button2/custom_dropdown_button2.dart';
import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/custom_dropdown_button2.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class DropDownField extends StatefulWidget {
  DropDownField({
    Key? key,
    this.height = 60,
    required this.items,
    required this.title,
    required this.onChanged,
    this.isRequired = false,
    this.initialValue,
    this.borderColor,
  }) : super(key: key);

  final double height;
  final List<String> items;
  final String title;
  final bool isRequired;
  final Function(String?)? onChanged;
  String? initialValue;
  Color? borderColor;

  @override
  State<DropDownField> createState() => _DropDownFieldState();
}

class _DropDownFieldState extends State<DropDownField> {
  @override
  Widget build(BuildContext context) {
    final dropdownWidth = MediaQuery.of(context).size.width - Values.v16.r * 2;
    widget.borderColor ??= AppColors.alto;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        widget.isRequired
            ? RichText(
                text: TextSpan(
                  text: widget.title,
                  style: AppTypography.poppinsRegular18(
                    color: UIColors.fitGrey,
                  ),
                  children: <TextSpan>[
                    TextSpan(
                      text: ' *',
                      style: AppTypography.poppinsRegular18(
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
              )
            : Text(
                widget.title,
                style: AppTypography.poppinsRegular18(
                  color: UIColors.fitGrey,
                ),
              ),
        SizedBox(height: Values.v5.h),
        CustomDropdownButton2(
          offset: const Offset(0, -5),
          dropdownElevation: 0,
          dropdownWidth: dropdownWidth,
          dropdownDecoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(3),
            border: Border.all(color: widget.borderColor!),
          ),
          buttonDecoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.borderColor!),
          ),
          buttonHeight: widget.height ?? Values.v50.h,
          buttonWidth: double.infinity,
          hint: 'Select Item',
          icon: SvgPicture.asset(Assets.dropDownButton),
          dropdownItems: widget.items,
          value: widget.initialValue,
          onChanged: (value) {
            setState(() {
              widget.initialValue = value;
              widget.onChanged!(value); //ensure frontend and backend change
            });
          },
        ),
      ],
    );
  }
}
