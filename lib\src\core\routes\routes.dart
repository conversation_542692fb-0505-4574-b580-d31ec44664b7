class Routes {
  static const String developerMode = '/developer_mode';
  static const String splash = '/splash';
  static const String onBoarding = '/on_boarding';
  static const String welcome = '/welcome';
  static const String signUp = '/sign_up';
  static const String login = '/login';
  static const String verification = '/verification';
  static const String resetPassword = '/reset_password';
  static const String home = '/home';
  static const String editProfile = '/edit_profile';
  static const String notification = '/notification';
  static const String dashboard = '/dashboard';
  static const String profileMenu = '/profile_menu';

  /// Story
  static const String createStory = '/create_story_page';
  static const String previewImage = '/preview_image';
  static const String previewVideo = '/preview_video';
  static const String storyViewers = '/story_viewers';

  static const String chatList = '/chat_list';
  static const String activeUsers = '/active_users';
  static const String groupChat = '/group_chat';

  static const String forgotPassword = '/forgot_password';
  static const String identityVerification = '/identity_verification';

  /// Diet
  static const String dietDashboard = '/diet_dashboard';
  static const String trackDiet = '/trackDiet';
  static const String foodDashboard = '/foodDashboard';
  static const String recentlyAddedFood = '/recentlyAddedFood';
  static const String myFoodDashboard = '/myFoodDashboard';
  static const String createFood = '/createFood';
  static const String foodSearchPage = '/food_search_page';

  /// Settings
  static const String settings = '/settings';
  static const String planner = '/planner';
  static const String personalInformation = '/personal_information';
  static const String subscription = '/subscription';
  static const String security = '/security';
  static const String privacy = '/privacy';
  static const String about = '/about';
  static const String notificationsSettings = '/notifications';
  static const String socialMediaAccount = '/socialMediaAccount';
  static const String changeEmail = '/changeEmail';
  static const String changePassword = '/changePassword';
  static const String deleteAccount = '/deleteAccount';
  static const String profileVisibility = '/profileVisibility';
  static const String blockList = '/blockList';
  static const String privacyPolicy = '/privacyPolicy';
  static const String termsOfUse = '/termsOfUse';
  static const String aboutUs = '/aboutUs';

  /// WebView
  static const String webView = '/web_view';

  /// Spot Me
  static const String spotNot = '/spot_not';
  static const String spotNotFilter = '/spot_not_filter';
  static const String spotDistanceFilter = '/spot_distance_filter';

  /// Club
  static const String club = 'club';
  static const String peopleFromYourClub = 'people_from_your_club';
  static const String peopleFromNearByClub = 'people_from_near_by_club';

  /// planner
  static const String noteDetails = '/note_details';

  /// News Feed
  static const String post = '/view_post';
  static const String createPost = '/create_post';

  /// comment
  static const String comment = '/comment';

  /// QR Scanner & Generator
  static const String generateQR = '/generate_qr';

  // Chat
  static const String groupChatMembers = "/group_chat_members";
  static const String addGroupChatMember = "/add_group_chat_member";

  /// search location
  static const String searchLocation = '/search_location';

  /// training
  static const String training = '/training';
  static const String exerciseCategory = '/exercise_category';
  static const String exerciseList = '/exercise_list';
  static const String bodybuildingProgramSelection =
      '/bodybuilding_program_selection';
  static const String bodybuildingProgramDetails =
      '/bodybuilding_program_details';
  static const String muscleGroupSelection = '/muscle_group_selection';
  static const String exerciseDetails = '/exercise_details';

  /// Hall of Weight
  static const String hallOfWeight = '/hall_of_weight';
  static const String leaderboard = '/leaderboard';
  static const String pointsHistory = '/points_history';
  static const String allMonthlyChallenges = '/all_monthly_challenges';
  static const String challengeHistory = '/challenge_history';
  static const String challengeDetails = '/challenge_details';
  static const String previewRecordedVideo = '/previewRecordedVideo';

  /// Search
  static const String search = '/search';

  /// fit market
  static const String fitMarketDashboard = '/fit_market_dashboard';
  static const String fitMarketSubCategory = '/fit_market_sub_category';
  static const String productDetails = '/product_details';
  static const String cart = '/cart';
  static const String addShippingAddress = '/add_shipping_address';
  static const String confirm = '/confirm';
  static const String payment = '/payment';
  static const String payNow = '/pay_now';
  static const String wishlist = '/wishlist';
  static const String orderHistory = '/orderHistory';

  /// Likers Info
  static const String likersInfo = '/likers_info';

  ///Subscription
  static const String subscriptionError = '/subscription_error';
  static const String updateDiet = '/update_diet';

  /// fitbot
  static const String fitbotLanding = '/fitbot_landing';
  static const String fitbotChat = '/fitbot_chat';
  static const String fitbotWelcome = '/fitbot_welcome';
  static const String chatbotStreamChat = '/chatbot_stream_chat';

  ///poll
  static const String bplTeamPoll = '/bpl_team_poll';
  static const String pollVoterInfoPage = 'poll_voter_info';

  /// events
  static const String eventsPage = '/events';
  static const String eventDetailsInfoPage = '/event_details_info';
  static const String eventDetailsPageById = '/event_details_page_by_eventId';
  static const String eventParticipantInfoPage = '/event_registered_user_info_page';
  static const String eventRegistrationPage = '/event_registration_page';

  /// coach
  static const String coachFeatureTestPage = '/coach_feature_test_page';
  static const String coachIntroductionPage = '/be_a_coach_introduction_page';
  static const String coachRegistrationFormPage = '/create_coach_profile_page';
  static const String coachProfileEditPage = '/edit_coach_profile_page';

  static const String coachDashboardPage = '/coach_dashboard_page';
  static const String coachProgramCreatePage = '/create_coach_program';
  static const String coachProgramPreviewPage = '/coach_program_preview_page';
  static const String coachProgramPreviewByIdPage = '/coach_program_preview_by_id_page';
  static const String coachProfileDetailsPage = '/coach_profile_details_page';
  
  static const String coachProgramEnrollmentHistoryPage = '/coach_enrollment_history';
  static const String coachProgramEnrolledDetailsPage = '/coach_program_enrolled_details_page';
  
  static const String studentEnrollmentInitialPage = '/student_enrollment_initial_page';
  static const String studentEnrollmentDashboardPage = '/student_enrollment_dashboard_page';
  static const String studentEnrollmentDetailsPage = '/student_enrollment_details_page';
  
  static const String coachNewsfeedProgramsPage = '/coach_newsfeed_programs_page';
  static const String coachProgramSearchPage = '/coach_program_search_page';
  static const String coachNameSearchPage = '/coach_search_page';

  static const String coachSearchListPage = '/coach_search_list_page';
  static const String coachSearchPage = '/coach_search_page';
  static const String coachOfferedPrograms = '/coach_offered_programs';
  static const String coachOptionsDashboardPage = '/coach_options_dashboard_page';
  static const String coachFeatureDashboardPage = '/coach_feature_dashboard_page';

  static const String coachRatigsPage = '/coach_ratings_page';
  static const String coachWithdrawPaymentPage = '/coach_withdraw_payment_page';


  //// rewards
  static const String rewardDashboardPage = '/reward_dashboard_page';
  static const String rewardLeaderboardPage = '/reward_leaderboard_paeg';

  /// referrals
  static const String referralListPage = '/referral_available_page';
  static const String referralDetailPage = '/referral_details_page';
  static const String rewardPointHistoryPage = '/reward_point_history_page';

  /// daily task
  static const String dailyTaskPage = '/daily_task_page';

  /// support page
  static const String supportPage = '/support_page';

  /// diet food scanner
  static const String cameraV2Page = '/camera_v2_page';
  static const String cameraV2PageTest = '/camera_v2_page_test';
  static const String foodScannerPage = '/food_scanner_page';
  static const String scanFoodDetailsPage = '/scan_food_details_page';

  /// diet
  static const String searchFoodDetailsPage = '/search_food_details_page';
}
