import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/services/local_storage/cache_service.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/verification_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'verification_event.dart';
part 'verification_state.dart';

class VerificationBloc extends Bloc<VerificationEvent, VerificationState> {
  VerificationBloc({required this.verificationUseCase})
      : super(VerificationInitial()) {
    on<VerificationEvent>(_onVerificationEvent);
  }

  final VerificationUseCase verificationUseCase;

  VerificationTimeCountDownState _verificationTimeCountDownEventToState(
    VerificationTimeCountDownEvent event,
    VerificationState state,
  ) {
    return const VerificationTimeCountDownState(
      duration: IntValue.v60,
    );
  }

  VerificationState _returnBasedOnResponseCede(String token) {
    CacheService.instance.storeBearerToken(token);

    return VerificationSuccessState();
  }

  FutureOr<void> _onVerificationEvent(
    VerificationEvent event,
    Emitter<VerificationState> emit,
  ) async {
    if (event is VerificationWithOtpEvent) {
      emit(VerificationLoadingState());

      Map<String, dynamic> map = {
        "otp": int.parse(event.otpCode),
        "email": event.email,
      };

      final response = await verificationUseCase(map);

      return response.fold(
        (l) => emit(VerificationErrorState(message: l.message!)),
        (r) => emit(_returnBasedOnResponseCede(r)),
      );
    } else if (event is VerificationTimeCountDownEvent) {
      emit(_verificationTimeCountDownEventToState(event, state));
    }
  }
}
