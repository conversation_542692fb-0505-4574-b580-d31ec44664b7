import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class AppToast {
  static showToast({
    required String message,
    Color? backgroundColor,
    Color? textColor,
    ToastGravity? gravity,
    Toast? toastLength,
  }) {
    return Fluttertoast.showToast(
      msg: message,
      backgroundColor: backgroundColor ?? AppColors.black54,
      textColor: textColor ?? AppColors.white,
      gravity: gravity ?? ToastGravity.CENTER,
      toastLength: toastLength ?? Toast.LENGTH_SHORT,
    );
  }
}
