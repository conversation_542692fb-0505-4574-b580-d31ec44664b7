import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/domain/entities/daily_task.dart';
import 'package:flutter/material.dart';

class DailyTaskCardWidget extends StatefulWidget {
  const DailyTaskCardWidget({super.key, required this.dailyTask});
  final DailyTask dailyTask;

  @override
  State<DailyTaskCardWidget> createState() => _DailyTaskCardWidgetState();
}

class _DailyTaskCardWidgetState extends State<DailyTaskCardWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(Values.v10),
      decoration: BoxDecoration(
        gradient: null,
          border: Border.all(color: AppColors.greyscale100),
          borderRadius: BorderRadius.circular(Values.v8)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitleSection(),
          SizedBox(
            height: Values.v10,
          ),
          _buildTaskInfoSection(),
        ],
      ),
    );
  }

  _buildTitleSection() {
    return Row(
      children: [Expanded(child: _buildTitleText())],
    );
  }

  _buildTitleText() {
    return Text(
      widget.dailyTask.title,
      style: AppTypography.poppinsSemiBold20(color: AppColors.greyscale950),
    );
  }

  _buildTaskInfoSection() {
    return Row(
      children: [
        _buildPointSection(),
        Spacer(),
        _buildTaskProgressSection(),
      ],
    );
  }

  _buildPointSection() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        RichText(
            text: TextSpan(
                style: AppTypography.poppinsMedium12(
                    color: UIColors.primaryGreen900),
                children: [
              TextSpan(text: 'Rewards: '),
              TextSpan(
                text: '${widget.dailyTask.totalRewardPoint} Points',
                style: AppTypography.poppinsMedium14(
                    color: UIColors.primaryGreen600),
              )
            ]))
      ],
    );
  }

  _buildTaskProgressSection() {
    double progress = widget.dailyTask.taskProgress * 100;
    
    
    return SizedBox(
      height: 40,
      width: 40,
      child: Stack(
        alignment: AlignmentDirectional.center,
        children: [
          CircularProgressIndicator(
            color: UIColors.primaryGreen500,
            backgroundColor: UIColors.primaryGreen100,
            value: widget.dailyTask.taskProgress,
          ),
          Text('${progress.toStringAsFixed(0)}%', style: AppTypography.poppinsRegular12(color: UIColors.primaryGreen500),),
        ],
      ),
    );
  }
}


