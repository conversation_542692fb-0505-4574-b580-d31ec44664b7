import 'dart:convert';

import 'package:fitsomnia_app/src/features/chat/direct_message/domain/entity/chat_history_entity.dart';

ChatHistoryEntity chatHistoryEntityFromJson(String str) => ChatHistoryModel.fromJson(json.decode(str));

class ChatHistoryModel extends ChatHistoryEntity {
  const ChatHistoryModel({
    required super.id,
    required super.senderId,
    required super.receiverId,
    required super.type,
    required super.content,
    required super.isLastSeen,
    required super.createdAt,
  });

  factory ChatHistoryModel.fromJson(Map<String, dynamic> json) => ChatHistoryModel(
    id: json["id"],
    senderId: json["senderId"],
    receiverId: json["receiverId"],
    type: json["type"],
    content: json["content"],
    isLastSeen: json["isLastSeen"] ?? false,
    createdAt: DateTime.parse(json["createdAt"]).toLocal(),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "senderId": senderId,
    "receiverId": receiverId,
    "type": type,
    "content": content,
    "isLastSeen": isLastSeen,
    "createdAt": createdAt.toIso8601String(),
  };
}
