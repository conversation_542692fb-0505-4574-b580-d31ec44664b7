part of 'group_chat_bloc.dart';

class GroupChatState extends Equatable {
  @override
  List<Object?> get props => [];
}

class GroupChatInitial extends GroupChatState {}

class GroupChatLoading extends GroupChatState {}

class GroupChatSuccess extends GroupChatState {
  final List<GroupChatEntity> groupChatEntities;

  GroupChatSuccess({required this.groupChatEntities});

  @override
  List<Object?> get props => groupChatEntities;
}

class GroupChatFailure extends GroupChatState {
  final String errorMessage;

  GroupChatFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}
