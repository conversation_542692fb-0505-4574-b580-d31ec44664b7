import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_point_rank_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/reward_point_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/presentation/bloc/reward_point_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';

class TotalPointsWidget extends StatefulWidget {
  const TotalPointsWidget({super.key, required this.userId});
  final String userId;

  @override
  State<TotalPointsWidget> createState() => _TotalPointsWidgetState();
}

class _TotalPointsWidgetState extends State<TotalPointsWidget> {
  RewardPointRankEntity? _rewardPointEntity;
  bool _isLoading = false;

  @override
  void initState() {
    BlocProvider.of<RewardPointBloc>(context).add(GetRewardPoints());
    _isLoading = true;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RewardPointBloc, RewardPointState>(
      listener: (context, state) {
        if (state is GetRewardPointSuccess) {
          setState(() {
            _rewardPointEntity = state.pointEntity;
            _isLoading = false;
          });
        }

        if (state is GetRewardPointFail) {
          setState(() {
            _isLoading = false;
            _rewardPointEntity = testPointRankEntity;
          });

          AppToast.showToast(message: '${(state.data as ErrorModel).message}', gravity: ToastGravity.BOTTOM);
        }
      },
      child: (_isLoading)
          ? const Center(
              child: CircularProgressIndicator(
                color: UIColors.primary,
              ),
            )
          : (_rewardPointEntity == null)
              ? const SizedBox(
                  height: 200,
                  child: Center(
                    child: Text('No Points data found'),
                  ),
                )
              : _buildTotalPointCard(),
    );
  }

  _buildTotalPointCard() {
    return Container(
      padding: EdgeInsets.all(Values.v20),
      decoration: BoxDecoration(
        gradient: _backgroundGradientBlueColor(),
        borderRadius: BorderRadius.circular(Values.v12),
      ),
      height: 200,
      width: double.infinity,
      child: Stack(
        alignment: AlignmentDirectional.center,
        children: [
          _buildImageLayerSection(),
          _buildPointsSection(),
        ],
      ),
    );
  }

  _backgroundGradientBlueColor() {
    return LinearGradient(colors: [UIColors.blueLight, UIColors.blueDeep]);
  }

  _buildImageLayerSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Image.asset(
          Assets.rewardsImg,
          height: Values.v117,
          width: Values.v135,
        )
      ],
    );
  }

  _buildPointsSection() {
    return Row(
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPointEarnSection(),
            _buildAmountSection(),
            _buildRedeemSection()
          ],
        ),
      ],
    );
  }

  _buildPointEarnSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildCoinImage(),
        SizedBox(
          width: Values.v10,
        ),
        _buildPointText()
      ],
    );
  }

  _buildAmountSection() {
    return Text('*You will get ${_rewardPointEntity!.pointPrice} taka per point',
        style: AppTypography.poppinsMedium12(color: UIColors.white));
  }

  _buildRedeemSection() {
    return Button.filled(
      textStyle: AppTypography.poppinsMedium16(color: UIColors.white),
      borderColor: UIColors.white,
      background: UIColors.purple950,
      label: 'Redeem',
      onPressed: () {
        Log.debug('redeem points');
        // AppToast.showToast(message: 'Need Minimum 5000 Points to redeem');

        Navigator.of(context).pushNamed(Routes.rewardPointHistoryPage);
      },
      width: Values.v175,
    );
  }

  _buildCoinImage() {
    return Image.asset(
      Assets.rewardPointImg,
      width: Values.v50,
      height: Values.v50,
    );
  }

  _buildPointText() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Total Points',
          style: AppTypography.poppinsMedium14(color: UIColors.white),
        ),
        Text('${_rewardPointEntity!.totalPoints}',
            style: AppTypography.poppinsSemiBold32(color: UIColors.white)),
      ],
    );
  }
}
