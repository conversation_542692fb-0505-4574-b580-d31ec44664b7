class NotificationModel {
  NotificationModel({
    required this.id,
    required this.notificationId,
    required this.module,
    this.seenAt,
    required this.targetUser,
    required this.createdAt,
    required this.updatedAt,
    required this.notification,
  });

  String id;
  String notificationId;
  String module;
  dynamic seenAt;
  String targetUser;
  DateTime createdAt;
  DateTime updatedAt;
  Notification notification;

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      NotificationModel(
        id: json["id"],
        notificationId: json["notificationId"],
        module: json["module"],
        seenAt: json["seenAt"],
        targetUser: json["targetUser"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        notification: Notification.fromJson(json["notification"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "notificationId": notificationId,
        "module": module,
        "seenAt": seenAt,
        "targetUser": targetUser,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "notification": notification.toJson(),
      };
}

class Notification {
  Notification({
    required this.id,
    required this.title,
    required this.content,
    this.documentId,
    this.type,
    this.feature,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
  });

  String id;
  String title;
  String content;
  String? type;
  String? documentId;
  String? feature; // Feature field for reminder navigation
  CreatedBy createdBy;
  DateTime createdAt;
  DateTime updatedAt;

  factory Notification.fromJson(Map<String, dynamic> json) => Notification(
        id: json["id"],
        title: json["title"],
        content: json["content"],
        type: json["type"] ?? '',
        documentId: json["documentId"] ?? '',
        feature: json["feature"], // Parse feature field from API
        createdBy: CreatedBy.fromJson(json["createdBy"]),
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "content": content,
        "type": type,
        "documentId": documentId ?? '',
        "feature": feature, // Include feature field in JSON
        "createdBy": createdBy.toJson(),
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
      };
}

class CreatedBy {
  CreatedBy({
    required this.name,
    this.avatar,
  });

  String name;
  String? avatar;

  factory CreatedBy.fromJson(Map<String, dynamic> json) => CreatedBy(
        name: json["name"],
        avatar: json["avatar"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "avatar": avatar,
      };
}
