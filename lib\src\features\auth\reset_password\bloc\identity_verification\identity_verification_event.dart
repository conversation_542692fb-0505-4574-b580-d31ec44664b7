part of 'identity_verification_bloc.dart';

class IdentityVerificationEvent extends Equatable {
  const IdentityVerificationEvent();

  @override
  List<Object?> get props => [];
}

class IdentityVerificationWithEmailAndOtpEvent
    extends IdentityVerificationEvent {
  const IdentityVerificationWithEmailAndOtpEvent({
    this.email,
    this.phone,
  });

  final String? email;
  final String? phone;
}
