import 'dart:async';
import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/identity_verification_use_case.dart';

part 'identity_verification_event.dart';

class IdentityVerificationBloc
    extends Bloc<IdentityVerificationEvent, BaseState> {
  IdentityVerificationBloc({
    required this.useCase,
  }) : super(InitialState()) {
    on<IdentityVerificationWithEmailAndOtpEvent>(_onIdentityVerificationEvent);
  }

  final IdentityVerificationUseCase useCase;

  FutureOr<void> _onIdentityVerificationEvent(event, emit) async {
    emit(const LoadingState());

    Map<String, dynamic> map = {
      if (event.email != null) "email": event.email,
      if (event.phone != null)
        "phone": event.phone.toString().startsWith('+')
            ? event.phone
            : '+${event.phone}',
      "otp": int.parse(otpField.text),
    };

    try {
      final response = await useCase.call(map);

      return response.fold(
        (l) => emit(ErrorState(data: l.message)),
        (r) {
          otpField.clear();

          return emit(const SuccessState());
        },
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      emit(ErrorState(data: ErrorModel()));
    }
  }

  TextEditingController otpField = TextEditingController();
}
