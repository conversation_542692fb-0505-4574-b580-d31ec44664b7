import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_income_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/use_case/coach_personal_use_case.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';

part 'coach_dashboard_event.dart';
part 'coach_dashboard_state.dart';

class CoachDashboardBloc
    extends Bloc<CoachDashboardEvent, CoachDashboardState> {
  CoachDashboardBloc({required this.coachPersonalUseCase})
      : super(CoachDashboardInitial()) {
    on<GetCoachOwnProfileEvent>(_onGetCoachOwnProfileEvent);
    on<GetCoachProgramsEvent>(_onGetCoachProgramsEvent);
    on<GetCoachAllProgramEnrollmentHistoryEvent>(
        _onGetCoachAllProgramEnrollmentHistoryEvent);
    on<GetCoachSingleProgramEnrollmentDetailEvent>(
        _onGetCoachSingleProgramEnrollmentHistoryEvent);
    on<ProgramSubscriptionCancelByCoachEvent>(
        _onProgramSubscriptionCancelByCoachEvent);
    on<GetCoachIncomeEvent>(_onGetCoachIncomeEvent);
    on<GetCoachProgramEnrollerHistoryEvent>(
        _onGetCoachProgramEnrollerHistoryEvent);
    on<GetCoachProgramPaymentHistoryEvent>(
        _onGetCoachProgramPaymentHistoryEvent);
  }

  final CoachPersonalUseCase coachPersonalUseCase;

  FutureOr<void> _onGetCoachOwnProfileEvent(
    GetCoachOwnProfileEvent event,
    Emitter<CoachDashboardState> emit,
  ) async {
    try {
      emit(CoachDashboardLoading());
      final response =
          await coachPersonalUseCase.getCoachOwnProfile(coachId: event.coachId);

      response.fold((l) {
        emit(GetCoachOwnProfileFail(data: l));
      }, (r) {
        emit(GetCoachOwnProfileSuccess(coachEntity: r));
      });
    } catch (e) {
      Log.debug(e.toString());
      emit(GetCoachOwnProfileFail(data: e));
    }
  }

  Future<void> _onGetCoachProgramsEvent(
      GetCoachProgramsEvent event, Emitter<CoachDashboardState> emit) async {
    try {
      emit(CoachDashboardLoading());
      final response =
          await coachPersonalUseCase.getCoachPrograms(coachId: event.coachId);

      response.fold((l) {
        emit(GetCoachOwnProgramsFail(data: l));
      }, (r) {
        emit(GetCoachOwnProgramsSuccess(programs: r));
      });
    } catch (e) {
      Log.debug(e.toString());
      emit(GetCoachOwnProgramsFail(data: e));
    }
  }

  Future<void> _onGetCoachAllProgramEnrollmentHistoryEvent(
      GetCoachAllProgramEnrollmentHistoryEvent event,
      Emitter<CoachDashboardState> emit) async {
    try {
      emit(CoachDashboardLoading());
      final response = await coachPersonalUseCase.getProgramEnrollers(
          coachId: event.coachId);
      response.fold((l) {
        emit(GetCoachProgramEnrollersFail(data: l));
      }, (r) {
        emit(GetCoachProgramEnrollersSuccess(userSubscriptionHistoryEntity: r));
      });
    } catch (e) {
      Log.debug(e.toString());

      emit(GetCoachProgramEnrollersFail(data: e));
    }
  }

  Future<void> _onGetCoachSingleProgramEnrollmentHistoryEvent(
      GetCoachSingleProgramEnrollmentDetailEvent event,
      Emitter<CoachDashboardState> emit) async {
    try {
      emit(CoachDashboardLoading());
      final response = await coachPersonalUseCase.getProgramEnroller(
          coachId: event.coachId, subscriptionId: event.subscriptionId);
      response.fold((l) {
        emit(GetCoachProgramSingleEnrollerFail(data: l));
      }, (r) {
        emit(GetCoachProgramSingleEnrollerSuccess(enrolledProgram: r));
      });
    } catch (e) {
      Log.debug(e.toString());

      emit(GetCoachProgramSingleEnrollerFail(data: e));
    }
  }

  Future<void> _onProgramSubscriptionCancelByCoachEvent(
    ProgramSubscriptionCancelByCoachEvent event,
    Emitter<CoachDashboardState> emit,
  ) async {
    try {
      emit(CoachDashboardLoading());
      final response =
          await coachPersonalUseCase.cancelProgramSubscriptionByCoach(
              coachId: event.coachId,
              subscriptionId: event.subscriptionId,
              cancelReason: event.cancelReason);

      response.fold((l) {
        emit(ProgramSubscriptionCancelByCoachFail(data: l));
      }, (r) {
        emit(ProgramSubscriptionCancelByCoachSuccess(enrolledProgram: r));
      });
    } catch (e) {
      Log.debug(e.toString());
      emit(ProgramSubscriptionCancelByCoachFail(data: e));
    }
  }

  Future<void> _onGetCoachIncomeEvent(
      GetCoachIncomeEvent event, Emitter<CoachDashboardState> emit) async {
    try {
      emit(CoachDashboardLoading());
      final response =
          await coachPersonalUseCase.getCoachIncomeInfo(coachId: event.coachId);

      response.fold((l) {
        emit(GetCoachIncomeInfoFail(data: l));
      }, (r) {
        emit(GetCoachIncomeInfoSuccess(coachIncomeEntity: r));
      });
    } catch (e) {
      Log.debug(e.toString());
      emit(GetCoachIncomeInfoFail(data: e));
    }
  }

  FutureOr<void> _onGetCoachProgramEnrollerHistoryEvent(
    GetCoachProgramEnrollerHistoryEvent event,
    Emitter<CoachDashboardState> emit,
  ) async {
     try {
      emit(CoachDashboardLoading());
      final response = await coachPersonalUseCase.getProgramEnrollerHistory(
          coachId: event.coachId, programId: event.programId);
      response.fold((l) {
        emit(GetCoachProgramEnrollerHistoryFail(data: l));
      }, (r) {
        emit(GetCoachProgramEnrollerHistorySuccess(userSubscriptionHistoryEntity: r));
      });
    } catch (e) {
      Log.debug(e.toString());

      emit(GetCoachProgramEnrollerHistoryFail(data: e));
    }
  }

  FutureOr<void> _onGetCoachProgramPaymentHistoryEvent(
    GetCoachProgramPaymentHistoryEvent event,
    Emitter<CoachDashboardState> emit,
  ) async {
     try {
      emit(CoachDashboardLoading());
      final response = await coachPersonalUseCase.getProgramPaymentHistory(
          coachId: event.coachId, programId: event.programId);
      response.fold((l) {
        emit(GetCoachProgramPaymentHistoryFail(data: l));
      }, (r) {
        emit(GetCoachProgramPaymentHistorySuccess(userSubscriptionHistoryEntity: r));
      });
    } catch (e) {
      Log.debug(e.toString());

      emit(GetCoachProgramPaymentHistoryFail(data: e));
    }
  }
}
