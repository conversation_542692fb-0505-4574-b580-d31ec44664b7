import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';

class DietCalendarWidget extends StatefulWidget {
  const DietCalendarWidget(
      {super.key,
      required this.firstDay,
      required this.lastDay,
      required this.focusDay,
      required this.onDaySelectionCallback});
  final DateTime firstDay;
  final DateTime lastDay;
  final DateTime focusDay;
  final Function(DateTime?) onDaySelectionCallback;

  @override
  State<DietCalendarWidget> createState() => _DietCalendarWidgetState();
}

class _DietCalendarWidgetState extends State<DietCalendarWidget> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime? _selectedDay;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 400,
      // width: 400,
      child: TableCalendar(
        firstDay: widget.firstDay,
        lastDay: widget.lastDay,
        focusedDay: widget.focusDay,
        currentDay: widget.focusDay,
        calendarFormat: _calendarFormat,
        availableCalendarFormats: {CalendarFormat.month: 'Months'},
        headerStyle: HeaderStyle(
          titleCentered: true,
          titleTextStyle:
              AppTypography.poppinsMedium16(color: AppColors.greyscale950),
        ),
        daysOfWeekStyle: DaysOfWeekStyle(
          weekdayStyle: AppTypography.poppinsMedium14(
            color: AppColors.greyscale400,
          ),
          weekendStyle: AppTypography.poppinsMedium14(
            color: AppColors.greyscale400,
          ),
        ),
        selectedDayPredicate: (day) {
          return isSameDay(_selectedDay, day);
        },
        onDaySelected: (selectedDay, focusedDay) {
          if (!isSameDay(_selectedDay, selectedDay)) {
            setState(() {
              _selectedDay = selectedDay;
            });

            widget.onDaySelectionCallback(_selectedDay);
            Navigator.of(context).pop();
          }
        },
        // onPageChanged: (focusedDay) {
        //   _focusedDay = focusedDay;
        // },
        calendarBuilders: CalendarBuilders(
          defaultBuilder: (context, day, focusedDay) {
            return Container(
              margin: const EdgeInsets.all(6.0),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: AppColors.greyscale50,
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: UIColors.white, width: 1),
              ),
              child: Text(
                day.day.toString(),
                style: AppTypography.poppinsSemiBold16(),
              ),
            );
          },
          todayBuilder: (context, day, focusedDay) {
            return Container(
              margin: const EdgeInsets.all(6.0),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: UIColors.primary,
                shape: BoxShape.rectangle,
                border: Border.all(color: UIColors.primary, width: 2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                day.day.toString(),
                style: AppTypography.poppinsSemiBold16(
                  color: UIColors.white,
                ),
              ),
            );
          },
          selectedBuilder: (context, day, focusedDay) {
            return Container(
              margin: const EdgeInsets.all(6.0),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: UIColors.primary,
                shape: BoxShape.rectangle,
                border: Border.all(color: UIColors.primary, width: 2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                day.day.toString(),
                style: AppTypography.poppinsSemiBold16(
                  color: UIColors.white,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
