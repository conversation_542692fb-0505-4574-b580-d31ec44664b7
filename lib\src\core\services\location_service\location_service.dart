import 'dart:io';

import 'package:fitsomnia_app/src/core/global/globals.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';

class LocationService {
  Future<void> init() async {
    if (await checkPermission()) {
      await getLocation();
    }
  }

  /// Permission
  LocationPermission? _grantedPermission;

  LocationPermission get grantedPermission =>
      _grantedPermission ?? LocationPermission.denied;

  bool get isGranted =>
      _grantedPermission == LocationPermission.whileInUse ||
      _grantedPermission == LocationPermission.always;

  bool get isDeniedForever =>
      _grantedPermission == LocationPermission.deniedForever;

  Future<bool> checkPermission({bool? isFromTurnOn}) async {
    if (await _checkService()) {
      _grantedPermission = await Geolocator.checkPermission();
      Log.debug("Permission: $_grantedPermission");
      if (_grantedPermission == LocationPermission.denied) {
        if (Platform.isIOS) {
          await requestPermission();
        } else if (!showPermissionAlert.value) {
          showPermissionAlert.value = true;
        } else if (showPermissionAlert.value) {
          showPermissionAlert.value = false;
          await requestPermission();
        }
      }
    } else if (isFromTurnOn == true) {
      await requestPermission();
    }

    return isGranted;
  }

  Future requestPermission() async {
    _grantedPermission = await Geolocator.requestPermission();

    _grantedPermission = await Geolocator.checkPermission();
    Log.debug("Permission: $_grantedPermission");
    if (Platform.isAndroid && isGranted) {
      await getLocation();
    }
  }

  /// Service
  bool _isServiceEnabled = false;

  bool get isServiceEnabled => _isServiceEnabled;

  Future<bool> _checkService() async {
    _isServiceEnabled = await Geolocator.isLocationServiceEnabled();

    return _isServiceEnabled;
  }

  Future requestService() async {
    _isServiceEnabled = await Geolocator.openLocationSettings();
  }

  /// Location and [Placemark]
  Position? userLocation;

  Future<Position?> getLocation() async {
    try {
      if (isGranted) {
        userLocation = await Geolocator.getCurrentPosition();
      } else {
        await checkPermission();
        if (isGranted) {
          userLocation = await Geolocator.getCurrentPosition();
        }
      }

      return userLocation;
    } catch (e) {
      Log.error('Error getting location: $e');
    }

    return null;
  }

  Future<List<Placemark>> getPlaceMarks(
    double latitude,
    double longitude,
  ) async {
    try {
      return await placemarkFromCoordinates(latitude, longitude);
    } catch (e) {
      Log.error('Error getting place marks: $e');
    }

    return [];
  }
}
