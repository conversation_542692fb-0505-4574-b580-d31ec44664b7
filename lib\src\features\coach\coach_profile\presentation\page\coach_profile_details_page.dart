import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/core/widgets/image_viewer.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/bloc/coach_profile_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/presentation/widgets/coach_profile_reviews_widget.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/presentation/page/spot_profile_others_details_page.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/presentation/widgets/image_slider_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:page_transition/page_transition.dart';

class CoachProfileDetailsPage extends StatefulWidget {
  CoachProfileDetailsPage(
    this.coachEntity,
    this.coachId,
    this.isFromCoachNewsfeed, {
    super.key,
  });
  final CoachEntity? coachEntity;
  final String? coachId;
  final bool isFromCoachNewsfeed;

  @override
  State<CoachProfileDetailsPage> createState() =>
      _CoachProfileDetailsPageState();
}

class _CoachProfileDetailsPageState extends State<CoachProfileDetailsPage> {
  CoachEntity? _coachEntity;

  @override
  void initState() {
    super.initState();

    if (widget.coachEntity != null) {
      _coachEntity = widget.coachEntity;
    }

    if (widget.coachId != null) {
      BlocProvider.of<CoachNewsfeedBloc>(context)
          .add(GetSingleCoachProfileEvent(coachId: widget.coachId!));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SafeArea(
        child: BlocListener<CoachNewsfeedBloc, CoachNewsfeedState>(
          listener: (context, state) {
            if (state is GetSingleCoachProfileSuccess) {
              Log.debug('get coach profile success');
              setState(() {
                _coachEntity = state.coachProfile;
              });
            }
            if (state is GetSingleCoachProfileFail) {
              Log.debug('get coach profile fail');
            }
          },
          child: (_coachEntity == null)
              ? Center(
                  child: CircularProgressIndicator(
                    color: UIColors.primary,
                  ),
                )
              : Stack(
                  alignment: AlignmentDirectional.bottomCenter,
                  children: [
                    SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: Values.v12, vertical: Values.v20),
                        child: Column(
                          children: [
                            ImageSliderWidget(
                              listOfImageUrls: _coachEntity!.media!
                                  .map<String>((x) => x.url)
                                  .toList(),
                              height: Values.v400,
                              width: Values.v400 * (1 / 1.43),
                              fit: BoxFit.fill,
                            ),
                            CoachProfileInfoWidget(coachEntity: _coachEntity!),
                            const SizedBox(height: Values.v100)
                          ],
                        ),
                      ),
                    ),
                    _buildViewCoachProgramsButton(),
                  ],
                ),
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      // title: BlocBuilder<SpotMeBloc, BaseState>(
      //   builder: (context, state) {
      //     return state is GetOthersSpotProfileSuccessState
      //         ? Text(
      //             (state.data as SpotMeProfileEntity).name!,
      //             style: const TextStyle(
      //               color: AppColors.black,
      //               fontWeight: FontWeight.bold,
      //             ),
      //           )
      //         : const SizedBox();
      //   },
      // ),
      title: Text(
        'Coach Details',
        style: const TextStyle(
          color: UIColors.primaryGreen950,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      elevation: 0,
    );
  }

  _buildViewCoachProgramsButton() {
    return Container(
      color: AppColors.white,
      padding: EdgeInsets.only(bottom: Values.v20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Button.filled(
              label: 'View Programs',
              onPressed: () {
                Navigator.of(context).pushNamed(Routes.coachOfferedPrograms,
                    arguments:
                        _coachEntity!.coachId!); // TODO need to provide coachId
              }),
        ],
      ),
    );
  }
}

class CoachProfileInfoWidget extends StatefulWidget {
  const CoachProfileInfoWidget({super.key, required this.coachEntity});
  final CoachEntity coachEntity;

  @override
  State<CoachProfileInfoWidget> createState() => _CoachProfileInfoWidgetState();
}

class _CoachProfileInfoWidgetState extends State<CoachProfileInfoWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildProfileBasicInfoSection(),
        Divider(height: 40, thickness: 2, color: AppColors.greyscale100),
        _buildProfileSkillSection(),
        Divider(
          height: 40,
          thickness: 2,
          color: AppColors.greyscale100,
        ),
        _buildProfileDeatailsInfoSection(),
        Divider(
          height: 40,
          thickness: 2,
          color: AppColors.greyscale100,
        ),
        _buildCoachRatingSection(),
      ],
    );
  }

  _buildProfileBasicInfoSection() {
    if (widget.coachEntity.name == '') return const SizedBox.shrink();

    return Column(
      children: [
        SizedBox(
          height: Values.v20,
        ),
        _buildNameSection(name: widget.coachEntity.name),
        SizedBox(
          height: Values.v10,
        ),
        _buildCoachTotalRatingSection(),
        SizedBox(
          height: Values.v16,
        ),
        Wrap(
          alignment: WrapAlignment.center,
          spacing: 10,
          children: [
            _buildStarIconSection(),
            _buildSubscriptionsSection(),
          ],
        ),
      ],
    );
  }

  _buildNameSection({required String name}) {
    return Text(
      name,
      textAlign: TextAlign.center,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: AppTypography.poppinsBold32(color: UIColors.primaryGreen900),
    );
  }

  _buildCoachTotalRatingSection() {
    if (widget.coachEntity.reviewCount == null ||
        widget.coachEntity.reviewCount! <= 0) {
      return SizedBox.shrink();
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.star,
          color: UIColors.primaryGreen400,
        ),
        SizedBox(
          width: Values.v3,
        ),
        Text(
          '${widget.coachEntity.currentRating ?? 0}',
          style:
              AppTypography.poppinsSemiBold16(color: UIColors.primaryGreen950),
        ),
        Text(
          '(${widget.coachEntity.reviewCount} Reviews)',
          style: AppTypography.poppinsRegular14(color: AppColors.greyscale400),
        )
      ],
    );
  }

  _buildStarIconSection() {
    if (widget.coachEntity.isStarCoach == null ||
        widget.coachEntity.isStarCoach! == false) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: BoxDecoration(
        color: AppColors.yellow50,
        border: Border.all(color: UIColors.orange500),
        borderRadius: BorderRadius.circular(100),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.star, color: UIColors.orange500),
          const SizedBox(
            width: 5,
          ),
          Text(
            'Star Coach',
            style: AppTypography.poppinsMedium14(color: UIColors.orange500),
          ),
        ],
      ),
    );
  }

  _buildSubscriptionsSection() {
    return (widget.coachEntity.subscriptionCount == null ||
            widget.coachEntity.subscriptionCount! <= 0)
        ? const SizedBox.shrink()
        : Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            decoration: BoxDecoration(
              color: UIColors.purple50,
              border: Border.all(color: UIColors.purple500),
              borderRadius: BorderRadius.circular(100),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon(Icons.star, color: UIColors.purple500),
                SvgPicture.asset(
                  Assets.coachSubscribeIcon,
                  color: UIColors.purple500,
                ),
                SizedBox(
                  width: 5,
                ),
                Text(
                  '${widget.coachEntity.subscriptionCount} Subscriptionns',
                  style:
                      AppTypography.poppinsMedium14(color: UIColors.purple500),
                ),
              ],
            ),
          );
  }

  _buildProfileSkillSection() {
    List<Widget> children = [];
    CoachSkillLevel skillEnum =
        getSkillEnumFromKey(widget.coachEntity.skillLevel);
    String skillName = 'Skill Level';
    String skillValue = skillEnum.getTitle();
    children.add(_buildSingleInfoSection(
        Assets.coachSkillLevelIcon, skillName, skillValue));

    String experienceName = 'Experience';
    String experienceValue = '${widget.coachEntity.experienceInYear} Years';
    children.add(_buildSingleInfoSection(
        Assets.coachExperienceIcon, experienceName, experienceValue));

    children.add(_buildSingleInfoSection(
        Assets.coachExpertiseIcon, 'Expertise', widget.coachEntity.expertise));

    String achievements = '' + widget.coachEntity.achievements.join(', ');

    children.add(_buildSingleInfoSection(
        Assets.coachAchievementIcon, 'Achievement', achievements));

    return Wrap(
      alignment: WrapAlignment.start,
      spacing: 20,
      runSpacing: 20,
      children: children,
    );
  }

  _buildSingleInfoSection(
    String skillIcon,
    String skillName,
    String skillDetails,
  ) {

    if(skillDetails.isEmpty) {
      return SizedBox.shrink();
    }

    return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon(Icons.sports_gymnastics),
                Padding(
                  padding: const EdgeInsets.all(3.0),
                  child: SizedBox(
                    height: 16,
                    width: 16,
                    child: Image.asset(skillIcon),
                  ),
                ),
                Text(
                  skillName,
                  style: AppTypography.poppinsSemiBold16(
                      color: UIColors.primaryGreen950),
                )
              ],
            ),
            Flexible(
              child: Text(
                skillDetails,
                softWrap: true,
                maxLines: 20,
                overflow: TextOverflow.ellipsis,
                style:
                    AppTypography.poppinsRegular14(color: AppColors.greyscale400),
              ),
            )
          ],
        );
  }

  _buildProfileDeatailsInfoSection() {
    return Column(
      children: [
        _buildAboutSection(),
        const SizedBox(
          height: Values.v20,
        ),
        _buildCredentialsSection(),
      ],
    );
  }

  _buildAboutSection() {
    if (widget.coachEntity.about == '') {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInfoSectionHeader(Assets.coachAboutIcon, 'About'),
        SizedBox(
          height: Values.v16.h,
        ),
        _buildAboutTextSection(),
      ],
    );
  }

  _buildCredentialsSection() {
    if (widget.coachEntity.credentials == null ||
        widget.coachEntity.credentials!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInfoSectionHeader(Assets.coachCredentialdIcon, 'Credentials'),
        SizedBox(
          height: Values.v16.h,
        ),
        _buildCredentialImages(),
      ],
    );
  }

  _buildInfoSectionHeader(String icon, String title) {
    return Row(
      children: [
        // Container(
        //   height: Values.v36.h,
        //   width: Values.v36.w,
        //   decoration: BoxDecoration(
        //     color: UIColors.primaryGreen100,
        //     borderRadius: BorderRadius.circular(Values.v36.r),
        //   ),
        //   child: Center(
        //     child: SvgPicture.asset(
        //       icon,
        //       height: Values.v36.h,
        //       width: Values.v36.w,
        //     ),
        //   ),
        // ),
        SvgPicture.asset(
          icon,
          height: Values.v36.h,
          width: Values.v36.w,
        ),
        SizedBox(
          width: Values.v10.w,
        ),

        Text(
          title,
          style:
              AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen600),
        ),
      ],
    );
  }

  _buildAboutTextSection() {
    // String text =
    //     'I will assess the physical and health conditions of clients, create appropriate exercise plans, and monitor their improvement.... see moreI will assess the physical and health conditions of clients, create appropriate exercise plans, and monitor their improvement.... see more';

    return ProfileAboutSectionWidget(aboutText: widget.coachEntity.about);
    // return ProfileAboutSectionWidget(aboutText: text);
  }

  _buildCredentialImages() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: widget.coachEntity.credentials!.map<Widget>((mediaFile) {
          return _buildCredentialSingleMediaWidget(coachMediaFile: mediaFile);
        }).toList(),
      ),
    );
  }

  _buildCredentialSingleMediaWidget({required CoachMediaFile coachMediaFile}) {
    return GestureDetector(
      onTap: () {
        _previewMediaFile(mediaFileUrl: coachMediaFile.url);
      },
      child: Container(
        margin: const EdgeInsets.all(Values.v5),
        decoration: BoxDecoration(
            border: Border.all(color: AppColors.greyscale100),
            borderRadius: BorderRadius.circular(Values.v6)),
        child: ImageContainer.rectangularImage(
          image: coachMediaFile.url,
          width: Values.v90,
          height: Values.v90,
          // useOriginal: false,
          useSmall: true,
          hideLoadingIndicator: true,
        ),
        //Image.network(coachMediaFile.url)
      ),
    );
  }

  _buildCoachRatingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInfoSectionHeader(Assets.coachRatingsIcon, 'Ratings'),
        SizedBox(
          height: Values.v16.h,
        ),
        CoachProfileReviewsWidget(
          coachId: widget.coachEntity.coachId!,
          isShowAllRating: false,
        ),
      ],
    );
  }

  void _previewMediaFile({required String mediaFileUrl}) {
    Navigator.push(
      context,
      PageTransition(
        type: PageTransitionType.scale,
        alignment: Alignment.bottomCenter,
        child: ImageViewer(
          image: mediaFileUrl,
        ),
      ),
    );
  }
}

class ProfileAboutSectionWidget extends StatefulWidget {
  const ProfileAboutSectionWidget({
    super.key,
    required this.aboutText,
  });
  final String aboutText;

  @override
  State<ProfileAboutSectionWidget> createState() =>
      _ProfileAboutSectionWidgetState();
}

class _ProfileAboutSectionWidgetState extends State<ProfileAboutSectionWidget> {
  bool isExpended = false;

  @override
  void initState() {
    isExpended = false;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [_buildTextSection(), _buildSeeMoreLessSection()],
    );
  }

  _buildTextSection() {
    if (!isExpended) {
      return Text(
        widget.aboutText,
        maxLines: 3,
        overflow: TextOverflow.ellipsis,
        style: AppTypography.poppinsSemiBold16(color: UIColors.primaryGreen950),
      );
    } else {
      return Text(
        widget.aboutText,
        style: AppTypography.poppinsSemiBold16(color: UIColors.primaryGreen950),
      );
    }
  }

  _buildSeeMoreLessSection() {
    return GestureDetector(
      onTap: () {
        setState(() {
          isExpended = !isExpended;
        });
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text(
            (isExpended == false) ? 'See More' : 'See Less',
            style: AppTypography.poppinsMedium16(color: AppColors.greyscale400),
          ),
        ],
      ),
    );
  }
}
