part of '../page/chat_dashboard_page.dart';

class _PeopleSearchBar extends StatelessWidget {
  const _PeopleSearchBar({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _onPressed(context),
      child: Container(
        margin: EdgeInsets.only(
          left: 16.w,
          right: 16.w,
          top: 6.h,
          bottom: 20.h,
        ),
        padding: const EdgeInsets.all(12),
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(30.r),
          border: Border.all(
            color: AppColors.softGrey, // Set the green border color
            width: 2, // Set the width of the border (adjust as needed)
          ),
        ),
        child: Row(
          children: [
            // Icon(
            //   Icons.search,
            //   color: AppColors.primaryGreen,
            // ),
            // SizedBox(width: 13.w),
            Text(
              "Search for people to send message",
              style: TextStyle(
                color: AppColors.grey2,
                fontSize: 16
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onPressed(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MessageToPage(
          autoFocus: true,
        ),
      ),
    );
  }
}
