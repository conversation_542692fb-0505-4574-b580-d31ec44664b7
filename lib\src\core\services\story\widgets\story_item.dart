import 'package:fitsomnia_app/src/core/services/story/controller/story_controller.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/entities/story_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'story_image.dart';
import 'story_video.dart';

/// This is a representation of a story item (or page).
class StoryItem {
  /// Specifies how long the page should be displayed. It should be a reasonable
  /// amount of time greater than 0 milliseconds.
  final Duration duration;

  /// Has this page been shown already? This is used to indicate that the page
  /// has been displayed. If some pages are supposed to be skipped in a story,
  /// mark them as shown `shown = true`.
  ///
  /// However, during initialization of the story view, all pages after the
  /// last unshown page will have their `shown` attribute altered to false. This
  /// is because the next item to be displayed is taken by the last unshown
  /// story item.
  bool shown;

  /// The page content
  final Widget view;
  final Widget headerView;
  final String? storyItemId;
  final Story story;

  StoryItem(
      this.view, {
        required this.duration,
        required this.story,
        this.shown = false,
        this.headerView = const SizedBox.shrink(),
        this.storyItemId,
      });

  /// Factory constructor for page images. [controller] should be same instance as
  /// one passed to the `StoryView`
  factory StoryItem.pageImage({
    required String url,
    Widget headerView = const SizedBox.shrink(),
    String? storyItemId,
    required StoryController controller,
    Key? key,
    BoxFit imageFit = BoxFit.fitWidth,
    bool shown = false,
    Map<String, dynamic>? requestHeaders,
    Duration? duration,
    required Story story,
  }) {
    return StoryItem(
      Container(
        key: key,
        color: Colors.black,
        child: Stack(
          children: <Widget>[
            Padding(
              padding: EdgeInsets.only(top: Values.v120.h),
              child: StoryImage.url(
                url,
                controller: controller,
                fit: imageFit,
                requestHeaders: requestHeaders,
              ),
            ),
            headerView,
          ],
        ),
      ),
      shown: shown,
      duration: duration ?? const Duration(seconds: 5),
      storyItemId: storyItemId,
      story: story,
    );
  }

  /// Shorthand for creating page video. [controller] should be same instance as
  /// one passed to the `StoryView`
  factory StoryItem.pageVideo(
      String url, {
        required StoryController controller,
        Widget headerView = const SizedBox.shrink(),
        String? storyItemId,
        Key? key,
        Duration? duration,
        BoxFit imageFit = BoxFit.fitWidth,
        bool shown = false,
        Map<String, dynamic>? requestHeaders,
        required Story story,
      }) {
    return StoryItem(
      Container(
        key: key,
        color: Colors.black,
        child: Stack(
          children: <Widget>[
            Padding(
              padding: EdgeInsets.only(top: Values.v120.h),
              child: StoryVideo.url(
                url,
                controller: controller,
                requestHeaders: requestHeaders,
              ),
            ),
            headerView,
          ],
        ),
      ),
      shown: shown,
      duration: duration ?? const Duration(seconds: 10),
      storyItemId: storyItemId,
      story: story,
    );
  }
}