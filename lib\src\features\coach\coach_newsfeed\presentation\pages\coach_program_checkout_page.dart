import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/extensions/extensions.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/validators/input_validators.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/core/widgets/custom_button.dart';
import 'package:fitsomnia_app/src/features/coach/coach_payment/presentation/pages/coach_bkash_payment_screen.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/model/coach_program_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/bloc/coach_program_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/widgets/coach_program_subscription_success_widget.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/presentation/bloc/referral_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CoachProgramCheckoutPage extends StatefulWidget {
  const CoachProgramCheckoutPage({
    super.key,
    required this.programEntity,
    required this.selectedPaymentType,
  });
  final CoachProgramEntity programEntity;
  final CoachProgramPaymentInfo selectedPaymentType;

  @override
  State<CoachProgramCheckoutPage> createState() =>
      _CoachProgramCheckoutPageState();
}

class _CoachProgramCheckoutPageState extends State<CoachProgramCheckoutPage> {
  bool _isLoading = false;
  TextEditingController referralCodeText = TextEditingController(text: '');

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    referralCodeText.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        // BlocListener<CoachBloc, CoachState>(
        //   listener: (context, state) {
        //     if (state is SubscribeProgramSuccess) {
        //       Log.debug(
        //           'subscribtion program  paymentUrl: //${state.paymentUrl}');
        //       // go to webview for bkash payment
        //     }

        //     if (state is SubscribeProgramFail) {
        //       Log.debug('subscription program fialed');
        //       AppToast.showToast(
        //           message: 'Program subscription failed',
        //           gravity: ToastGravity.BOTTOM);
        //     }
        //   },
        // ),
        BlocListener<CoachProgramBloc, CoachProgramState>(
          listener: (context, state) {
            if (state is SubscribeCoachProgramSuccess) {
              Log.debug(
                  'subscribtion program  id: ${state.enrollmentEntity.subscriptionId}');

              AppToast.showToast(
                message: 'Subscribing to Program ...',
                gravity: ToastGravity.BOTTOM,
              );

              // AppToast.showToast(
              //     message: 'Program subscription success. Payment on process', gravity: ToastGravity.BOTTOM,);

              BlocProvider.of<CoachProgramBloc>(context)
                  .add(GetPaymentGetwayUrlEvent(
                subscriptionId: state.enrollmentEntity.subscriptionId,
                paymentTerm: widget.selectedPaymentType.paymentTerm!,
              ));
            }

            if (state is SubscribeProgramFail) {
              Log.debug('Program subscription fialed');
              AppToast.showToast(
                  message: 'Program subscription failed. Try again later',
                  gravity: ToastGravity.BOTTOM);

              setState(() {
                _isLoading = false;
              });
            }

            if (state is GetPaymentGetwayUrlSuccess) {
              Log.debug('Program payment url get success');
              setState(() {
                _isLoading = false;
              });

              AppToast.showToast(
                message: 'Payment Process Starting',
                gravity: ToastGravity.BOTTOM,
              );

              //TODO go to webview for bkash payment
              navigateToWebView(state.subscriptionFeePaymentInfo.paymentUrl);
            }

            if (state is GetPaymentGetwayUrlFail) {
              Log.debug('Program payment getway url not found');
              AppToast.showToast(
                  message: 'Program payment failed',
                  gravity: ToastGravity.BOTTOM);

              setState(() {
                _isLoading = false;
              });
            }
          },
        ),
      ],
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          title: const Text('Program Cart'),
          centerTitle: true,
          titleTextStyle:
              AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen900),
        ),
        body: SingleChildScrollView(
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildProgramInfoCard(),
                  const SizedBox(
                    height: Values.v20,
                  ),
                  _buildProgramPriceSection(),
                  const SizedBox(
                    height: Values.v20,
                  ),
                  _buildProgramReferralCodeSection(),
                  SizedBox(
                    height: 20,
                  ),
                  // const Spacer(),
                  if (_isLoading)
                    const SizedBox(
                        height: Values.v200,
                        child: Center(
                          child: CircularProgressIndicator(
                            color: UIColors.primary,
                          ),
                        )),
                  _buildCheckoutButtonSection(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  _buildProgramInfoCard() {
    return Container(
      margin: const EdgeInsets.only(top: Values.v20),
      padding: const EdgeInsets.symmetric(
          vertical: Values.v20, horizontal: Values.v16),
      decoration: BoxDecoration(
          color: UIColors.primaryGreen50,
          border: Border.all(color: UIColors.primaryGreen400),
          borderRadius: BorderRadius.circular(Values.v10)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProgramTitle(title: widget.programEntity.title),
            ],
          ),
          _buildProgramDescriptionSection(desc: widget.programEntity.desc),
          const SizedBox(
            height: Values.v20,
          ),
          _buildProgramInfoSection(),
        ],
      ),
    );
  }

  _buildProgramTitle({required String title}) {
    return Expanded(
      child: Text(
        title,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
      ),
    );
  }

  _buildProgramDescriptionSection({required String? desc}) {
    if (desc == null) {
      return const SizedBox.shrink();
    }

    return Text(
      desc,
      style: AppTypography.poppinsMedium14(color: AppColors.greyscale400),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  _buildProgramInfoSection() {
    List<Widget> supports = [
      _buildInfoText(
          text:
              '${widget.programEntity.durationCount} ${widget.programEntity.durationType.toCapitalFirst()}')
    ];

    for (String eachGuarantee in widget.programEntity.guarantees) {
      supports.add(_buildItemSeperator());
      supports.add(_buildInfoText(text: eachGuarantee));
    }

    return Wrap(
      children: supports,
    );
  }

  _buildItemSeperator() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 5),
      height: 15,
      width: 2,
      color: UIColors.primaryGreen400,
    );
  }

  _buildInfoText({required String text}) {
    return Text(
      text,
      style: AppTypography.poppinsMedium12(color: UIColors.primaryGreen950),
    );
  }

  _buildProgramPriceSection() {
    return Container(
      width: double.maxFinite,
      color: AppColors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Total: BDT ${widget.selectedPaymentType.discountedPrice}',
            style: AppTypography.poppinsBold24(
              color: (UIColors.primaryGreen950),
            ),
          ),
          Text(
            '${getPaymentTermEnumFromKey(widget.selectedPaymentType.paymentTerm!).name()} Payment',
            style: AppTypography.poppinsBold14(
              color: (UIColors.primary),
            ),
          ),
          Text('Discount applied automatically',
              style:
                  AppTypography.poppinsMedium14(color: AppColors.greyscale400)),
        ],
      ),
    );
  }

  _buildCheckoutButtonSection() {
    return Button.filled(
      label: 'Process To Checkout',
      onPressed: () {
        Log.debug('checkout button pressed');
        // _showAlert();
        //
        // return;
        setState(() {
          _isLoading = true;
        });

        BlocProvider.of<CoachProgramBloc>(context).add(
            SubscribeCoachProgramEvent(
                programId: widget.programEntity.programId!,
                coachId: widget.programEntity.coachId!));
        // _showProgramSubscriptionSUccessAlert();
      },
      // disable: true,
      borderColor: UIColors.navGrey,
    );
  }

  _showProgramSubscriptionSuccessAlert() {
    showDialog(
      context: context,
      builder: (context) {
        return const AlertDialog(
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(Values.v10))),
          contentPadding: EdgeInsets.all(1),
          // titlePadding: EdgeInsets.all(1),
          // actionsPadding: EdgeInsets.all(1),
          // buttonPadding: EdgeInsets.all(1),
          insetPadding: EdgeInsets.all(1),
          content: CoachProgramSubscriptionSuccessWidget(),
        );
      },
      barrierDismissible: false,
    );
  }

  _showFailedAlert() {
    showDialog(
      context: context,
      builder: (context) {
        return _buildFailedDialog();
      },
      barrierDismissible: true,
    );
  }

  Future<void> navigateToWebView(String url) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CoachBkashPaymentScreen(
          url: url,
        ),
      ),
    );

    if (result == "completed") {
      // showDialog(
      //   context: context,
      //   barrierDismissible: false,
      //   builder: (context) {
      //     return _buildSuccessDialog();
      //   },
      // );

      _navigateToCoachFeatureDashboard(context);

      _showProgramSubscriptionSuccessAlert();
    } else {
      // _showFailedAlert();
      _showPaymentFailedAlert();
    }
  }

  AlertDialog _buildSuccessDialog() {
    return AlertDialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      title: Column(
        children: [
          SizedBox(height: 24.h),
          Image.asset(
            Assets.successIcon,
            height: 40.h,
            width: 40.w,
          ),
          SizedBox(height: 16.h),
          Text(
            "Your order has been placed successfully!",
            textAlign: TextAlign.center,
            style: AppTypography.bold18(),
          ),
          SizedBox(height: 4.h),
          Text(
            "Thanks for shopping with Fitsomnia!",
            style: AppTypography.regular14(color: AppColors.grey),
          ),
          SizedBox(height: 48.h),
          Button(
            label: "Exit",
            onPressed: () {
              Navigator.of(context).pop();
            },
          )
        ],
      ),
    );
  }

  AlertDialog _buildFailedDialog() {
    return AlertDialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      title: Column(
        children: [
          SizedBox(height: 24.h),
          // Image.asset(
          //   Assets.successIcon,
          //   height: 40.h,
          //   width: 40.w,
          // ),
          SizedBox(height: 16.h),
          Text(
            "Payment Failed",
            textAlign: TextAlign.center,
            style: AppTypography.bold18(),
          ),
          SizedBox(height: 4.h),
          Text(
            "Try again later",
            style: AppTypography.regular14(color: AppColors.grey),
          ),
          SizedBox(height: 48.h),
          Button(
            label: "Exit",
            onPressed: () {
              Navigator.of(context).pop();
            },
          )
        ],
      ),
    );
  }

  _showAlert() {
    showDialog(
      context: context,
      builder: (context) {
        return CupertinoAlertDialog(
          title: Column(
            children: [
              Text(
                'Program Subscription',
                style: AppTypography.poppinsSemiBold16(
                    color: UIColors.primaryGreen900),
              ),
            ],
          ),
          content: Text(
            "Program subscription will be available soon",
            style: AppTypography.poppinsRegular14(
              color: AppColors.greyscale400,
            ),
          ),
          actions: [
            CupertinoDialogAction(
              child: const Text('Ok'),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        );
      },
    );
  }

  _showPaymentFailedAlert() {
    showDialog(
      context: context,
      builder: (context) {
        return CupertinoAlertDialog(
          title: Column(
            children: [
              Text(
                'Subscription Failed',
                style: AppTypography.poppinsSemiBold16(
                    color: UIColors.primaryGreen900),
              ),
            ],
          ),
          content: Text(
            "Try Again Later",
            style: AppTypography.poppinsRegular14(
              color: AppColors.greyscale400,
            ),
          ),
          actions: [
            CupertinoDialogAction(
              child: const Text('Exit'),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        );
      },
    );
  }

  void _navigateToCoachFeatureDashboard(BuildContext context) {
    // move to coach featue page
    // Navigator.of(context).popUntil((route) => route.isFirst);
    navigatorKey?.currentState?.pushAndRemoveUntil(
      MaterialPageRoute(
          builder: (_) => DashboardPage(
                selectedIndex: 2,
              )),
      (route) => false,
    );
    // context
    //     .read<DashboardCubit>()
    //     .changePage(2); // coach featue position is 2 in nav bar
  }

  _buildProgramReferralCodeSection() {
    // return CoachProgramReferralCodeWidget(
    //   referralCodeText: referralCodeText,
    // );

    return Row(
      children: [
        IconButton(
          onPressed: () {
            _showReferralCodeDialog();
          },
          icon: Text(
            'Use Referral code',
            style: TextStyle(
                fontSize: FontSize.s16,
                fontFamily: FontConstants.poppinsFontFamily,
                fontWeight: FontWeight.w500,
                color: UIColors.skyBlue500,
                decoration: TextDecoration.underline,
                decorationColor: UIColors.primary),
          ),
        ),
      ],
    );
  }

  void _sendCoachProgramReferralCodeToServer() {
    Log.debug('send program referralcode to server');
    if (referralCodeText.text != '') {
      String userId = BlocProvider.of<SharedBloc>(context).userProfile!.id;
      BlocProvider.of<ReferralBloc>(context).add(
          UseReferralCode(userId: userId, referralCode: referralCodeText.text));
    }
  }

  void _showReferralCodeDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Referral code',
                style: AppTypography.poppinsMedium20(color: UIColors.primary),
              ),
            ],
          ),
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(Values.v10))),
          contentPadding: const EdgeInsets.all(20),
          // titlePadding: EdgeInsets.all(1),
          // actionsPadding: EdgeInsets.all(1),
          // buttonPadding: EdgeInsets.all(1),
          insetPadding: const EdgeInsets.all(Values.v10),
          content: SingleChildScrollView(
            child: CoachProgramReferralCodeWidget(),
          ),
        );
      },
      barrierDismissible: true,
    );
  }
}

class CoachProgramReferralCodeWidget extends StatefulWidget {
  const CoachProgramReferralCodeWidget(
      {super.key});

  @override
  State<CoachProgramReferralCodeWidget> createState() =>
      _CoachProgramReferralCodeWidgetState();
}

class _CoachProgramReferralCodeWidgetState
    extends State<CoachProgramReferralCodeWidget> {
  TextEditingController referralCodeText = TextEditingController();
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();

    referralCodeText.addListener(() {
      if (referralCodeText.text == '') {
        setState(() {
          _errorMessage = '';
        });
      }
    });
  }

  @override
  void dispose(){
    referralCodeText.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ReferralBloc, ReferralState>(
      listener: (context, state) {
        if (state is UserReferralCodeFail) {
          setState(() {
            if (state.data is ErrorModel) {
              var errorModel = state.data as ErrorModel;

              if (errorModel.code == 'REFERRER_CODE_NOT_FOUND') {
                _errorMessage =
                    'Invalid referal code. Try correct referral code';
              } else {
                ///SELF_REFERRAL_NOT_ALLOWED
                _errorMessage = errorModel.message ?? 'Unknown Server Error';
              }

              // else if (errorModel.code == 'DUPLICATE_REFERRAL_USE') {
              //   AppToast.showToast(
              //     message: 'Code apply successfully',
              //     gravity: ToastGravity.BOTTOM,
              //     backgroundColor: UIColors.primary
              //   );

              //   Navigator.of(context).pop();
              // }
            }
          });
        }

        if (state is UserReferralCodeSuccess) {
          AppToast.showToast(
              message: 'Code apply successfully',
              gravity: ToastGravity.BOTTOM,
              backgroundColor: UIColors.primary);

          Navigator.of(context).pop();
        }
      },
      child: Container(
        child: Column(
          children: [
            _buildUseReferralCodeCheckbox(),
            _buildInputReferralCodeSection(),
            _buildErrorMessage(),
            _buildButtonSection()
          ],
        ),
      ),
    );
  }

  _buildUseReferralCodeCheckbox() {
    return SizedBox.shrink();
  }

  _buildInputReferralCodeSection() {
    return _buildEditTextInfo(
      name: 'Enter referral code',
      text: referralCodeText.text,
      controller: referralCodeText,
      keyboardType: TextInputType.text,
      validator: InputValidators.name,
    );
  }

  _buildEditTextInfo(
      {required String name,
      required String? text,
      required TextEditingController controller,
      required String? Function(String?)? validator,
      int maxLine = 1,
      TextInputType? keyboardType = null}) {
    if (text != null) controller.text = text;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "$name",
          style:
              AppTypography.poppinsRegular16(color: UIColors.primaryGreen950),
        ),
        SizedBox(
          height: Values.v5,
        ),
        TextFormField(
          cursorColor: AppColors.black,
          controller: controller,
          maxLines: maxLine,
          minLines: maxLine,
          autofocus: false,
          autocorrect: false,
          decoration: _buildInputDecoration(),
          validator: validator,
          keyboardType: keyboardType,
          autovalidateMode: AutovalidateMode.onUserInteraction,
        )
      ],
    );
  }

  InputDecoration _buildInputDecoration() {
    var inputBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(Values.v10),
      borderSide: BorderSide(color: AppColors.greyscale100, width: Values.v2),
    );

    return InputDecoration(
      enabledBorder: inputBorder,
      focusedBorder: inputBorder,
      hintText: 'Use referral code',
      hintStyle: AppTypography.regular18(
        color: AppColors.silver,
      ),
      contentPadding: EdgeInsets.all(Values.v16),
      // filled: true,
      // fillColor: AppColors.alto.withOpacity(0.2),
    );
  }

  Widget _buildButtonSection() {
    return Row(
      children: [
        _buildCancelButton(),
        _buildApplyButton(),
      ],
    );
  }

  Widget _buildCancelButton() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: ElevatedButton(
          onPressed: () {
            Log.debug('cancel button pressed');
            Navigator.of(context).pop();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: UIColors.white,
            elevation: 0,
            side: const BorderSide(
              color: UIColors.primary,
              width: 1,
            ),
            // fixedSize: Size(width, height),
            // backgroundColor: background ?? UIColors.primary,
            shape: RoundedRectangleBorder(
              side: BorderSide(width: 2.0),
              borderRadius: BorderRadius.circular(Values.v100.r),
            ),
          ),
          child: Container(
              height: Values.v40,
              child: Center(
                child: Text(
                  'Cancel',
                  style: AppTypography.poppinsMedium14(color: UIColors.primary),
                ),
              )),
        ),
      ),
    );
  }

  Widget _buildApplyButton() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: ElevatedButton(
          onPressed: () {
            Log.debug('apply button pressed');
            _sendCoachProgramReferralCodeToServer();
          },
          child: Container(
            height: Values.v40,
            child: Center(
              child: Text(
                'Apply',
                style: AppTypography.poppinsMedium16(color: UIColors.white),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _sendCoachProgramReferralCodeToServer() {
    Log.debug('send program referralcode to server');
    if (referralCodeText.text != '') {
      String userId = BlocProvider.of<SharedBloc>(context).userProfile!.id;
      BlocProvider.of<ReferralBloc>(context).add(UseReferralCode(
          userId: userId, referralCode: referralCodeText.text));
    }
  }

  _buildErrorMessage() {
    if (_errorMessage == '') return SizedBox.shrink();

    return Align(
      alignment: Alignment.centerLeft,
      child: Wrap(
        children: [
          Text(
            _errorMessage,
            style: AppTypography.poppinsRegular12(color: UIColors.red600),
          ),
        ],
      ),
    );
  }
}
