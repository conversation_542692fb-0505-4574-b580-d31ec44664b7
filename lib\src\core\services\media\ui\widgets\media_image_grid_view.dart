import 'package:fitsomnia_app/src/core/services/media/model/media_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';

import 'media_widget.dart';

class MediaImageGridView extends StatelessWidget {
  final List<AssetEntity> mediaFiles;
  const MediaImageGridView({Key? key, required this.mediaFiles})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
        itemCount: mediaFiles.length,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
        ),
        itemBuilder: (BuildContext context, int index) {
          return _loadMediaWidget(index);
        });
  }

  Widget _loadMediaWidget(int index) {
    return FutureBuilder(
      future: mediaFiles[index]
          .thumbnailDataWithSize(const ThumbnailSize(200, 200)),
      builder: (BuildContext context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return MediaItemWidget(
            mediaModel: MediaModel(
              snapshot.data as Uint8List,
              mediaFiles[index].type,
              mediaFiles[index].file,
            ),
            mediaFiles: mediaFiles,
            selectedIndex: index,
          );
        }

        return Container();
      },
    );
  }
}
