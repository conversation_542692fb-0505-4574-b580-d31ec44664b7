import 'dart:io';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/image_upload/image_upload_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SendAttachment extends StatelessWidget {
  const SendAttachment({
    Key? key,
    required this.imagePath,
  }) : super(key: key);

  final String imagePath;

  @override
  Widget build(BuildContext context) {
    return BlocListener<ImageUploadBloc, ImageUploadState>(
      listener: (context, state) {
        if (state is ImageUploadSuccess) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: AppColors.white,
          elevation: Values.v0,
          leading: GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Icon(
              Icons.close,
              color: AppColors.greyDark,
            ),
          ),
          title: const Text(
            "Send Attachment",
            style: TextStyle(
              color: AppColors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
        ),
        backgroundColor: AppColors.white,
        body: Padding(
          padding: EdgeInsets.all(16.w),
          child: Center(
            child: InteractiveViewer(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8.r),
                child: Image.file(
                  File(imagePath),
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            BlocProvider.of<ImageUploadBloc>(context).add(
              ImageUploadEvent(
                filePath: imagePath,
                file: File(imagePath),
              ),
            );
          },
          backgroundColor: UIColors.primary,
          child: BlocBuilder<ImageUploadBloc, ImageUploadState>(
            builder: (context, state) {
              if (state is ImageUploadLoading) {
                return const CircularProgressIndicator(
                  color: UIColors.white,
                );
              }

              return const Icon(
                Icons.send,
                color: UIColors.white,
              );
            },
          ),
        ),
      ),
    );
  }
}
