part of 'coach_program_bloc.dart';

sealed class Coach<PERSON><PERSON>ramState extends Equatable {
  const CoachProgramState();

  @override
  List<Object> get props => [];
}

final class CoachProgramInitial extends CoachProgramState {}

final class CoachProgramLoading extends Coach<PERSON>rogramState {}

class Coach<PERSON>rogramAddSuccess extends Coach<PERSON>rogramState {
  final CoachProgramEntity programEntity;

  const CoachProgramAddSuccess({required this.programEntity});

  @override
  List<Object> get props => [programEntity];
}

class CoachProgramAddFail<T> extends CoachProgramState {
  final T? data;

  const CoachProgramAddFail({required this.data});
}

class GetCoachOwnProgramByIdSuccess extends Coach<PERSON><PERSON>ramState {
  final CoachProgramEntity programEntity;

  const GetCoachOwnProgramByIdSuccess({required this.programEntity});

  @override
  List<Object> get props => [programEntity];
}

class GetCoachOwnProgramByIdFail<T> extends Coach<PERSON><PERSON>ramState {
  final T? data;

  const GetCoachOwnProgramByIdFail({required this.data});
}

class UpdateCoachProgramSuccess extends Coach<PERSON>rogramState {
  final CoachProgramEntity programEntity;

  const UpdateCoachProgramSuccess({required this.programEntity});

  @override
  List<Object> get props => [programEntity];
}

class UpdateCoachProgramFail<T> extends CoachProgramState {
  final T? data;

  const UpdateCoachProgramFail({required this.data});
}

class DeleteCoachProgramSuccess extends CoachProgramState {
  final CoachProgramEntity programEntity;

  const DeleteCoachProgramSuccess({required this.programEntity});

  @override
  List<Object> get props => [programEntity];
}

class DeleteCoachProgramFail<T> extends CoachProgramState {
  final T? data;

  const DeleteCoachProgramFail({required this.data});
}

class SubscribeCoachProgramSuccess extends CoachProgramState {
  final CoachProgramEnrollmentEntity enrollmentEntity;
  const SubscribeCoachProgramSuccess({required this.enrollmentEntity});

  @override
  List<Object> get props => [enrollmentEntity];
}

class SubscribeCoachProgramFail<T> extends CoachProgramState {
  final T? data;

  const SubscribeCoachProgramFail({required this.data});
}

class GetPaymentGetwayUrlSuccess extends CoachProgramState {
  final ProgramSubscriptionFeePaymentInfo subscriptionFeePaymentInfo;
  const GetPaymentGetwayUrlSuccess({required this.subscriptionFeePaymentInfo});

  @override
  List<Object> get props => [subscriptionFeePaymentInfo];
}

class GetPaymentGetwayUrlFail<T> extends CoachProgramState {
  final T? data;

  const GetPaymentGetwayUrlFail({required this.data});
}


