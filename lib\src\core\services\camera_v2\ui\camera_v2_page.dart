import 'dart:io';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart' as path_provider;

class CameraV2Page extends StatefulWidget {
  const CameraV2Page({
    super.key,
    this.onImageCaptured,
    this.maxRecordingTimeInSecond = 30,
    this.showMediaIcon = true,
    this.onlyRecord = false,
  });

  final Function(File)? onImageCaptured;
  final int maxRecordingTimeInSecond;
  final bool showMediaIcon;
  final bool onlyRecord;

  @override
  State<CameraV2Page> createState() => _CameraV2PageState();
}

class _CameraV2PageState extends State<CameraV2Page> {
  final ImagePicker _picker = ImagePicker();
  File? _image;

  Future<void> _takePicture() async {
    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        preferredCameraDevice: CameraDevice.rear,
        imageQuality: 80, // Adjust quality (0-100)
      );
      
      if (photo != null) {
        Log.debug('Image captured: ${photo.path}');

        setState(() {
          _image = File(photo.path);
        });
        
        if (widget.onImageCaptured != null) {
          widget.onImageCaptured!(_image!);
        }
      }
    } catch (e) {
      debugPrint('Error taking picture: $e');
    }
  }

  Future<void> _pickFromGallery() async {
    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );
      
      if (photo != null) {
        setState(() {
          _image = File(photo.path);
        });
        
        if (widget.onImageCaptured != null) {
          widget.onImageCaptured!(_image!);
        }
      }
    } catch (e) {
      debugPrint('Error picking image: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (_image != null)
              Expanded(
                child: Image.file(_image!),
              ),
            if (_image == null)
              Expanded(
                child: Center(
                  child: SizedBox(
                    height: 300,
                    width: 300,
                    child: Center(
                      child: Text(
                        'No image selected',
                        style: TextStyle(color: Colors.white, fontSize: 18),
                      ),
                    ),
                  ),
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  if (widget.showMediaIcon)
                    IconButton(
                      icon: const Icon(
                        Icons.photo_library,
                        color: Colors.white,
                        size: 32,
                      ),
                      onPressed: _pickFromGallery,
                    ),
                  IconButton(
                    icon: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 32,
                    ),
                    onPressed: _takePicture,
                  ),
                  if(_image != null)
                    IconButton(
                      icon: Text(
                        'Compress',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                    
                      onPressed: () {
                        _compressImage(_image!);
                      },
                    ),
                ],
              ),
            ),

          ],
        ),
      ),
    );
  }
  
  Future<void> _compressImage(File file) async {
    try {
      final dir = await path_provider.getTemporaryDirectory();
      final targetPath = "${dir.absolute.path}/compressed_${DateTime.now().millisecondsSinceEpoch}.jpg";

      final result = await FlutterImageCompress.compressWithList(
        await file.readAsBytes(),
        minWidth: 1024,
        minHeight: 1024,
        quality: 50,
        rotate: 0,
      );
      
      // Write the compressed data to a file
      final compressedFile = File(targetPath);
      await compressedFile.writeAsBytes(result);

      setState(() {
        _image = compressedFile;
      });

      if (widget.onImageCaptured != null) {
        widget.onImageCaptured!(_image!);
      }

      Log.debug('Compressed image saved to: ${compressedFile.path}');
      Log.debug('Original size: ${file.lengthSync() / (1024 * 1024)} MB');
      Log.debug('Compressed size: ${compressedFile.lengthSync() / (1024 * 1024)} MB');
    } catch (e) {
      Log.error('Error compressing image: $e');
    }
  }
}

// class CustomCameraPreviewFoodScanner extends StatefulWidget {
//   const CustomCameraPreviewFoodScanner(
//       {super.key, required this.showMediaIcon, required this.onlyRecord});

//   final bool showMediaIcon;
//   final bool onlyRecord;
//   @override
//   State<CustomCameraPreviewFoodScanner> createState() =>
//       _CustomCameraPreviewFoodScannerState();
// }

// class _CustomCameraPreviewFoodScannerState
//     extends State<CustomCameraPreviewFoodScanner> with WidgetsBindingObserver {
//   final CameraController cameraController =
//       AppCameraService.instance.selectNewCamera();
//   @override
//   void initState() {
//     super.initState();
//     WidgetsBinding.instance.addObserver(this);
//     context.read<CameraBloc>().add(ChangeCameraEvent(cameraController));
//   }

//   @override
//   void didChangeAppLifecycleState(AppLifecycleState state) {
//     if (!cameraController.value.isInitialized) {
//       return;
//     }

//     if (state == AppLifecycleState.inactive) {
//       cameraController.dispose();
//     } else if (state == AppLifecycleState.resumed) {
//       CameraController newController =
//           AppCameraService.instance.selectNewCamera();
//       if (mounted) {
//         context.read<CameraBloc>().add(ChangeCameraEvent(newController));
//       }
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: Colors.black,
//       body: SafeArea(
//         child: Stack(
//           children: [
//             _buildCameraPreview(),
//             const CameraHeaderWidget(),
//             CameraFooterWidget(
//               showMediaIcon: widget.showMediaIcon,
//               onlyRecord: widget.onlyRecord,
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildCameraPreview() {
//     return BlocBuilder<CameraBloc, CameraState>(
//       builder: (context, state) {
//         return state.cameraController == null
//             ? const SizedBox()
//             : FutureBuilder(
//                 future: state.cameraController!.initialize(),
//                 builder: (context, snapshot) {
//                   if (snapshot.connectionState == ConnectionState.done) {
//                     return Center(
//                       child: SizedBox(
//                         width: MediaQuery.of(context).size.width,
//                         height:MediaQuery.of(context).size.width,
//                         child: AspectRatio(
//                         aspectRatio:
//                             1 / state.cameraController!.value.aspectRatio,
//                         child: CameraPreview(state.cameraController!),
//                       ),
//                       ),
//                     );
//                   }

//                   return const SizedBox();
//                 },
//               );
//       },
//     );
//   }
// }
