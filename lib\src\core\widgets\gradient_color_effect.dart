import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:flutter/material.dart';

class GradientColorEffect {
  // static Widget primaryGradientColorEffect(Widget child) {
  //   return ShaderMask(
  //     blendMode: BlendMode.srcIn,
  //     shaderCallback: (Rect bounds) {
  //       return const SweepGradient(
  //               center: FractionalOffset.center,
  //               startAngle: 2 * (3.14 / 2),
  //               endAngle: 4 * (3.14 / 2),
  //               colors: [UIColors.primary, Colors.lightBlueAccent],
  //               // stops: [0.2, 1.0],
  //               tileMode: TileMode.mirror)
  //           .createShader(bounds);
  //     },
  //     child: child,
  //   );
  // }

  static Widget applyPrimaryGradientColor(Widget child) {
    return ShaderMask(
      blendMode: BlendMode.srcIn,
      shaderCallback: (Rect bounds) {
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF25D366), Color(0xFF45CCDE)],
          stops: [0.3, 0.6],
        ).createShader(bounds);
      },
      child: child,
    );
  }

  // static Widget applyGradientColor(Widget child, Color fromColor, Color toColor) {
  //   return ShaderMask(
  //     blendMode: BlendMode.srcIn,
  //     shaderCallback: (Rect bounds) {
  //       return LinearGradient(
  //         begin: Alignment.topLeft,
  //         end: Alignment.bottomRight,
  //         colors: [fromColor, toColor],
  //         stops: const [0.5, 1.0],
  //       ).createShader(bounds);
  //     },
  //     child: child,
  //   );
  // }

  // static Widget greenCyanBlueColor(Widget child) {
  //   return ShaderMask(
  //     blendMode: BlendMode.srcIn,
  //     shaderCallback: (Rect bounds) {
  //       return const LinearGradient(
  //         begin: Alignment.topLeft,
  //         end: Alignment.bottomRight,
  //         colors: [Colors.green, Colors.cyan, Colors.blue],
  //         stops: [0.0, 0.5, 1.0],
  //       ).createShader(bounds);
  //     },
  //     child: child,
  //   );
  // }
}
