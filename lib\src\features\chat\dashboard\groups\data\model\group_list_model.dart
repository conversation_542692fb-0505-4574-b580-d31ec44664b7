import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/entity/group_list_entity.dart';

class GroupListModel extends GroupListEntity {
  const GroupListModel({
    required super.groupId,
    required super.name,
    super.image,
  });

  factory GroupListModel.fromJson(Map<String, dynamic> json) => GroupListModel(
        groupId: json["groupId"],
        name: json["name"],
        image: json["image"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "groupId": groupId,
        "name": name,
        "image": image ?? "",
      };
}
