import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_income_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/bloc/coach_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CoachPaymentWithdrawWidget extends StatefulWidget {
  const CoachPaymentWithdrawWidget({super.key, required this.coachEntity});
  final CoachEntity coachEntity;

  @override
  State<CoachPaymentWithdrawWidget> createState() =>
      _CoachPaymentWithdrawWidgetState();
}

class _CoachPaymentWithdrawWidgetState
    extends State<CoachPaymentWithdrawWidget> {
  CoachIncomeEntity? coachIncomeEntity;
  bool _isLoading = false;

  @override
  void initState() {
    //TODO call total coach withdraw api
    super.initState();

    BlocProvider.of<CoachDashboardBloc>(context)
        .add(GetCoachIncomeEvent(coachId: widget.coachEntity.coachId!));
    _isLoading = true;
  }

  @override
  Widget build(BuildContext context) {
    return _buildPaymentWithdrawSection();
  }

  Widget _buildPaymentWithdrawSection() {
    return BlocListener<CoachDashboardBloc, CoachDashboardState>(
      listener: (context, state) {
        // TODO: implement listener
        if (state is GetCoachIncomeInfoSuccess) {
          setState(() {
            coachIncomeEntity = state.coachIncomeEntity;
            _isLoading = false;
          });
        }

        if (state is GetCoachIncomeInfoFail) {
          Log.debug('coach incom info failed');
          setState(() {
            _isLoading = false;
          });
        }
      },

      ///TODO: do not show withdraw when account balance is empty
      //|| coachIncomeEntity!.currentAccountBalance <= 0
      child: (_isLoading || coachIncomeEntity == null)
          ? const SizedBox.shrink()
          : Container(
              margin: const EdgeInsets.only(top: Values.v20),
              padding: const EdgeInsets.symmetric(
                  vertical: Values.v20, horizontal: Values.v16),
              decoration: BoxDecoration(
                  color: AppColors.greyscale10,
                  border: Border.all(color: AppColors.greyscale100),
                  borderRadius: BorderRadius.circular(Values.v10)),
              child: Column(
                children: [
                  // if (coachIncomeEntity!.isWithdrawRequestPending)
                  //   _buildWithdrawRequestPendingText(),
                  Row(
                    children: [
                      Flexible(
                        flex: 2,
                        child: _buildAvailableAmountSection(),
                      ),
                      SizedBox(width: 20),
                      Flexible(
                        flex: 2,
                        child: _buildWithdrawButton(),
                      ),
                    ],
                  ),
                  Divider(
                    color: AppColors.greyscale100,
                    indent: Values.v10,
                    endIndent: Values.v10,
                  ),
                  _buildPlatformFeeText(),
                ],
              ),
            ),
    );
  }

  _buildWithdrawButton() {
    bool enableWithdraw = (coachIncomeEntity!.withdrawableAmount > 0);

    return Button.filled(
      label: 'Withdraw',
      height: Values.v34,
      textStyle: AppTypography.poppinsSemiBold14(color: AppColors.white),
      background: UIColors.orange500,
      borderColor: enableWithdraw ? UIColors.orange500 : UIColors.navGrey,
      disable: !enableWithdraw,
      onPressed: () {
        Log.debug('withdraw payment pressed');

        if (coachIncomeEntity!.withdrawableAmount <
            coachIncomeEntity!.withdrawThreshold) {
          AppToast.showToast(
              message:
                  'Need to have at least ${coachIncomeEntity!.withdrawThreshold} BDT');

          return;
        }

        // _showAlert();

        // return;

        Navigator.of(context).pushNamed(Routes.coachWithdrawPaymentPage);
      },
    );
  }

  _buildAvailableAmountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Playment to withdraw',
          style: AppTypography.poppinsMedium12(color: AppColors.greyscale400),
        ),
        SizedBox(height: Values.v10),
        Text(
          'BDT ${coachIncomeEntity!.withdrawableAmount.toStringAsFixed(0)}',
          style:
              AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
        ),
      ],
    );
  }

  _buildPlatformFeeText() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Expanded(
            child: Text(
          textAlign: TextAlign.start,
          '*Platform Fee 30% will be deducted on withdrawal',
          style:
              AppTypography.poppinsRegular12(color: UIColors.primaryGreen600),
        )),
      ],
    );
  }

  // _buildWithdrawRequestPendingText() {
  //   return Container(
  //     margin: const EdgeInsets.only(bottom: Values.v10),
  //     padding: const EdgeInsets.symmetric(
  //         vertical: Values.v5, horizontal: Values.v5),
  //     decoration: BoxDecoration(
  //         color: UIColors.red50,
  //         border: Border.all(color: UIColors.red500),
  //         borderRadius: BorderRadius.circular(10)),
  //     child: Row(
  //       children: [
  //         Icon(Icons.info_outline_rounded),
  //         SizedBox(
  //           width: Values.v5,
  //         ),
  //         Expanded(
  //             child: Container(
  //           child: Text(
  //             'Previous Withdraw requst is being processed',
  //             style: AppTypography.poppinsRegular12(
  //                 color: UIColors.primaryGreen950),
  //             overflow: TextOverflow.ellipsis,
  //             maxLines: 3,
  //           ),
  //         )),
  //       ],
  //     ),
  //   );
  // }

  _showAlert() {
    showDialog(
      context: context,
      builder: (context) {
        return CupertinoAlertDialog(
          title: Column(
            children: [
              Text(
                'Withdraw',
                style: AppTypography.poppinsSemiBold16(
                    color: UIColors.primaryGreen900),
              ),
            ],
          ),
          content: Text(
            "Withdraw is not available in this version. Please Update your App",
            style: AppTypography.poppinsRegular14(
              color: AppColors.greyscale400,
            ),
          ),
          actions: [
            CupertinoDialogAction(
              child: const Text('Ok'),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        );
      },
    );
  }
}
