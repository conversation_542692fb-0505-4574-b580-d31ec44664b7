import 'dart:io';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/core/widgets/image_viewer.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/entity/chat_history_entity.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/chat_history/chat_history_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/delete_message/delete_message_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:page_transition/page_transition.dart';

import 'package:fitsomnia_app/src/core/extensions/extensions.dart';

class MessageBubble extends StatefulWidget {
  const MessageBubble(
      {required this.text,
      required this.isSentByMe,
      required this.data,
      this.image});

  final String text;
  final bool isSentByMe;
  final ChatHistoryEntity data;
  final String? image;

  @override
  State<MessageBubble> createState() => _MessageBubbleState();
}

class _MessageBubbleState extends State<MessageBubble> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 7.5.h),
      child: Row(
        mainAxisAlignment:
            widget.isSentByMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: widget.isSentByMe
                ? CrossAxisAlignment.end
                : CrossAxisAlignment.start,
            children: [
              widget.isSentByMe
                  ? Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        _buildFlexible(),
                        SizedBox(height: 5.h),
                        _buildDateStamp(widget.data.createdAt),
                      ],
                    )
                  : Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ImageContainer.circularImage(
                          image: widget.image ?? '',
                          radius: Values.v20,
                        ),
                        const SizedBox(width: 10),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            
                            _buildFlexible(),
                            SizedBox(height: 5.h),
                            _buildDateStamp(widget.data.createdAt),
                          ],
                        )
                      ],
                    ),
              // if (!widget.isSentByMe)
              //   ImageContainer.circularImage(
              //     image: widget.image,
              //     radius: Values.v20,
              //   ),
              // _buildFlexible(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFlexible() {
    return Flexible(
      child: GestureDetector(
        onLongPress: () {
          if (widget.isSentByMe) {
            _showDeleteAlert(context);
          }
        },
        child: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * Values.v0_75,
            ),
            decoration: BoxDecoration(
              color: widget.data.type == 'image'
                  ? Colors.transparent
                  : widget.isSentByMe
                      ? AppColors.black5
                      : AppColors.primaryGreen50,
              borderRadius: !widget.isSentByMe
                  ? const BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                      bottomLeft: Radius.circular(10),
                      bottomRight: Radius.circular(10),
                    )
                  : const BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                      bottomLeft: Radius.circular(10),
                      bottomRight: Radius.circular(10),
                    ),
            ),
            padding: widget.data.type == 'image'
                ? EdgeInsets.zero
                : EdgeInsets.all(10.w),
            child: widget.data.type == 'text'
                ? Text(
                    widget.text,
                    style: TextStyle(
                      color:
                          widget.isSentByMe ? AppColors.black : AppColors.black,
                    ),
                  )
                : widget.data.type == 'image'
                    ? GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            PageTransition(
                              type: PageTransitionType.scale,
                              alignment: Alignment.bottomCenter,
                              child: ImageViewer(
                                image: widget.text,
                              ),
                            ),
                          );
                        },
                        child: ImageContainer.rectangularImage(
                          image: widget.text,
                          height: 280.h,
                          width: 235.w,
                          fit: BoxFit.contain,
                          useMedium: true,
                        ),
                      )
                    : Image.file(
                        File(widget.text),
                        color: AppColors.softGrey,
                        height: 280.h,
                        width: 235.w,
                        fit: BoxFit.fitHeight,
                        errorBuilder: (BuildContext context, Object exception,
                            StackTrace? stackTrace) {
                          return Container(
                            height: 280.h,
                            width: 235.w,
                            color: AppColors.softGrey,
                          );
                        },
                      )),
      ),
    );
  }

  Widget _buildDateStamp(DateTime dateTime) {
    // var currentDate = DateTime.now();

    // if (today(currentDate, dateTime)) {
    //   var justTime = DateFormat('hh:mm a').format(dateTime);

    //   return Text(
    //     justTime,
    //     style: const TextStyle(
    //       fontSize: 10,
    //       color: AppColors.grey,
    //     ),
    //   );
    // }

    // var dateTimeFormattedString =
    //     DateFormat('yyyy-MM-dd hh:mm a').format(dateTime);

    return Text(
       dateTime.timeAgo(),
      style: const TextStyle(
        fontSize: 10,
        color: AppColors.grey,
      ),
    );
  }

  _showDeleteAlert(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return BlocListener<DeleteMessageBloc, DeleteMessageState>(
            listener: (context, state) {
              if (state is DeleteMessageSuccess) {
                BlocProvider.of<ChatHistoryBloc>(context).emit(
                  ChatHistoryInitial(),
                );
                Navigator.of(context).pop();
              }
            },
            child: CupertinoAlertDialog(
              title: Column(
                children: const [
                  Text("Alert"),
                ],
              ),
              content:
                  const Text("Are you sure you want to delete this message?"),
              actions: [
                CupertinoDialogAction(
                  child: const Text("Yes"),
                  onPressed: () {
                    BlocProvider.of<DeleteMessageBloc>(context).add(
                      DeleteOneToOneChatMessageEvent(messageId: widget.data.id),
                    );
                  },
                ),
                CupertinoDialogAction(
                  child: const Text("No"),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            ),
          );
        });
  }

  bool today(DateTime currentDate, DateTime dateTime) {
    return currentDate.day == dateTime.day &&
        currentDate.month == dateTime.month &&
        currentDate.year == dateTime.year;
  }
}
