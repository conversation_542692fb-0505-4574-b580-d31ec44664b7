class RewardPointHistoryEntity {
  final String userId;
  final String userName;
  final String userImage;
  final int totalPoints;
  final String pintType;
  final String title;
  final DateTime? createdAt;
  final DateTime? earnedAt;

  RewardPointHistoryEntity({required this.userId, required this.userName, required this.userImage, required this.totalPoints, required this.pintType, required this.title, required this.createdAt, required this.earnedAt});
  
}