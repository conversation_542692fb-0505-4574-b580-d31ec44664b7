import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/repository/club_repository.dart';

class JoinClubUseCase {
  const JoinClubUseCase({required this.clubRepository});

  final ClubRepository clubRepository;

  Future<Either<ErrorResponseModel, String>> call(String clubId) async {
    return clubRepository.joinClub(clubId);
  }

  Future<Either<ErrorResponseModel, String>> leaveAndJoinClub(
      String clubId) async {
    return clubRepository.leaveAndJoinClub(clubId);
  }
}
