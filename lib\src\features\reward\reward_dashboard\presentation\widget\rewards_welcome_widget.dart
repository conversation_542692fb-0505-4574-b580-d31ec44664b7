import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class RewardsWelcomeWidget extends StatelessWidget {
  const RewardsWelcomeWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            // decoration: BoxDecoration(border: Border.all(color: Colors.red)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  // iconSize: Values.v18,
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  icon: Icon(Icons.clear_sharp),
                )
              ],
            ),
          ),
          Container(
            // decoration: BoxDecoration(border: Border.all(color: Colors.red)),
            child: Text(
              'Welcome !',
              style: TextStyle(
                fontSize: FontSize.s64.sp,
                fontFamily: FontConstants.caveatFontFamily,
                fontWeight: FontWeight.w400,
                // height: 1.0
              ),
            ),
          ),
          Stack(
            alignment: Alignment.topCenter,
            children: [
              SvgPicture.asset(
                Assets.coachConsultBackgroundImg,
                height: Values.v324.h,
                width: Values.v375.w,
                fit: BoxFit.fill,
              ),
              Container(
                // decoration: BoxDecoration(image: DecorationImage(image: AssetImage(Assets.beginner), fit: BoxFit.cover)),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'To The First Bangladeshi Social Media',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: FontSize.s16.w,
                        fontFamily: FontConstants.poppinsFontFamily,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Text(
                      'App For Health & Fitness',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: FontSize.s16.w,
                        fontFamily: FontConstants.poppinsFontFamily,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    SizedBox(
                      height: Values.v215.h,
                      width: Values.v200.w,
                      child: SvgPicture.asset(Assets.completeProfileHi),
                    ),
                    Text(
                      'Complete challenges and earn upto',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: FontSize.s16.w,
                        fontFamily: FontConstants.poppinsFontFamily,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Text(
                      '2000 Taka!',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: FontSize.s32.w,
                        fontStyle: FontStyle.italic,
                        fontFamily: FontConstants.poppinsFontFamily,
                        fontWeight: FontWeight.w700,
                        color: UIColors.primaryGreen700
                      ),
                    ),
                    _buildViewRewardsButton(context),
                  ],
                ),
              ),
            ],
          ),
        ]
    );
  }

  _buildViewRewardsButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(Values.v20.w, 0, Values.v20.w, Values.v24.h),
      child: Button(
        background: UIColors.primaryGreen900,
        borderColor: UIColors.white,
        width: Values.v304.w,
        label: 'View Challenges',
        textStyle: const TextStyle(
          fontSize: FontSize.s16,
          fontFamily: FontConstants.poppinsFontFamily,
          fontWeight: FontWeight.w500,
          color: Colors.white,
        ),
        onPressed: () {
          Navigator.of(context).pop();
          Navigator.of(context).pushNamed(Routes.rewardDashboardPage);
        },
      ),
    );
  }
}