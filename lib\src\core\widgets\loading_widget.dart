import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LoadingIndicator extends StatelessWidget {
  const LoadingIndicator({
    Key? key,
    this.showReloadIndicator = false,
    this.onReload,
  }) : super(key: key);

  final bool showReloadIndicator;
  final Function()? onReload;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        height: 50,
        width: 50,
        padding: const EdgeInsets.all(10),
        decoration: const BoxDecoration(
          color: UIColors.primary,
          borderRadius: BorderRadius.all(Radius.circular(10)),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 5,
              spreadRadius: 1,
            ),
          ],
        ),
        child: showReloadIndicator
            ? InkWell(
                onTap: onReload,
                child: const Icon(
                  Icons.refresh,
                  color: UIColors.white,
                ),
              )
            : const Center(
                child: CircularProgressIndicator.adaptive(
                  backgroundColor: UIColors.white,
                ),
              ),
      ),
    );
  }
}

/// Will be deprecated soon
class LoadingWidget extends StatelessWidget {
  const LoadingWidget({
    Key? key,
    this.message,
    this.textColor,
    this.loaderOnly = false,
    this.loaderColor,
    this.scale,
  }) : super(key: key);

  final String? message;
  final Color? textColor;
  final Color? loaderColor;
  final bool loaderOnly;
  final double? scale;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Transform.scale(
            scale: scale ?? Values.v1,
            child: CircularProgressIndicator.adaptive(
              backgroundColor: loaderColor ?? UIColors.primary,
            ),
          ),
          SizedBox(width: Values.v10.w),
          loaderOnly
              ? const SizedBox()
              : Text(
                  message ?? TextConstants.loadingMessage,
                  style: AppTypography.regular14(
                    color: textColor ?? UIColors.black50,
                  ),
                ),
        ],
      ),
    );
  }
}
