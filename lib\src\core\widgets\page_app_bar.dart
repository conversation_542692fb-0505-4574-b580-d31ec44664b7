import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:flutter/material.dart';

class PageAppBar extends StatelessWidget {
  const PageAppBar({super.key, required this.titleText});
  final String titleText;

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(100),
      child: _buildAppBar(),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        'Coaching Program',
        style: TextStyle(
          color: UIColors.primaryGreen950,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: true,
      elevation: 0,
    );
  }
}
