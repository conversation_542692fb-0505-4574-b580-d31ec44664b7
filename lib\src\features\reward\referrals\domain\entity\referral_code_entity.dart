class ReferralCodeEntity {
  final String referralCode;
  final String referralId;
  final String? shareUrl;

  ReferralCodeEntity({
    required this.referralCode,
    required this.referralId,
    this.shareUrl,
  });

  factory ReferralCodeEntity.fromJson(Map<String, dynamic> json) {
    return ReferralCodeEntity(
        referralCode: json['code'],
        referralId: json['campaignId'],
        shareUrl: json['shareUrl']);
  }
}
