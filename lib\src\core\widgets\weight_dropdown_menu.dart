import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/presentation/widgets/custom_dropdown_menu.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/bench_squat_unit/bench_squat_unit_cubit.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BenchSquatUnitDropdownMenu extends StatefulWidget {
  BenchSquatUnitDropdownMenu({
    super.key,
    required this.initialValue,
    required this.controller,
  });
  final TextEditingController controller;
  final String initialValue;

  final List<MenuItem> weightUnits = [
    MenuItem(name: "lb", label: "lb"),
    MenuItem(name: "kg", label: "kg"),
  ];

  @override
  State<BenchSquatUnitDropdownMenu> createState() => _BenchSquatUnitDropdownMenuState();
}

class _BenchSquatUnitDropdownMenuState extends State<BenchSquatUnitDropdownMenu> {

  void _onChange(BuildContext context, String unitType) {
    Log.debug("Change unit to: $unitType");
    // context.read<BenchSquatUnitCubit>().update(unitType);
  }
  @override
  Widget build(BuildContext context) {
    CustomDropdownMenu weightSelectionMenu = CustomDropdownMenu(
        items: weightUnits,
        menuWidth: 80.0,
        controller: widget.controller,
        initialSelectionValue: widget.initialValue,
        callback: _onChange);

    return weightSelectionMenu;
  }
}
