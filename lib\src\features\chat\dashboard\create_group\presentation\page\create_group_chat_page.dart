import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/presentation/bloc/all_users_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/presentation/bloc/create_group_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/presentation/model/user_view_model.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/pages/group_chat_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CreateGroup extends StatefulWidget {
  const CreateGroup({Key? key}) : super(key: key);

  @override
  State<CreateGroup> createState() => _CreateGroupState();
}

class _CreateGroupState extends State<CreateGroup> {
  final TextEditingController searchController = TextEditingController();
  final TextEditingController groupNameController = TextEditingController();

  final _formKey = GlobalKey<FormState>();

  List<UserViewModel> selectedUsers = [];

  @override
  void initState() {
    super.initState();
    BlocProvider.of<AllUsersBloc>(context).add(
      const GetAllUsersEvent(name: ""),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CreateGroupBloc, CreateGroupState>(
      listener: (context, state) {
        if (state is CreateGroupLoading) {
          AppToast.showToast(message: "Loading");
        } else if (state is CreateGroupSuccess) {
          _showToastAndNavigateToGroupChatPage(state);
        } else if (state is CreateGroupFailure) {
          AppToast.showToast(message: "Failure");
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.white,
        appBar: _buildAppBar(context),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildGroupNameField(),
            const SizedBox(height: 16),
            _buildSearchBar(),
            Visibility(
              visible: selectedUsers.isNotEmpty,
              child: const SizedBox(height: 16),
            ),
            Visibility(
              visible: selectedUsers.isNotEmpty,
              child: _buildSelectedPeopleList(),
            ),
            const SizedBox(height: 16),
            _buildPeopleList()
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text(
        "New Group",
        style: TextStyle(
          color: AppColors.black,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevation: 0,
      iconTheme: const IconThemeData(color: AppColors.black),
      backgroundColor: AppColors.white,
      actions: [
        TextButton(
          onPressed: () => _navigateToGroupChatPage(context),
          child: Text(
            "Create",
            style: TextStyle(
              fontSize: Values.v14,
              color: AppColors.apple,
            ),
          ),
        )
      ],
    );
  }

  Widget _buildGroupNameField() {
    return Container(
      height: Values.v48,
      padding: const EdgeInsets.symmetric(horizontal: 10),
       margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.white60,
        borderRadius: BorderRadius.circular(Values.v10),
        border: Border.all(
          color: AppColors.softGrey, // Set the green border color
          width: 2, // Set the width of the border (adjust as needed)
        ),
      ),
      child: Form(
        key: _formKey,
        child: TextFormField(
          controller: groupNameController,
          decoration: InputDecoration(
            hintText: "Create Group name",
            border: InputBorder.none,
            // prefixIcon: Icon(
            //   Icons.group,
            //   color: AppColors.apple,
            // ),
            errorStyle: const TextStyle(height: 0.0),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return "";
            }

            return null;
          },
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      height: Values.v48,
      padding: const EdgeInsets.symmetric(horizontal: 10),
      margin: const EdgeInsets.symmetric(horizontal: 40),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(Values.v30),
        border: Border.all(
          color: AppColors.softGrey, // Set the green border color
          width: 2, // Set the width of the border (adjust as needed)
        ),
      ),
      child: 
      TextFormField(
        controller: searchController,
        onChanged: (value) {
          BlocProvider.of<AllUsersBloc>(context).add(
            SearchUsersEvent(name: searchController.text),
          );
        },
        decoration: InputDecoration(
          hintText: "Search for people ",
          border: InputBorder.none,
          prefixIcon: GestureDetector(
            onTap: () => BlocProvider.of<AllUsersBloc>(context).add(
              SearchUsersEvent(name: searchController.text),
            ),
            child: Icon(
              Icons.search,
              color: AppColors.apple,
            ),
            // child: Text(''),
          ),
        ),
      ),
    );
  }

  Widget _buildPeopleList() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "Users",
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: AppColors.black54,
              ),
            ),
            const SizedBox(height: 16),
            BlocBuilder<AllUsersBloc, BaseState>(
              builder: (context, state) {
                if (state is SuccessState) {
                  return context.read<AllUsersBloc>().displayedUsers.isNotEmpty
                      ? dataList()
                      : errorOrNoDataView(
                          text: "No users found!",
                        );
                } else if (state is ErrorState) {
                  return errorOrNoDataView(text: state.data);
                } else if (state is LoadingState) {
                  return errorOrNoDataView(
                    text: "Please wait...",
                    isLoadingWidget: true,
                  );
                }

                return errorOrNoDataView(
                  text: "Something went wrong. Please try again later...",
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedPeopleList() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "Selected Users",
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: AppColors.black54,
            ),
          ),
          const SizedBox(height: 16),
          selectedDataList(),
        ],
      ),
    );
  }

  Widget dataList() {
    return Expanded(
      flex: 1,
      child: ListView.builder(
        itemCount: context.read<AllUsersBloc>().displayedUsers.length,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return _buildUserCard(
            context,
            context.read<AllUsersBloc>().displayedUsers[index],
            index,
          );
        },
      ),
    );
  }

  Widget selectedDataList() {
    return SizedBox(
      height: 110.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: selectedUsers.length,
        itemBuilder: (context, index) {
          return _buildUserGridCard(
            context,
            selectedUsers[index],
            index,
          );
        },
      ),
    );
  }

  Widget errorOrNoDataView({
    required String text,
    bool isLoadingWidget = false,
  }) {
    return Column(
      children: [
        isLoadingWidget
            ? const Center(
                child: CircularProgressIndicator(
                  color: Colors.green,
                ),
              )
            : Text(text),
      ],
    );
  }

  Widget _buildUserCard(
    BuildContext context,
    UserViewModel userViewModel,
    int index,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: InkWell(
              onTap: () {
                _addUserToTheSelected(index);
              },
              child: Row(
                children: [
                  _buildAvatar(index),
                  const SizedBox(width: 16),
                  _buildUserName(index),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserGridCard(
    BuildContext context,
    UserViewModel userViewModel,
    int index,
  ) {
    return SizedBox(
      width: 80.w,
      child: InkWell(
        onTap: () {
          _removeUserFromSelected(index);
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(50.r),
                border: Border.all(width: 2),
              ),
              child: ImageContainer.circularImage(
                image: userViewModel.image,
                radius: Values.v26,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              userViewModel.name,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar(int index) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        ImageContainer.circularImage(
          image: context.read<AllUsersBloc>().displayedUsers[index].image,
          radius: Values.v26,
        ),
      ],
    );
  }

  Widget _buildUserName(int index) {
    return Expanded(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: AppColors.black10,
            ),
          ),
        ),
        child: Row(
          children: [
            Text(
              context.read<AllUsersBloc>().displayedUsers[index].name,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 18,
                color: AppColors.black,
              ),
            ),
            const Spacer(),
            context.read<AllUsersBloc>().displayedUsers[index].isSelected
                ? Icon(
                    Icons.check_circle_rounded,
                    color: AppColors.primaryGreen,
                  )
                : const Icon(
                    Icons.crop_square,
                    color: AppColors.black54,
                  )
          ],
        ),
      ),
    );
  }

  void _navigateToGroupChatPage(BuildContext context) {
    if (_formKey.currentState!.validate() && selectedUsers.length > 1) {
      List<String> selectedUserIds = [];
      for (var element in selectedUsers) {
        selectedUserIds.add(element.id);
      }

      BlocProvider.of<CreateGroupBloc>(context).add(
        CreateGroupChatEvent(
          groupName: groupNameController.text,
          listOfUserId: selectedUserIds,
        ),
      );
    } else {
      if (!_formKey.currentState!.validate()) {
        AppToast.showToast(
          message: "Please name the group.",
          backgroundColor: AppColors.red,
          textColor: AppColors.white,
          gravity: ToastGravity.BOTTOM,
        );
      } else {
        AppToast.showToast(
          message: "Please add more people to the group.",
          backgroundColor: AppColors.red,
          textColor: AppColors.white,
          gravity: ToastGravity.BOTTOM,
        );
      }
    }
  }

  void _showToastAndNavigateToGroupChatPage(CreateGroupSuccess state) {
    AppToast.showToast(
      message: "Group Created",
      backgroundColor: AppColors.primaryGreen,
      textColor: AppColors.white,
      gravity: ToastGravity.BOTTOM,
    );
    selectedUsers.clear();
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => GroupChatPage(
          groupId: state.groupResponseEntity.id,
          groupName: state.groupResponseEntity.name,
          groupImage: state.groupResponseEntity.image ?? "",
        ),
      ),
    );
  }

  void _removeUserFromSelected(int index) {
    selectedUsers[index].isSelected = false;
    context.read<AllUsersBloc>().allUsers.insert(0, selectedUsers[index]);

    setState(() {
      selectedUsers.removeAt(index);
    });
  }

  void _addUserToTheSelected(int index) {
    final allUsersBloc = context.read<AllUsersBloc>();
    final displayedUser = allUsersBloc.displayedUsers[index];
    if (!displayedUser.isSelected) {
      setState(() {
        displayedUser.isSelected = true;
      });
      selectedUsers.add(displayedUser);
      allUsersBloc.allUsers.removeWhere((element) => element.id == displayedUser.id);
      allUsersBloc.displayedUsers.removeAt(index);
    }
  }
}
