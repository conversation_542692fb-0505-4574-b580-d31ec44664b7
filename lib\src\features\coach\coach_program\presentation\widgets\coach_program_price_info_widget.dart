import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/validators/input_validators.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/list_multi_selection_widget.dart';
import 'package:fitsomnia_app/src/core/widgets/list_single_selection_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/model/coach_program_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/pages/create_coach_program_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/widgets/add_discount_widget.dart';
import 'package:flutter/material.dart';

class CoachProgramPriceInfoWidget extends StatefulWidget {
  const CoachProgramPriceInfoWidget({
    Key? key,
    this.programEntity,
  }) : super(key: key);

  final CoachProgramEntity? programEntity;

  @override
  State<CoachProgramPriceInfoWidget> createState() =>
      CoachProgramPriceInfoWidgetState();
}

class CoachProgramPriceInfoWidgetState
    extends State<CoachProgramPriceInfoWidget>
    with AutomaticKeepAliveClientMixin<CoachProgramPriceInfoWidget> {
  List<ChoiceOption> choiceOptions = [];
  bool isOneTimePaymentOptionSelected = false;
  // bool isDailyPaymentOptionSelected = false;
  bool isMonthlyPaymentOptionSelected = false;
  // bool isWeeklyPaymentOptionSelected = false;

  @override
  void initState() {
    super.initState();
    choiceOptions = avialablePaymentTerms.map((paymentTerm) {
      return ChoiceOption(name: paymentTerm.key(), label: paymentTerm.name());
    }).toList();

    _setPreviousPriceValue();
  }

  _setPreviousPriceValue() {
    if (widget.programEntity != null) {
      onetimePriceNotifier.value = widget.programEntity!.oneTimePrice;
      monthlyPriceNotifier.value = widget.programEntity!.monthlyPrice;

      if (onetimePriceNotifier.value != null) {
        isOneTimePaymentOptionSelected = true;
        for (ChoiceOption option in choiceOptions) {
          if (option.name ==
              CoachProgramSubscriptionPaymentTerm.ONE_TIME.key()) {
            selectedOptions.add(option);
            break;
          }
        }
      }

      if (monthlyPriceNotifier.value != null) {
        isMonthlyPaymentOptionSelected = true;
        for (ChoiceOption option in choiceOptions) {
          if (option.name ==
              CoachProgramSubscriptionPaymentTerm.MONTHLY.key()) {
            selectedOptions.add(option);
            break;
          }
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPageHeader(),
          _buildPaymentTermsSecrion(),
        ],
      ),
    );
  }

  _buildPageHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 10,
        ),
        _completeCoachProfileTitle(),
        Text(
          'Provide pricing information including monthly or one time price',
          style: AppTypography.poppinsMedium14(color: AppColors.greyscale400),
        ),
        const SizedBox(
          height: 24,
        ),
      ],
    );
  }

  Widget _completeCoachProfileTitle() {
    return RichText(
      text: TextSpan(
        style: AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
        children: [
          const TextSpan(text: 'Add your program pricing'),
        ],
      ),
    );
  }

  _buildPaymentTermsSecrion() {
    return Form(
      key: programPriceFormValidationKey,
      child: Column(
        children: [
          _buildSupportedPaymentOptionsSection(),
          if (isOneTimePaymentOptionSelected) _buildOneTimePriceSection(),
          // if (isDailyPaymentOptionSelected) _buildDailyTimePriceSecrion(),
          // if (isWeeklyPaymentOptionSelected) _buildWeeklyTimePriceSecrion(),
          if (isMonthlyPaymentOptionSelected) _buildMonthlyTimePriceSecrion(),
        ],
      ),
    );
  }

  List<ChoiceOption> selectedOptions = [];
  _buildSupportedPaymentOptionsSection() {
    // Log.debug('price validity: ${previousSelectedOption?.name}');

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Price Validity",
          style:
              AppTypography.poppinsRegular16(color: UIColors.primaryGreen950),
        ),
        const SizedBox(
          height: Values.v5,
        ),
        ListMultiSelectionWidget(
          key: UniqueKey(),
          options: choiceOptions,
          preSelectedOptions: selectedOptions,
          onSelectCallback: (List<ChoiceOption> options) {
            Log.debug('selected option: ${options.length}');
            setState(() {
              _onUpdatePaymentTerms(options: options);
            });
          },
        ),
      ],
    );
  }

  _onUpdatePaymentTerms({required List<ChoiceOption> options}) {
    selectedOptions = options;
    isOneTimePaymentOptionSelected = false;
    // isDailyPaymentOptionSelected = false;
    // isWeeklyPaymentOptionSelected = false;
    isMonthlyPaymentOptionSelected = false;

    for (ChoiceOption option in options) {
      if (option.name == CoachProgramSubscriptionPaymentTerm.ONE_TIME.key()) {
        isOneTimePaymentOptionSelected = true;
      }
      // else if (option.name ==
      //     CoachProgramSubscriptionPaymentTerm.DAILY.key()) {
      //   isDailyPaymentOptionSelected = true;
      // }
      // else if (option.name ==
      //     CoachProgramSubscriptionPaymentTerm.WEEKLY.key()) {
      //   isWeeklyPaymentOptionSelected = true;
      // }
      else if (option.name ==
          CoachProgramSubscriptionPaymentTerm.MONTHLY.key()) {
        isMonthlyPaymentOptionSelected = true;
      }
    }

    if (isOneTimePaymentOptionSelected == false) {
      onetimePriceNotifier.value = null;
    }
    // if (isDailyPaymentOptionSelected == false) {
    //   dailyPriceNotifier.value = null;
    // }
    // if (isWeeklyPaymentOptionSelected == false) {
    //   weeklyPriceNotifier.value = null;
    // }
    if (isMonthlyPaymentOptionSelected == false) {
      monthlyPriceNotifier.value = null;
    }
  }

  ValueNotifier<CoachProgramPaymentInfo?> onetimePriceNotifier =
      ValueNotifier(null);
  // ValueNotifier<CoachProgramPaymentInfo?> dailyPriceNotifier =
  //     ValueNotifier(null);
  // ValueNotifier<CoachProgramPaymentInfo?> weeklyPriceNotifier =
  //     ValueNotifier(null);
  ValueNotifier<CoachProgramPaymentInfo?> monthlyPriceNotifier =
      ValueNotifier(null);

  _buildOneTimePriceSection() {
    return CoachProgramSetPriceWidget(
      paymentType: CoachProgramSubscriptionPaymentTerm.ONE_TIME,
      previousPriceInfo: onetimePriceNotifier.value,
      valueNotifier: onetimePriceNotifier,
    );
  }

  // _buildDailyTimePriceSecrion() {
  //   return CoachProgramSetPriceWidget(
  //     paymentType: CoachProgramSubscriptionPaymentTerm.DAILY,
  //     valueNotifier: dailyPriceNotifier,
  //   );
  // }

  // _buildWeeklyTimePriceSecrion() {
  //   return CoachProgramSetPriceWidget(
  //     paymentType: CoachProgramSubscriptionPaymentTerm.WEEKLY,
  //     valueNotifier: weeklyPriceNotifier,
  //   );
  // }

  _buildMonthlyTimePriceSecrion() {
    return CoachProgramSetPriceWidget(
      paymentType: CoachProgramSubscriptionPaymentTerm.MONTHLY,
      previousPriceInfo: monthlyPriceNotifier.value,
      valueNotifier: monthlyPriceNotifier,
    );
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}

class CoachProgramSetPriceWidget extends StatefulWidget {
  const CoachProgramSetPriceWidget({
    super.key,
    required this.paymentType,
    required this.valueNotifier,
    this.previousPriceInfo,
  });
  final CoachProgramSubscriptionPaymentTerm paymentType;

  final ValueNotifier<CoachProgramPaymentInfo?> valueNotifier;

  final CoachProgramPaymentInfo? previousPriceInfo;

  @override
  State<CoachProgramSetPriceWidget> createState() =>
      _CoachProgramSetPriceWidgetState();
}

class _CoachProgramSetPriceWidgetState
    extends State<CoachProgramSetPriceWidget> {
  TextEditingController discountedPriceController =
      TextEditingController(text: '');
  TextEditingController actualPriceController =
      TextEditingController(text: '');

  @override
  void initState() {
    super.initState();

    if (widget.previousPriceInfo != null) {
      if (widget.previousPriceInfo!.actualPrice != null) {
        actualPriceController.text =
            widget.previousPriceInfo!.actualPrice.toString();
      }

      if (widget.previousPriceInfo!.discountedPrice != null) {
        discountedPriceController.text =
            widget.previousPriceInfo!.discountedPrice.toString();
      }
    }

    widget.valueNotifier.value = widget.previousPriceInfo;

    // if discounted price is not provides use actual price as discounted price
    discountedPriceController.addListener(() {
      widget.valueNotifier.value = CoachProgramPaymentInfo(
        paymentTerm: widget.paymentType.key(),
        actualPrice: double.tryParse(actualPriceController.text) ??
            double.tryParse(discountedPriceController.text),
        discountedPrice: double.tryParse(discountedPriceController.text)
      );
    });

    actualPriceController.addListener(() {
      widget.valueNotifier.value = CoachProgramPaymentInfo(
        paymentTerm: widget.paymentType.key(),
        actualPrice: double.tryParse(actualPriceController.text),
        discountedPrice: double.tryParse(discountedPriceController.text),
      );
    });
  }

  @override
  void dispose() {
    discountedPriceController.dispose();
    actualPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildProgramPriceSection(),
        // const SizedBox(
        //   height: 1,
        // ),
        _buildAddDiscountSection(),
        const SizedBox(
          height: 20,
        ),
      ],
    );
  }

  String prevPrice = '';
  _buildProgramPriceSection() {
    return _buildEditTextInfo(
      name: 'Set ${widget.paymentType.name()} Price (BDT)',
      text: discountedPriceController.text,
      controller: discountedPriceController,
      validator: InputValidators.name,
      keyboardType: TextInputType.number,
    );
  }

  _buildAddDiscountSection() {
    return AddDiscountWidget(
        key: UniqueKey(),
        priceStr: actualPriceController.text,
        callback: (int? price) {
          Log.debug('discount price: $price');
          actualPriceController.text = price.toString();
        });
  }

  _buildEditTextInfo(
      {required String name,
      required String? text,
      required TextEditingController controller,
      required String? Function(String?)? validator,
      int maxLine = 1,
      TextInputType? keyboardType = null}) {
    if (text != null) controller.text = text;

    // return TextFormField(
    //     cursorColor: AppColors.black,
    //     controller: controller,
    //     maxLines: 5,
    //     minLines: 5,
    //     autofocus: true,
    //     autocorrect: false,
    //     decoration: _buildInputDecoration(),

    //   );

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "$name",
          style:
              AppTypography.poppinsRegular16(color: UIColors.primaryGreen950),
        ),
        SizedBox(
          height: Values.v5,
        ),
        TextFormField(
          cursorColor: AppColors.black,
          controller: controller,
          maxLines: maxLine,
          minLines: maxLine,
          autofocus: false,
          autocorrect: false,
          decoration: _buildInputDecoration(),
          validator: validator,
          keyboardType: keyboardType,
          autovalidateMode: AutovalidateMode.onUserInteraction,
        )
      ],
    );
  }

  InputDecoration _buildInputDecoration() {
    var inputBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(Values.v10),
      borderSide: BorderSide(color: AppColors.greyscale50, width: Values.v2),
    );

    return InputDecoration(
      enabledBorder: inputBorder,
      focusedBorder: inputBorder,
      // hintText: 'Write a short bio?',
      hintStyle: AppTypography.regular18(
        color: AppColors.silver,
      ),
      contentPadding: EdgeInsets.all(Values.v16),
      // filled: true,
      // fillColor: AppColors.alto.withOpacity(0.2),
    );
  }
}
