class InputValidators {
  static final _userNameRegX = RegExp(r'^[A-Za-z][A-Za-z0-9_]{7,29}$');
  static final _emailRegX = RegExp(
      r'(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))');
  static final _phoneRegX = RegExp(r'^[0-9]{10,11}$');
  static final _doubleRegX = RegExp(r'^[+-]?([0-9]*[.])?[0-9]+');

  static String? userName(String? value) {
    if (value!.isEmpty) return 'Field can\'t be empty';

    return !_userNameRegX.hasMatch(value)
        ? 'Username can only contain letters, numbers and underscore(_) and must be of length between 8-30 characters'
        : null;
  }

  static String? email(String? value) {
    if (value!.isEmpty) return 'Field can\'t be empty';

    return !_emailRegX.hasMatch(value) ? 'Invalid email' : null;
  }

  static String? emptyEmail(String? value) {
    if (value!.isEmpty) return null;

    return !_emailRegX.hasMatch(value) ? 'Invalid email' : null;
  }

  static String? phone(String? value) {
    if (value!.isEmpty) {
      return null;
    } else if (value.startsWith('+')) {
      if (value.startsWith('+880') && value.length != 14) {
        return "Please enter a valid mobile number";
      } else if (value.startsWith('+880') && value.length == 14) {
        return null;
      } else if (value.startsWith('+1') && value.length != 12) {
        return "Please enter a valid mobile number";
      } else if (value.startsWith('+1') && value.length == 12) {
        return null;
      } else {
        return "Please enter a valid mobile number";
      }
    } else if (!value.startsWith('+')) {
      if (value.startsWith('880') && value.length != 13) {
        return "Please enter a valid mobile number";
      } else if (value.startsWith('880') && value.length == 13) {
        return null;
      } else if (value.startsWith('1') && value.length == 10) {
        return null;
      } else if (value.startsWith('0') && value.length == 11) {
        return null;
      } else {
        return "Please enter a valid mobile number";
      }
    } else {
      return "Please enter a valid mobile number";
    }
  }

  static String? phoneWithoutCountryCode(String? value) {
    if (value!.isEmpty) return 'Field can\'t be empty';

    return !_phoneRegX.hasMatch(value) ? 'Invalid phone number' : null;
  }

  static String? emailOrPhone(String? value) {
    if (value!.isEmpty) {
      return 'Field can\'t be empty';
    } else if (value.startsWith('+')) {
      if (value.startsWith('+880') && value.length != 14) {
        return "Please enter a valid mobile number";
      } else if (value.startsWith('+880') && value.length == 14) {
        return null;
      } else if (value.startsWith('+1') && value.length != 12) {
        return "Please enter a valid mobile number";
      } else if (value.startsWith('+1') && value.length == 12) {
        return null;
      } else {
        return "Please enter a valid mobile number";
      }
    } else if (!value.startsWith('+') && int.tryParse(value) != null) {
      if (value.startsWith('880') && value.length != 13) {
        return "Please enter a valid mobile number";
      } else if (value.startsWith('880') && value.length == 13) {
        return null;
      } else if (value.startsWith('1') && value.length != 11) {
        return "Please enter a valid mobile number";
      } else if (value.startsWith('1') && value.length == 11) {
        return null;
      } else {
        return "Please enter a valid mobile number";
      }
    } else {
      return !_emailRegX.hasMatch(value) ? 'Invalid email' : null;
    }
  }

  static String? password(String? value) {
    if (value!.isEmpty) return 'Field can\'t be empty';

    return value.length < 8
        ? 'Password must be at least 8 characters long'
        : null;
  }

  static String? confirmPassword(String? value, String? password) {
    if (value!.isEmpty) return 'Field can\'t be empty';

    return value != password ? 'Passwords don\'t match' : null;
  }

  static String? name(String? value) {
    return value!.isEmpty ? 'Field can\'t be empty' : null;
  }

  static String? otp(String? value) {
    if (value!.isEmpty) return 'Field can\'t be empty';

    return value.length != 6 ? 'Must be 6 digits long' : null;
  }


  static String? doubleNumber(String? value) {
    if (value!.isEmpty) return 'Field can\'t be empty';

    return !_doubleRegX.hasMatch(value) ? 'Invalid value' : null;
  }
}
