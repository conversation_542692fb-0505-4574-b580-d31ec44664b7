class ReferralDataEntity {
  final String referralId;
  final String referralTitle;
  final String referralDesc;
  final int totalPoint;
  final int totalAmount;
  final String referralCode;
  final List<String> referralRules;

  ReferralDataEntity({
    required this.referralId,
    required this.referralTitle,
    required this.referralDesc,
    required this.totalPoint,
    required this.totalAmount,
    required this.referralCode,
    required this.referralRules,
  });
}

List<String> testRules = [
  "Use your unique link or code to share the coaching program.",
  "You can share via WhatsApp, Instagram, Facebook, email, or SMS.",
  "Only shares that result in a friend clicking and signing up for the coaching program count.",
  "Your friend must complete onboarding and start a program for you to earn points.",
  "Referral must be completed within 7 days of the share.",
  "You can earn up to 1000 points per month through this challenge.",
  "You’ll receive points only once per friend, even if they join multiple times.",
];

var testReferralData = ReferralDataEntity(
  referralId: 'test-referral-id',
  referralTitle: 'Share Our Coach Program And Get',
  referralDesc: 'No description',
  totalPoint: 1000,
  totalAmount: 2000,
  referralCode: 'FM1234546',
  referralRules: testRules,
);
