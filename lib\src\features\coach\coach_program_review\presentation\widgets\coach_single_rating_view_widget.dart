import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/domain/entities/user_review_entity.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CoachSingleRatingViewWidget extends StatefulWidget {
  const CoachSingleRatingViewWidget(
      {super.key, required this.coachRatingEntity});
  final UserReviewEntity coachRatingEntity;

  @override
  State<CoachSingleRatingViewWidget> createState() =>
      _CoachSingleRatingViewWidgetState();
}

class _CoachSingleRatingViewWidgetState
    extends State<CoachSingleRatingViewWidget> {
  bool _showDetailsComment = false;

  @override
  void initState() {
    super.initState();
    _showDetailsComment = false;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(Values.v10),
      child: GestureDetector(
        onTap: () {
          setState(() {
            _showDetailsComment = !_showDetailsComment;
          });
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildRatingUserSection(),
            const SizedBox(
              height: Values.v10,
            ),
            _buildRatingCommentSection()
          ],
        ),
      ),
    );
  }

  _buildRatingUserSection() {
    return Row(
      children: [
        _buildUserImage(),
        const SizedBox(
          width: Values.v5,
        ),
        Expanded(child: _buildUserInfo()),
      ],
    );
  }

  _buildUserImage() {
    return ImageContainer.circularImage(
        image: widget.coachRatingEntity.userImage, radius: Values.v20);
  }

  _buildUserInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildNameDateSection(),
        _buildRatingSection(),
      ],
    );
  }

  _buildRatingCommentSection() {
    return Text(
      widget.coachRatingEntity.comment,
      maxLines: (_showDetailsComment) ? 100 : 2,
      overflow: TextOverflow.ellipsis,
      style: AppTypography.poppinsRegular14(color: UIColors.primaryGreen900),
    );
  }

  _buildNameDateSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.coachRatingEntity.userName,
          style: AppTypography.semiBold16(color: UIColors.primaryGreen900),
        ),
        Text(
          DateFormat.yMMMd().format(widget.coachRatingEntity.updatedAt),
          style: AppTypography.regular14(color: AppColors.greyscale400),
        )
      ],
    );
  }

  _buildRatingSection() {
    return Row(
      children: [
        const Icon(
          Icons.star,
          color: UIColors.primaryGreen400,
        ),
        Text(
          '${widget.coachRatingEntity.rating} Stars',
          style:
              AppTypography.poppinsRegular14(color: UIColors.primaryGreen900),
        )
      ],
    );
  }
}
