part of 'food_scan_bloc.dart';

sealed class FoodScanEvent extends Equatable {
  const FoodScanEvent();

  @override
  List<Object> get props => [];
}

class SendFoodScanImageEvent extends FoodScanEvent {
  final String imagePath;

  const SendFoodScanImageEvent({required this.imagePath});

  @override
  List<Object> get props => [imagePath];
}

class SendFoodScanFeedbackEvent extends FoodScanEvent {
  final String feedback;

  const SendFoodScanFeedbackEvent({required this.feedback});

  @override
  List<Object> get props => [feedback];
}

