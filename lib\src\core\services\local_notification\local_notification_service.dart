import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:fitsomnia_app/src/core/global/globals.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/global_navigation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class LocalNotificationService {
  final _localNotificationsPlugin = FlutterLocalNotificationsPlugin();

  Future<void> setup() async {
    const androidSetting = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );

    var iosSetting = const DarwinInitializationSettings();

    var initSettings = InitializationSettings(
      android: androidSetting,
      iOS: iosSetting,
    );

    await _localNotificationsPlugin
        .initialize(
          initSettings,
          onDidReceiveNotificationResponse: onDidReceiveLocalNotification,
        )
        .then((_) => Log.info('Local Notification Service: Setup Successful'))
        .catchError((Object error) {
      Log.info('Error(Local Notification Service): $error');
    });
  }

  Future<void> onDidReceiveLocalNotification(
      NotificationResponse response) async {
    Log.info('onDidReceiveLocalNotification: ${response.payload}');

    if (response.payload != null) {
      try {
        Map<String, dynamic> data = jsonDecode(response.payload!);
        /// Top-Level handle notification method
        handleMessage(data);
      } catch (e) {
        Log.error('Error parsing notification payload: $e');
      }
    } else {
      Log.warning('Notification payload is null');
    }
  }

  Future<void> showNotification({
    required RemoteMessage remoteMessage,
  }) async {
    notificationCount.value++;

    const AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      'regular-notification',
      'Regular Notification',
      playSound: true,
    );

    const DarwinNotificationDetails darwinNotificationDetails =
        DarwinNotificationDetails();

    const NotificationDetails notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: darwinNotificationDetails,
    );

    await _localNotificationsPlugin.show(
      remoteMessage.hashCode,
      remoteMessage.data["title"],
      remoteMessage.data["body"],
      notificationDetails,
      payload: jsonEncode(remoteMessage.data),
    );
  }

  Future<void> getActiveNotification() async {
    List<ActiveNotification> list =
        await _localNotificationsPlugin.getActiveNotifications();

    int allCount = 0;
    int chatCount = 0;

    for (var element in list) {
      // Safely check if title exists and contains 'message'
      String title = element.title ?? '';
      title.toLowerCase().contains('message')
          ? chatCount++
          : allCount++;
    }

    notificationCount.value = allCount;
    chattingCount.value += chatCount;
  }

  Future<void> clearNotifications() async {
    await _localNotificationsPlugin.cancelAll();
  }
}
