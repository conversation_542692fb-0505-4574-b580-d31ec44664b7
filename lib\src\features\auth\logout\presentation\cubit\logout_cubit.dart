import 'package:bloc/bloc.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/auth/logout/domain/use_case/logout_use_case.dart';

part 'logout_state.dart';

class LogoutCubit extends Cubit<BaseState> {
  LogoutCubit({required this.useCase}) : super(InitialState());

  final LogoutUseCase useCase;

  Future<void> logout() async {
    emit(const LoadingState());
    try {
      final result = await useCase();

      result.fold(
        (l) => emit(ErrorState(data: l)),
        (r) => emit(SuccessState(data: r)),
      );
    } catch (e, stackTrace) {
      Log.info('User logout: Fail');
      Log.error(e.toString());
      Log.error(stackTrace.toString());
      emit(ErrorState(data: ErrorModel()));
    }
  }
}
