import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/domain/entities/daily_task.dart';
import 'package:flutter/material.dart';

class DailyTaskCompletedCardWidget extends StatefulWidget {
  const DailyTaskCompletedCardWidget({super.key, required this.dailyTask});
  final DailyTask dailyTask;

  @override
  State<DailyTaskCompletedCardWidget> createState() =>
      _DailyTaskCompletedCardWidgetState();
}

class _DailyTaskCompletedCardWidgetState
    extends State<DailyTaskCompletedCardWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(Values.v10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
            colors: [UIColors.primaryGreen950, UIColors.primaryGreen700],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight),
        border: Border.all(color: AppColors.greyscale100),
        borderRadius: BorderRadius.circular(Values.v8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCompletedStatusSection(),
          SizedBox(
            height: Values.v10,
          ),
          _buildTitleSection(),
          SizedBox(
            height: Values.v10,
          ),
          _buildTaskInfoSection(),
        ],
      ),
    );
  }

  _buildTitleSection() {
    return Row(
      children: [Expanded(child: _buildTitleText())],
    );
  }

  _buildTitleText() {
    return Text(
      widget.dailyTask.title,
      style: AppTypography.poppinsSemiBold20(color: AppColors.white),
    );
  }

  _buildTaskInfoSection() {
    return Row(
      children: [
        _buildPointSection(),
        // Spacer(),
        // _buildTaskProgressSection(),
      ],
    );
  }

  _buildPointSection() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        RichText(
            text: TextSpan(
                style: AppTypography.poppinsMedium12(
                    color: UIColors.white),
                children: [
              TextSpan(text: 'Rewards: '),
              TextSpan(
                text: '${widget.dailyTask.totalRewardPoint} Points',
                style: AppTypography.poppinsMedium14(
                    color: UIColors.yellow400),
              )
            ]))
      ],
    );
  }

  _buildTaskProgressSection() {
    return SizedBox(
      height: 40,
      width: 40,
      child: Stack(
        alignment: AlignmentDirectional.center,
        children: [
          CircularProgressIndicator(
            color: UIColors.purple500,
            backgroundColor: UIColors.purple200,
            value: 0.3,
          ),
          Text(
            '100%',
            style: AppTypography.poppinsRegular12(color: UIColors.purple500),
          ),
        ],
      ),
    );
  }
  
  _buildCompletedStatusSection() {
    return Row(children: [_buildCompletedIcon(), _buildCompletedText()],);
  }
  
  _buildCompletedIcon() {
    return Icon(Icons.check_circle_rounded, color: UIColors.primary,);
  }
  
  _buildCompletedText() {
    return Text('Completed', style: AppTypography.poppinsMedium12(color: UIColors.primaryGreen200),);
  }
}
