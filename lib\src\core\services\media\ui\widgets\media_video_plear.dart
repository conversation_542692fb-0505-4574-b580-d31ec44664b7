import 'dart:io';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_info_widget.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class MediaVideoPlayer extends StatefulWidget {
  const MediaVideoPlayer({
    Key? key,
    required this.videoPath,
  }) : super(key: key);

  final File videoPath;

  @override
  State<MediaVideoPlayer> createState() => _MediaVideoPlayerState();
}

class _MediaVideoPlayerState extends State<MediaVideoPlayer>
    with SingleTickerProviderStateMixin {
  late VideoPlayerController _controller;
  bool showActionWidget = true;
  bool isError = false;
  bool isLoading = true;
  bool isPauseButtonTapped = false;

  //bool _isButtonVisible = true;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.file(widget.videoPath)
      ..initialize().then((_) {
        setState(() {
          isLoading = false;
        });
      });
    _videoControllerListener();
  }

  Future<void> _makeButtonInVisible() async {
    setState(() {
      showActionWidget = true;
    });
    await Future.delayed(const Duration(seconds: 1), () {
      if (!isPauseButtonTapped) {
        setState(() {
          showActionWidget = false;
        });
      }
    });
  }

  void _videoControllerListener() {
    return _controller.addListener(() {
      if (_controller.value.hasError) {
        setState(() {
          isError = true;
        });
      }

      if (_controller.value.position == _controller.value.duration) {
        setState(() => '');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return isError
        ? const InfoWidget(
            message: TextConstants.cannotPlayVideo,
          )
        : GestureDetector(
            onTap: () {
              _makeButtonInVisible();
            },
            child: Stack(
              children: [
                Center(
                  child: AspectRatio(
                    aspectRatio: _controller.value.aspectRatio,
                    child: VideoPlayer(_controller),
                  ),
                ),
                Visibility(
                  visible: showActionWidget,
                  child: Align(
                    alignment: Alignment.center,
                    child: InkWell(
                      child: Icon(
                        _controller.value.isPlaying
                            ? Icons.pause_circle
                            : Icons.play_circle_fill_outlined,
                        size: Values.v60,
                        color: AppColors.white,
                      ),
                      onTap: () {
                        setState(() {
                          if (_controller.value.isPlaying) {
                            isPauseButtonTapped = true;
                            _controller.pause();
                            showActionWidget = true;
                          } else {
                            isPauseButtonTapped = false;
                            _controller.play();
                            showActionWidget = false;
                          }
                        });
                      },
                    ),
                  ),
                ),
              ],
            ),
          );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
