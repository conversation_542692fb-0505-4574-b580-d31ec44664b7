import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/presentation/widgets/custom_dropdown_menu.dart';
import 'package:flutter/material.dart';

class DropdownSelectionMenu extends StatefulWidget {
  const DropdownSelectionMenu({
    Key? key,
    required this.title,
    required this.lists,
    required this.valuteNotifier,
    this.initialSelectedItem,
    this.isExpanded = true,
  }) : super(key: key);

  final String? title;
  final List<MenuItem> lists;
  final MenuItem? initialSelectedItem;
  final ValueNotifier<MenuItem> valuteNotifier;
  final bool isExpanded;

  @override
  State<DropdownSelectionMenu> createState() => _DropdownSelectionMenuState();
}

class _DropdownSelectionMenuState extends State<DropdownSelectionMenu> {
  @override
  Widget build(BuildContext context) {
    return _buildSelectionMenu();
  }

  _buildSelectionMenu() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null && widget.title! != '')
          _buildTitle(),
        if (widget.title != null && widget.title! != '')
          const SizedBox(
            height: Values.v5,
          ),
        _buildCustomItemSelectionMenu(
            lists: widget.lists,
            initialSelectedItem: widget.initialSelectedItem,
            valueChangeNotifier: widget.valuteNotifier),
      ],
    );
  }

  _buildTitle() {
    return RichText(
                text: TextSpan(
                  text: widget.title,
                  style: AppTypography.poppinsRegular16(
                      color: UIColors.primaryGreen950),
                  children: const <TextSpan>[
                    TextSpan(
                      text: '*',
                      style: TextStyle(color: Colors.red),
                    ),
                  ],
                ),
              );
  }

  _buildCustomItemSelectionMenu({
    required List<MenuItem> lists,
    required MenuItem? initialSelectedItem,
    required ValueNotifier<MenuItem> valueChangeNotifier,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: Values.v10),
      decoration: BoxDecoration(
          border: Border.all(color: UIColors.navGrey),
          borderRadius: BorderRadius.circular(Values.v10)),
      child: DropdownMenu<MenuItem>(
          // width: MediaQuery.of(context).size.width
          expandedInsets: (widget.isExpanded) ? EdgeInsets.zero : null,
          trailingIcon: const Icon(Icons.keyboard_arrow_down_outlined,
              color: UIColors.fitGrey),
          selectedTrailingIcon: const Icon(Icons.keyboard_arrow_up_outlined,
              color: UIColors.fitGrey),
          textStyle:
              AppTypography.poppinsRegular16(color: UIColors.primaryGreen950),
          inputDecorationTheme:
              const InputDecorationTheme(border: InputBorder.none),
          controller: TextEditingController(),
          initialSelection: initialSelectedItem,
          onSelected: (item) {
            if (item != null) valueChangeNotifier.value = item;
          },
          dropdownMenuEntries: lists.map<DropdownMenuEntry<MenuItem>>(
            (item) {
              return DropdownMenuEntry(
                value: item,
                label: item.label,
                style: MenuItemButton.styleFrom(
                    textStyle: AppTypography.poppinsRegular16(
                        color: UIColors.primaryGreen950),
                    padding: const EdgeInsets.only(left: Values.v10)),
              );
            },
          ).toList()),
    );
  }
}

class DropdownSelectionMenu2 extends StatefulWidget {
  const DropdownSelectionMenu2({
    Key? key,
    required this.title,
    required this.lists,
    required this.valuteNotifier,
    this.initialSelectedItem,
    this.isExpanded = true,
  }) : super(key: key);

  final Widget? title;
  final List<MenuItem> lists;
  final MenuItem? initialSelectedItem;
  final ValueNotifier<MenuItem> valuteNotifier;
  final bool isExpanded;

  @override
  State<DropdownSelectionMenu2> createState() => _DropdownSelectionMenu2State();
}

class _DropdownSelectionMenu2State extends State<DropdownSelectionMenu2> {
  @override
  Widget build(BuildContext context) {
    return _buildSelectionMenu();
  }

  _buildSelectionMenu() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null)
          _buildTitle(),
        if (widget.title != null)
          const SizedBox(
            height: Values.v5,
          ),
        _buildCustomItemSelectionMenu(
            lists: widget.lists,
            initialSelectedItem: widget.initialSelectedItem,
            valueChangeNotifier: widget.valuteNotifier),
      ],
    );
  }

  _buildTitle() {
    return widget.title;

    // return RichText(
    //             text: TextSpan(
    //               text: widget.title,
    //               style: AppTypography.poppinsRegular16(
    //                   color: UIColors.primaryGreen950),
    //               children: const <TextSpan>[
    //                 TextSpan(
    //                   text: '*',
    //                   style: TextStyle(color: Colors.red),
    //                 ),
    //               ],
    //             ),
    //           );
  }

  _buildCustomItemSelectionMenu({
    required List<MenuItem> lists,
    required MenuItem? initialSelectedItem,
    required ValueNotifier<MenuItem> valueChangeNotifier,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: Values.v10),
      decoration: BoxDecoration(
          border: Border.all(color: UIColors.navGrey),
          borderRadius: BorderRadius.circular(Values.v10)),
      child: DropdownMenu<MenuItem>(
          // width: MediaQuery.of(context).size.width
          expandedInsets: (widget.isExpanded) ? EdgeInsets.zero : null,
          trailingIcon: const Icon(Icons.keyboard_arrow_down_outlined,
              color: UIColors.fitGrey),
          selectedTrailingIcon: const Icon(Icons.keyboard_arrow_up_outlined,
              color: UIColors.fitGrey),
          textStyle:
              AppTypography.poppinsRegular16(color: UIColors.primaryGreen950),
          inputDecorationTheme:
              const InputDecorationTheme(border: InputBorder.none),
          controller: TextEditingController(),
          initialSelection: initialSelectedItem,
          onSelected: (item) {
            if (item != null) valueChangeNotifier.value = item;
          },
          dropdownMenuEntries: lists.map<DropdownMenuEntry<MenuItem>>(
            (item) {
              return DropdownMenuEntry(
                value: item,
                label: item.label,
                style: MenuItemButton.styleFrom(
                    textStyle: AppTypography.poppinsRegular16(
                        color: UIColors.primaryGreen950),
                    padding: const EdgeInsets.only(left: Values.v10)),
              );
            },
          ).toList()),
    );
  }
}
