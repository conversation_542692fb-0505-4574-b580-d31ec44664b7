import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/pages/coach_feature_dashboard.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/bloc/dashboard_cubit.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:fitsomnia_app/src/features/settings/personal_information/presentation/pages/personal_information_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CoachProgramSubscriptionSuccessWidget extends StatelessWidget {
  const CoachProgramSubscriptionSuccessWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          // decoration: BoxDecoration(border: Border.all(color: Colors.red)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              IconButton(
                // iconSize: Values.v18,
                onPressed: () {
                  Navigator.of(context).pop();
                },
                icon: Icon(Icons.clear_sharp),
              )
            ],
          ),
        ),
        Container(
          // decoration: BoxDecoration(border: Border.all(color: Colors.red)),
          child: Text(
            'Congratulations !',
            style: TextStyle(
              fontSize: FontSize.s40.sp,
              fontFamily: FontConstants.caveatFontFamily,
              fontWeight: FontWeight.w400,
              // height: 1.0
            ),
          ),
        ),
        Stack(
          alignment: Alignment.topCenter,
          children: [
            SvgPicture.asset(
              Assets.completeProfileBackground,
              height: Values.v324.h,
              width: Values.v375.w,
              fit: BoxFit.fill,
            ),
            Container(
              // decoration: BoxDecoration(image: DecorationImage(image: AssetImage(Assets.beginner), fit: BoxFit.cover)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'You Have Subscribed To The Program',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: FontSize.s16.w,
                      fontFamily: FontConstants.poppinsFontFamily,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  // Text(
                  //   'App For Health & Fitness',
                  //   textAlign: TextAlign.center,
                  //   style: TextStyle(
                  //     fontSize: FontSize.s16.w,
                  //     fontFamily: FontConstants.poppinsFontFamily,
                  //     fontWeight: FontWeight.w400,
                  //   ),
                  // ),
                  SizedBox(
                    height: Values.v215.h,
                    width: Values.v200.w,
                    child: SvgPicture.asset(Assets.completeProfileHi),
                  ),
                  _buildGoToLearnerDashboardButton(context),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  _buildGoToLearnerDashboardButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(Values.v20.w, 0, Values.v20.w, Values.v24.h),
      child: Button(
        background: UIColors.primaryGreen900,
        width: Values.v304.w,
        label: 'Go To Dashboard',
        textStyle: const TextStyle(
          fontSize: FontSize.s20,
          fontFamily: FontConstants.poppinsFontFamily,
          fontWeight: FontWeight.w500,
          color: Colors.white,
        ),
        onPressed: () {
          // Navigator.of(context).pushNamedAndRemoveUntil(
          //     Routes.studentEnrollmentDashboardPage, (route) => route.isFirst);

          // Navigator.of(context).popUntil((route) => route.isFirst);

          // Navigator.push(
          //   context,
          //   MaterialPageRoute(
          //     builder: (_) => const PersonalInformationPage(
          //       isFromDashboard: true,
          //     ),
          //   ),
          // );

          // _navigateToCoachFeatureDashboard(context);
          coachInitialPageLocalNotifier.value = CoachInitialPage.STUDENT_DASHBOARD.index;
          Navigator.of(context).pop();
        },
      ),
    );
  }

  void _navigateToCoachFeatureDashboard(BuildContext context) {
    // move to coach featue page 
    // Navigator.of(context).popUntil((route) => route.isFirst);
    navigatorKey?.currentState?.pushAndRemoveUntil(
                              MaterialPageRoute(
                                  builder: (_) => DashboardPage(selectedIndex: 2,)),
                              (route) => false,
                            );
    // context
    //     .read<DashboardCubit>()
    //     .changePage(2); // coach featue position is 2 in nav bar
  }
}
