package com.fitsomnia.fitsomniaApp

import com.fitsomnia.GoogleNativeAdFactory
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugins.googlemobileads.GoogleMobileAdsPlugin

class MainActivity: FlutterFragmentActivity() {

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        flutterEngine.plugins.add(GoogleMobileAdsPlugin())
        super.configureFlutterEngine(flutterEngine)

        //TODO: Register the googleNativeAdFactory
        GoogleMobileAdsPlugin.registerNativeAdFactory(flutterEngine, "googleNativeAdFactory", GoogleNativeAdFactory(this))
    }

    override fun cleanUpFlutterEngine(flutterEngine: FlutterEngine) {
        super.cleanUpFlutterEngine(flutterEngine)

        //TODO: unregister the googleNativeAdFactory
        GoogleMobileAdsPlugin.unregisterNativeAdFactory(flutterEngine, "googleNativeAdFactory")
    }
}
