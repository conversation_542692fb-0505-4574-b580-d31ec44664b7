part of 'daily_task_bloc.dart';

sealed class DailyTaskEvent extends Equatable {
  const DailyTaskEvent();

  @override
  List<Object> get props => [];
}

class GetDailyTasks extends DailyTaskEvent {
  final int? offset;
  final int? limit;
  final String? taskTypeFilter;
  final String? taskStatusFilter;

  const GetDailyTasks({
    this.offset,
    this.limit,
    this.taskTypeFilter,
    this.taskStatusFilter,
  });
}
