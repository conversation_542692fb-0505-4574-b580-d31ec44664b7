import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class CoachNewsfeedDataSource {
  Future<Response> getCoachProfiles({
    required CoachProfileFilterType filterType,
    String? namePrefix,
    String? categoryId,
    String? subcategoryId,
    int? offset,
    int? limit,
  });

  Future<Response> getCoachProfileById({
    required String coachId,
  });

  Future<Response> getPrograms({
    required CoachProfileFilterType filterType,
    String? namePrefix,
    int? offset,
    int? limit,
  });
}

class CoachNewsfeedDataSourceImpl extends CoachNewsfeedDataSource {
  final RestClient restClient;

  CoachNewsfeedDataSourceImpl({required this.restClient});
  @override
  Future<Response> getCoachProfiles(
      {required CoachProfileFilterType filterType,
      String? namePrefix,
      String? categoryId,
      String? subcategoryId,
      int? offset,
      int? limit}) async {
    String path = '${API.coachProfiles}?filterType=${filterType.getValue()}';

    if (offset != null && limit != null) {
      path += '&offset=$offset&limit=$limit';
    }

    switch (filterType) {
      case CoachProfileFilterType.CATEGORY:
        path += '&categoryId=$categoryId';
        break;
      case CoachProfileFilterType.SUBCATEGORY:
        path += '&subCategoryId=$subcategoryId';
        break;
      case CoachProfileFilterType.NAME:
        path += '&namePrefix=$namePrefix';
        break;
      case CoachProfileFilterType.BEST:
      default:
    }

    final response = await restClient.get(APIType.PROTECTED, path);

    return response;
  }

  @override
  Future<Response> getCoachProfileById({required String coachId}) async {
    final response =
        await restClient.get(APIType.PROTECTED, '${API.coachProfile}/$coachId');

    return response;
  }

  @override
  Future<Response> getPrograms({
    required CoachProfileFilterType filterType,
    String? namePrefix,
    int? offset,
    int? limit,
  }) async {
    String path =
        '${API.coachProgramFilter}?filterType=${filterType.getValue()}';
        
    if (offset != null && limit != null) {
      path += '&offset=$offset&limit=$limit';
    }

    if (namePrefix != null) {
      path += '&namePrefix=$namePrefix';
    }

    final response = await restClient.get(APIType.PROTECTED, path);

    return response;
  }
}
