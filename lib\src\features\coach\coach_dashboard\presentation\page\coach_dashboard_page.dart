import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/bloc/coach_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/widget/coach_my_program_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/widget/coach_payment_withdraw_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/widget/coach_total_income_info_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/pages/coach_feature_dashboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

class CoachDashboardPage extends StatefulWidget {
  const CoachDashboardPage(
      {Key? key, this.isFromBottom = false, this.pageValueNotifier})
      : super(key: key);

  final bool isFromBottom;
  final ValueNotifier<int>? pageValueNotifier;

  @override
  State<CoachDashboardPage> createState() => _CoachDashboardPageState();
}

class _CoachDashboardPageState extends State<CoachDashboardPage> {
  bool isACoach = false;
  bool isCoachApplicationIsPending = false;
  bool isLearner = false;
  CoachEntity? _coachEntity;
  bool _isLoading = false;

  String? coachId;

  @override
  void initState() {
    super.initState();

    _updateCoachProdileStatus();
    coachId = context.read<CoachBloc>().coachId;

    if (coachId != null)
      BlocProvider.of<CoachDashboardBloc>(context)
          .add(GetCoachOwnProfileEvent(coachId: coachId!));
    _isLoading = true;
  }

  _updateCoachProdileStatus() {
    isACoach = context.read<CoachBloc>().isCoach;
    isCoachApplicationIsPending =
        context.read<CoachBloc>().isCoachApplicationPending;
    isLearner = context.read<CoachBloc>().isStudent;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<CoachDashboardBloc, CoachDashboardState>(
          listener: (context, state) {
            if (state is GetCoachOwnProfileSuccess) {
              Log.debug('get coach own profile success');
              setState(() {
                _coachEntity = state.coachEntity;
                _isLoading = false;

                // downlaod coach own programs
                BlocProvider.of<CoachDashboardBloc>(context)
                    .add(GetCoachProgramsEvent(coachId: coachId!));
              });
            }
            if (state is GetCoachOwnProfileFail) {
              Log.debug('get coach own profile fail');
              setState(() {
                _isLoading = false;
              });
            }
          },
        ),
        BlocListener<CoachBloc, CoachState>(
          listener: (context, state) {
            if (state is GetCoachProfileStatusSuccess) {
              setState(() {
                _updateCoachProdileStatus();
              });
            }
          },
        ),
      ],
      child: Scaffold(
          appBar: (widget.isFromBottom) ? null : _buildAppBar(),
          body: SafeArea(
              child: (_isLoading)
                  ? const Center(
                      child: CircularProgressIndicator(
                        color: UIColors.primary,
                      ),
                    )
                  : (_coachEntity == null)
                      ? const Center(
                          child: Text('No coach profile found'),
                        )
                      : SingleChildScrollView(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: Values.v20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (isCoachApplicationIsPending)
                                  _buildCoachApplicationPendingSection(),
                                if (isACoach)
                                  Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      _buildCoachProfileSection(),
                                      CoachPaymentWithdrawWidget(
                                        coachEntity: _coachEntity!,
                                      ),
                                      CoachTotalIncomeInfoWidget(
                                        coachEntity: _coachEntity!,
                                      ),
                                      CoachMyProgramWidget(
                                          key: UniqueKey(),
                                          coachEntity: _coachEntity!),
                                    ],
                                  )
                              ],
                            ),
                          ),
                        ))),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        'Coach Dashboard',
        style: TextStyle(
          color: UIColors.primaryGreen950,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: true,
      elevation: 0,
    );
  }

  _buildCoachProfileSection() {
    return Container(
      margin: EdgeInsets.only(top: Values.v20),
      child: Row(
        children: [
          _buildCoachProfileImage(),
          const SizedBox(
            width: Values.v10,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _coachEntity!.name,
                  style: AppTypography.poppinsSemiBold20(),
                  maxLines: 2,
                  overflow: TextOverflow.clip,
                ),
                _buildEditCoachProfileButton()
              ],
            ),
          ),
          // Spacer(),
          _buildProfileSwitchOption(),
        ],
      ),
    );
  }

  _buildCoachProfileImage() {
    if (_coachEntity!.media == null || _coachEntity!.media!.isEmpty) {
      // return ImageContainer.circularImage(
      //     image: Assets.spotMeNoImage, radius: Values.v32);

      return ClipRRect(
        borderRadius: BorderRadius.circular(Values.v100),
        child: SizedBox(
          height: Values.v64,
          width: Values.v64,
          child: Image.asset(
            Assets.spotMeNoImage,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    if (_coachEntity!.media!.first.url == 'string') {
      // return ImageContainer.circularImage(
      //     image: Assets.spotMeNoImage, radius: Values.v32);

      return ClipRRect(
        borderRadius: BorderRadius.circular(Values.v100),
        child: SizedBox(
          height: Values.v64,
          width: Values.v64,
          child: Image.asset(
            Assets.spotMeNoImage,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    return ImageContainer.circularImage(
      image: _coachEntity!.media!.first.url,
      useOriginal: true,
      radius: Values.v32,
      showBorder: false,
    );
  }

  _buildCoachApplicationPendingSection() {
    if (isACoach) return SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: Values.v20),
      padding: const EdgeInsets.symmetric(
          vertical: Values.v20, horizontal: Values.v10),
      decoration: BoxDecoration(
          color: UIColors.yellow50,
          border: Border.all(color: UIColors.yellow400),
          borderRadius: BorderRadius.circular(10)),
      child: Row(
        children: [
          Icon(Icons.error_outline_rounded),
          SizedBox(
            width: Values.v5,
          ),
          Expanded(
              child: Container(
            child: Text(
              TextConstants.coachApplicationUnderReviewText,
              style: AppTypography.poppinsSemiBold14(
                  color: UIColors.primaryGreen950),
              overflow: TextOverflow.ellipsis,
              maxLines: 3,
            ),
          )),
        ],
      ),
    );
  }

  _buildEditCoachProfileButton() {
    return SizedBox(
      height: Values.v34,
      child: Button.filled(
        label: 'Edit Coach Profile',
        onPressed: () {
          Log.debug('edit coach profile pressed');
          Navigator.of(context).pushNamed(Routes.coachProfileEditPage,
              arguments: [_coachEntity!.coachId, true]);
        },
        width: Values.v180,
        textStyle: AppTypography.poppinsSemiBold14(color: UIColors.white),
      ),
    );
  }

  _buildProfileSwitchOption() {
    // return IconButton(
    //   onPressed: () {
    //     Navigator.of(context)
    //         .pushReplacementNamed(Routes.studentEnrollmentDashboardPage);
    //   },
    //   icon: Icon(Icons.change_circle_outlined),
    // );

    return _buildProfileOptionsMenu();
  }

  _buildProfileOptionsMenu() {
    List<CoachProfileOptionItem> options = [];

    options.add(CoachProfileOptionItem.COACH_PROFILE);
    options.add((isLearner)
        ? CoachProfileOptionItem.STUDENT_PROFILE
        : CoachProfileOptionItem.CREATE_STUDENT_PROFILE);

    return PopupMenuButton(
      // icon: Icon(Icons.change_circle_outlined, color: AppColors.primaryGreen,),
      // icon: SizedBox(height: Values.v40, width: Values.v40, child: SvgPicture.asset(Assets.coachProfileSwitchIcon),),
      icon: SvgPicture.asset(
        Assets.coachProfileSwitchIcon,
      ),
      iconSize: Values.v40,
      position: PopupMenuPosition.under,
      initialValue: CoachProfileOptionItem.COACH_PROFILE,
      onSelected: (value) {
        // if (value != CoachProfileOptionItem.COACH_PROFILE) {
        //   Navigator.of(context).pushReplacementNamed(value.route());
        // }

        if (widget.pageValueNotifier != null) {
          switch (value) {
            case CoachProfileOptionItem.COACH_PROFILE:
              widget.pageValueNotifier!.value =
                  CoachInitialPage.COACH_DASHBOARD.index;
              break;
            case CoachProfileOptionItem.STUDENT_PROFILE:
              widget.pageValueNotifier!.value =
                  CoachInitialPage.STUDENT_DASHBOARD.index;
              break;
            case CoachProfileOptionItem.CREATE_STUDENT_PROFILE:
              widget.pageValueNotifier!.value =
                  CoachInitialPage.STUDENT_INITIAL.index;
              break;
            default:
          }
        }
      },
      itemBuilder: (BuildContext context) {
        return options
            .asMap()
            .map((index, menuEntity) {
              final menuItem = PopupMenuItem(
                value: options[index],
                labelTextStyle: WidgetStatePropertyAll(
                    AppTypography.poppinsBold14(
                        color: UIColors.primaryGreen900)),
                child: Text(menuEntity.name()),
              );

              return MapEntry(index, menuItem);
            })
            .values
            .toList();
      },
    );
  }
}
