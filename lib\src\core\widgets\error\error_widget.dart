import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ErrorFeedbackWidget extends StatelessWidget {
  const ErrorFeedbackWidget({
    Key? key,
    required this.error,
  }) : super(key: key);

  final ErrorResponseModel? error;

  @override
  Widget build(BuildContext context) {
    final ErrorModel? error = this.error?.error;

    return Center(
      child: SizedBox(
        height: MediaQuery.of(context).size.height * 0.20,
        width: MediaQuery.of(context).size.width * 0.8,
        child: Column(
          children: [
            Text(
              error?.code ?? "500",
              style: TextStyle(
                color: Colors.grey,
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10.h),
            Center(
              child: Text(
                error?.message ?? "Something went wrong",
                textAlign: TextAlign.center,
                textScaler: const TextScaler.linear(1),
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 20.sp,
                  fontWeight: FontWeight.normal,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
