part of 'coach_dashboard_bloc.dart';

sealed class CoachDashboardEvent extends Equatable {
  const CoachDashboardEvent();

  @override
  List<Object> get props => [];
}


class GetCoachOwnProfileEvent extends CoachDashboardEvent {
  final String coachId;

  GetCoachOwnProfileEvent({required this.coachId});
  @override
  List<Object> get props => [coachId];
}

class GetCoachProgramsEvent extends CoachDashboardEvent {
  final String coachId;

  GetCoachProgramsEvent({required this.coachId});
  @override
  List<Object> get props => [coachId];
}

class GetCoachSingleProgramEvent extends CoachDashboardEvent {
  final String coachId;
  final String programId;

  const GetCoachSingleProgramEvent({required this.programId, required this.coachId});

  @override
  List<Object> get props => [programId, coachId];
}

class GetCoachAllProgramEnrollmentHistoryEvent extends CoachDashboardEvent {
  final String coachId;
  final int? offset;
  final int? limit;

  GetCoachAllProgramEnrollmentHistoryEvent({required this.coachId, this.offset, this.limit});
  @override
  List<Object> get props => [coachId];
}

class GetCoachSingleProgramEnrollmentDetailEvent extends CoachDashboardEvent {
  final String coachId;
  final String subscriptionId;

  const GetCoachSingleProgramEnrollmentDetailEvent({required this.subscriptionId,required this.coachId});

  @override
  List<Object> get props => [subscriptionId, coachId];
}

class ProgramSubscriptionCancelByCoachEvent extends CoachDashboardEvent {
  final String coachId;
  final String subscriptionId;
  final String cancelReason;

  const ProgramSubscriptionCancelByCoachEvent({
    required this.coachId,
    required this.subscriptionId,
    required this.cancelReason,
  });

  @override
  List<Object> get props => [coachId, subscriptionId, cancelReason,];
}

class GetCoachIncomeEvent extends CoachDashboardEvent {
  final String coachId;

  const GetCoachIncomeEvent({required this.coachId});
  @override
  List<Object> get props => [coachId];
}

class GetCoachProgramEnrollerHistoryEvent extends CoachDashboardEvent {
  final String coachId;
  final String programId;

  const GetCoachProgramEnrollerHistoryEvent({required this.programId, required this.coachId});

  @override
  List<Object> get props => [programId, coachId];
}

class GetCoachProgramPaymentHistoryEvent extends CoachDashboardEvent {
  final String coachId;
  final String programId;

  const GetCoachProgramPaymentHistoryEvent({required this.programId, required this.coachId});

  @override
  List<Object> get props => [programId, coachId];
}
