import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_profile_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/best_coach_view_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_subcategory_view_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/coach/root/data/model/coach_program_category_model.dart';
import 'package:fitsomnia_app/src/features/coach/root/domain/entities/coach_program_category_entity.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class BestCoachSectionWidget extends StatefulWidget {
  const BestCoachSectionWidget({super.key});

  @override
  State<BestCoachSectionWidget> createState() => _BestCoachSectionWidgetState();
}

class _BestCoachSectionWidgetState extends State<BestCoachSectionWidget> {
  List<CoachProfileEntity> bestCoaches = [];

  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    BlocProvider.of<CoachNewsfeedBloc>(context)
        .add(GetBeastCoachProfilesEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachNewsfeedBloc, CoachNewsfeedState>(
      listener: (context, state) {
        if (state is CoachProfilesLoading) {
          Log.debug('coach profiles is loading');
        }

        if (state is GetBestCoachProfilesSuccess) {
          Log.debug('get best coach profile success');
          setState(() {
            bestCoaches = state.coachProfiles;
          });
        }
        if (state is GetBestCoachProfilesFail) {
          Log.debug('get best coach profile fail');
        }
      },
      child: Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: Values.v20.h,
            ),
            _buildCategoriesTitle(),
            SizedBox(
              height: Values.v20.h,
            ),
            _buildSubCategorySection(),
          ],
        ),
      ),
    );
  }

  _buildCategoriesTitle() {
    return Padding(
      padding: EdgeInsets.only(left: Values.v10, right: Values.v20),
      child: Row(
        children: [
          Text(
            'Best Coaches',
            style: AppTypography.poppinsSemiBold16(
                color: UIColors.primaryGreen900),
          ),
          Spacer(),
          _buildSeeAllBestCoachesSection(),
        ],
      ),
    );
  }

  _buildSeeAllBestCoachesSection() {
    return GestureDetector(
      onTap: () {
        Log.debug('see all best coaches');
        Navigator.of(context).pushNamed(Routes.coachSearchListPage,
            arguments: [null, null, CoachProfileFilterType.BEST]);
      },
      child: Text(
        'See all',
        style: AppTypography.poppinsMedium14(color: UIColors.primaryGreen500),
      ),
    );
  }

  _buildSubCategorySection() {
    // bestCoaches = List.generate(5, (index) {
    //   return testBestCoachProgileEntity;
    // });

    return Container(
      height: Values.v200.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        itemCount: bestCoaches.length,
        itemBuilder: (BuildContext context, int index) {
          return BestCoachViewWidget(coachProfileEntity: bestCoaches[index]);
        },
      ),
    );
  }
}

CoachProgramCategoryEntity testCoachCategory = CoachProgramCategoryEntity(
  id: 'coach-cateogy-id',
  title: 'Fitness',
  desc: 'fitness desc',
  mediaList: [
    CoachCategoryMediaFile(
        urlPath:
            'https://dev-public-cdn.fitsomnia.com/original/coach/2025/1/28/6293a7cb-9788-4cc1-9cf3-12eb56ff687e/938925_1738071637314B9A405711.jpg',
        mediaType: 'image')
  ],
  subcategories: [],
  totalCoach: 100,
);

CoachProfileEntity testBestCoachProgileEntity = CoachProfileEntity(
    coachId: 'coachId',
    userId: 'userId',
    userName: 'Mahmudul Hasan',
    legalName: 'Mahmudul Hasan',
    expertise: 'expertise',
    experienceInYears: '5',
    subscriptionCount: 100,
    currentRating: 4,
    profilePictures: [
      CoachMediaFile(
        url:
            'https://dev-public-cdn.fitsomnia.com/original/coach/2025/1/28/6293a7cb-9788-4cc1-9cf3-12eb56ff687e/938925_1738071637314B9A405711.jpg',
        mediaType: 'image',
      )
    ]);
