class FoodInfoEntity {
  final String name;
  final String benglaName;
  final String calories;
  final String protein;
  final String carbs;
  final String fats;
  final bool isFound;
  final String? servingSize;
  final List<String>? ingredients;

  FoodInfoEntity({
    required this.name,
    required this.benglaName,
    required this.calories,
    required this.protein,
    required this.carbs,
    required this.fats,
    
    required this.isFound,
    this.servingSize,
    this.ingredients,
  });

  @override
  List<Object> get props => [name, calories, protein, carbs, fats];
}