import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/presentation/bloc/group_list_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/leave_group/leave_group_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class GroupChatInformationPage extends StatelessWidget {
  const GroupChatInformationPage({
    Key? key,
    required this.groupId,
    required this.groupName,
    required this.groupImage,
  }) : super(key: key);

  final String groupId;
  final String groupName;
  final String? groupImage;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.white,
        iconTheme: const IconThemeData(color: AppColors.black),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const SizedBox(height: 20),
            ImageContainer.circularImage(
              image: groupImage ?? '',
              radius: Values.v50,
              isGroup: true,
            ),
            const SizedBox(height: 10),
            Text(
              groupName,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.black,
              ),
            ),
            const SizedBox(height: 40),

            /// Actions
            InkWell(
              onTap: () => Navigator.pushNamed(context, Routes.groupChatMembers,
                  arguments: groupId),
              child: Card(
                elevation: Values.v10,
                shadowColor: AppColors.apple.withOpacity(Values.v0_15),
                margin: const EdgeInsets.only(bottom: 10),
                child: const ListTile(
                  visualDensity: VisualDensity.compact,
                  title: Text("View Participants"),
                  trailing: Icon(Icons.arrow_forward_ios),
                ),
              ),
            ),
            InkWell(
              onTap: () => _onLeaveGroupTapped(context),
              child: Card(
                elevation: Values.v10,
                shadowColor: AppColors.apple.withOpacity(Values.v0_15),
                margin: const EdgeInsets.only(bottom: 10),
                child: const ListTile(
                  visualDensity: VisualDensity.compact,
                  title: Text("Leave"),
                  trailing: Icon(Icons.arrow_forward_ios),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _onLeaveGroupTapped(BuildContext context) async {
    Future.delayed(
      const Duration(seconds: 0),
      () async => await showDialog<void>(
          context: context,
          builder: (context) {
            return CupertinoAlertDialog(
              title: Column(
                children: const [
                  Text("Alert"),
                ],
              ),
              content: const Text("Are you sure you want to leave this group?"),
              actions: [
                CupertinoDialogAction(
                  child: const Text("Yes"),
                  onPressed: () {
                    BlocProvider.of<LeaveGroupBloc>(context).add(
                      LeaveGroupEvent(groupId: groupId),
                    );
                    BlocProvider.of<GroupListBloc>(context).add(
                      GetGroupListEvent(limit: 5),
                    );
                    Navigator.of(context).pop();
                    AppToast.showToast(message: "You left the Group!");
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();
                  },
                ),
                CupertinoDialogAction(
                  child: const Text("No"),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          }),
    );
  }
}
