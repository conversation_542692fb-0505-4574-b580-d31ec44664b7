import 'dart:async';
import 'dart:core';

import 'package:fitsomnia_app/developer_mode.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/camera_v2/ui/camera_v2_page.dart';
import 'package:fitsomnia_app/src/core/services/camera_v2/ui/camera_v2_test.dart';
import 'package:fitsomnia_app/src/core/services/local_storage/cache_service.dart';
import 'package:fitsomnia_app/src/core/widgets/unknown_page.dart';
import 'package:fitsomnia_app/src/features/auth/login/presentation/pages/login_page.dart';
import 'package:fitsomnia_app/src/features/auth/reset_password/presentation/pages/forgot_password_page.dart';
import 'package:fitsomnia_app/src/features/auth/reset_password/presentation/pages/identity_verification_page.dart';
import 'package:fitsomnia_app/src/features/auth/reset_password/presentation/pages/reset_password_page.dart';
import 'package:fitsomnia_app/src/features/auth/sign_up/presentation/pages/sign_up_page.dart';
import 'package:fitsomnia_app/src/features/auth/verification/presentation/pages/verification_page.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/presentation/pages/active_user_list.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/presentation/pages/group_chat_list_page.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/root/presentation/page/chat_dashboard_page.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/pages/add_group_chat_member_page.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/pages/group_chat_members_page.dart';
import 'package:fitsomnia_app/src/features/club/dashboard/presentation/page/club_dashboard_page.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/pages/all_club_members_page.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/pages/all_nearby_clubs_members_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/page/coach_dashboard_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/page/coach_program_enrollment_history_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/page/program_enrollment_details_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_profile_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/pages/coach_list_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/pages/coach_newsfeed_programs_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/pages/coach_offered_programs_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/pages/coach_search_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/pages/coach_program_preview_by_id_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/pages/coach_program_preview_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/pages/create_coach_program_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/widgets/create_coach_program_feature_info_list_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/page/coach_profile_details_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/page/coach_registration_introduction_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/page/create_coach_profile_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/presentation/pages/coach_rating_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_withdraw_payment/presentation/pages/coach_withdraw_payment_page.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/pages/coach_feature_dashboard.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/pages/coach_options_dashboard_page.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/pages/coach_test_page.dart';
import 'package:fitsomnia_app/src/features/coach/student_dashboard/presentation/page/student_dashboard_enrollment_page.dart';
import 'package:fitsomnia_app/src/features/coach/student_dashboard/presentation/page/student_enrollement_details_page.dart';
import 'package:fitsomnia_app/src/features/coach/student_dashboard/presentation/page/student_enrollment_initial_page.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/presentation/page/diet_dashboard_page.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/page/food_dashboard_page.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/page/food_search_page.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/page/search_food_details_page.dart';
import 'package:fitsomnia_app/src/features/diet/food/personalised_food/presentation/view/create_food_page.dart';
import 'package:fitsomnia_app/src/features/diet/food/personalised_food/presentation/view/my_food_dashboard_page.dart';
import 'package:fitsomnia_app/src/features/diet/food/recent_foods/presentation/recent_food_list_page.dart';
import 'package:fitsomnia_app/src/features/diet/food/root/data/model/food_response_model.dart';
import 'package:fitsomnia_app/src/features/diet/settings/update_diet_plan/presentation/view/update_diet_plan_page.dart';
import 'package:fitsomnia_app/src/features/diet/track_diet/presentation/page/track_diet_page.dart';
import 'package:fitsomnia_app/src/features/event/domain/entities/event_entity.dart';
import 'package:fitsomnia_app/src/features/event/presentation/pages/event_detail_page.dart';
import 'package:fitsomnia_app/src/features/event/presentation/pages/event_detail_page_by_event_id.dart';
import 'package:fitsomnia_app/src/features/event/presentation/pages/event_registered_user_info_page.dart';
import 'package:fitsomnia_app/src/features/event/presentation/pages/event_registration_page.dart';
import 'package:fitsomnia_app/src/features/event/presentation/pages/events_page.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/add_new_address/presentation/pages/add_shipping_address.dart';
import 'package:fitsomnia_app/src/features/fit_market/cart/presentations/pages/cart_page.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/presentation/pages/fit_market_dashboard_page.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/presentation/page/wishlist_page.dart';
import 'package:fitsomnia_app/src/features/fit_market/order/presentation/page/order_history_page.dart';
import 'package:fitsomnia_app/src/features/fit_market/payment/presentation/pages/payment_page.dart';
import 'package:fitsomnia_app/src/features/fit_market/product_details/presentation/pages/product_details_page.dart';
import 'package:fitsomnia_app/src/features/fitbot/fitbot_chat/presentation/pages/fitbot_chat_page.dart';
import 'package:fitsomnia_app/src/features/fitbot/fitbot_chat/presentation/pages/fitbot_chat_stream_page.dart';
import 'package:fitsomnia_app/src/features/fitbot/fitbot_chat/presentation/pages/fitbot_landing_page.dart';
import 'package:fitsomnia_app/src/features/fitbot/fitbot_chat/presentation/pages/fitbot_welcome_page.dart';
import 'package:fitsomnia_app/src/features/food_scanner/presentation/pages/food_scanner_page.dart';
import 'package:fitsomnia_app/src/features/food_scanner/presentation/pages/food_scanner_page2.dart';
import 'package:fitsomnia_app/src/features/food_scanner/presentation/pages/scan_food_details_page.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/challenge_details/presentation/page/challenge_details_page.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/challenge_details/presentation/page/preview_recorded_video_page.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/dashboard/presentation/model/challenge_in_progress_view_model.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/dashboard/presentation/page/hall_of_weight_dashboard_page.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/history/challenge_history/presentation/page/challenge_history_page.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/history/points_history/presentation/page/points_history_page.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/leaderboard/presentation/page/leaderboard_page.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/monthly_challenges/presentation/page/all_monthly_challenges_page.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/pages/comment_page.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/pages/create_post_page.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/pages/search_location_page.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/pages/feed_page.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/pages/likers_info_page.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/pages/preview_image_page.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/pages/preview_video_page.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/pages/story_viewers_page.dart';
import 'package:fitsomnia_app/src/features/notification/presentation/pages/notification_page.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/pages/on_boarding_page.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/pages/splash_page.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/pages/welcome_page.dart';
import 'package:fitsomnia_app/src/features/planner/presentation/pages/note_details_page.dart';
import 'package:fitsomnia_app/src/features/planner/presentation/pages/planner_page.dart';
import 'package:fitsomnia_app/src/features/polls/domain/entities/team_poll_entity.dart';
import 'package:fitsomnia_app/src/features/polls/presentation/pages/bpl_team_poll_page.dart';
import 'package:fitsomnia_app/src/features/polls/presentation/pages/poll_voter_info_page.dart';
import 'package:fitsomnia_app/src/features/profile_menu/presentation/page/profile_menu_page.dart';
import 'package:fitsomnia_app/src/features/qr_code/generate_qr_code/presentation/pages/generate_qr_page.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/presentation/page/daily_task_page.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/presentation/page/reward_leaderboard_page.dart';
import 'package:fitsomnia_app/src/features/reward/redeem_point/presentation/pages/redeem_points_page.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/presentation/page/referral_available_page.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/presentation/page/referral_details_page.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/presentation/page/reward_dashboard_page.dart';
import 'package:fitsomnia_app/src/features/search/presentation/page/search_page.dart';
import 'package:fitsomnia_app/src/features/settings/about/presentation/page/about_page.dart';
import 'package:fitsomnia_app/src/features/settings/about/presentation/widget/webivew_page.dart';
import 'package:fitsomnia_app/src/features/settings/notifications/presentation/page/notifications_settings_page.dart';
import 'package:fitsomnia_app/src/features/settings/personal_information/presentation/pages/personal_information_page.dart';
import 'package:fitsomnia_app/src/features/settings/privacy/block_list/presentation/page/block_list_page.dart';
import 'package:fitsomnia_app/src/features/settings/privacy/profile_visibility/presentation/page/profile_visibility_page.dart';
import 'package:fitsomnia_app/src/features/settings/privacy/root/presentation/page/privacy_page.dart';
import 'package:fitsomnia_app/src/features/settings/root/presentation/pages/settings_page.dart';
import 'package:fitsomnia_app/src/features/settings/security/delete_account/presentation/page/delete_account_page.dart';
import 'package:fitsomnia_app/src/features/settings/security/email/presentation/page/change_email_page.dart';
import 'package:fitsomnia_app/src/features/settings/security/password/presentation/page/change_password_page.dart';
import 'package:fitsomnia_app/src/features/settings/security/root/presentation/pages/security_page.dart';
import 'package:fitsomnia_app/src/features/settings/security/unlink_social_account/presentation/pages/social_media_account_page.dart';
import 'package:fitsomnia_app/src/features/settings/subscription/presentation/pages/subscription_error_page.dart';
import 'package:fitsomnia_app/src/features/settings/subscription/presentation/pages/subscriptions_page.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/presentation/pages/spot_not_page.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/presentation/page/spot_distance_filter_page.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_not_filter/presentation/pages/spot_not_filter_page.dart';
import 'package:fitsomnia_app/src/features/support/presentation/pages/support_page.dart';
import 'package:fitsomnia_app/src/features/training/body_building_program_details/presentation/pages/body_building_program_details_page.dart';
import 'package:fitsomnia_app/src/features/training/body_building_program_selection/presentation/pages/body_building_program_selection_page.dart';
import 'package:fitsomnia_app/src/features/training/dashboard/presentation/pages/training_dashboard_page.dart';
import 'package:fitsomnia_app/src/features/training/exercise_category/presentation/pages/exercise_category_page.dart';
import 'package:fitsomnia_app/src/features/training/exercise_details/pages/exercise_details_page.dart';
import 'package:fitsomnia_app/src/features/training/exercise_list/domain/entities/training_exercise_entity.dart';
import 'package:fitsomnia_app/src/features/training/exercise_list/presentation/pages/exercise_list_page.dart';
import 'package:fitsomnia_app/src/features/training/muscle_group_selection/presentation/pages/muscle_group_selection_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RouteGenerator {
  static Route<dynamic>? generateRoute(RouteSettings routeSettings) {
    Log.info('ROUTE name: ${routeSettings.name}');

    // if(routeSettings.name == '/') {
    //   return MaterialPageRoute(builder: (_) => const SplashPage());
    // }

    // // Handle '/details/:id'
    // var uri = Uri.parse(routeSettings.name!);
    // // for (String part in uri.pathSegments) {
    // //   Log.info('ROUTE part: ${part}');
    // // }

    // if (uri.pathSegments.length == 2 && uri.pathSegments.first == 'events') {
    //   var eventId = uri.pathSegments[1];

    //   // when app is terminated state
    //   //https://fitsomnia.com/events/65aefe02-618c-4c03-ac63-5153a61d7f98
    //   if(!routeSettings.name!.startsWith('/events')) {
    //     return MaterialPageRoute(
    //       builder: (_) => SplashPage(), settings: routeSettings);

    //   }

    //   return MaterialPageRoute(
    //       builder: (_) => EventDetailPageByEventId(
    //             eventId: eventId));
    // }

    // if(routeSettings.name == '/') {
    //   return MaterialPageRoute(builder: (_) => const SplashPage());
    // }

    // Handle '/details/:id'
    var uri = Uri.parse(routeSettings.name!);

    if (uri.pathSegments.length == 2 && uri.pathSegments.first == 'events') {
      var eventId = uri.pathSegments[1];

      // when app is terminated state
      //https://fitsomnia.com/events/65aefe02-618c-4c03-ac63-5153a61d7f98
      if (!routeSettings.name!.startsWith('/events')) {
        return MaterialPageRoute(builder: (_) => SplashPage());
        // return null;
      }

      return MaterialPageRoute(
          builder: (_) => EventDetailPageByEventId(eventId: eventId));
    }

    switch (routeSettings.name) {
      case Routes.splash:
        return MaterialPageRoute(builder: (_) => const SplashPage());

      case Routes.onBoarding:
        return MaterialPageRoute(builder: (_) => const OnBoardingPage());

      case Routes.welcome:
        return MaterialPageRoute(builder: (_) => const WelcomePage());

      case Routes.signUp:
        return MaterialPageRoute(builder: (_) => SignUpPage());

      case Routes.login:
        return MaterialPageRoute(builder: (_) => const LoginPage());

      case Routes.fitMarketDashboard:
        return MaterialPageRoute(
            builder: (_) => const FitMarketDashboardPage());

      case Routes.spotNot:
        return MaterialPageRoute(
          builder: (_) => const SpotNotPage(),
          settings: RouteSettings(name: Routes.spotNot),
        );

      case Routes.verification:
        Map<String, dynamic> data =
            routeSettings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => VerificationPage(
            email: data['email'],
          ),
        );

      case Routes.home:
        return MaterialPageRoute(builder: (_) => const FeedPage());

      case Routes.developerMode:
        return MaterialPageRoute(builder: (_) => const DeveloperMode());

      case Routes.notification:
        return MaterialPageRoute(
            builder: (_) => const NotificationPage(
                  activeTab: 0,
                ));

      case Routes.chatList:
        return MaterialPageRoute(builder: (_) => const ChatDashboardPage());

      case Routes.activeUsers:
        return MaterialPageRoute(builder: (_) => const ActiveUserListPage());

      case Routes.groupChat:
        return MaterialPageRoute(builder: (_) => const GroupChatList());

      case Routes.dashboard:
        return MaterialPageRoute(
          builder: (_) => DashboardPage(),
          settings: RouteSettings(name: Routes.dashboard),
        );

      case Routes.previewVideo:
        final path = routeSettings.arguments as String;
        return MaterialPageRoute(
          builder: (_) => PreviewVideoPage(
            path: path,
          ),
        );
      case Routes.previewImage:
        final path = routeSettings.arguments as String;
        return MaterialPageRoute(
          builder: (_) => PreviewImagePage(
            path: path,
          ),
        );
      case Routes.forgotPassword:
        return MaterialPageRoute(
          builder: (_) => ForgotPasswordPage(),
        );

      case Routes.identityVerification:
        return MaterialPageRoute(
          builder: (_) => IdentityVerificationPage(),
        );

      case Routes.search:
        return MaterialPageRoute(
          builder: (_) => const SearchPage(),
        );

      case Routes.settings:
        return MaterialPageRoute(
          builder: (_) => const SettingsPage(),
        );

      case Routes.security:
        return MaterialPageRoute(
          builder: (_) => const SecurityPage(),
        );

      case Routes.changeEmail:
        return MaterialPageRoute(
          builder: (_) => ChangeEmailAddressPage(
            needToAddEmail: routeSettings.arguments as bool,
          ),
        );

      case Routes.changePassword:
        return MaterialPageRoute(
          builder: (_) => const ChangePasswordPage(),
        );

      case Routes.deleteAccount:
        return MaterialPageRoute(
          builder: (_) => const DeleteAccountPage(),
        );

      case Routes.socialMediaAccount:
        return MaterialPageRoute(
          builder: (_) => const SocialMediaAccountPage(),
        );

      case Routes.privacy:
        return MaterialPageRoute(
          builder: (_) => const PrivacyPage(),
        );

      case Routes.profileVisibility:
        return MaterialPageRoute(
          builder: (_) => const ProfileVisibilityPage(),
        );

      case Routes.profileMenu:
        return MaterialPageRoute(
          builder: (_) => const ProfileMenuPage(),
        );

      case Routes.blockList:
        return MaterialPageRoute(
          builder: (_) => const BlockListPage(),
        );

      case Routes.about:
        return MaterialPageRoute(
          builder: (_) => const AboutPage(),
        );

      case Routes.notificationsSettings:
        return MaterialPageRoute(
          builder: (_) => const NotificationsSettingsPage(),
        );

      case Routes.resetPassword:
        return MaterialPageRoute(
          builder: (_) => ResetPasswordPage(),
        );

      case Routes.personalInformation:
        return MaterialPageRoute(
          builder: (_) => const PersonalInformationPage(
            isCameToEdit: true,
          ),
        );

      case Routes.subscription:
        return MaterialPageRoute(
          builder: (_) => const SubscriptionPage(),
        );

      case Routes.spotNotFilter:
        return MaterialPageRoute(
          builder: (_) => const SpotNotFilterPage(),
        );

      case Routes.spotDistanceFilter:
        return MaterialPageRoute(
          builder: (_) => SpotDistanceFilterPage(),
        );
      case Routes.planner:
        return MaterialPageRoute(
          builder: (_) => const PlannerPage(),
        );

      case Routes.supportPage:
        return MaterialPageRoute(
          builder: (_) => const SupportPage(),
        );

      case Routes.noteDetails:
        return MaterialPageRoute(
          builder: (_) => NoteDetailsPage(
            id: routeSettings.arguments.toString(),
          ),
        );

      case Routes.club:
        return MaterialPageRoute(
          builder: (_) => const ClubDashboardPage(),
        );

      case Routes.peopleFromYourClub:
        return MaterialPageRoute(
          builder: (_) => const AllClubMembersPage(),
        );
      case Routes.peopleFromNearByClub:
        return MaterialPageRoute(
          builder: (_) => const AllNearbyClubsMembersPage(),
        );

      case Routes.createPost:
        return MaterialPageRoute(
          builder: (_) => const CreatePostPage(),
        );

      case Routes.comment:
        return MaterialPageRoute(
          builder: (_) => CommentPage(),
        );

      case Routes.generateQR:
        return MaterialPageRoute(
          builder: (_) => GenerateQRPage(),
        );

      case Routes.groupChatMembers:
        return MaterialPageRoute(
          builder: (_) => GroupChatMembersPage(
            groupId: routeSettings.arguments as String,
          ),
        );

      case Routes.addGroupChatMember:
        return MaterialPageRoute(
          builder: (_) => AddGroupChatMemberPage(
            groupId: routeSettings.arguments as String,
          ),
        );

      case Routes.searchLocation:
        return MaterialPageRoute(
          builder: (_) => const SearchLocationPage(),
        );

      /// Hall Of Weight
      case Routes.hallOfWeight:
        return MaterialPageRoute(
          builder: (_) => HallOfWeightDashboard(),
        );

      case Routes.leaderboard:
        return MaterialPageRoute(
          builder: (_) => Leaderboard(),
        );

      case Routes.pointsHistory:
        return MaterialPageRoute(
          builder: (_) => PointsHistory(),
        );

      case Routes.allMonthlyChallenges:
        return MaterialPageRoute(
          builder: (_) => AllMonthlyChallengesPage(),
        );

      case Routes.challengeHistory:
        return MaterialPageRoute(
          builder: (_) => ChallengeHistory(),
        );

      case Routes.challengeDetails:
        ChallengeInProgressViewModel challengeInProgressViewModel =
            routeSettings.arguments as ChallengeInProgressViewModel;
        return MaterialPageRoute(
          builder: (_) => ChallengeDetails(
            challengeInProgressViewModel: challengeInProgressViewModel,
          ),
        );

      case Routes.previewRecordedVideo:
        final path = routeSettings.arguments as String;
        return MaterialPageRoute(
          builder: (_) => PreviewRecordedVideoPage(
            path: path,
          ),
        );

      case Routes.training:
        return MaterialPageRoute(
          builder: (_) => const TrainingDashBoard(),
        );

      case Routes.exerciseCategory:
        return MaterialPageRoute(
          builder: (_) => ExerciseCategoryPage(
            exerciseLevel: routeSettings.arguments as String,
          ),
        );

      case Routes.exerciseList:
        return MaterialPageRoute(
          builder: (_) => ExerciseListPage(
            exerciseNameAndLevel: routeSettings.arguments as List<String>,
          ),
        );

      case Routes.bodybuildingProgramSelection:
        return MaterialPageRoute(
          builder: (_) => const BodyBuildingProgramSelectionPage(),
        );

      case Routes.bodybuildingProgramDetails:
        Map<String, dynamic> map =
            routeSettings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => BodyBuildingProgramDetailsPage(
            week: map['week'],
            programId: map['programId'],
          ),
        );

      case Routes.muscleGroupSelection:
        return MaterialPageRoute(
          builder: (_) => const MuscleGroupSelectionPage(),
        );

      case Routes.exerciseDetails:
        return MaterialPageRoute(
          builder: (_) => ExerciseDetailsPage(
            entity: routeSettings.arguments as TrainingExerciseEntity,
          ),
        );

      /// Diet
      case Routes.dietDashboard:
        List<dynamic> args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) => DietDashboardPage(
                  showStepperOnTop: args[0] as bool,
                  isFromBottom: args[1] as bool,
                ),
            settings: const RouteSettings(name: Routes.dietDashboard));

      case Routes.trackDiet:
        return MaterialPageRoute(
          builder: (_) => const TrackDietPage(),
        );

      case Routes.foodDashboard:
        return MaterialPageRoute(
          builder: (_) => const FoodDashboardPage(),
          settings: const RouteSettings(name: Routes.foodDashboard),
        );

      case Routes.recentlyAddedFood:
        return MaterialPageRoute(
          builder: (_) => const RecentFoodListPage(),
        );

      case Routes.myFoodDashboard:
        return MaterialPageRoute(
          builder: (_) => const MyFoodDashboardPage(),
        );

      case Routes.createFood:
        return MaterialPageRoute(
          builder: (_) => const CreateFoodPage(),
        );

      case Routes.productDetails:
        return MaterialPageRoute(
          builder: (_) => const ProductDetailsPage(),
        );

      case Routes.cart:
        return MaterialPageRoute(
          builder: (_) => const CartPage(),
        );

      case Routes.addShippingAddress:
        return MaterialPageRoute(
          builder: (_) => const AddShippingAddress(),
        );

      case Routes.payment:
        return MaterialPageRoute(
          builder: (_) => const PaymentPage(),
        );

      case Routes.wishlist:
        return MaterialPageRoute(
          builder: (_) => WishlistPage(),
        );

      case Routes.orderHistory:
        return MaterialPageRoute(
          builder: (_) => OrderHistoryPage(),
        );

      case Routes.storyViewers:
        return MaterialPageRoute(
          builder: (_) => StoryViewersPage(
            storyId: routeSettings.arguments as String,
          ),
        );

      case Routes.webView:
        return MaterialPageRoute(
          builder: (_) => WebViewPage(
            url: routeSettings.arguments as String,
          ),
        );

      case Routes.likersInfo:
        List<dynamic> args = routeSettings.arguments as List<dynamic>;
        return CupertinoPageRoute(
          builder: (_) => LikersInfoPage(
            postId: args[0],
            likersInfo: args[1],
          ),
        );

      case Routes.subscriptionError:
        return MaterialPageRoute(
          builder: (_) => const SubscriptionErrorPage(),
        );

      case Routes.updateDiet:
        return MaterialPageRoute(
          builder: (_) => const UpdateDietPlanPage(),
        );

      case Routes.fitbotLanding:
        return MaterialPageRoute(
          builder: (_) => const FitbotLandingPage(),
        );

      case Routes.fitbotChat:
        return MaterialPageRoute(
          builder: (_) => const FitbotChatPage(),
        );

      case Routes.chatbotStreamChat:
        return MaterialPageRoute(
          builder: (_) => const FitbotChatStreamPage(),
        );

      case Routes.fitbotWelcome:
        return MaterialPageRoute(builder: (_) => FitbotWelcomePage());

      case Routes.bplTeamPoll:
        return MaterialPageRoute(builder: (_) => BplTeamPollPage());

      case Routes.pollVoterInfoPage:
        List<dynamic> args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) => PollVoterInfoPage(
                  voters: args[0] as List<PollParticipantInfo>,
                ));

      case Routes.eventsPage:
        return MaterialPageRoute(builder: (_) => EventsPage());

      case Routes.eventDetailsInfoPage:
        List<dynamic> args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) => EventDetailPage(
                  eventEntity: args[0] as EventEntity,
                ));
      case Routes.eventDetailsPageById:
        List<dynamic> args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) => EventDetailPageByEventId(
                  eventId: args[0] as String,
                ));

      case Routes.eventRegistrationPage:
        List<dynamic> args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) => EventRegistrationPage(
                  eventEntity: args[0] as EventEntity,
                ));

      case Routes.eventParticipantInfoPage:
        List<dynamic> args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) => EventRegisteredUserInfoPage(
                  voters: args[0] as List<EventParticipantInfo>,
                ));
      case Routes.coachIntroductionPage:
        return MaterialPageRoute(
            builder: (_) => CoachRegistrationIntroductionPage());

      case Routes.coachRegistrationFormPage:
        return MaterialPageRoute(builder: (_) => CreateCoachProfilePage());

      case Routes.coachProfileEditPage:
        List<dynamic> args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) => CreateCoachProfilePage(
                  coachId: args[0] as String?,
                  isUpdateCoachProfile: args[1] as bool,
                ));

      case Routes.coachFeatureTestPage:
        return MaterialPageRoute(builder: (_) => CoachTestPage());

      case Routes.coachProgramCreatePage:
        return MaterialPageRoute(builder: (_) => CreateCoachProgramPage());

      case Routes.coachProgramPreviewPage:
        List<dynamic> args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) => CoachProgramPreviewPage(
                  isCreatingCoachProgram: args[0] as bool,
                  isUpdateCoachProgram: args[1] as bool,
                  isPreviewCoachProgram: args[2] as bool,
                  programEntity: args[3] as CoachProgramEntity,
                ));

      case Routes.coachProgramPreviewByIdPage:
        List<dynamic> args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) => CoachProgramPreviewByIdPage(
                  programId: args[0] as String,
                  coachId: args[1] as String,
                ));

      case Routes.coachProfileDetailsPage:
        List<dynamic> args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) => CoachProfileDetailsPage(
                  args[0],
                  args[1],
                  args[2],
                ));

      case Routes.coachDashboardPage:
        return MaterialPageRoute(builder: (_) => CoachDashboardPage());

      case Routes.coachProgramEnrollmentHistoryPage:
        List<dynamic> args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) => CoachProgramsEnrollmentHistoryPage(
                coachEntity: args[0] as CoachEntity));

      case Routes.coachProgramEnrolledDetailsPage:
        return MaterialPageRoute(
          builder: (_) => ProgramEnrollmentDetailsPage(
            subscriptionId: routeSettings.arguments as String,
          ),
        );

      case Routes.studentEnrollmentInitialPage:
        return MaterialPageRoute(
            builder: (_) => StudentEnrollmentInitialPage());

      case Routes.studentEnrollmentDashboardPage:
        return MaterialPageRoute(
            builder: (_) => StudentDashboardEnrollmentPage());

      case Routes.studentEnrollmentDetailsPage:
        return MaterialPageRoute(
          builder: (_) => StudentProgramEnrollmentDetailsPage(
            subscriptionId: routeSettings.arguments as String,
          ),
        );

      case Routes.coachNewsfeedProgramsPage:
        return MaterialPageRoute(builder: (_) => CoachNewsfeedProgramsPage());

      case Routes.coachSearchListPage:
        List<dynamic>? args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
          builder: (_) => CoachListPage(
            subcategoryId: args[0] as String?,
            categoryId: args[1] as String?,
            coachProfileFilterType: args[2] as CoachProfileFilterType?,
          ),
        );

      case Routes.coachSearchPage:
        return MaterialPageRoute(builder: (_) => CoachSearchPage());

      case Routes.coachOfferedPrograms:
        return MaterialPageRoute(
          builder: (_) => CoachOfferedProgramsPage(
            coachId: routeSettings.arguments as String,
          ),
        );

      case Routes.coachOptionsDashboardPage:
        return MaterialPageRoute(builder: (_) => CoachOptionsDashboardPage());

      case Routes.coachFeatureDashboardPage:
        return MaterialPageRoute(builder: (_) => const CoachFeatureDashboard());

      case Routes.coachRatigsPage:
        List<dynamic>? args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) => CoachRatingPage(
                  coachId: args[0] as String,
                ));

      case Routes.coachWithdrawPaymentPage:
        return MaterialPageRoute(
            builder: (_) => const CoachWithdrawPaymentPage());

      case Routes.rewardDashboardPage:
        return MaterialPageRoute(builder: (_) => RewardDashboardPage());

      case Routes.rewardLeaderboardPage:
        return MaterialPageRoute(builder: (_) => RewardLeaderboardPage());

      case Routes.referralListPage:
        return MaterialPageRoute(builder: (_) => ReferralAvailablePage());

      case Routes.referralDetailPage:
        List<dynamic>? args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) => ReferralDetailsPage(referralId: args[0] as String));

      case Routes.rewardPointHistoryPage:
        return MaterialPageRoute(builder: (_) => RedeemPointsPage());

      case Routes.dailyTaskPage:
        return MaterialPageRoute(builder: (_) => DailyTaskPage());

      case Routes.cameraV2Page:
        return MaterialPageRoute(
          builder: (_) => CameraV2Page(
            maxRecordingTimeInSecond: 0,
            onImageCaptured: null,
            showMediaIcon: true,
            onlyRecord: false, // Show/hide gallery picker
          ),
        );

      case Routes.cameraV2PageTest:
        return MaterialPageRoute(
          builder: (_) => FoodScannerPage(),
        );

      case Routes.foodScannerPage:
        return MaterialPageRoute(
            builder: (_) => FoodScannerPage2(),
            settings: RouteSettings(name: Routes.foodScannerPage));

      case Routes.scanFoodDetailsPage:
        List<dynamic>? args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) =>
                ScanFoodDetailsPage(foodImageFilePath: args[0] as String),
            settings: RouteSettings(name: Routes.scanFoodDetailsPage));

      case Routes.foodSearchPage:
        return MaterialPageRoute(
            builder: (_) => FoodSearchPage(),
            settings: RouteSettings(name: Routes.foodSearchPage));

      case Routes.searchFoodDetailsPage:
        List<dynamic>? args = routeSettings.arguments as List<dynamic>;
        return MaterialPageRoute(
            builder: (_) => SearchFoodDetailsPage(
                  foodInfo: args[0] as FoodModel,
                ),
            settings: RouteSettings(name: Routes.searchFoodDetailsPage));

      default:
        // return null;
        // return MaterialPageRoute(builder: (_) => const SplashPage());
        return MaterialPageRoute(
          builder: (_) => const UnknownPage(),
        );
    }
  }
}
