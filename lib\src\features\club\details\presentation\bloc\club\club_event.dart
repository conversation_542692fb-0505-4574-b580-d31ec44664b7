part of 'club_bloc.dart';

enum ClubEventType {
  MY_CLUB,
  LEAVE_CLUB,
}

class ClubEvent extends Equatable {
  const ClubEvent();

  @override
  List<Object?> get props => [];
}

class MyClubEvent extends ClubEvent {
  const MyClubEvent({required this.club});

  final ClubEntity club;
}

class LeaveClubEvent extends ClubEvent {
  const LeaveClubEvent({required this.clubId});

  final String clubId;

  @override
  List<Object?> get props => [];
}

class SpotEvent extends ClubEvent {
  const SpotEvent({required this.id});

  final String id;
}
