package com.fitsomnia

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.fitsomnia.fitsomniaApp.R
import com.google.android.gms.ads.nativead.MediaView
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.gms.ads.nativead.NativeAdView
import io.flutter.plugins.googlemobileads.GoogleMobileAdsPlugin

class GoogleNativeAdFactory(val context: Context): GoogleMobileAdsPlugin.NativeAdFactory {
    override fun createNativeAd(
        nativeAd: NativeAd?, //optional is removed
        customOptions: MutableMap<String, Any>?
    ): NativeAdView {
        val nativeAdView = LayoutInflater.from(context).inflate(R.layout.google_native_ad, null) as NativeAdView
        if(nativeAd == null) return nativeAdView

        with(nativeAdView){
            var iconView = findViewById<ImageView>(R.id.ad_app_icon)

            var icon = nativeAd.icon
            if(icon != null) {
                iconView.visibility = View.VISIBLE
                iconView.setImageDrawable(icon.drawable)
            }
            this.iconView = iconView

            var advertiserView = findViewById<TextView>(R.id.ad_advertiser)
            var advertiser = nativeAd.advertiser
            if(advertiser != null) {
                advertiserView.visibility = View.VISIBLE
                advertiserView.text = advertiser
            }
            this.advertiserView = advertiserView

            var headlineView = findViewById<TextView>(R.id.ad_headline)
            var headline = nativeAd.headline;
            if(headline != null){
                headlineView.visibility = View.VISIBLE
                headlineView.text = headline
            } else{
                headlineView.visibility = View.GONE
            }
            this.headlineView = headlineView

            var mediaView = findViewById<MediaView>(R.id.ad_media)
            var media = nativeAd.mediaContent
            if(media != null) {
                mediaView.visibility = View.VISIBLE
                mediaView.mediaContent = media
            } else {
                mediaView.visibility = View.GONE
            }
            this.mediaView = mediaView

            var bodyView = findViewById<TextView>(R.id.ad_body);
            var bodyText = nativeAd.body
            if(bodyText != null) {
                bodyView.visibility = View.VISIBLE
                bodyView.text = bodyText
            } else {
                bodyView.visibility = View.GONE
            }
            this.bodyView = bodyView

            var callToAction = findViewById<Button>(R.id.ad_call_to_action)
            var actionText = nativeAd.callToAction
            if(actionText != null) {
                callToAction.visibility = View.VISIBLE
                callToAction.text = actionText
            }
            this.callToActionView = callToAction

//            var priceView = findViewById<TextView>(R.id.ad_price)
//            var price = nativeAd.price
//            if(price != null){
//                priceView.visibility = View.VISIBLE
//                priceView.text = price
//            } else {
//                priceView.visibility = View.GONE
//            }
//            this.priceView = priceView
//
//            var storeView = findViewById<TextView>(R.id.ad_store)
//            var store = nativeAd.store
//            if(store != null) {
//                storeView.visibility = View.VISIBLE
//                storeView.text = store
//            } else {
//                storeView.visibility = View.GONE
//            }
//            this.storeView = storeView


            setNativeAd(nativeAd)
        }

        return nativeAdView
    }
}