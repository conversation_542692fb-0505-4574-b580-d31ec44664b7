import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/services/media/bloc/media_bloc.dart';
import 'package:fitsomnia_app/src/core/services/media/model/media_model.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:photo_manager/photo_manager.dart';

class MediaItemWidget extends StatefulWidget {
  final MediaModel mediaModel;
  final int selectedIndex;
  final List<AssetEntity> mediaFiles;

  const MediaItemWidget({
    super.key,
    required this.mediaModel,
    required this.selectedIndex,
    required this.mediaFiles,
  });

  @override
  State<MediaItemWidget> createState() => _MediaItemWidgetState();
}

class _MediaItemWidgetState extends State<MediaItemWidget>
    with AutomaticKeepAliveClientMixin {
  bool isSelected = false;
  @override
  void initState() {
    super.initState();

    /// If multiple image selection is disable add the tapped media as selected
    selectMediaOnPreview();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return BlocBuilder<MediaBloc, MediaState>(
      builder: (context, state) {
        return GestureDetector(
          onTap: _onItemTap,
          onLongPress: context.read<MediaBloc>().selectMultipleMedia
              ? _onLongPress
              : null,
          child: _buildImageView(),
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;

  Widget _buildImageView() {
    return Stack(
      children: <Widget>[
        _showMedia(),
        if (context.read<MediaBloc>().previewIndex == widget.selectedIndex)
          _addOverlay(),
        if (isSelected) _addSelectedIcon(),
        if (widget.mediaModel.type == AssetType.video) _addVideoIcon()
      ],
    );
  }

  void _onItemTap() {
    context.read<MediaBloc>().previewIndex = widget.selectedIndex;
    context.read<MediaBloc>().add(
          PreviewSelectedImageEvent(
              widget.mediaModel.thumbnail, widget.mediaFiles),
        );

    /// If multiple image selection is disable add the tapped media as selected
    selectMediaOnPreview();
  }

  void _onLongPress() {
    if (context.read<MediaBloc>().selectedFiles.contains(widget.mediaModel)) {
      context.read<MediaBloc>().selectedFiles.remove(widget.mediaModel);
    } else {
      if (context.read<MediaBloc>().selectedFiles.length >
          context.read<MediaBloc>().maxSelectionLimit) {
        AppToast.showToast(
            message: 'You can\'t select more than '
                '${context.read<MediaBloc>().maxSelectionLimit} items!');

        return;
      } else {
        context.read<MediaBloc>().selectedFiles.add(widget.mediaModel);
      }
      setState(() {
        isSelected = !isSelected;
      });
    }
  }

  Widget _showMedia() {
    return Positioned.fill(
      child: Container(
        decoration:
            BoxDecoration(border: Border.all(color: Colors.blueGrey, width: 1)),
        child: Image.memory(
          widget.mediaModel.thumbnail,
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  Widget _addOverlay() {
    return Align(
        alignment: Alignment.center,
        child: Container(
          color: AppColors.white.withOpacity(Values.v0_6),
        ));
  }

  Widget _addSelectedIcon() {
    return const Align(
        alignment: Alignment.topRight,
        child: Padding(
          padding: EdgeInsets.only(right: 5, bottom: 5, top: 5),
          child: CircleAvatar(
            backgroundColor: Colors.green,
            maxRadius: 12,
            child: Icon(
              Icons.done,
              color: Colors.white,
              size: 16,
            ),
          ),
        ));
  }

  Widget _addVideoIcon() {
    return const Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: EdgeInsets.only(right: 5, bottom: 5),
        child: Icon(
          Icons.videocam,
          color: Colors.white,
        ),
      ),
    );
  }

  void selectMediaOnPreview() {
    if (!context.read<MediaBloc>().selectMultipleMedia) {
      context.read<MediaBloc>().selectedFiles.insert(0, widget.mediaModel);
    }
  }
}
