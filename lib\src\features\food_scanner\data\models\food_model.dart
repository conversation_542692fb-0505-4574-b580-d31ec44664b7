import 'package:fitsomnia_app/src/features/food_scanner/domain/entities/food_info_entity.dart';

class FoodAiModel extends FoodInfoEntity {
  FoodAiModel({
    required String name,
    required String benglaName,
    required String calories,
    required String protein,
    required String carbs,
    required String fats,
    required bool isFound,
    String? servingSize,
    List<String>? ingredients,
    
  }) : super(
          name: name,
          benglaName: benglaName,
          calories: calories,
          protein: protein,
          carbs: carbs,
          fats: fats,
          isFound: isFound,
          servingSize: servingSize,
          ingredients: ingredients,
        );

  // factory FoodModel.fromEntity(FoodInfoEntity entity) {
  //   return FoodModel(
  //     name: entity.name,
  //     calories: entity.calories,
  //     protein: entity.protein,
  //     carbs: entity.carbs,
  //     fats: entity.fats,
  //     servingSize: entity.servingSize,
  //     ingredients: entity.ingredients,
  //   );
  // }

  // factory FoodModel.fromJson(Map<String, dynamic> json) {
  //   return FoodModel(
  //     name: json['name'] == null ? '' : json['name'].toString(),
  //     calories: json['calories'] == null ? '' : json['calories'].toString(),
  //     protein: json['protein'] == null ? '' : json['protein'].toString(),
  //     carbs: json['carbs'] == null ? '' : json['carbs'].toString(),
  //     fats: json['fats'] == null ? '' : json['fats'].toString(),
  //     servingSize: json['servingSize'] as String?,
  //     ingredients: (json['ingredients'] as List<dynamic>?)
  //         ?.map((e) => e as String)
  //         .toList(),
  //   );
  // }

  factory FoodAiModel.fromJson(Map<String, dynamic> json) {
    return FoodAiModel(
      name: json['name'] == null ? 'No Name' : json['name'] as String,
      benglaName: json['knownAs'] == null ? 'No Name' : json['name'] as String,
      calories: json['calories'] == null
          ? ''
          : json['calories'] is num
              ? json['calories'].toString()
              : json['calories'] as String,
      protein: json['protein'] == null
          ? ''
          : json['protein'] is num
              ? json['protein'].toString()
              : json['protein'] as String,
      carbs: json['carb'] == null
          ? ''
          : json['carb'] is num
              ? json['carb'].toString()
              : json['carb'] as String,
      fats: json['fat'] == null
          ? ''
          : json['fat'] is num
              ? json['fat'].toString()
              : json['fat'] as String,
      isFound: json['isFound'] ?? true,
      servingSize: json['servingSize']?.toString(),
      ingredients: (json['foodItems'] as List<dynamic>?)
          ?.map((e) => e.toString())
          .toList(),
    );
  }
}
