import 'dart:io';

import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class GoogleBannerAdsWidget extends StatefulWidget {
  GoogleBannerAdsWidget({
    super.key,
    this.adSize = AdSize.banner,
  });
  final AdSize adSize;
  final String adUnitId = Platform.isAndroid
      ? 'ca-app-pub-3940256099942544/6300978111'
      : 'ca-app-pub-3940256099942544/2934735716';

  @override
  State<GoogleBannerAdsWidget> createState() => _GoogleBannerAdsWidgetState();
}

class _GoogleBannerAdsWidgetState extends State<GoogleBannerAdsWidget> {
  BannerAd? _bannerAd;
  @override
  Widget build(BuildContext context) { // show loaded ads in ui
    return SizedBox(
      width: widget.adSize.width.toDouble(),
      height: widget.adSize.height.toDouble(),
      child: (_bannerAd == null)
          ? const SizedBox()
          : AdWidget(ad: _bannerAd!), // show barrer ads
    );
  }

  @override
  void initState() {
    super.initState();
    _loadAd();
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  // load banner ads from  google ads network
  void _loadAd() {
    final bannerAd = BannerAd(
      size: widget.adSize,
      adUnitId: widget.adUnitId,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          if (!mounted) {
            ad.dispose();
          }

          setState(() {
            _bannerAd = ad as BannerAd;
          });
        },
        onAdFailedToLoad: (ad, error){
          Log.debug('Banner ad fail to load with error: $error');
          ad.dispose();
        }
      ),
    );

    bannerAd.load();
  }
}
