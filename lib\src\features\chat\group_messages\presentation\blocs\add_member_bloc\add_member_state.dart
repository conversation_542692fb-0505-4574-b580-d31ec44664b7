part of 'add_member_bloc.dart';

class AddMemberState extends Equatable {
  @override
  List<Object?> get props => [];
}

class AddMemberInitial extends AddMemberState {}

class AddMemberSuccess extends AddMemberState {
  final String message;

  AddMemberSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class AddMemberFailure extends AddMemberState {
  final String message;

  AddMemberFailure({required this.message});

  @override
  List<Object?> get props => [message];
}
