import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/domain/entity/active_user_entity.dart';

class ActiveUsersModel extends ActiveUserEntity {
  const ActiveUsersModel({
    super.userInfo
  });

  factory ActiveUsersModel.fromJson(Map<String, dynamic> json) => ActiveUsersModel(
    userInfo: json["userInfo"] == null
        ? null
        : UserInfo.fromJson(json["userInfo"]),
  );

  Map<String, dynamic> toJson() => {
    "userInfo":
    userInfo == null ? null : userInfo!.toJson(),
  };
}