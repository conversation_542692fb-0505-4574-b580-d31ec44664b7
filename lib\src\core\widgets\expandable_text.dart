import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

class ExpandableText extends StatefulWidget {
  const ExpandableText({
    Key? key,
    required this.text,
    this.trimLines = 2,
    this.style,
    this.linkStyle,
  }) : super(key: key);

  final String text;
  final int trimLines;
  final TextStyle? style;
  final TextStyle? linkStyle;

  @override
  ExpandableTextState createState() => ExpandableTextState();
}

class ExpandableTextState extends State<ExpandableText> {
  bool _readMore = true;

  void _onTapLink() {
    setState(() => _readMore = !_readMore);
  }

  @override
  Widget build(BuildContext context) {
    TextSpan link = _buildLink();

    Widget result = LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        assert(constraints.hasBoundedWidth);
        final double maxWidth = constraints.maxWidth;
        // Create a TextSpan with data
        final text = TextSpan(
          text: widget.text,
          style: widget.style,
        );
        // Layout and measure link
        TextPainter textPainter = _buildTextPainter(link);
        textPainter.layout(minWidth: constraints.minWidth, maxWidth: maxWidth);
        final linkSize = textPainter.size;
        // Layout and measure text
        textPainter.text = text;
        textPainter.layout(minWidth: constraints.minWidth, maxWidth: maxWidth);
        final textSize = textPainter.size;
        // Get the endIndex of data
        int? endIndex;
        final positionForOffset = textPainter.getPositionForOffset(
          Offset(
            textSize.width - linkSize.width,
            textSize.height,
          ),
        );
        endIndex = textPainter.getOffsetBefore(positionForOffset.offset);
        TextSpan textSpan;
        textSpan = textPainter.didExceedMaxLines
            ? _buildTextSpan(endIndex, link)
            : TextSpan(text: widget.text, style: widget.style);

        return RichText(
          softWrap: true,
          overflow: TextOverflow.clip,
          text: textSpan,
        );

        // return SelectableText.rich(
        //   textSpan,
          
        // );
      },
    );

    return result;
  }

  TextSpan _buildTextSpan(int? endIndex, TextSpan link) {
    return TextSpan(
      text: _readMore ? widget.text.substring(0, endIndex) : widget.text,
      style: widget.style,
      children: <TextSpan>[
        link,
      ],
    );
  }

  TextPainter _buildTextPainter(TextSpan link) {
    return TextPainter(
      text: link,
      textDirection: TextDirection.rtl,
      //better to pass this from master widget if ltr and rtl both supported
      maxLines: widget.trimLines,
      // ellipsis: '...',
    );
  }

  TextSpan _buildLink() {
    return TextSpan(
      text: _readMore ? '...Show More' : '',
      style: widget.linkStyle ??
          AppTypography.regular14(
            color: AppColors.charlestonGreen,
          ),
      recognizer: TapGestureRecognizer()..onTap = _onTapLink,
    );
  }
}
