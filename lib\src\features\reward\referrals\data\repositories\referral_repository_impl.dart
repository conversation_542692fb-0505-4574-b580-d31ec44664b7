import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/exception/network_exception.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/data/data_source/referral_data_source.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/data/model/referral_data_model.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/entity/referral_code_entity.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/entity/referral_data_entity.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/repositories/referral_repository.dart';

class ReferralRepositoryImpl  extends ReferralRepository{
  final ReferralDataSource dataSource;

  ReferralRepositoryImpl({required this.dataSource});
  
  @override
  Future<Either<ErrorModel, ReferralDataEntity>> getReferralById({required String referralId}) async {
    try {
        final response = await dataSource.getReferralById(referralId: referralId);
        final data = response.data['data'];
        ReferralDataEntity referral = ReferralDataModel.fromJson(data).toEntity();

        return Right(referral);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, ReferralCodeEntity>> getReferralCode({required String userId, required String referralId}) async {
    try {
        final response = await dataSource.getReferralCode(userId: userId, referralId: referralId);
        final data = response.data['data'];
        ReferralCodeEntity referralCodeEntity = ReferralCodeEntity.fromJson(data);

        return Right(referralCodeEntity);

        // return Right(ReferralCodeEntity(referralCode: 'text-code', referralId: referralId));
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, List<ReferralDataEntity>>> getReferrals() async {
    try {
        final response = await dataSource.getReferrals();
        final data = response.data['data'];
        List<ReferralDataEntity> referrals = List<ReferralDataEntity>.from(data!.map((x) => ReferralDataModel.fromJson(x).toEntity()));

        return Right(referrals);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, ReferralDataEntity>> useReferralCode({required String userId, required String referralCode}) async {
     try {
        final response = await dataSource.useReferralCode(userId: userId, referralCode: referralCode);
        final data = response.data['data'];
        ReferralDataEntity referralDataEntity = ReferralDataModel.fromJson(data).toEntity();

        //TODO need to update return type
        return Right(referralDataEntity);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }
  
}