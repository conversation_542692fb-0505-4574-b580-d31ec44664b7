import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/repositories/delete_message_repository.dart';

class DeleteMessageUseCase {
  const DeleteMessageUseCase({required this.deleteMessageRepository});

  final DeleteMessageRepository deleteMessageRepository;

  Future<Either<String, String>> call({required String messageId}) async {
    return await deleteMessageRepository.deleteMessage(
      messageId: messageId,
    );
  }
}
