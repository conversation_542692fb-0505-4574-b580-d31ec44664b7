import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/repositories/auth_repository.dart';

class IdentityVerificationUseCase {
  IdentityVerificationUseCase({required this.authRepository});

  final AuthRepository authRepository;

  Future<Either<ErrorModel, bool>> call(Map<String, dynamic> map) async {
    return await authRepository.identityVerification(map);
  }
}
