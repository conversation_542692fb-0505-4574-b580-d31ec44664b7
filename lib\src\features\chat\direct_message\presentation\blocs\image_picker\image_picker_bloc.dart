import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:camera/camera.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/use_cases/pick_image_use_case.dart';

part 'image_picker_event.dart';

part 'image_picker_state.dart';

class ImagePickerBloc extends Bloc<ImagePickerEvent, ImagePickerState> {
  ImagePickerBloc({
    required this.pickImageUseCase,
  }) : super(ImagePickerInitial()) {
    on<ImagePickerEvent>(_onImagePickerEvent);
  }

  late PickImageUseCase pickImageUseCase;

  Future<void> _onImagePickerEvent(
    ImagePickerEvent event,
    Emitter<ImagePickerState> emit,
  ) async {
    try {
      final response = await pickImageUseCase.call(isImageCaptureEvent: event.isImageCapture);
      if (response != null) {
        emit(ImagePickerSuccess(image: response, featureName: event.featureName, fieldName: event.fieldName));
      } else {
        emit(ImagePickerFailure("Try again later."));
      }
    } catch (_) {
      emit(ImagePickerFailure("Try again later."));
    }
  }
}
