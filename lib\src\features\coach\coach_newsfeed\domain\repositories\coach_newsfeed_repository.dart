import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_profile_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_program_search_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';

abstract class CoachNewsfeedRepository {
  Future<Either<ErrorModel, List<CoachProfileEntity>>> getCoachProfiles({
    required CoachProfileFilterType filterType,
    String? namePrefix,
    String? categoryId,
    String? subcategoryId,
    int? offset,
    int? limit,
  });

  Future<Either<ErrorModel, CoachEntity>> getCoachProfileById({
    required String coachId,
  });

  Future<Either<ErrorModel, List<CoachProgramSearchEntity>>> getPrograms({
    required CoachProfileFilterType filterType,
    String? namePrefix,
    int? offset,
    int? limit,
  });
}