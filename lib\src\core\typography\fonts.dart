import 'package:flutter/material.dart';

class FontConstants {
  static const String urbanistFontFamily = 'Urbanist';
  static const String caveatFontFamily = 'Caveat';
  static const String poppinsFontFamily = 'Poppins';
  static const String sfProDisplayFontFamily = 'SF Pro Display';
  static const String promptFontFamily = 'Prompt';
  static const String notoSansBengaliFamily = 'NotoSansBengali';
}

class FontWeightManager {
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
}

class FontSize {
  static const double s8 = 8.0;
  static const double s10 = 10.0;
  static const double s12 = 12.0;
  static const double s14 = 14.0;
  static const double s16 = 16.0;
  static const double s18 = 18.0;
  static const double s20 = 20.0;
  static const double s22 = 22.0;
  static const double s24 = 24.0;
  static const double s32 = 32.0;
  static const double s30 = 30.0;
  static const double s36 = 36.0;
  static const double s40 = 40.0;
  static const double s42 = 42.0;
  static const double s48 = 48.0;
  static const double s50 = 50.0;
  static const double s64 = 64.0;
  static const double s72 = 72.0;
}
