import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:camera/camera.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/camera/app_camera_service.dart';
import 'package:fitsomnia_app/src/core/services/camera/bloc/camera_bloc.dart';
import 'package:fitsomnia_app/src/core/services/camera/bloc/camera_cubit.dart';
import 'package:fitsomnia_app/src/core/services/media/ui/widgets/trimmer_view.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/challenge_details/presentation/page/preview_recorded_video_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';

class CameraFooterWidget extends StatefulWidget {
  const CameraFooterWidget({
    Key? key,
    this.showMediaIcon = true,
    this.onlyRecord = false,
  }) : super(key: key);

  final bool showMediaIcon;
  final bool onlyRecord;

  @override
  State<CameraFooterWidget> createState() => _CameraFooterWidgetState();
}

class _CameraFooterWidgetState extends State<CameraFooterWidget> {
  final ImagePicker _picker = ImagePicker();
  bool isRecording = false;
  double transform = 0;
  Timer? _timer;
  late GestureDetector captureIcon;

  @override
  void initState() {
    super.initState();
    captureIcon = _buildCaptureIcon(null);
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: Values.v0.h,
      child: Container(
        color: Colors.black,
        padding: EdgeInsets.only(top: Values.v5.h, bottom: Values.v5.h),
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: [
            BlocBuilder<CameraBloc, CameraState>(
              builder: (context, state) {
                if (state.cameraController != null) {
                  captureIcon = _buildCaptureIcon(state.cameraController);
                }

                return state.cameraController == null
                    ? const Center(
                        child: CircularProgressIndicator(),
                      )
                    : Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.all(Values.v8.r),
                            child: _timer == null
                                ? const SizedBox()
                                : BlocBuilder<CameraCubit, int>(
                                    builder: (context, timeInSecond) {
                                    return Text(
                                      _getFormattedTimeInSecond(timeInSecond),
                                      style: AppTypography.bold18(
                                          color: AppColors.white),
                                    );
                                  }),
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Opacity(
                                opacity: widget.showMediaIcon ? 1 : 0,
                                child: _buildGalleryIcon(),
                              ),
                              captureIcon,
                              Opacity(
                                opacity: isRecording ? 0 : 1,
                                child: _buildFlipCameraIcon(
                                  state.cameraController!,
                                  doNothing: isRecording,
                                ),
                              ),
                            ],
                          ),
                        ],
                      );
              },
            ),
            SizedBox(
              height: Values.v4.h,
            ),
            isRecording
                ? const SizedBox()
                : Text(
                    widget.onlyRecord
                        ? TextConstants.tapForVideo
                        : TextConstants.holdForVideo,
                    style: AppTypography.bold14(color: AppColors.white),
                    textAlign: TextAlign.center,
                  )
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    if (_timer != null) {
      _timer!.cancel();
    }
    super.dispose();
  }

  Widget _buildGalleryIcon() {
    return Opacity(
      opacity: isRecording ? 0 : 1,
      child: IconButton(
          icon: Icon(Icons.image, color: Colors.white, size: Values.v28.r),
          onPressed: isRecording
              ? null
              : widget.onlyRecord
                  ? () async {
                      await _pickVideo(context);
                    }
                  : () {
                      showDialog(
                          context: context,
                          builder: (context) {
                            return CupertinoAlertDialog(
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  CupertinoButton(
                                    onPressed: () async {
                                      await _pickImage(context);
                                    },
                                    padding: const EdgeInsets.all(10),
                                    child: _buildCupertionIcon(
                                        icon: Icons.photo_camera,
                                        caption: 'Select image'),
                                  ),
                                  CupertinoButton(
                                    onPressed: () async {
                                      await _pickVideo(context);
                                    },
                                    padding: const EdgeInsets.all(10),
                                    child: _buildCupertionIcon(
                                      icon: Icons.videocam,
                                      caption: 'Select video',
                                    ),
                                  ),
                                ],
                              ),
                            );
                          });
                    }),
    );
  }

  Widget _buildCupertionIcon(
      {required IconData icon, required String caption}) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppColors.primaryGreen900,
        ),
        const SizedBox(height: 10),
        Text(
          caption,
          style: AppTypography.regular16(
            color: Colors.black54,
          ),
        ),
      ],
    );
  }

  Future<void> _pickImage(BuildContext context) async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      Future.delayed(Duration.zero, () {
        Navigator.pushNamed(
          context,
          Routes.previewImage,
          arguments: image.path,
        );
      });
    }
  }

  Future<void> _pickVideo(BuildContext context) async {
    final XFile? video = await _picker.pickVideo(source: ImageSource.gallery);
    if (video != null && mounted) {
      if (widget.onlyRecord) {
        final pop = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PreviewRecordedVideoPage(
              path: video.path,
              uploadingChallengeVideo: true,
            ),
          ),
        );
        if (pop == "true" && mounted) Navigator.pop(context, video);
      } else {
        Future.delayed(Duration.zero, () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => TrimmerView(
                File(video.path),
              ),
            ),
          );
        });

        if (mounted) {
          Navigator.pop(context);
        }
      }
    }
  }

  //ignore: long-method
  GestureDetector _buildCaptureIcon(CameraController? cameraController) {
    if (cameraController == null) return GestureDetector();

    return widget.onlyRecord
        ? GestureDetector(
            onTap: () async {
              if (isRecording) {
                setState(() {
                  isRecording = false;
                });
                if (cameraController.value.isRecordingVideo) {
                  XFile file = await cameraController.stopVideoRecording();
                  if (_timer != null) {
                    _timer!.cancel();
                  }

                  if (file != null && mounted) {
                    context.read<CameraCubit>().resetTimerTick();
                    final pop = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PreviewRecordedVideoPage(
                          path: file.path,
                          uploadingChallengeVideo: true,
                        ),
                      ),
                    );
                    if (pop == "true") Navigator.pop(context, file);
                  }
                }
              } else {
                setState(() {
                  isRecording = true;
                });
                if (context.read<CameraBloc>().maxRecordingTimeInSecond > 0) {
                  startTimer();
                }
                await cameraController.startVideoRecording();
              }
            },
            child: isRecording
                ? Icon(
                    Icons.radio_button_on,
                    color: Colors.red,
                    size: Values.v80.r,
                  )
                : Icon(
                    Icons.panorama_fish_eye,
                    color: Colors.white,
                    size: Values.v70.r,
                  ),
          )
        : GestureDetector(
            onTap: () async {
              XFile file = await cameraController.takePicture();
              if (mounted) {
                Navigator.pop(context, file);
              }
            },
            onLongPress: () async {
              setState(() {
                isRecording = true;
              });
              if (context.read<CameraBloc>().maxRecordingTimeInSecond > 0) {
                startTimer();
              }
              await cameraController.startVideoRecording();
            },
            onLongPressUp: () async {
              setState(() {
                isRecording = false;
              });
              if (cameraController.value.isRecordingVideo) {
                XFile file = await cameraController.stopVideoRecording();
                if (_timer != null) {
                  _timer!.cancel();
                }
                if (mounted) {
                  Navigator.pop(context, file);
                  if (mounted) {
                    context.read<CameraCubit>().resetTimerTick();
                  }
                }
              }
            },
            child: isRecording
                ? Icon(
                    Icons.radio_button_on,
                    color: Colors.red,
                    size: Values.v80.r,
                  )
                : Icon(
                    Icons.panorama_fish_eye,
                    color: Colors.white,
                    size: Values.v70.r,
                  ),
          );
  }

  Widget _buildFlipCameraIcon(CameraController cameraController,
      {bool doNothing = false}) {
    return IconButton(
        icon: Transform.rotate(
          angle: transform,
          child: Icon(
            Icons.loop,
            color: Colors.white,
            size: Values.v28.r,
          ),
        ),
        onPressed: !doNothing
            ? () async {
                CameraLensDirection cameraLensDirection =
                    CameraLensDirection.back;
                if (cameraController.description.lensDirection ==
                    CameraLensDirection.back) {
                  cameraLensDirection = CameraLensDirection.front;
                }
                CameraController newController = AppCameraService.instance
                    .selectNewCamera(cameraLensDirection);

                setState(() {
                  transform = transform + pi;
                });
                context
                    .read<CameraBloc>()
                    .add(ChangeCameraEvent(newController));
              }
            : null);
  }

  void startTimer() {
    const oneSec = Duration(seconds: 1);
    _timer = Timer.periodic(
      oneSec,
      (Timer timer) {
        context.read<CameraCubit>().incrementTimerTick();
        if (timer.tick == context.read<CameraBloc>().maxRecordingTimeInSecond) {
          captureIcon.onLongPressUp!.call();
          timer.cancel();
        }
      },
    );
  }

  String _getFormattedTimeInSecond(int timeInSecond) {
    if (timeInSecond <= 9) {
      return '00:0$timeInSecond';
    }

    return '00:$timeInSecond';
  }
}
