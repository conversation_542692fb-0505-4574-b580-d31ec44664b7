import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/program_subscription_fee_payment_info.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/use_case/coach_program_use_case.dart';

part 'coach_program_event.dart';
part 'coach_program_state.dart';

class CoachProgramBloc extends Bloc<CoachProgramEvent, CoachProgramState> {
  CoachProgramBloc({required this.coachProgramUseCase})
      : super(CoachProgramInitial()) {
    on<AddCoachProgramEvent>(_onAddCoachProgramEvent);
    on<GetCoachOwnProgramByIdEvent>(_onGetCoachOwnProgramByIdEvent);
    on<UpdateCoachProgramEvent>(_onUpdateCoachProgramEvent);
    on<SubscribeCoachProgramEvent>(_onSubscribeCoachProgramEvent);
    on<GetPaymentGetwayUrlEvent>(_onGetPaymentGetwayUrlEvent);
  }

  final CoachProgramUseCase coachProgramUseCase;

  FutureOr<void> _onAddCoachProgramEvent(
    AddCoachProgramEvent event,
    Emitter<CoachProgramState> emit,
  ) async {
    try {
      emit(CoachProgramLoading());
      final response = await coachProgramUseCase.createCoachProgram(
          coachId: event.coachId, programEntity: event.programEntity);

      response.fold(
        (l) {
          emit(CoachProgramAddFail(data: l));
        },
        (r) {
          emit(CoachProgramAddSuccess(programEntity: r));
        },
      );
    } catch (e) {
      Log.info(e.toString());

      emit(CoachProgramAddFail(data: e));
    }
  }

  Future<void> _onGetCoachOwnProgramByIdEvent(GetCoachOwnProgramByIdEvent event,
      Emitter<CoachProgramState> emit) async {
    try {
      emit(CoachProgramLoading());
      final response = await coachProgramUseCase.getCoachProgramById(
          coachId: event.coachId, programId: event.programId);

      response.fold(
        (l) {
          emit(GetCoachOwnProgramByIdFail(data: l));
        },
        (r) {
          emit(GetCoachOwnProgramByIdSuccess(programEntity: r));
        },
      );
    } catch (e) {
      Log.info(e.toString());

      emit(GetCoachOwnProgramByIdFail(data: e));
    }
  }

  FutureOr<void> _onUpdateCoachProgramEvent(
      UpdateCoachProgramEvent event, Emitter<CoachProgramState> emit) async {
    try {
      emit(CoachProgramLoading());
      final response = await coachProgramUseCase.updateCoachProgram(
          coachId: event.coachId,
          programId: event.programId,
          programEntity: event.programEntity);

      response.fold(
        (l) {
          emit(UpdateCoachProgramFail(data: l));
        },
        (r) {
          emit(UpdateCoachProgramSuccess(programEntity: r));
        },
      );
    } catch (e) {
      Log.info(e.toString());

      emit(UpdateCoachProgramFail(data: e));
    }
  }

  Future<void> _onSubscribeCoachProgramEvent(SubscribeCoachProgramEvent event, Emitter<CoachProgramState> emit) async {
    try {
      emit(CoachProgramLoading());
      final response = await coachProgramUseCase.subscribeProgram(coachId: event.coachId, programId: event.programId);

      response.fold(
        (l) {
          emit(SubscribeCoachProgramFail(data: l));
        },
        (r) {
          emit(SubscribeCoachProgramSuccess(enrollmentEntity: r));
        },
      );
    } catch (e) {
      Log.info(e.toString());

      emit(SubscribeCoachProgramFail(data: e));
    }
  }

  Future<void> _onGetPaymentGetwayUrlEvent(GetPaymentGetwayUrlEvent event, Emitter<CoachProgramState> emit) async {
    try {
      emit(CoachProgramLoading());
      final response = await coachProgramUseCase.paySubscriptionFee(subscriptionId: event.subscriptionId, paymentTerm: event.paymentTerm);

      response.fold(
        (l) {
          emit(GetPaymentGetwayUrlFail(data: l));
        },
        (r) {
          emit(GetPaymentGetwayUrlSuccess(subscriptionFeePaymentInfo: r));
        },
      );
    } catch (e) {
      Log.info(e.toString());

      emit(GetPaymentGetwayUrlFail(data: e));
    }
  }
}
