import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/forgot_password_use_case.dart';

part 'forgot_password_event.dart';

class ForgotPasswordBloc extends Bloc<ForgotPasswordEvent, BaseState> {
  ForgotPasswordBloc({
    required this.useCase,
  }) : super(InitialState()) {
    on<SendOTPToEmailEvent>(_onSendOTPToEmailEvent);
  }

  final ForgotPasswordUseCase useCase;

  FutureOr<void> _onSendOTPToEmailEvent(event, emit) async {
    emit(const LoadingState());

    Map<String, dynamic> requestBody = {
      if (emailOrPhoneField.text.trim().contains('@'))
        "email": emailOrPhoneField.text.trim(),
      if (!emailOrPhoneField.text.trim().contains('@'))
        "phone": emailOrPhoneField.text.startsWith('+')
            ? emailOrPhoneField.text.trim()
            : '+${emailOrPhoneField.text.trim()}',
    };

    try {
      final response = await useCase.sendOTP(requestBody);

      return response.fold(
        (l) => emit(ErrorState(data: l)),
        (r) => emit(const SuccessState()),
      );
    } on SocketException catch (_) {
      emit(
        const ErrorState(
          data: TextConstants.connectionError,
        ),
      );
    } catch (e) {
      emit(ErrorState(data: e.toString()));
    }
  }

  TextEditingController emailOrPhoneField = TextEditingController();
}
