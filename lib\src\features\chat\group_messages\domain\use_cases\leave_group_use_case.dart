import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/repository/group_repository.dart';

class LeaveGroupUseCase {
  const LeaveGroupUseCase({required this.groupRepository});

  final GroupRepository groupRepository;

  Future<Either<String, String>> call({required String groupId}) async {
    return await groupRepository.leaveGroup(groupId: groupId);
  }
}
