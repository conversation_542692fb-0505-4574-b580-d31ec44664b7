import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/entity/group_list_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/domain/entity/all_users_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/domain/entity/group_response_entity.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/entity/group_chat_entity.dart';

abstract class GroupRepository {
  Future<Either<String, GroupResponseEntity>> createGroup({
    required Map<String, dynamic> mapData,
  });

  Future<Either<String, List<GroupListEntity>>> getGroupList(int? limit);

  Future<Either<String, List<GroupChatEntity>>> getGroupHistory({
    required String groupId,
    int? limit,
    int? offset,
  });

  Future<Either<String, List<AllUsersEntityWithoutProfile>>>
      getGroupMembersList({
    required String groupId,
    int? limit,
    int? offset,
  });

  Future<Either<String, String>> leaveGroup({required String groupId});

  Future<Either<String, String>> addNewMember(
      {required String groupId, required String memberId});
}
