import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_leaderboard_data_entity.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/presentation/bloc/reward_leaderboard_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LeaderboardTopUsersWidget extends StatefulWidget {
  const LeaderboardTopUsersWidget({super.key});

  @override
  State<LeaderboardTopUsersWidget> createState() =>
      _LeaderboardTopUsersWidgetState();
}

class _LeaderboardTopUsersWidgetState extends State<LeaderboardTopUsersWidget> {
  late List<RewardLeaderboardDataEntity> _topUserLists;
  ValueNotifier<bool> showAllUserTopTenValueNotifier = ValueNotifier(true);

  bool _isLoading = false;
  @override
  void initState() {
    // _createTestData();

    _topUserLists = [];
    _isLoading = true;
    BlocProvider.of<RewardLeaderboardBloc>(context).add(
        GetRewardLeaderboardTopUserList(
            limit: 10, offset: 0, filterType: null));

    showAllUserTopTenValueNotifier.addListener(() {
      if (showAllUserTopTenValueNotifier.value) {
        //TODO get all user top ten
        Log.debug('get all user topten');
      } else {
        //TODO get friends top ten
        Log.debug('get friends user topten');
      }
    });

    super.initState();
  }

  _createTestData() {
    _topUserLists = List.generate(10, (index) {
      return testLeaderBoardEntity;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          _builHeaderSection(),
          SizedBox(
            height: Values.v20,
          ),
          _buildUserStandingSection(),
        ],
      ),
    );
  }

  _builHeaderSection() {
    return Row(
      children: [
        _buildTopUserIcon(),
        SizedBox(
          width: 10,
        ),
        Text(
          'Top Users',
          style:
              AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
        ),
        // Spacer(),
        // _buitlAllFriendsSwitch()
      ],
    );
  }

  _buildTopUserIcon() {
    return Container(
      decoration:
          BoxDecoration(borderRadius: BorderRadius.circular(Values.v30)),
      child: Image.asset(Assets.topUserImg),
    );
  }

  _buildUserStandingSection() {
    return BlocListener<RewardLeaderboardBloc, RewardLeaderboardState>(
      listener: (context, state) {
        if (state is GetRewardLeaderboardTopUserListSuccess) {
          setState(() {
            _topUserLists = state.leaderboardEntity;
            _isLoading = false;
          });
        }

        if (state is GetRewardLeaderboardTopUserListFail) {
          setState(() {
            _topUserLists = [];
            _isLoading = false;
          });
        }
      },
      child: (_isLoading)
          ? Center(
              child: CircularProgressIndicator(
                color: AppColors.primaryGreen,
              ),
            )
          : (_topUserLists.isEmpty)
              ? const SizedBox(
                  height: 200,
                  child: Center(
                    child: Text('No user found'),
                  ),
                )
              : Container(
                  color: AppColors.greyscale50,
                  child: ListView.separated(
                    physics:
                        const NeverScrollableScrollPhysics(), // disable top user list scroll
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      _topUserLists[index].rank = index + 1;

                      return _buildTopUserRecord(entity: _topUserLists[index]);
                    },
                    separatorBuilder: (context, index) {
                      return Divider(
                        height: Values.v2,
                        color: AppColors.greyscale100,
                      );
                    },
                    itemCount: _topUserLists.length,
                  ),
                ),
    );
  }

  _buildTopUserRecord({required RewardLeaderboardDataEntity entity}) {
    return RewardLeaderboardUserWidget(userPointEntity: entity);
  }

  _buitlAllFriendsSwitch() {
    return ValueListenableBuilder(
        valueListenable: showAllUserTopTenValueNotifier,
        builder: (context, value, _) {
          return Row(
            children: [_buildAllButton(), _buildFriendsButton()],
          );
        });
  }

  _buildAllButton() {
    return GestureDetector(
      onTap: () {
        showAllUserTopTenValueNotifier.value = true;
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
            vertical: Values.v8, horizontal: Values.v16),
        decoration: BoxDecoration(
          color: (showAllUserTopTenValueNotifier.value)
              ? UIColors.primary
              : AppColors.greyscale50,
          // border: Border.all(color: AppColors.greyscale200),
          borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(Values.v100),
              bottomLeft: Radius.circular(Values.v100)),
        ),
        child: Text(
          'All',
          style: AppTypography.poppinsMedium14(
              color: (showAllUserTopTenValueNotifier.value)
                  ? UIColors.white
                  : UIColors.black),
        ),
      ),
    );
  }

  _buildFriendsButton() {
    return GestureDetector(
      onTap: () {
        showAllUserTopTenValueNotifier.value = false;
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
            vertical: Values.v8, horizontal: Values.v16),
        decoration: BoxDecoration(
          color: (!showAllUserTopTenValueNotifier.value)
              ? UIColors.primary
              : AppColors.greyscale50,
          // border: Border.all(color: AppColors.greyscale200),
          borderRadius: const BorderRadius.only(
              topRight: Radius.circular(100),
              bottomRight: Radius.circular(100)),
        ),
        child: Text(
          'Friends',
          style: AppTypography.poppinsMedium14(
              color: (!showAllUserTopTenValueNotifier.value)
                  ? UIColors.white
                  : UIColors.black),
        ),
      ),
    );
  }
}

class RewardLeaderboardUserWidget extends StatefulWidget {
  const RewardLeaderboardUserWidget({
    super.key,
    required this.userPointEntity,
  });
  final RewardLeaderboardDataEntity userPointEntity;

  @override
  State<RewardLeaderboardUserWidget> createState() =>
      _RewardLeaderboardUserWidgetState();
}

class _RewardLeaderboardUserWidgetState
    extends State<RewardLeaderboardUserWidget> {
  Color _backgroundColor = AppColors.greyscale50;
  Color _nameTextColor = UIColors.primaryGreen950;
  Color _pointTextColor = AppColors.greyscale400;
  int position = 1;

  @override
  void initState() {
    _initializeUserData();
    super.initState();
  }

  _initializeUserData() {
    position = widget.userPointEntity.rank!;

    _backgroundColor = (position == 1)
        ? UIColors.primaryGreen800
        : (position == 2)
            ? UIColors.red900
            : (position == 3)
                ? UIColors.purple950
                : AppColors.greyscale50;

    _nameTextColor = (position > 3) ? UIColors.primaryGreen950 : UIColors.white;
    _pointTextColor = (position > 3) ? AppColors.greyscale400 : UIColors.white;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
          vertical: Values.v5, horizontal: Values.v10),
      decoration: BoxDecoration(
          color: _backgroundColor,
          borderRadius: (position != 1)
              ? null
              : BorderRadius.only(
                  topLeft: Radius.circular(Values.v8),
                  topRight: Radius.circular(Values.v8))),
      child: Row(
        children: [
          _buildRankBatchSection(),
          const SizedBox(
            width: Values.v10,
          ),
          _buildUserImage(),
          const SizedBox(
            width: Values.v10,
          ),
          _buildUserName(),
          _buildTotalPointSection()
        ],
      ),
    );
  }

  _buildUserName() {
    return Expanded(
      child: Text(
        widget.userPointEntity.userName,
        overflow: TextOverflow.ellipsis,
        style: AppTypography.poppinsMedium14(color: _nameTextColor),
      ),
    );
  }

  _buildRankBatchSection() {
    Widget userBatch = _buildGeneralBatch();

    if (position == 1) {
      userBatch = _buildDifferentUserBatch(batchImage: Assets.firstBadge);
    } else if (position == 2) {
      userBatch = _buildDifferentUserBatch(batchImage: Assets.secondBadge);
    } else if (position == 3) {
      userBatch = _buildDifferentUserBatch(batchImage: Assets.thirdBadge);
    }

    return userBatch;
  }

  _buildDifferentUserBatch({required String batchImage}) {
    return Container(
      height: Values.v35,
      width: Values.v35,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(Values.v100),
      ),
      child: Center(child: Image.asset(batchImage)),
    );
  }

  _buildGeneralBatch() {
    String positionStr = ((widget.userPointEntity.rank! < 10) ? '0' : '') +
        '${widget.userPointEntity.rank!}';

    return Container(
      height: Values.v35,
      width: Values.v35,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Values.v100),
          border: Border.all(color: _pointTextColor)),
      child: Center(
          child: Text(
        '${positionStr}',
        style: AppTypography.poppinsSemiBold14(color: _pointTextColor),
      )),
    );
  }

  _buildTotalPointSection() {
    return Row(
      children: [
        Image.asset(
          Assets.rewardPointImg,
          height: Values.v16,
          width: Values.v16,
        ),
        SizedBox(
          width: Values.v10,
        ),
        Text('${widget.userPointEntity.totalPoints} Points',
            style: AppTypography.poppinsSemiBold14(color: _pointTextColor))
      ],
    );
  }

  _buildUserImage() {
    String imageUrl = widget.userPointEntity.userImage;

    return ImageContainer.circularImage(
      image: imageUrl,
      radius: Values.v16,
      showBorder: false,
    );
  }
}
