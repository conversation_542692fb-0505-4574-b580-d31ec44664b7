import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_leaderboard_data_entity.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_point_rank_entity.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/use_case/reward_leaderboard_use_case.dart';

part 'reward_leaderboard_event.dart';
part 'reward_leaderboard_state.dart';

class RewardLeaderboardBloc
    extends Bloc<RewardLeaderboardEvent, RewardLeaderboardState> {

  RewardLeaderboardBloc({required this.useCase})
      : super(RewardLeaderboardInitial()) {
    on<GetRewardLeaderboardTopUserList>(_onGetRewardLeaderboardTopUserList);

    on<GetUserCurrentRank>(_onGetUserCurrentRank);
  }

  final RewardLeaderboardUseCase useCase;


  Future<void> _onGetRewardLeaderboardTopUserList(
      GetRewardLeaderboardTopUserList event,
      Emitter<RewardLeaderboardState> emit) async {
    try {
      emit(RewardLeaderboardLoading());
      final response = await useCase.getTopUser(
          offset: event.offset,
          limit: event.limit,
          filterType: event.filterType);

      response.fold((l) {
        emit(GetRewardLeaderboardTopUserListFail(data: l));
      }, (r) {
        emit(GetRewardLeaderboardTopUserListSuccess(leaderboardEntity: r));
      });
    } catch (e) {
      Log.debug(e.toString());
      emit(GetRewardLeaderboardTopUserListFail(data: e));
    }
  }

  Future<void> _onGetUserCurrentRank(
      GetUserCurrentRank event, Emitter<RewardLeaderboardState> emit) async {
    try {
      emit(RewardLeaderboardLoading());
      final response = await useCase.getUserCurrentRank();

      response.fold((l) {
        emit(GetUserCurrentRankFail(data: l));
      }, (r) {
        emit(GetUserCurrentRankSuccess(rankEntity: r));
      });
    } catch (e) {
      Log.debug(e.toString());
      emit(GetUserCurrentRankFail(data: e));
    }
  }
}
