import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/domain/entities/daily_task.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/presentation/bloc/daily_task_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/presentation/widget/daily_task_card_widget.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/presentation/widget/daily_task_completed_card_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DailyTaskList extends StatefulWidget {
  const DailyTaskList({super.key});

  @override
  State<DailyTaskList> createState() => _DailyTaskListState();
}

class _DailyTaskListState extends State<DailyTaskList> {
  ValueNotifier<bool> showActiveTaskListNotifier = ValueNotifier(true);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    showActiveTaskListNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      // mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeaderTitle(),
        Divider(
          height: 10,
          color: AppColors.greyscale100,
        ),
        Expanded(child: _buildDailyTaskList()),
      ],
    );
  }

  _buildHeaderTitle() {
    return Column(
      children: [
        Row(
          children: [
            _buildDailyTaskIcon(),
            SizedBox(
              width: 10,
            ),
            Text(
              'Daily Tasks',
              style: AppTypography.poppinsSemiBold20(color: UIColors.orange500),
            ),
            // Spacer(),
            // TaskActiveCompleteSelectionWidget(),
          ],
        ),
        SizedBox(
          height: 10,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TaskActiveCompleteSelectionWidget(
              showActiveTasksNotifier: showActiveTaskListNotifier,
            ),
          ],
        ),
        SizedBox(
          height: 10,
        ),
      ],
    );
  }

  _buildDailyTaskIcon() {
    return Container(
      decoration:
          BoxDecoration(borderRadius: BorderRadius.circular(Values.v30)),
      child: Image.asset(
        Assets.daily_task_icon,
      ),
    );
  }

  _buildDailyTaskList() {
    return ValueListenableBuilder(
      valueListenable: showActiveTaskListNotifier,
      builder: (context, value, _) {
        return DailyTaskDataList(key: UniqueKey(), showActiveTaskList: value,);
      }
    );
  }
}

class TaskActiveCompleteSelectionWidget extends StatefulWidget {
  const TaskActiveCompleteSelectionWidget(
      {super.key, required this.showActiveTasksNotifier});
  final ValueNotifier<bool> showActiveTasksNotifier;

  @override
  State<TaskActiveCompleteSelectionWidget> createState() =>
      _TaskActiveCompleteSelectionWidgetState();
}

class _TaskActiveCompleteSelectionWidgetState
    extends State<TaskActiveCompleteSelectionWidget> {
  @override
  Widget build(BuildContext context) {
    return _buildShowTaskSwitch();
  }

  _buildShowTaskSwitch() {
    return ValueListenableBuilder(
        valueListenable: widget.showActiveTasksNotifier,
        builder: (context, value, _) {
          return Row(
            children: [_buildAllButton(), _buildFriendsButton()],
          );
        });
  }

  _buildAllButton() {
    return GestureDetector(
      onTap: () {
        widget.showActiveTasksNotifier.value = true;
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
            vertical: Values.v8, horizontal: Values.v16),
        decoration: BoxDecoration(
          color: (widget.showActiveTasksNotifier.value)
              ? UIColors.primary
              : AppColors.greyscale50,
          // border: Border.all(color: AppColors.greyscale200),
          borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(Values.v100),
              bottomLeft: Radius.circular(Values.v100)),
        ),
        child: Text(
          'Active',
          style: AppTypography.poppinsMedium14(
              color: (widget.showActiveTasksNotifier.value)
                  ? UIColors.white
                  : UIColors.black),
        ),
      ),
    );
  }

  _buildFriendsButton() {
    return GestureDetector(
      onTap: () {
        widget.showActiveTasksNotifier.value = false;
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
            vertical: Values.v8, horizontal: Values.v16),
        decoration: BoxDecoration(
          color: (!widget.showActiveTasksNotifier.value)
              ? UIColors.primary
              : AppColors.greyscale50,
          // border: Border.all(color: AppColors.greyscale200),
          borderRadius: const BorderRadius.only(
              topRight: Radius.circular(100),
              bottomRight: Radius.circular(100)),
        ),
        child: Text(
          'Complete',
          style: AppTypography.poppinsMedium14(
              color: (!widget.showActiveTasksNotifier.value)
                  ? UIColors.white
                  : UIColors.black),
        ),
      ),
    );
  }
}

class DailyTaskDataList extends StatefulWidget {
   DailyTaskDataList({super.key, required this.showActiveTaskList,});

  final bool showActiveTaskList;

  @override
  State<DailyTaskDataList> createState() => _DailyTaskDataListState();
}

class _DailyTaskDataListState extends State<DailyTaskDataList> {
  List<DailyTask> _tasks = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // _tasks = List.generate(5, (index) {
    //   return testDailyTaks;
    // });

    _isLoading = true;

    BlocProvider.of<DailyTaskBloc>(context)
        .add(GetDailyTasks(taskStatusFilter: (widget.showActiveTaskList) ? DailyTaskFilter.ACTIVE.key() : DailyTaskFilter.COMPLETED.key()));

  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<DailyTaskBloc, DailyTaskState>(
      listener: (context, state) {
        if (state is GetDailyTasksSuccess) {
          setState(() {
            _tasks = state.dailyTasks;
            _isLoading = false;
          });
        }

        if (state is GetDailyTasksFail) {
          setState(() {
            _tasks = [];
            _isLoading = false;
          });
        }
      },
      child: (_isLoading)
          ? const Center(
              child: CircularProgressIndicator(
                color: UIColors.primary,
              ),
            )
          : Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_tasks.isNotEmpty) Flexible(child: _buildDailyTaskList()),
                if (_tasks.isEmpty) _buildEmptyTaskSection(),
              ],
            ),
    );
  }

  _buildDailyTaskList() {
    return Padding(
      padding: const EdgeInsets.only(top: Values.v10),
      child: ListView.separated(
        shrinkWrap: true,
        primary: true,
        physics: AlwaysScrollableScrollPhysics(),
        itemCount: _tasks.length,
        itemBuilder: (ctx, index) {
          if (_tasks[index].isCompleted == false)
            return DailyTaskCardWidget(dailyTask: _tasks[index]);

          return DailyTaskCompletedCardWidget(
            dailyTask: _tasks[index],
          );
        },
        separatorBuilder: (BuildContext context, int index) {
          return Divider(
            height: 10,
          );
        },
      ),
    );
  }

  _buildEmptyTaskSection() {
    String emptyMessage = 'No Daily Task';
    if (!widget.showActiveTaskList) {
      emptyMessage = 'No Task Completed Yet';
    }

    return SizedBox(
        height: 200,
        child: Center(
          child: Text(
            emptyMessage,
            style: AppTypography.poppinsMedium16(color: AppColors.greyscale400),
          ),
        ));
  }
}
