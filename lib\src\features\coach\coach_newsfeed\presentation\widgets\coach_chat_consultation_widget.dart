import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/pages/chat_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class CoachChatColsultationWidget extends StatefulWidget {
  const CoachChatColsultationWidget({super.key, required this.coachEntity});
  final CoachEntity coachEntity;

  @override
  State<CoachChatColsultationWidget> createState() =>
      _CoachChatColsultationWidgetState();
}

class _CoachChatColsultationWidgetState
    extends State<CoachChatColsultationWidget> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        SvgPicture.asset(
          Assets.coachConsultBackgroundImg,
          fit: BoxFit.fill,
        ),
        Container(
          // padding: const EdgeInsets.only(left: Values.v20, right: Values.v20, bottom: Values.v20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                // decoration: BoxDecoration(border: Border.all(color: Colors.red)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    IconButton(
                      // iconSize: Values.v18,
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      icon: Icon(Icons.clear_sharp),
                    )
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.only(
                    left: Values.v20, right: Values.v20, bottom: Values.v20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildCoachProfileSection(),
                    SizedBox(
                      height: Values.v10,
                    ),
                    _buildChatForConsultationSection(),
                    Text(
                      'Free consultation with coach',
                      style: AppTypography.poppinsRegular16(
                          color: UIColors.primaryGreen600),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  _buildCoachProfileSection() {
    return Container(
      child: Row(
        children: [
          _buildCoachProfileImage(),
          SizedBox(
            width: Values.v10,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  widget.coachEntity.name,
                  overflow: TextOverflow.clip,
                  maxLines: 2,
                  style: AppTypography.poppinsSemiBold24(
                      color: UIColors.primaryGreen950),
                  // style: TextStyle(fontSize: FontSize.s24, fontFamily: FontConstants.poppinsFontFamily, fontWeight: FontWeight.w600, height: 1.0),
                ),
                Text(
                  'Let\'s discuss what you need to know about the program !',
                  style: AppTypography.poppinsMedium14(
                      color: UIColors.primaryGreen950),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  _buildChatForConsultationSection() {
    return IconButton(
        onPressed: () {
          Log.debug('chat button pressed');
          Navigator.of(context).pop();
          _navigateToChatPage();
        },
        icon: Container(
          padding: EdgeInsets.symmetric(
              vertical: Values.v10, horizontal: Values.v30),
          decoration: BoxDecoration(
              color: UIColors.primaryGreen900,
              borderRadius: BorderRadius.circular(Values.v100)),
          child: Text(
            'Message Now',
            style: AppTypography.poppinsMedium20(color: UIColors.white),
          ),
        ));
  }

  void _navigateToChatPage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(
          title: widget.coachEntity.name,
          fitBuddyId: widget.coachEntity.userId!,
          image: widget.coachEntity.media!.first.url,
        ),
      ),
    );
  }

  _buildCoachProfileImage() {
    if (widget.coachEntity.media == null || widget.coachEntity.media!.isEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(Values.v100),
        child: SizedBox(
          height: Values.v64,
          width: Values.v64,
          child: Image.asset(
            Assets.spotMeNoImage,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    if (widget.coachEntity.media!.first.url == 'string') {
      return ClipRRect(
        borderRadius: BorderRadius.circular(Values.v100),
        child: SizedBox(
          height: Values.v64,
          width: Values.v64,
          child: Image.asset(
            Assets.spotMeNoImage,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    return ImageContainer.circularImage(
      image: widget.coachEntity.media!.first.url,
      radius: Values.v32,
      showBorder: false,
    );
  }

  LinearGradient _buildLinearGradientGreenColor() {
    return const LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        UIColors.primaryGreenGradientStart,
        UIColors.primaryGreenGradientEnd,
      ],
    );
  }
}
