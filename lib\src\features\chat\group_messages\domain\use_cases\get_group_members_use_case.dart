import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/domain/entity/all_users_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/repository/group_repository.dart';

class GetGroupMembersUseCase {
  const GetGroupMembersUseCase({required this.groupRepository});

  final GroupRepository groupRepository;

  Future<Either<String, List<AllUsersEntityWithoutProfile>>> call(
      {required String groupId, int? limit, int? offset}) async {
    return await groupRepository.getGroupMembersList(
        groupId: groupId, limit: limit, offset: offset);
  }
}
