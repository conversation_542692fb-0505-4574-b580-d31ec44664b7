import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class RecentConversationDataSource {
  Future<Response> getRecentConversation(
      {required int? limit, required int? offset});
}

class RecentConversationDataSourceImpl implements RecentConversationDataSource {
  const RecentConversationDataSourceImpl({required this.restClient});

  final RestClient restClient;

  @override
  Future<Response> getRecentConversation(
      {required int? limit, required int? offset}) async {
    final response =
        await restClient.get(APIType.PROTECTED, API.recentConversation, data: {
      if (limit != null) "limit": limit,
      if (offset != null) "offset": offset,
    });

    

    return response;
  }
}
