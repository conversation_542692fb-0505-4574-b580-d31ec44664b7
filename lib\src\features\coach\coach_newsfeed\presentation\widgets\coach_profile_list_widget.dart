import 'dart:async';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_profile_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/best_coach_view_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_profile_search_view_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_subcategory_view_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/coach/root/data/model/coach_program_category_model.dart';
import 'package:fitsomnia_app/src/features/coach/root/domain/entities/coach_program_category_entity.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CoachProfileListWidget extends StatefulWidget {
  const CoachProfileListWidget({super.key, required this.searchText});
  final String searchText;

  @override
  State<CoachProfileListWidget> createState() => _CoachProfileListWidgetState();
}

class _CoachProfileListWidgetState extends State<CoachProfileListWidget> {
  List<CoachProfileEntity> coachProfiles = [];

  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    isLoading = true;
    // if(widget.searchText != '') BlocProvider.of<CoachNewsfeedBloc>(context).add(GetCoachProfilesEvent(filterType: CoachProfileFilterType.NAME, namePrefix: widget.searchText));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachNewsfeedBloc, CoachNewsfeedState>(
      listener: (context, state) {
        if (state is CoachProfilesLoading) {
          Log.debug('coach profiles is loading');
        }

        if (state is GetCoachProfilesSuccess) {
          Log.debug('get best coach profile success');
          setState(() {
            coachProfiles = state.coachProfiles;
            isLoading = false;
          });
        }
        if (state is GetCoachProfilesFail) {
          Log.debug('get best coach profile fail');
          AppToast.showToast(message: 'Failed to get coach profiles');
          setState(() {
            isLoading = false;
          });
        }
      },
      child: Container(
        margin: EdgeInsets.only(
          top: Values.v20,
        ),
        child: _buildSubCategorySection(),
      ),
    );
  }

  _buildSubCategorySection() {
    // coachProfiles = List.generate(5, (index) {
    //   return testBestCoachProfileEntity;
    // });

    return (isLoading)
        ? Center(
            child: CircularProgressIndicator(
              color: UIColors.primary,
            ),
          )
        : (coachProfiles.isEmpty)
            ? Center(child: Text('No Coach Found'))
            : Container(
                child: GridView.builder(
                  physics: const AlwaysScrollableScrollPhysics(),
                  scrollDirection: Axis.vertical,
                  shrinkWrap: true,
                  itemCount: coachProfiles.length,
                  padding: EdgeInsets.all(Values.v5),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 200.0 / 260.0,
                      mainAxisSpacing: Values.v10,
                      crossAxisSpacing: Values.v10),
                  itemBuilder: (BuildContext context, int index) {
                    return CoachProfileSearchViewWidget(
                        coachProfileEntity: coachProfiles[index]);
                  },
                ),
              );
  }
}
