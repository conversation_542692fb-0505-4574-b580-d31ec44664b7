import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/widgets/loading_widget.dart';
import 'package:fitsomnia_app/src/features/ads/google_ads/google_banner_ads.dart';
import 'package:fitsomnia_app/src/features/ads/google_ads/google_native_ads.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/club/club_bloc.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/club_members/club_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/live_members/live_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/nearby_clubs_members/nearby_clubs_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/widgets/google_map_widget.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/widgets/my_club_widget.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/widgets/people_from_nearby_club.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/widgets/people_from_your_club.dart';
import 'package:fitsomnia_app/src/features/spot_not/suggested_spot_profiles/presentation/widgets/suggested_spot_profiles_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ClubPage extends StatefulWidget {
  const ClubPage({
    Key? key,
  }) : super(key: key);

  @override
  State<ClubPage> createState() => _ClubPageState();
}

class _ClubPageState extends State<ClubPage> {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ClubBloc, ClubState>(
      listener: (context, state) {
        if (state is ClubLoadedState) _onClubLoadedState(state);
      },
      buildWhen: (previous, current) => _buildWhen(current),
      builder: (context, state) {
        if (state is ClubLoadedState) {
          return RefreshIndicator(
            color: AppColors.primaryGreen,
            onRefresh: () async => _onRefresh(state),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  MyClubWidget(club: state.club),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const PeopleFromYourClub(),
                        // SuggestedSpotProfilesWidget(),
                        SizedBox(height: 20.h),
                        const PeopleFromNearbyClub(),
                        SizedBox(height: 30.h),
                        const GoogleMapWidget(),
                        SizedBox(height: 30.h),
                        // GoogleBannerAdsWidget(),
                        // GoogleNativeAdsWidget(),
                      ],
                    ),
                  )
                ],
              ),
            ),
          );
        } else if (state is ClubLoadingState) {
          return const LoadingWidget();
        } else if (state is ClubErrorState) {
          return Center(child: Text(state.message));
        } else {
          return const SizedBox();
        }
      },
    );
  }

  bool _buildWhen(ClubState current) {
    return (current is ClubLoadedState ||
        (current is ClubErrorState && current.event == ClubEventType.MY_CLUB) ||
        (current is ClubLoadingState &&
            current.event == ClubEventType.MY_CLUB));
  }

  void _onRefresh(ClubLoadedState state) {
    context.read<ClubMembersCubit>().getClubMembers(
          clubId: state.club.id,
          refresh: true,
        );
    context.read<NearbyClubsMembersCubit>().getNearbyClubsMembers(
          clubId: state.club.id,
          refresh: true,
        );
  }

  void _onClubLoadedState(ClubLoadedState state) {
    context.read<ClubMembersCubit>().getClubMembers(
          clubId: state.club.id,
        );
    context.read<NearbyClubsMembersCubit>().getNearbyClubsMembers(
          clubId: state.club.id,
        );
    context.read<LiveMembersCubit>().getLiveMembers();
  }
}
