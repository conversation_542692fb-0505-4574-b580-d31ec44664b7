import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/entity/group_list_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/repository/group_repository.dart';

class GetGroupListUseCase {
  const GetGroupListUseCase({required this.groupRepository});

  final GroupRepository groupRepository;

  Future<Either<String, List<GroupListEntity>>> call(int? limit) async {
    return await groupRepository.getGroupList(limit);
  }
}