import 'package:camera/camera.dart';
import 'package:fitsomnia_app/src/core/services/camera/app_camera_service.dart';
import 'package:fitsomnia_app/src/core/services/camera/bloc/camera_bloc.dart';
import 'package:fitsomnia_app/src/core/services/camera/bloc/camera_cubit.dart';
import 'package:fitsomnia_app/src/core/services/camera/ui/camera_footer_widget.dart';
import 'package:fitsomnia_app/src/core/services/camera/ui/camera_header_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CameraServiceView extends StatelessWidget {
  const CameraServiceView({
    Key? key,
    this.maxRecordingTimeInSecond = 0,
    this.onMediaIconTap,
    this.showMediaIcon = true,
    this.onlyRecord = false,
  }) : super(key: key);
  final int maxRecordingTimeInSecond;
  final Function? onMediaIconTap;
  final bool showMediaIcon;
  final bool onlyRecord;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => CameraCubit(),
        ),
        BlocProvider(
          create: (context) => CameraBloc(
            maxRecordingTimeInSecond: maxRecordingTimeInSecond,
            onMediaIconTap: onMediaIconTap,
          ),
        ),
      ],
      child: CustomCameraPreview(
        showMediaIcon: showMediaIcon,
        onlyRecord: onlyRecord,
      ),
    );
  }
}

class CustomCameraPreview extends StatefulWidget {
  const CustomCameraPreview({
    Key? key,
    this.showMediaIcon = true,
    this.onlyRecord = false,
  }) : super(key: key);

  final bool showMediaIcon;
  final bool onlyRecord;

  @override
  State<CustomCameraPreview> createState() => _CreateStoryPageState();
}

class _CreateStoryPageState extends State<CustomCameraPreview>
    with WidgetsBindingObserver {
  final CameraController cameraController =
      AppCameraService.instance.selectNewCamera();
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    context.read<CameraBloc>().add(ChangeCameraEvent(cameraController));
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (!cameraController.value.isInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      cameraController.dispose();
    } else if (state == AppLifecycleState.resumed) {
      CameraController newController =
          AppCameraService.instance.selectNewCamera();
      if (mounted) {
        context.read<CameraBloc>().add(ChangeCameraEvent(newController));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            _buildCameraPreview(),
            const CameraHeaderWidget(),
            CameraFooterWidget(
              showMediaIcon: widget.showMediaIcon,
              onlyRecord: widget.onlyRecord,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCameraPreview() {
    return BlocBuilder<CameraBloc, CameraState>(
      builder: (context, state) {
        return state.cameraController == null
            ? const SizedBox()
            : FutureBuilder(
                future: state.cameraController!.initialize(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.done) {
                    return Center(
                      child: AspectRatio(
                        aspectRatio:
                            1 / state.cameraController!.value.aspectRatio,
                        child: CameraPreview(state.cameraController!),
                      ),
                    );
                  }

                  return const SizedBox();
                },
              );
      },
    );
  }
}
