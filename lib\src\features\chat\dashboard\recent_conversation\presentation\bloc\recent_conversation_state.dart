part of 'recent_conversation_bloc.dart';

abstract class RecentConversationState extends Equatable {
  const RecentConversationState();
}

class RecentConversationInitial extends RecentConversationState {
  @override
  List<Object> get props => [];
}

class RecentConversationLoading extends RecentConversationState {
  @override
  List<Object> get props => [];
}

class RecentConversationSuccess extends RecentConversationState {
  const RecentConversationSuccess({required this.chatListEntity});

  final List<ChatListEntity> chatListEntity;

  @override
  List<Object> get props => chatListEntity;
}

class RecentConversationFailure extends RecentConversationState {
  const RecentConversationFailure({required this.errorMessage});

  final String errorMessage;

  @override
  List<Object?> get props => [errorMessage];
}
