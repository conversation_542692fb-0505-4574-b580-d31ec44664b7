import 'dart:io';

import 'package:dio/dio.dart';
import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/exception/network_exception.dart';
import 'package:fitsomnia_app/src/core/global/globals.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/services/local_storage/cache_service.dart';
import 'package:fitsomnia_app/src/core/services/socket/scoket_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/error_screen.dart';
import 'package:fitsomnia_app/src/features/auth/login/presentation/pages/login_page.dart';
import 'package:fitsomnia_app/src/features/settings/subscription/presentation/pages/subscription_error_page.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

import 'end_points.dart';
import 'pretty_dio_logger.dart';

class RestClient {
  late Dio _dio;
  final connectionTimeout = 30000;
  final receiveTimeout = 30000; // 30000

  RestClient() {
    BaseOptions options = BaseOptions(
      baseUrl: API.base,
      connectTimeout: Duration(milliseconds: connectionTimeout),
      receiveTimeout: Duration(milliseconds: receiveTimeout),
    );
    _dio = Dio(options);
  }

  Future<Response<dynamic>> get(
    APIType apiType,
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? headers,
  }) async {
    _setDioInterceptorList();

    final standardHeaders = await _getOptions(apiType);

    return await _dio
        .get(path, queryParameters: data, options: standardHeaders)
        .then((value) => value)
        .catchError(_getDioException);
  }

  Future<Response<dynamic>> getSSE(
    APIType apiType,
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? headers,
  }) async {
    _setDioInterceptorList();

    final standardHeaders = await _getOptions(apiType);

    return await _dio.get<ResponseBody>(path,
        queryParameters: data, options: standardHeaders);
  }

  Future<Response<dynamic>> post(
    APIType apiType,
    String path,
    Map<String, dynamic> data, {
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParams,
  }) async {
    _setDioInterceptorList();

    final standardHeaders = await _getOptions(apiType);
    if (headers != null) {
      standardHeaders.headers?.addAll(headers);
    }

    return await _dio
        .post(
          path,
          data: data,
          options: standardHeaders,
          queryParameters: queryParams,
        )
        .then((value) => value)
        .catchError(_getDioException);
  }

  /// Supports media upload
  Future<Response<dynamic>> postFormData(
    APIType apiType,
    String path,
    Map<String, dynamic> data, {
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParams,
  }) async {
    _setDioInterceptorList();

    final standardHeaders = await _getOptions(apiType);
    standardHeaders.headers?.addAll({
      HttpHeaders.contentTypeHeader: Headers.multipartFormDataContentType,
    });

    return await _dio
        .post(
          path,
          data: FormData.fromMap(data),
          options: standardHeaders,
          queryParameters: queryParams,
        )
        .then((value) => value)
        .catchError(_getDioException);
  }

  Future<Response<dynamic>> patch(
    APIType api,
    String path,
    Map<String, dynamic> data, {
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParams,
  }) async {
    _setDioInterceptorList();

    final standardHeaders = await _getOptions(api);
    if (headers != null) {
      standardHeaders.headers?.addAll(headers);
    }

    return await _dio
        .patch(
          path,
          data: data,
          options: standardHeaders,
          queryParameters: queryParams,
        )
        .then((value) => value)
        .catchError(_getDioException);
  }

  Future<Response<dynamic>> put(
    APIType apiType,
    String path,
    Map<String, dynamic> data, {
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParams,
  }) async {
    _setDioInterceptorList();

    final standardHeaders = await _getOptions(apiType);
    if (headers != null) {
      standardHeaders.headers?.addAll(headers);
    }

    return await _dio
        .put(
          path,
          data: data,
          options: standardHeaders,
        )
        .then((value) => value)
        .catchError(_getDioException);
  }

  Future<Response<dynamic>> delete(
    APIType apiType,
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParams,
  }) async {
    _setDioInterceptorList();

    final standardHeaders = await _getOptions(apiType);
    if (headers != null) {
      standardHeaders.headers?.addAll(headers);
    }

    return await _dio
        .delete(
          path,
          data: data,
          queryParameters: queryParams,
          options: standardHeaders,
        )
        .then((value) => value)
        .catchError(_getDioException);
  }

  /// Supports media upload
  Future<Response<dynamic>> putFormData(
    APIType apiType,
    String path,
    Map<String, dynamic> data, {
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParams,
  }) async {
    _setDioInterceptorList();

    final standardHeaders = await _getOptions(apiType);
    if (headers != null) {
      standardHeaders.headers?.addAll({
        HttpHeaders.contentTypeHeader: Headers.multipartFormDataContentType,
      });
    }
    data.addAll({
      '_method': 'PUT',
    });

    return await _dio
        .post(
          path,
          data: FormData.fromMap(data),
          options: standardHeaders,
        )
        .then((value) => value)
        .catchError(_getDioException);
  }

  /// Upload file in s3bucket
  Future<Response> fileUploadInS3Bucket({
    required String preAssignedUrl,
    required File file,
  }) async {
    return await _dio.put(
      preAssignedUrl,
      data: file.openRead(),
      options: Options(
        headers: {
          Headers.contentLengthHeader: await file.length(),
          Headers.contentTypeHeader: Headers.jsonContentType,
          'x-ms-blob-type': 'BlockBlob',
        },
      ),
    );
  }

  dynamic _getDioException(error) {
    if (error is DioException) {
      _dioError(error);
    } else {
      throw UnexpectedException(000, 'Something went wrong. Please try again.');
    }
  }

  //ignore: long-method
  void _dioError(DioException error) {
    Log.error(
      'DIO ERROR: ${error.type} ENDPOINT: ${error.requestOptions.baseUrl}${error.requestOptions.path}',
    );
    String endPoint = error.requestOptions.path;

    switch (error.type) {
      case DioExceptionType.cancel:
        _handleAllError(endPoint: endPoint);
        throw RequestCancelledException(
          001,
          TextConstants.defaultErrorMessage,
        );
      case DioExceptionType.connectionError:
      case DioExceptionType.connectionTimeout:
        ErrorResponseModel errorModel = ErrorResponseModel(
          error: ErrorModel(
            code: ExceptionConstant.exc408.toString(),
            message: TextConstants.couldNotConnectToServer,
          ),
        );

        _handleAllError(endPoint: endPoint);
        throw RequestTimeoutException(errorModel);
      case DioExceptionType.unknown:
        _handleAllError(endPoint: endPoint);
        throw DefaultException(
          ErrorResponseModel(
            error: ErrorModel(
              code: '500',
              message: TextConstants.defaultErrorMessage,
            ),
          ),
        );
      case DioExceptionType.receiveTimeout:
        _handleAllError(endPoint: endPoint);
        throw ReceiveTimeoutException(
          004,
          TextConstants.couldNotConnectToServer,
        );
      case DioExceptionType.sendTimeout:
        ErrorResponseModel errorModel = ErrorResponseModel(
          error: ErrorModel(
            code: ExceptionConstant.exc408.toString(),
            message: TextConstants.couldNotConnectToServer,
          ),
        );

        _handleAllError(endPoint: endPoint);
        throw RequestTimeoutException(errorModel);
      case DioExceptionType.badCertificate:
      case DioExceptionType.badResponse:
        ErrorResponseModel errorModel = ErrorResponseModel.fromJson(
          error.response!.data,
        );

        String message = errorModel.error!.message!;
        _networkException(errorModel, error.response?.statusCode, message,
            endPoint: endPoint);
    }
  }

  void _networkException(
    ErrorResponseModel errorModel,
    int? statusCode,
    String message, {
    required String endPoint,
  }) {
    switch (statusCode) {
      case 400:
        Log.debug('error code: ${errorModel.error!.code!}');

        if (message != 'Invalid credentials' &&
            errorModel.error!.code! != "REFERRER_CODE_NOT_FOUND" &&
            errorModel.error!.code! != "SELF_REFERRAL_NOT_ALLOWED" &&
            errorModel.error!.code! != 'DUPLICATE_REFERRAL_USE'&&
            errorModel.error!.code! != 'DIFFERENT_REFERRER_NOT_ALLOWED') {
          _handleAllError(endPoint: endPoint);
        }
        throw BadRequestException(errorModel);

      case 403:
        // _handleAllError(endPoint: endPoint);
        throw UnauthorisedException(errorModel);
      case 401:
        _handleUnauthorizedException();
        _handleAllError(endPoint: endPoint);
        throw UnauthorisedException(errorModel);

      case 402:
        _handleSubscriptionError();
        throw RequiredSubscriptionException(errorModel);

      case 404:
        throw NotFoundException(errorModel);
      case 409:
        _handleAllError(endPoint: endPoint);
        throw ConflictException(409, message);
      case 500:
        _handleAllError(endPoint: endPoint);
        throw InternalServerException(500, message);
      case 502:
        _handleAllError(endPoint: endPoint);
        throw BadGatewayException(errorModel);
      default:
        _handleAllError(endPoint: endPoint);
        throw DefaultException(
          ErrorResponseModel(
            error: ErrorModel(
              code: '500',
              message: TextConstants.defaultErrorMessage,
            ),
          ),
        );
    }
  }

  void _handleUnauthorizedException() {
    try {
      CacheService.instance.clear();
      SocketService.instance.disconnect();
      notificationCount.value = 0;
      chattingCount.value = 0;
    } catch (e) {
      Log.error(e.toString());
    }

    navigatorKey?.currentState?.pushAndRemoveUntil(
      MaterialPageRoute(builder: (_) => const LoginPage()),
      (route) => false,
    );
  }

  Future<void> _handleAllError({required String endPoint}) async {
    if (endPoint != API.validateAddress &&
        endPoint != API.forgotPasswordSendOTP &&
        endPoint != API.identityVerification &&
        endPoint != API.verification &&
        endPoint != API.registration &&
        endPoint != API.changeEmailRequestForOTP &&
        endPoint != API.changeEmailVerifyOTP &&
        !endPoint.contains('/rating') &&
        !endPoint.contains('/user-activity-streak')) {
      bool result = await InternetConnection().hasInternetAccess;
      Log.debug('internet status: ${result}');
      if (!result && !noInternetScreenActive.value) {
        navigatorKey?.currentState?.push(
          MaterialPageRoute(
            builder: (_) => const ErrorScreen(
              noInternet: true,
            ),
          ),
        );
      } else if (!noInternetScreenActive.value && !errorScreenActive.value) {
        navigatorKey?.currentState?.push(
          MaterialPageRoute(
            builder: (_) => const ErrorScreen(
              noInternet: false,
            ),
          ),
        );
      }
    }
  }

  Future<void> _handleSubscriptionError() async {
    navigatorKey?.currentState?.push(
      MaterialPageRoute(
        builder: (_) => const SubscriptionErrorPage(),
      ),
    );
  }

  void _setDioInterceptorList() {
    List<Interceptor> interceptorList = [];
    _dio.interceptors.clear();

    if (kDebugMode) {
      interceptorList.add(PrettyDioLogger());
    }
    _dio.interceptors.addAll(interceptorList);
  }

  Future<Options> _getOptions(APIType api) async {
    String? token = await CacheService.instance.retrieveBearerToken();
    switch (api) {
      case APIType.PUBLIC:
        return PublicApiOptions().options;

      case APIType.PROTECTED:
        return ProtectedApiOptions(token!).options;

      case APIType.PROTECTED_STREAM:
        return ProtectedStreamApiOptions(token!).options;

      default:
        return PublicApiOptions().options;
    }
  }
}

abstract class ApiOptions {
  Options options = Options();
}

//PUBLIC => Generic API url without access token
//PROTECTED => Generic API url with access token
enum APIType { PUBLIC, PROTECTED, PROTECTED_STREAM }

class PublicApiOptions extends ApiOptions {
  PublicApiOptions() {
    super.options.headers = <String, dynamic>{
      HttpHeaders.acceptHeader: Headers.jsonContentType,
      HttpHeaders.contentTypeHeader: Headers.jsonContentType,
    };
  }
}

class ProtectedApiOptions extends ApiOptions {
  ProtectedApiOptions(String apiToken) {
    super.options.headers = <String, dynamic>{
      HttpHeaders.acceptHeader: Headers.jsonContentType,
      HttpHeaders.contentTypeHeader: Headers.jsonContentType,
      HttpHeaders.authorizationHeader: 'Bearer $apiToken',
    };
  }
}

class ProtectedStreamApiOptions extends ApiOptions {
  ProtectedStreamApiOptions(String apiToken) {
    super.options.headers = <String, dynamic>{
      HttpHeaders.acceptHeader: 'text/event-stream',
      HttpHeaders.contentTypeHeader: Headers.jsonContentType,
      HttpHeaders.authorizationHeader: 'Bearer $apiToken'
    };

    super.options.responseType = ResponseType.stream;
  }
}
