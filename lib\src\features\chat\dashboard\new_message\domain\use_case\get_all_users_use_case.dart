import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/domain/entity/all_users_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/domain/repository/all_users_repository.dart';

class GetAllUsersUseCase {
  const GetAllUsersUseCase({required this.allUserRepository});

  final AllUsersRepository allUserRepository;

  Future<Either<String, List<AllUsersEntity>>> call({String name = ""}) async {
    return await allUserRepository.getAllUsers(name: name);
  }
}