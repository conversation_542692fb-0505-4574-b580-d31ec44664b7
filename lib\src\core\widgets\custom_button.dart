import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final Color textColor;
  final Color backgroundColor;
  final double fontSize;
  final Function onPressed;
  final double width;
  final double height;
  final Color borderColor;


  const CustomButton({
    required this.text,
    this.textColor = Colors.white,
    this.backgroundColor = Colors.blue,
    this.fontSize = 12.0,
    required this.onPressed,
    this.width = double.infinity,
    this.height = Values.v35,
    this.borderColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: MaterialButton(
        onPressed: () {
          onPressed();
        },
        color: backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5.0),
          side: BorderSide(color: borderColor),
        ),
        elevation: 0,
        child: Text(
          text,
          style: AppTypography.regular12(
            color: textColor,
          ).copyWith(fontSize: fontSize),
        ),
      ),
    );
  }
}
