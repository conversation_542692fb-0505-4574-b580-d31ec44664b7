import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/domain/entity/active_user_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/presentation/bloc/online_fit_buddy_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/presentation/widget/user_avatar.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/pages/chat_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ActiveUsers extends StatefulWidget {
  const ActiveUsers({Key? key}) : super(key: key);

  @override
  State<ActiveUsers> createState() => _ActiveUsersState();
}

class _ActiveUsersState extends State<ActiveUsers> {
  @override
  void initState() {
    super.initState();
    BlocProvider.of<OnlineFitBuddyBloc>(context).add(
      const OnlineFitBuddyEvent(limit: 5, offset: 0),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<OnlineFitBuddyBloc, OnlineFitBuddyState>(
      listener: (context, state) {
        if (state is OnlineFitBuddyInitial) {
          BlocProvider.of<OnlineFitBuddyBloc>(context).add(
            const OnlineFitBuddyEvent(limit: 5, offset: 0),
          );
        }
      },
      buildWhen: (previous, current) => previous != current,
      builder: (context, state) {
        return state is OnlineFitBuddySuccess
            ? state.activeUserEntity.isEmpty
                ? const SizedBox.shrink()
                : Container(
                    padding: EdgeInsets.only(
                      left: Values.v16.w,
                      top: Values.v5.h,
                      right: Values.v16.w,
                      bottom: Values.v11.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      border: Border(
                        bottom: BorderSide(
                          color: AppColors.black.withOpacity(Values.v0_1),
                        ),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(right: Values.v10.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                TextConstants.activePeople,
                                style: AppTypography.regular14(),
                              ),
                              InkWell(
                                onTap: () =>
                                    _navigateToActiveUsersListPage(context),
                                child: Text(
                                  TextConstants.seeAll,
                                  style: AppTypography.regular12(
                                    color: AppColors.apple,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: Values.v15.h),
                        SizedBox(
                          height: Values.v80.h,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: state.activeUserEntity.length,
                            itemBuilder: (context, index) {
                              return _buildActiveUserWidget(
                                context,
                                user: state.activeUserEntity[index],
                              );
                            },
                          ),
                        )
                      ],
                    ),
                  )
            : const SizedBox.shrink();
      },
    );
  }

  Widget _buildActiveUserWidget(
    BuildContext context, {
    required ActiveUserEntity user,
  }) {
    return GestureDetector(
      onTap: () => _navigateToChatPage(context, user),
      child: Container(
        margin: EdgeInsets.only(right: Values.v20.w),
        width: Values.v58.w,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            UserAvatar(
              image: user.userInfo!.image!.profile!,
              isActive: true,
            ),
            SizedBox(height: Values.v7.h),
            Text(
              user.userInfo!.name!,
              style: AppTypography.regular10(
                color: AppColors.black,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToActiveUsersListPage(BuildContext context) {
    Navigator.pushNamed(
      context,
      Routes.activeUsers,
    );
  }

  void _navigateToChatPage(BuildContext context, ActiveUserEntity user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(
          title: user.userInfo!.name!,
          fitBuddyId: user.userInfo!.id!,
          image: user.getActiveUsersImage(),
        ),
      ),
    );
  }
}
