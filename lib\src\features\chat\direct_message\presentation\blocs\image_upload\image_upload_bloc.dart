import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/use_cases/get_image_url_use_case.dart';

part 'image_upload_event.dart';

part 'image_upload_state.dart';

class ImageUploadBloc extends Bloc<ImageUploadEvent, ImageUploadState> {
  ImageUploadBloc({required this.getImageUrlUseCase})
      : super(ImageUploadInitial()) {
    on<ImageUploadEvent>(_onImageUploadEvent);
  }

  late GetImageUrlUseCase getImageUrlUseCase;

  Future<void> _onImageUploadEvent(
    ImageUploadEvent event,
    Emitter<ImageUploadState> emit,
  ) async {
    emit(ImageUploadLoading());
    try {
      final response = await getImageUrlUseCase.call(
        filePath: event.filePath,
        file: event.file,
        featureName: event.featureName,
      );

      if (response != null) {
        emit(
          ImageUploadSuccess(
            imageUrl: response.s3UploadedURLKey!,
            filePath: event.filePath,
            featureName: event.featureName,
            fieldName: event.fieldName,
          ),
        );
      } else {
        emit(ImageUploadFailure(
          "Try again later.",
          featureName: event.featureName,
          fieldName: event.fieldName,
        ));
      }
    } catch (_) {
      emit(ImageUploadFailure(
        "Try again later.",
        featureName: event.featureName,
        fieldName: event.fieldName,
      ));
    }
  }
}
