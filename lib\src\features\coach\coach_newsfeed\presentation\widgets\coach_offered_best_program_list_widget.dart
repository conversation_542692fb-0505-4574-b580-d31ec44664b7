import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_offered_program_view_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

class CoachOfferedBestProgramsWidget extends StatefulWidget {
  CoachOfferedBestProgramsWidget({super.key, required this.coachId});
  final String coachId;

  @override
  State<CoachOfferedBestProgramsWidget> createState() =>
      _CoachOfferedBestProgramsWidgetState();
}

class _CoachOfferedBestProgramsWidgetState
    extends State<CoachOfferedBestProgramsWidget> {
  List<CoachProgramEntity> _allOfferedBestPrograms = [];
  // List<CoachProgramEntity> testPrograms = List.generate(1, (index) {
  //   return testCoachProgram;
  // });

  @override
  void initState() {
    super.initState();

    BlocProvider.of<CoachBloc>(context)
        .add(GetCoachOfferedBestPrograms(coachId: widget.coachId));
    // _allOfferedPrograms = testPrograms; //TODO: for testing
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachBloc, CoachState>(
      listener: (context, state) {
        if (state is GetCoachOfferedBestProgramsSuccess) {
          Log.debug('get coach offered program success');
          setState(() {
            _allOfferedBestPrograms = state.programs;
          });
        }

        if (state is GetCoachOfferedBestProgramsFail) {
          Log.debug('get coach offered program fail');
        }
      },
      child: Container(
        margin: EdgeInsets.only(top: Values.v40),
        child: Column(
          children: [
            _buildProgramSectionHeader(
              Assets.coachProgramEnrolledIcon,
              'Best Program',
              showCount: true,
              iconColor: UIColors.primaryGreen100,
              textColor: UIColors.purple500,
            ),
            _buildStudentProgramsEnrollmentList(),
          ],
        ),
      ),
    );
  }

  _buildProgramSectionHeader(
    String icon,
    String title, {
    required bool showCount,
    Color? iconColor,
    Color? textColor,
  }) {
    return Row(
      children: [
        // Container(
        //   margin: const EdgeInsets.only(right: Values.v4),
        //   decoration: BoxDecoration(
        //     color: (iconColor != null) ? iconColor : UIColors.primaryGreen100,
        //     borderRadius: BorderRadius.circular(Values.v36),
        //   ),
        //   child: Center(
        //     child: SvgPicture.asset(
        //       icon,
        //       height: Values.v36,
        //       width: Values.v36,
        //     ),
        //   ),
        // ),
        Text(
          '$title ${(showCount && _allOfferedBestPrograms.isNotEmpty) ? '(${_allOfferedBestPrograms.length})' : ''}',
          style: AppTypography.poppinsSemiBold20(
              color:
                  (textColor != null) ? textColor : UIColors.primaryGreen950),
        ),
      ],
    );
  }

  _buildStudentProgramsEnrollmentList() {
    return (_allOfferedBestPrograms.isEmpty)
        ? Container(
            height: 100,
            width: double.infinity,
            child: Center(
              child: Text(
                'No program found',
                style:
                    AppTypography.poppinsRegular16(color: AppColors.greyscale400),
              ),
            ),
          )
        : Column(
            children: _allOfferedBestPrograms.map<Widget>((program) {
              return CoachOfferedProgramViewWidget(
                programEntity: program,
                isBestProgram: true,
              );
            }).toList(),
          );
  }
}
