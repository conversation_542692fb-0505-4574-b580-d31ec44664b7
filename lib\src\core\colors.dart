import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';

class UIColors {
  // static const Color background = Color(0xFFF9FDFF);
  static const Color background = Colors.white;
  static const Color transparent = Colors.transparent;
  static const Color white = Colors.white;
  static const Color black = Colors.black;
  static Color black50 = Colors.black.withOpacity(.50);
  static Color black10 = Colors.black.withOpacity(.10);
  static Color black05 = Colors.black.withOpacity(.05);
  // static const Color primary = Color(0xFF6AB557);
  static const Color primary = const Color(0xFF1EA951); // Color(0xFF25D366);
  static const Color secondary = Color(0xFFCDFFE0);
  static const Color grey = Color(0xFFD9D9D9);
  static const Color greyscale200 = const Color(0xFFC2C5CC);
  static const Color fitBlack = Color(0xFF21212F);
  static const Color fitGrey = Color(0xFFB3B3B3);
  static const Color navGrey = Color(0xFFF1F1F1);
  static const Color strokeGrey = Color(0xFFF1F1F1);
  static const Color splashTextBackground = Color(0xFF1EA952);
  static const Color sliderGrey = Color(0xFFEFEFEF);
  // static Color secondary = const Color(0xFF6AB557).withOpacity(.2);
  static Color disabled = const Color(0xFFAAAAAA);
  static Color commentDoveGray = const Color(0xFF6A6A6A).withOpacity(.50);

  static Color purple50 = const Color(0xFFEEF2FF);
  static Color purple200 = const Color(0xFFC7D2FE);
  static Color purple500 = const Color(0xFF6366F1);
  static Color purple950 = const Color(0xFF1E1B4B);

  static Color orange50 = const Color(0xFFFFF7ED);
  static Color orange200 = const Color(0xFFFED7AA);
  static Color orange400 = const Color(0xFFFB923C);
  static Color orange500 = const Color(0xFFF97316);

  static Color yellow50 = const Color(0xFFFEFCE8);
  static Color yellow400 = const Color(0xFFFACC15);

  static Color skyBlue50 = const Color(0xFFF0F9FF);
  static Color skyBlue500 = const Color(0xFF0EA5E9);

  static Color red50 = const Color(0xFFFEF2F2);
  static Color red500 = const Color(0xFFEF4444);
  static Color red600 = const Color(0xFFDC2626);
  static Color red900 = const Color(0xFF7F1D1D);

  static Color limeGreen500 = const Color(0xFF84CC16);

  static const Color primaryGreen50 = const Color(0xFFE9FBF0);
  static const Color primaryGreen100 = const Color(0xFFD4F7E1);
  static const Color primaryGreen200 = const Color(0xFFA4EFBF);
  static const Color primaryGreen500 = const Color(0xFF25D366);
  static const Color primaryGreen400 = const Color(0xFF4DE083);
  static const Color primaryGreen600 = const Color(0xFF1EA951);
  static const Color primaryGreen700 = const Color(0xFF167E3C);
  static const Color primaryGreen800 = const Color(0xFF0E5227);
  static const Color primaryGreen900 = const Color(0xFF082B15);
  static const Color primaryGreen950 = const Color(0xFF04160A);
  static const Color primaryGreenGradientStart = Color(0xFF99FFBE);
  static const Color primaryGreenGradientEnd = Color(0xFF00FF5E);
  static Color blueLight = const Color(0xFF4F46E5);
  static Color blueDeep = const Color(0xFF2C277F);
  static Color greenLight = const Color(0xFFE7EF00);
  static Color greenDeep = const Color(0xFF00732A);
}

class AppColors {
  static const Color transparent = Colors.transparent;

  static final Color cyan = _HexColor.fromHex('#19C9B8');
  static final Color blueRibbon = _HexColor.fromHex('#005BE6');
  static final Color cyanAqua = _HexColor.fromHex('#00FFE6');
  static final Color emperor = _HexColor.fromHex('#4F4F4F');
  static final Color info = _HexColor.fromHex('#25BCF1');
  static final Color success = _HexColor.fromHex('#0ABB75');
  static final Color warning = _HexColor.fromHex('#FFC519');
  static final Color error = _HexColor.fromHex('#EF486A');
  static final Color dark = _HexColor.fromHex('#1B1B28');
  static final Color greyDark = _HexColor.fromHex('#76767E');
  static final Color greyLight = _HexColor.fromHex('#8D8D93');
  static final Color grey6 = _HexColor.fromHex('#F0F0F0');
  static final Color grey2 = _HexColor.fromHex('#4F4F4F');
  static final Color yellow = _HexColor.fromHex('#DFF017');
  static final Color alto = _HexColor.fromHex('#D9D9D9');
  static final Color tundora = _HexColor.fromHex('#424242');
  static final Color dustGray = _HexColor.fromHex('#949494');
  static final Color mercury = _HexColor.fromHex('#E8E8E8');
  static final Color mineShaft = _HexColor.fromHex('#333333');
  static final Color dustyGrey = _HexColor.fromHex('#9B9B9B');
  static final Color quickSilver = _HexColor.fromHex('#A6A6A6');
  static final Color graniteGray = _HexColor.fromHex('#5F5F5F');
  static final Color charlestonGreen = _HexColor.fromHex('#2B2B2B');
  static final Color gallery = _HexColor.fromHex('#ECECEC');
  static final Color gallery_06 = _HexColor.fromHex('#ECECEC').withOpacity(.6);
  static final Color hintOfGreen = _HexColor.fromHex('#E2FFE7');
  static final Color pippin = _HexColor.fromHex('#FFE2E2');
  static final Color cornflowerBlue = _HexColor.fromHex('#6788FF');
  static final Color alabaster = _HexColor.fromHex('#F8F8F8');
  static final Color silver = _HexColor.fromHex('#BDBDBD');
  static final Color softBlue = _HexColor.fromHex('#1F80F3');
  static final Color softGreen = _HexColor.fromHex('#FFAED581');
  static final Color primaryGreen = _HexColor.fromHex('#1EA951'); // 25D366
  static final Color primaryGreen50 = _HexColor.fromHex('#E9FBF0'); // 25D366
  static final Color primaryGreen900 = _HexColor.fromHex('#082B15'); // 25D366
  static final Color primaryGreen10 =
      _HexColor.fromHex('#4CB849').withOpacity(.10);
  static final Color primaryGreen400 = _HexColor.fromHex('#4DE083');
  static final Color softYellow = _HexColor.fromHex('#FF9C1A');
  static final Color burntSienna = _HexColor.fromHex('#EB5757');
  static final Color magnolia = _HexColor.fromHex('#FAF4FF');
  static final Color darkSoftBg = _HexColor.fromHex('#0E0E0F');
  static final Color purple = _HexColor.fromHex('#9B51E0');
  static final Color darkChat = _HexColor.fromHex('#08080A');
  static final Color nobel = _HexColor.fromHex('#B5B5B5');
  static final Color alto_08 = _HexColor.fromHex('#DBDBDB').withOpacity(.8);
  static final Color curiousBlue = _HexColor.fromHex('#DBDBDB');
  static final Color silverChalice = _HexColor.fromHex('#B0B0B0');
  static final Color cerulean = _HexColor.fromHex('#01A2E7');
  static final Color apple = _HexColor.fromHex('#1EA952'); // 25D366
  static final Color envy25 = _HexColor.fromHex('#8CA088').withOpacity(.25);
  static final Color concrete = _HexColor.fromHex('#F2F2F2');
  static final Color scorpion = _HexColor.fromHex('#565656');
  static final Color greyPostComment = _HexColor.fromHex('#8B8B8B');
  static final Color boulder = _HexColor.fromHex('#7A7A7A');

  static final Color softRed = _HexColor.fromHex('#FF4344');
  static final Color gold = _HexColor.fromHex('#FFD600');
  static const Color red = Colors.red;
  static const Color green = Colors.green;
  static const Color grey = Colors.grey;
  static Color grey50 = Colors.grey.withOpacity(.50);
  static const Color blue = Colors.blue;
  static const Color black = Colors.black;
  static Color black10 = Colors.black.withOpacity(.10);
  static Color black5 = Colors.black.withOpacity(.05);
  static final Color black15 = _HexColor.fromHex('#00000080');
  static const Color black54 = Colors.black54;
  static const Color white = Colors.white;
  static Color white60 = Colors.white.withOpacity(Values.v0_6);
  static Color softGrey = _HexColor.fromHex('#F4F4F4');

  static Color navyBlue = _HexColor.fromHex('#000A65');
  static Color ghost = _HexColor.fromHex('#C0C2D4');
  static Color athensGray = _HexColor.fromHex('#F3F3F3');
  static Color whiteLilac = _HexColor.fromHex('#F9F3FC');
  static Color mineShaftTraining = _HexColor.fromHex('#202020');
  static Color scorpionTraining = _HexColor.fromHex('#5C5C5C');
  static Color doveGray = _HexColor.fromHex('#6C6C6C');
  static Color dustyGray = _HexColor.fromHex('#969696');
  static Color jade = _HexColor.fromHex('#00C773');
  static Color jordyBlue = _HexColor.fromHex('#7CC0EF');
  static Color blueChalk = _HexColor.fromHex('#EEE9FF');
  static Color splashScreenBgColor = const Color(0xFF25D366);
  static Color pinkLight = _HexColor.fromHex('#FDF6FC');
  static Color brownLight = _HexColor.fromHex('#F17A11');
  static Color loginButtonBackgroundColor =
      _HexColor.fromHex('#1EA952').withOpacity(0.15);

  static Color greyscale10 = const Color(0xFFFCFCFC);
  static Color greyscale50 = const Color(0xFFF1F2F3);
  static Color greyscale100 = _HexColor.fromHex('#E0E2E5');
  static Color greyscale200 = const Color(0xFFC2C5CC);
  static Color greyscale400 = const Color(0xFF888E98);
  static Color greyscale500 = const Color(0xFF6B7280);
  static Color greyscale700 = const Color(0xFF41454E);
  static Color greyscale950 = const Color(0xFF0C0C0E);

  static Color yellow500 = _HexColor.fromHex('#EAB308');
  static Color yellow50 = _HexColor.fromHex('#FEFCE8');

  // Gradient Colors
  static Gradient primaryGradient = LinearGradient(
    colors: [primaryGreen, primaryGreen], // previously: [blue, cyan]
    begin: Alignment.topLeft,
    end: Alignment.bottomCenter,
  );

  // [Color(0xFF25D366), Color(0xFF45CCDE)]

  static Gradient cyanBlueGradient = LinearGradient(
    colors: [Color(0xFF1EA952), Color(0xFF45CCDE)], // previously: [blue, cyan]
    begin: Alignment.topLeft,
    end: Alignment.bottomCenter,
  );

  static Gradient cyanBlueLinearGradientBackground = LinearGradient(
    colors: [
      Color(0xFF1EA952),
      Color(0xFF45CCDE),
    ], // previously: [blue, cyan]
    begin: Alignment.topLeft,
    end: Alignment.bottomCenter,
  );

  static Gradient cyanBlueRadialGradient = RadialGradient(
    colors: [
      AppColors.primaryGreen,
      AppColors.cyan,
    ],
  );

  // static Gradient primaryGradientColor = const LinearGradient(
  //   begin: Alignment.topLeft,
  //   end: Alignment.bottomRight,
  //   colors: [Color(0xFF25D366), Color(0xFF45CCDE)],
  //   stops: [0.3, 0.6],
  // );

  // static BoxDecoration primaryBoxDecoration = BoxDecoration(
  //   border: Border.all(),
  //   gradient: AppColors.primaryGradientColor,
  // );

  static LinearGradient buildLinearGradientWhiteToBlack() {
    return LinearGradient(
      // begin: Alignment.topLeft,
      // end: Alignment.bottomRight,
      colors: [
        UIColors.black.withOpacity(0.53),
        AppColors.transparent.withOpacity(0.53),
      ],
    );
  }

  static RadialGradient buildRadialGradientWhiteToBlack() {
    return RadialGradient(
      colors: [
        AppColors.transparent.withOpacity(0.53),
        UIColors.black.withOpacity(0.53),
      ],
      radius: 1.0,
      stops: [0.1, 0.3],
    );
  }

  static RadialGradient whiteToBlackBackgroundGradient() {
    return RadialGradient(
      colors: [
        AppColors.black.withOpacity(0.2),
        UIColors.transparent,
      ],
      radius: 0.5,
      stops: [1.0, 1.0],
    );
  }
}

extension _HexColor on Color {
  static Color fromHex(String hexColorString) {
    int radixNumber = 16;

    hexColorString = hexColorString.replaceAll('#', '');
    if (hexColorString.length == 6) {
      hexColorString = 'FF$hexColorString'; //8 char with opacity 100%
    }

    return Color(int.parse(hexColorString, radix: radixNumber));
  }
}
