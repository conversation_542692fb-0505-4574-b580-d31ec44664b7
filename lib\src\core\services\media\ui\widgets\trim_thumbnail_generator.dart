import 'dart:io';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_thumbnail_video/index.dart';
import 'package:get_thumbnail_video/video_thumbnail.dart';
import 'package:path_provider/path_provider.dart';

class TrimThumbnailGenerator extends StatefulWidget {
  const TrimThumbnailGenerator({Key? key, required this.videoUrl})
      : super(key: key);

  final String videoUrl;

  @override
  State<TrimThumbnailGenerator> createState() => _TrimThumbnailGeneratorState();
}

class _TrimThumbnailGeneratorState extends State<TrimThumbnailGenerator> {
  XFile? _thumbnailFile;

  @override
  void initState() {
    super.initState();
    generateThumbnail();
  }

  void generateThumbnail() async {
    _thumbnailFile = await VideoThumbnail.thumbnailFile(
        video: widget.videoUrl,
        thumbnailPath: (await getTemporaryDirectory()).path,
        imageFormat: ImageFormat.WEBP,
        quality: 10);
    if (mounted) {
      setState(() {
        '';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return _thumbnailFile != null
        ? Stack(
            alignment: Alignment.center,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(Values.v4),
                child: Image.file(
                  File(_thumbnailFile!.path),
                  width: Values.v128.w,
                  height: Values.v128.h,
                  fit: BoxFit.cover,
                  cacheHeight: 100,
                  cacheWidth: 100,
                  errorBuilder: (context, error, stackTrace) {
                    return const Center(
                      child: Text('Cannot load video!'),
                    );
                  },
                ),
              ),
              CircleAvatar(
                radius: Values.v30.r,
                backgroundColor: Colors.black45,
                child: Icon(
                  Icons.play_arrow,
                  size: Values.v30.r,
                  color: Colors.white,
                ),
              )
            ],
          )
        : Center(
            child: CircularProgressIndicator(
              color: AppColors.primaryGreen,
            ),
          );
  }
}
