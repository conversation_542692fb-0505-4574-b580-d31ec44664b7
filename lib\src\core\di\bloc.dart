part of 'injection_container.dart';

Future<void> _initBlocs() async {
  sl.registerFactory(
    () => SignUpBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => UserProfileBloc(
      getUserProfileUseCase: sl.call(),
      updateUserProfileInfoUseCase: sl.call(),
      updateUserProfilePictureUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => SharedBloc(
      userProfileUseCases: sl.call(),
      sendLocationToServerUseCase: sl.call(),
      locationService: sl.call(),
      appInfoUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => LoginBloc(
      loginUseCase: sl.call(),
      loginWithGoogleUseCase: sl.call(),
      loginWithFacebookUseCase: sl.call(),
      loginWithAppleUseCase: sl.call(),
    ),
  );

  /// Story
  sl.registerFactory(
    () => StoryBloc(
      getStoriesUseCase: sl.call(),
      createStoryUseCase: sl.call(),
      markStoryAsViewedStoryUseCase: sl.call(),
      deleteStoryUseCase: sl.call(),
      myStoryUseCase: sl.call(),
      userProfileUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => StoryViewersBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => VerificationBloc(
      verificationUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ForgotPasswordBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => IdentityVerificationBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ResetPasswordBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => NotificationBloc(
      getAllNotificationsUseCase: sl.call(),
      markAllNotificationAsReadUseCase: sl.call(),
      spotListUseCase: sl.call(),
      updateRequestUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => SpotNotFilterBloc(
      suggestedUserListUseCase: sl.call(),
      locationService: sl.call(),
    ),
  );

  sl.registerFactory(
    () => SpotNotBloc(
      locationService: sl.call(),
      suggestedUserListUseCase: sl.call(),
      spotRequestUseCase: sl.call(),
      notOrUpdateRequestUseCase: sl.call(),
      eventUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => NearByUserProfileBloc(
      spotRequestUseCase: sl.call(),
      notOrUpdateRequestUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => PlannerBloc(
      getNotesUseCase: sl.call(),
      createNoteUseCase: sl.call(),
      getNoteDetailsUseCase: sl.call(),
      updateNoteUseCase: sl.call(),
      deleteNoteUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CreatePostBloc(
      createPostUseCase: sl.call(),
      fileUploadUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => EditPostBloc(
      editPostUseCase: sl.call(),
      fileUploadUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ScanQrBloc(
      scanQRCodeUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => FeedBloc(
      feedsUseCase: sl.call(),
      likePostUseCase: sl.call(),
      deletePostUseCase: sl.call(),
      pollUseCase: sl.call(),
      eventUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => AllUsersBloc(
      getAllUsersUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => RecentConversationBloc(
      recentChatListUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CreateGroupBloc(
      createGroupUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => GroupListBloc(
      getGroupListUseCase: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GroupChatBloc(
      getGroupHistoryUseCase: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => DeleteMessageBloc(
      deleteMessageUseCase: sl.call(),
      deleteSingleMessageUseCase: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GroupMembersBloc(
      getGroupMembersUseCase: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => LeaveGroupBloc(
      leaveGroupUseCase: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => ChatHistoryBloc(
      getChatHistoryUseCase: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => AddMemberBloc(
      addMemberUseCase: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => OnlineFitBuddyBloc(
      activeUserListUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => SearchLocationBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CommentBloc(
      getAllCommentsUseCase: sl.call(),
      addCommentUseCase: sl.call(),
      likePostUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => MonthlyChallengeBloc(
      getMonthlyChallengeUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => AcceptChallengeBloc(
      acceptChallengeUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ChallengeHistoryBloc(
      getChallengeHistoryUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ImagePickerBloc(
      pickImageUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ImageUploadBloc(
      getImageUrlUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => FeedImageBloc(
      getProfileFeedPhotosUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ExerciseListBloc(
      getTrainingExerciseListUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ExerciseCategoryBloc(
      getExerciseCategoryUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ChangePasswordBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ProfileVisibilityBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => UpdateProfileVisibilityBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => GetNotificationSettingsBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => UpdateNotificationSettingsBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ChangeEmailBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => BlockUserBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => PointsBloc(
      getPointsUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => UploadChallengeBloc(
      uploadChallengeVideoUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => LeaderboardBloc(
      getLeaderboardUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => PointsHistoryBloc(
      getPointsHistoryUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => FeedVideoBloc(
      getProfileFeedVideosUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => FitBuddiesBloc(
      getFitBuddiesUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => BlockFitBuddyBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => TimelineBloc(
      useCase: sl.call(),
      likePostUseCase: sl.call(),
      deletePostUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => PostCommentsBloc(
      getAllCommentsUseCase: sl.call(),
      addCommentUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => MuscleGroupBloc(
      getMuscleGroupUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => BodyBuildingBloc(
      getBodyBuildingProgramUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => IntermediateTrainingBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => FitMarketDashboardBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CartBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => SelectShippingAddressBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => AddShippingAddressBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => UpdateShippingAddressBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => UnfriendFitBuddyBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => SpotMeBloc(
      getOthersSpotMeUseCase: sl.call(),
      getMySpotMeProfileUseCase: sl.call(),
      createSpotMeProfileUseCase: sl.call(),
      updateSpotMeProfileUseCase: sl.call(),
      getSpotProfileUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ProductDetailsBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => AddToWishlistBloc(
      addToWishlistUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => RemoveFromWishlistBloc(
      removeFromWishlistUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => GetWishlistBloc(
      getWishlistUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => BillingAddressBloc(
      billingAddressUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => AddBillingAddressBloc(
      billingAddressUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => UpdateBillingAddressBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => OrderHistoryBloc(
      orderHistoryUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CreateOrderBloc(
      createOrderUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => SpotBloc(
      spotRequestUseCase: sl.call(),
      notRequestUseCase: sl.call(),
      getFitBuddyStatusUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => OrderSummaryBloc(
      orderSummaryUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => OrderDetailsBloc(
      orderDetailsUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => MakePaymentBloc(
      createPaymentUseCase: sl.call(),
      createPaymentByPointsUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => DashboardCubit(),
  );

  sl.registerFactory(
    () => PaymentMethodsCubit(useCase: sl.call()),
  );

  sl.registerFactory(
    () => CreateReviewBloc(createReviewUseCase: sl.call()),
  );

  sl.registerFactory(
    () => GetReviewBloc(useCase: sl.call()),
  );

  sl.registerFactory(
    () => EditReviewBloc(
      editReviewUseCase: sl.call(),
      deleteReviewUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => OthersProfileBloc(useCase: sl.call()),
  );

  sl.registerFactory(
    () => SpotBackCubit(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => PostDetailsCubit(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => LikeOrUnlikeCubit(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => UpdateFcmTokenCubit(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => LogoutCubit(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => DeleteSpotProfileCubit(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ValidateAddressCubit(
      useCase: sl.call(),
    ),
  );

  /// Diet Module
  sl.registerFactory(
    () {
      return DietPlanBloc(
        fetchDietPlansUseCase: sl.call(),
        createDietPlanUseCase: sl.call(),
      );
    },
  );
  sl.registerFactory(
    () {
      return DietHistoryBloc(
        useCase: sl.call(),
        waterConsumptionUseCase: sl.call(),
        idealCaloriesUseCase: sl.call(),
      );
    },
  );
  sl.registerFactory(() => IdealCaloriesCubit(useCase: sl.call()));
  sl.registerFactory(() => ActivityLevelBloc(useCase: sl.call()));
  sl.registerFactory(() => VendorFoodsCubit(useCase: sl.call()));
  sl.registerFactory(() => MyFoodsCubit(useCase: sl.call()));
  sl.registerFactory(() => CreateFoodCubit(useCase: sl.call()));
  sl.registerFactory(() => WaterCubit(useCase: sl.call()));
  sl.registerFactory(() => FoodConsumptionCubit(useCase: sl.call()));
  sl.registerFactory(() => UpdateDietPlanCubit(useCase: sl.call()));
  sl.registerFactory(() => DeleteConsumedFoodCubit(useCase: sl.call()));
  sl.registerFactory(() => WeightUnitCubit('lb'));

  /// Search - Global
  sl.registerFactory(() => SearchViewCubit());
  sl.registerFactory(() => PeopleSearchCubit(useCase: sl.call()));
  sl.registerFactory(() => ClubSearchCubit(useCase: sl.call()));
  sl.registerFactory(() => ProductSearchCubit(useCase: sl.call()));

  /// Club Module
  sl.registerFactory(() => ClubDashboardBloc(useCase: sl.call()));
  sl.registerFactory(
    () {
      return ClubBloc(
        locationService: sl.call(),
        myClubUseCase: sl.call(),
        leaveClubUseCase: sl.call(),
      );
    },
  );
  sl.registerFactory(
    () => NearbyClubsBloc(
      nearbyClubUseCase: sl.call(),
      joinClubUseCase: sl.call(),
      locationService: sl.call(),
    ),
  );
  sl.registerFactory(() => LiveMembersCubit(useCase: sl.call()));
  sl.registerFactory(() => ClubMembersCubit(useCase: sl.call()));
  sl.registerFactory(() => NearbyClubsMembersCubit(useCase: sl.call()));

  /// Spot Not Request Management
  sl.registerFactory(
    () => SpotNotCubit(
      sendRequestUseCase: sl.call(),
      cancelRequestUseCase: sl.call(),
      updateRequestUseCase: sl.call(),
      notRequestUseCase: sl.call(),
    ),
  );

  /// Likers Info
  sl.registerFactory(
    () => LikersInfoBloc(
      likersInfoUseCase: sl.call(),
    ),
  );

  ///Subscriptions
  sl.registerFactory(
    () => SubscriptionBloc(
      subscriptionUseCase: sl.call(),
      mySubscriptionPackageUseCase: sl.call(),
      subscribePackageUseCase: sl.call(),
    ),
  );

  /// Delete Account
  sl.registerFactory(
    () => DeleteAccountCubit(
      useCase: sl.call(),
    ),
  );

  //User profile
  sl.registerFactory(
    () => BenchSquatUnitCubit('lb'),
  );

  //fitbot
  sl.registerFactory(
    () => FitbotChatBloc(
      fitbotChatUseCase: sl.call(),
    ),
  );

  ///poll
  sl.registerFactory(
    () => TeamPollBloc(
      teamPollUseCase: sl.call(),
    ),
  );

  /// event
  sl.registerFactory(
    () => EventBloc(
      eventUseCase: sl.call(),
    ),
  );

  /// coach
  sl.registerFactory(
    () => CoachBloc(
      coachUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CoachProfileBloc(
      coachUseCase: sl.call(),
      coachRegistrationUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CoachProgramBloc(
      coachProgramUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CoachDashboardBloc(
      coachPersonalUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => StudentDashboardBloc(
      userProgramSubscriptionUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CoachNewsfeedBloc(
      coachNewsfeedUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CoachProgramReviewBloc(
      coachProgramReviewUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => WithdrawPaymentBloc(
      usecase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => RewardPointBloc(
      rewardPointUseCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => RewardLeaderboardBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ReferralBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => DailyTaskBloc(
      useCase: sl.call(),
    ),
  );

  sl.registerFactory(
    () => FoodScanBloc(
      getImageUrlUseCase: sl.call(),
      usecase: sl.call()
    ),
  );
}
