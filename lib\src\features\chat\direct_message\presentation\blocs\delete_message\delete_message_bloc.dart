import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/use_cases/delete_one_to_one_message_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/use_cases/delete_group_message_use_case.dart';

part 'delete_message_event.dart';
part 'delete_message_state.dart';

class DeleteMessageBloc extends Bloc<DeleteMessageEvent, DeleteMessageState> {
  DeleteMessageBloc(
      {required this.deleteMessageUseCase,
      required this.deleteSingleMessageUseCase})
      : super(DeleteMessageInitial()) {
    on<DeleteGroupChatMessageEvent>(_onDeleteGroupChatMessageEvent);
    on<DeleteOneToOneChatMessageEvent>(_onDeleteOneToOneMessageEvent);
  }

  late DeleteMessageUseCase deleteMessageUseCase;
  late DeleteOneToOneMessageUseCase deleteSingleMessageUseCase;

  Future<void> _onDeleteGroupChatMessageEvent(
    DeleteGroupChatMessageEvent event,
    Emitter<DeleteMessageState> emit,
  ) async {
    emit(DeleteMessageLoading());

    try {
      final response = await deleteMessageUseCase.call(
        messageId: event.messageId,
      );

      response.fold(
        (l) => emit(
          DeleteMessageFailure(
            errorMessage: l.toString(),
          ),
        ),
        (r) => emit(DeleteMessageSuccess(message: r)),
      );
    } catch (_) {
      emit(
        DeleteMessageFailure(
          errorMessage: TextConstants.failedToDeleteData,
        ),
      );
    }
  }

  Future<void> _onDeleteOneToOneMessageEvent(
    DeleteOneToOneChatMessageEvent event,
    Emitter<DeleteMessageState> emit,
  ) async {
    emit(DeleteMessageLoading());

    try {
      final response = await deleteSingleMessageUseCase.call(
        messageId: event.messageId,
      );

      response.fold(
        (l) => emit(
          DeleteMessageFailure(
            errorMessage: l.toString(),
          ),
        ),
        (r) => emit(DeleteMessageSuccess(message: r)),
      );
    } catch (_) {
      emit(
        DeleteMessageFailure(
          errorMessage: TextConstants.failedToDeleteData,
        ),
      );
    }
  }
}
