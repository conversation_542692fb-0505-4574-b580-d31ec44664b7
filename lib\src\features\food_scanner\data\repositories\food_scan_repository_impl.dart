import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/exception/network_exception.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/food_scanner/data/data_source/food_scan_datasource.dart';
import 'package:fitsomnia_app/src/features/food_scanner/data/models/food_model.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/entities/food_info_entity.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/entities/food_scan_image_request.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/repositories/food_scan_repository.dart';
import 'package:mime/mime.dart';

class FoodScanRepositoryImpl extends FoodScanRepository {
  final FoodScanDatasource datasource;

  FoodScanRepositoryImpl({required this.datasource});

  @override
  Future<Either<ErrorModel, FoodInfoEntity>> getFoodInfoFromFeedback(
      {required FoodScanFeedbackRequest request}) async {
    try {
      // Simulate a network call to scan the food image
      await Future.delayed(Duration(seconds: 2));
      // Here you would typically call a repository method to get the food info
      FoodInfoEntity foodInfo = FoodInfoEntity(
        name: 'Sample Food',
        benglaName: 'Sample Bengla Name',
        calories: '200',
        protein: '10g',
        carbs: '30g',
        fats: '5g',
        isFound: true,
        servingSize: '1 serving',
        ingredients: ['Ingredient 1', 'Ingredient 2'],
      );

      return Right(foodInfo);
    } catch (error) {
      return Left(ErrorModel(message: error.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, FoodInfoEntity>> getFoodInfoFromSingleMedia(
      {required FoodScanImageRequest request}) async {
    try {
      // get media file meta-data
      final mimeType = lookupMimeType(request.filePath);
      String mediaType = mimeType!.split("/")[0];
      String fileExtension = mimeType.split("/")[1];

      request.mediaType = mediaType;
      request.fileExtention = fileExtension;

      // Here you would typically call a repository method to get the food info
      final response =
          await datasource.getFoodInfoFromSingleMedia(request: request);
      final data = response.data['data'];

      // FoodInfoEntity foodInfo = FoodAiModel.fromJson(data);
      List<FoodInfoEntity> foods = List<FoodAiModel>.from(data.map((x) => FoodAiModel.fromJson(x)));
      bool isFoodDetected = response.data['isFoodDetected'] ?? false;

      if(!isFoodDetected || foods.isEmpty){
        return Left(ErrorModel(message: 'No Food Detected', code: 'NO_FOOD_DETECTED'));
      }

      return Right(foods.first);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  Future<Either<ErrorModel, FoodInfoEntity>> getFoodInfoFromImageTest(
      {required FoodScanImageRequest request}) async {
    try {
      // Simulate a network call to scan the food image
      await Future.delayed(Duration(seconds: 2));
      // Here you would typically call a repository method to get the food info
      FoodInfoEntity foodInfo = FoodInfoEntity(
        name: 'Sample Food',
        benglaName: 'Sample Bengla Name',
        calories: '200',
        protein: '10g',
        carbs: '30g',
        fats: '5g',
        isFound: true,
        servingSize: '1 serving',
        ingredients: ['Ingredient 1', 'Ingredient 2'],
      );

      return Right(foodInfo);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }
}
