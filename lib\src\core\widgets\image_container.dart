import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ImageContainer extends StatelessWidget {
  const ImageContainer({
    Key? key,
    required this.image,
    this.height,
    this.radius,
    this.cornerRadius,
    this.fit,
    this.errorWidget,
    this.isGroup = false,
    this.width = double.infinity,
    this.hideLoadingIndicator = false,
    this.isShoppingCart = false,
    this.isShoppingCartSelected = false,
    this.size,
    this.isFile = false,
    this.useOriginal = false,
    this.useMedium = false,
    this.useSmall = false,
    this.showBorder = true,
    this.modelName,
  }) : super(key: key);

  final String image;
  final double? radius;
  final bool isGroup;
  final double? height;
  final double width;
  final double? cornerRadius;
  final BoxFit? fit;
  final Widget? errorWidget;
  final bool hideLoadingIndicator;
  final bool isShoppingCart;
  final bool isShoppingCartSelected;
  final double? size;
  final bool isFile;
  final bool useOriginal;
  final bool useMedium;
  final bool useSmall;
  final bool showBorder;
  final String? modelName;

  @override
  factory ImageContainer.circularImage({
    required String? image,
    required double radius,
    bool isGroup = false,
    bool isShoppingCart = false,
    bool isShoppingCartSelected = false,
    double? size,
    Widget? errorWidget,
    bool isFile = false,
    bool useOriginal = false,
    bool useMedium = false,
    bool useSmall = false,
    bool showBorder = true,
    BoxFit? fit,
    String? modelName,
  }) {
    return ImageContainer(
      image: (image == null || image == '') ? Assets.spotMeNoImage : image,
      radius: radius,
      isGroup: isGroup,
      isShoppingCart: isShoppingCart,
      isShoppingCartSelected: isShoppingCartSelected,
      size: size,
      errorWidget: errorWidget,
      isFile: isFile,
      useOriginal: useOriginal,
      useMedium: useMedium,
      useSmall: useSmall,
      showBorder: showBorder,
      fit: fit,
      modelName: modelName,
    );
  }
  @override
  factory ImageContainer.rectangularImage({
    required String? image,
    required double width,
    double? height,
    double cornerRadius = 0.0,
    bool useOriginal = true,
    bool useMedium = false,
    bool useSmall = false,
    bool hideLoadingIndicator = false,
    BoxFit? fit,
    Widget? errorWidget,
  }) {
    return ImageContainer(
      image: (image == null || image == '') ? Assets.spotMeNoImage : image,
      width: width,
      height: height,
      cornerRadius: cornerRadius,
      useOriginal: useOriginal,
      useMedium: useMedium,
      useSmall: useSmall,
      hideLoadingIndicator: hideLoadingIndicator,
      fit: fit,
      errorWidget: errorWidget,
    );
  }

  @override
  Widget build(BuildContext context) {
    int imageDuration = 100;

    String cacheKeyString = image.split("?")[0];

    return radius != null
        ? Container(
            width: (radius! * 2).r,
            height: (radius! * 2).r,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(Values.v100.r),
                border: showBorder
                    ? Border.all(
                        color: UIColors.primary,
                        width: 2,
                      )
                    : null),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(Values.v100.r),
              child: SizedBox(
                width: (radius! * 2).r,
                height: (radius! * 2).r,
                child: isFile
                    ? Image.file(File(image), fit: BoxFit.cover)
                    : useOriginal
                        ? _buildOriginalImage()
                        : useMedium
                            ? _buildMediumImage()
                            : _buildNonMediumImage(),
              ),
            ),
          )
        : ClipRRect(
            borderRadius: cornerRadius != null
                ? BorderRadius.circular(cornerRadius!)
                : BorderRadius.zero,
            child: _buildRectImage(),
          );
  }

  bool _isValidImageURI() {
    if (image == null || image.isEmpty) {
      return false;
    }

    try {
      final uri = Uri.parse(image);

      return uri.hasAbsolutePath && uri.hasScheme && uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  Widget _buildMediumImage() {
    bool isImagePathValid = _isValidImageURI();
    if (isImagePathValid) {
      String cacheKeyString = image.split("?")[0];
      int imageDuration = 100;

      return CachedNetworkImage(
        imageUrl: image.contains('/original/') &&
                !image.contains('/medium/original/') &&
                !image.contains('/thumbnail/original/') &&
                !image.contains('/small/original/')
            ? image.replaceFirst('/original/', '/medium/original/')
            : image,
        fit: BoxFit.cover,
        cacheKey: cacheKeyString,
        fadeOutDuration: Duration(milliseconds: imageDuration),
        fadeInDuration: Duration(milliseconds: imageDuration),
        progressIndicatorBuilder: (context, value, progress) {
          return _loaderWidget(progress, isCircularImage: true);
        },
        errorWidget: (context, error, stackTrace) =>
            errorWidget ??
            _circularErrorWidget(
              radius!,
              isGroup,
              isShoppingCart: isShoppingCart,
              isShoppingCartSelected: isShoppingCartSelected,
              size: size,
            ),
      );
    } else {
      return _circularErrorWidget(
        radius!,
        isGroup,
        isShoppingCart: isShoppingCart,
        isShoppingCartSelected: isShoppingCartSelected,
        size: size,
      );
    }
  }

  Widget _buildNonMediumImage() {
    bool isImagePathValid = _isValidImageURI();
    if (isImagePathValid) {
      String cacheKeyString = image.split("?")[0];
      int imageDuration = 100;

      return CachedNetworkImage(
        imageUrl: image.contains('/original/') &&
                !image.contains('/medium/original/') &&
                !image.contains('/thumbnail/original/') &&
                !image.contains('/small/original/')
            ? (useSmall)
                ? image.replaceFirst('/original/', '/small/original/')
                : image.replaceFirst('/original/', '/thumbnail/original/')
            : image,
        fit: BoxFit.cover,
        cacheKey: cacheKeyString,
        fadeOutDuration: Duration(milliseconds: imageDuration),
        fadeInDuration: Duration(milliseconds: imageDuration),
        progressIndicatorBuilder: (context, value, progress) {
          return _loaderWidget(progress, isCircularImage: true);
        },
        errorWidget: (context, error, stackTrace) =>
            errorWidget ??
            _circularErrorWidget(
              radius!,
              isGroup,
              isShoppingCart: isShoppingCart,
              isShoppingCartSelected: isShoppingCartSelected,
              size: size,
            ),
      );
    } else {
      return _circularErrorWidget(
        radius!,
        isGroup,
        isShoppingCart: isShoppingCart,
        isShoppingCartSelected: isShoppingCartSelected,
        size: size,
      );
    }
  }

  Widget _buildRectImage() {
    bool isImagePathValid = _isValidImageURI();
    if (isImagePathValid) {
      String cacheKeyString = image.split("?")[0];
      int imageDuration = 100;

      return CachedNetworkImage(
        imageUrl: image.contains('/original/') &&
                !image.contains('/medium/original/') &&
                !image.contains('/thumbnail/original/') &&
                !image.contains('/small/original/')
            ? (useSmall)
                ? image.replaceFirst('/original/', '/small/original/')
                : (useMedium)
                    ? image.replaceFirst('/original/', '/medium/original/')
                    : image
            : image,
        fit: fit ?? BoxFit.fitHeight,
        // fit: fit ?? BoxFit.fitHeight,
        width: width,
        height: height,
        // memCacheWidth: (useOriginal ? null : Values.defaultWidth.toInt()),
        // memCacheHeight: (useOriginal ? null : Values.defaultHeight.toInt()),
        cacheKey: cacheKeyString,
        fadeOutDuration: Duration(milliseconds: imageDuration),
        fadeInDuration: Duration(milliseconds: imageDuration),
        cacheManager: CacheManager(
          Config(cacheKeyString, stalePeriod: const Duration(days: 7)),
        ),
        progressIndicatorBuilder: (context, value, progress) {
          return _loaderWidget(
            progress,
            hideLoadingIndicator: hideLoadingIndicator,
          );
        },
        errorWidget: (context, error, stackTrace) =>
            errorWidget ?? rectangularErrorWidget(),
      );
    } else {
      return rectangularErrorWidget();
    }
  }

  Widget rectangularErrorWidget() {
    return Container(
      height: height,
      width: width,
      color: AppColors.alto,
      child: Image.asset(
        Assets.spotMeNoImage,
        fit: BoxFit.cover,
      ),
    );
  }

  Widget _loaderWidget(
    DownloadProgress progress, {
    bool hideLoadingIndicator = false,
    bool isCircularImage = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.alto,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          isCircularImage
              ? Image.asset(
                  Assets.spotMeNoImage,
                  fit: BoxFit.cover,
                )
              : SizedBox(
                  width: 40.w,
                  height: 40.h,
                  child: hideLoadingIndicator
                      ? null
                      : CircularProgressIndicator(
                          color: AppColors.primaryGreen,
                          value: progress.progress,
                        ),
                ),
        ],
      ),
    );
  }

  Widget _circularErrorWidget(
    double radius,
    bool isGroup, {
    required bool isShoppingCart,
    required bool isShoppingCartSelected,
    double? size,
  }) {
    if (isShoppingCart) {
      return Container(
        padding: EdgeInsets.all(3.r),
        decoration: BoxDecoration(
          border: isShoppingCartSelected
              ? Border.all(
                  color: AppColors.primaryGreen,
                  width: 1,
                )
              : null,
          shape: BoxShape.circle,
        ),
        child: CircleAvatar(
          radius: radius.r,
          backgroundColor: isShoppingCartSelected
              ? AppColors.primaryGreen
              : UIColors.secondary,
          child: SvgPicture.asset(
            modelName == "Men's Clothing"
                ? Assets.male
                : modelName == "Women's Clothing"
                    ? Assets.female
                    : modelName == "Unisex"
                        ? Assets.unisex
                        : Assets.allIcon,
            colorFilter: ColorFilter.mode(
              isShoppingCartSelected ? UIColors.white : UIColors.fitBlack,
              BlendMode.srcIn,
            ),
          ),
        ),
      );
    }

    return CircleAvatar(
      radius: radius.r,
      backgroundColor: Colors.transparent,
      child: isGroup
          ? Icon(
              Icons.group,
              color: AppColors.grey,
              size: size ?? 30.r,
            )
          : Image.asset(
              Assets.spotMeNoImage,
              fit: BoxFit.cover,
              width: radius * 2,
              height: radius * 2,
            ),
    );
  }

  _buildOriginalImage() {
    bool isImagePathValid = _isValidImageURI();
    if (isImagePathValid) {
      return Image.network(
        image,
        fit: BoxFit.cover,
        // cacheWidth: Values.defaultWidth.toInt(),
        // cacheHeight: Values.defaultHeight.toInt(),
        errorBuilder: (context, error, stackTrace) => Image.asset(
          Assets.spotMeNoImage,
          fit: BoxFit.cover,
        ),
      );
    } else {
      return Image.asset(
        Assets.spotMeNoImage,
        fit: BoxFit.cover,
      );
    }
  }
}
