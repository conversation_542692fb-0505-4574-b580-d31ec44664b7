import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_profile_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_program_search_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

class CoachProgramTabView extends StatefulWidget {
  const CoachProgramTabView({
    super.key,
  });

  @override
  State<CoachProgramTabView> createState() => _CoachProgramTabViewState();
}

class _CoachProgramTabViewState extends State<CoachProgramTabView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: _buildAppBar(),
      body: SafeArea(
        child: Column(
          children: [
            // _buildPageHeader(),
            // _buildSearchSection(),
            Expanded(
              child: _buildProgramListSection(),
            )
          ],
        ),
      ),
    );
  }

  // AppBar _buildAppBar() {
  //   return AppBar(
  //     title: const Text(
  //       'Coach List',
  //       style: TextStyle(
  //         color: UIColors.primaryGreen950,
  //         fontWeight: FontWeight.bold,
  //       ),
  //       maxLines: 1,
  //       overflow: TextOverflow.ellipsis,
  //     ),
  //     centerTitle: true,
  //     elevation: 0,
  //   );
  // }

  // _buildPageHeader() {
  //   return Padding(
  //     padding: const EdgeInsets.only(top: Values.v20, left: Values.v20),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         _buildCoachListTitle(),
  //         Text(
  //           'Get to know your coach and subscibe for training',
  //           style: AppTypography.poppinsMedium14(color: AppColors.greyscale400),
  //         ),
  //         const SizedBox(
  //           height: 24,
  //         ),
  //       ],
  //     ),
  //   );
  // }

  _buildProgramListSection() {
    // return CoachProfileListWidget();
    return CoachProgramSearchList();
  }

  Widget _buildCoachListTitle() {
    return RichText(
      text: TextSpan(
        style: AppTypography.poppinsSemiBold24(color: UIColors.primaryGreen950),
        children: [
          TextSpan(
            text: 'Visit our ',
            style: AppTypography.poppinsSemiBold24(color: UIColors.primary),
          ),
          const TextSpan(text: 'coaches ! '),
        ],
      ),
    );
  }
}

class CoachProgramSearchList extends StatefulWidget {
  const CoachProgramSearchList({super.key});

  @override
  State<CoachProgramSearchList> createState() => _CoachProgramSearchListState();
}

class _CoachProgramSearchListState extends State<CoachProgramSearchList> {
  List<CoachProgramSearchEntity> programs = [];

  bool isLoading = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachNewsfeedBloc, CoachNewsfeedState>(
      listener: (context, state) {
        if (state is CoachProgramsLoading) {
          Log.debug('coach profiles is loading');
        }

        if (state is GetCoachProgramsSuccess) {
          Log.debug('get best coach profile success');
          setState(() {
            programs = state.programs;
          });
        }
        if (state is GetCoachProgramsFail) {
          Log.debug('get best coach profile fail');
        }
      },
      child: Container(
        margin: EdgeInsets.only(
          top: Values.v20,
        ),
        child: _buildCoachProfileList(),
      ),
    );
  }

  _buildCoachProfileList() {
    // coachProfiles = List.generate(20, (index) {
    //   return testBestCoachProfileEntity;
    // });

    return (programs.isEmpty)
        ? Center(child: Text('No Program Found'))
        : ListView.separated(
            shrinkWrap: true,
            itemCount: programs.length,
            padding: EdgeInsets.all(Values.v5),
            itemBuilder: (BuildContext context, int index) {
              return CoachProgramCard(programEntity: programs[index]);
            },
            separatorBuilder: (context, index) {
              return Divider(
                height: Values.v20,
                indent: Values.v20,
                endIndent: Values.v20,
                color: AppColors.greyscale100,
              );
            },
          );
  }
}

class CoachProgramCard extends StatelessWidget {
  const CoachProgramCard({super.key, required this.programEntity});

  final CoachProgramSearchEntity programEntity;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        _navigateToProgramPreviewPage(context);
      },
      child: Container(
        margin: EdgeInsets.only(
          left: Values.v20,
        ),
        child: Row(
          children: [
            _buildProfileImage(),
            _buildProfileInformation(),
          ],
        ),
      ),
    );
  }

  _buildProfileImage() {
    // return Container(
    //   child: Image.network(
    //     widget.profileImage,
    //     fit: BoxFit.fitHeight,
    //     // width: double.infinity,
    //     // height: double.infinity,
    //   ),
    // );

    if (programEntity.images.isEmpty) {
      return Image.asset(
        Assets.spotMeNoImage,
        height: Values.v80,
        width: Values.v80,
      );
    }

    return ImageContainer.rectangularImage(
      cornerRadius: Values.v10,
      image: programEntity.images.first.url,
      width: Values.v80,
      height: Values.v80,
      fit: BoxFit.fill,
      useOriginal: false,
      useSmall: true,
      hideLoadingIndicator: true,
      errorWidget: Container(
        height: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Values.v10),
          image: DecorationImage(
            image: const Image(
              image: AssetImage(
                Assets.spotMeNoImage,
              ),
            ).image,
            fit: BoxFit.fill,
          ),
        ),
      ),
    );
  }

  _buildProfileInformation() {
    return Flexible(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
                  programEntity.title,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: AppTypography.poppinsSemiBold16(
                      color: UIColors.primaryGreen950),
                ),
              
            Text(
              '${programEntity.coachName}',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style:
                  AppTypography.poppinsRegular12(color: AppColors.greyscale400),
            ),
            _buildCoachRatingSubscriberSection(),
          ],
        ),
      ),
    );
  }

  _buildCoachRatingSubscriberSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        _buildCoachRatingInfo(),
        // Spacer(),
        // _buildCoachSubscriberInfo(),
      ],
    );
  }

  _buildCoachRatingInfo() {
    return Container(
      // padding:
      //     EdgeInsets.symmetric(horizontal: Values.v8, vertical: Values.v4),
      // decoration: BoxDecoration(
      //   color: AppColors.black.withOpacity(0.5),
      //   borderRadius: BorderRadius.circular(Values.v15),
      // ),
      child: Row(
        children: [
          SvgPicture.asset(
            Assets.coachRateIcon,
          ),
          SizedBox(
            width: 3,
          ),
          Text(
            '${programEntity.programRating} ',
            style: AppTypography.poppinsSemiBold16(
                color: UIColors.primaryGreen950),
          ),
        ],
      ),
    );
  }

  void _navigateToProgramPreviewPage(BuildContext context) {
    Log.debug('coach profile tap');
    Navigator.of(context).pushNamed(Routes.coachProgramPreviewByIdPage,
        arguments: [programEntity.programId, programEntity.coachId]);
  }
}
