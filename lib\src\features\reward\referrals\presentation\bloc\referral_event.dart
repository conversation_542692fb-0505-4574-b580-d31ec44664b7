part of 'referral_bloc.dart';

sealed class ReferralEvent extends Equatable {
  const ReferralEvent();

  @override
  List<Object> get props => [];
}

class GetReferrals extends ReferralEvent {

}

class GetSingleReferral extends ReferralEvent {
  final String referralId;

  GetSingleReferral({required this.referralId});

  @override
  List<Object> get props => [referralId];
}

class GetReferralCode extends ReferralEvent {
  final String referralId;
  final String userId;

  GetReferralCode({required this.userId, required this.referralId});

  @override
  List<Object> get props => [userId, referralId];
}

class UseReferralCode extends ReferralEvent {
  final String userId;
  final String referralCode;

  UseReferralCode({required this.userId, required this.referralCode});

  @override
  List<Object> get props => [userId, referralCode];
}


