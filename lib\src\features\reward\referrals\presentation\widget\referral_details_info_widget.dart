import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/entity/referral_code_entity.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/entity/referral_data_entity.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/presentation/bloc/referral_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:share_plus/share_plus.dart';

class ReferralDetailsInfoWidget extends StatefulWidget {
  const ReferralDetailsInfoWidget({super.key, required this.referralId});
  final String referralId;

  @override
  State<ReferralDetailsInfoWidget> createState() =>
      _ReferralDetailsInfoWidgetState();
}

class _ReferralDetailsInfoWidgetState extends State<ReferralDetailsInfoWidget> {
  ReferralDataEntity? _referralEntity;
  ReferralCodeEntity? _referralCodeEntity;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // _referralEntity = testReferralData;
    _isLoading = true;
    BlocProvider.of<ReferralBloc>(context)
        .add(GetSingleReferral(referralId: widget.referralId));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ReferralBloc, ReferralState>(
      listener: (context, state) {
        if (state is GetSingleReferralSuccess) {
          setState(() {
            _referralEntity = state.referralDataEntity;
            _isLoading = false;
          });
        }

        if (state is GetSingleReferralFail) {
          setState(() {
            _isLoading = false;
          });
        }
      },
      child: (_isLoading)
          ? const SizedBox(
              height: 300,
              child: Center(
                child: CircularProgressIndicator(
                  color: UIColors.primary,
                ),
              ),
            )
          : (_referralEntity == null)
              ? const SizedBox(
                  height: 200,
                  child: Center(
                    child: Text('No information found'),
                  ),
                )
              : Container(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          // decoration: BoxDecoration(border: Border.all(color: Colors.red)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              IconButton(
                                // iconSize: Values.v18,
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                icon: Icon(Icons.clear_sharp),
                              )
                            ],
                          ),
                        ),
                        _buildTitleSection(),
                        Divider(
                          height: 20,
                          indent: 20,
                          endIndent: 20,
                          color: AppColors.greyscale100,
                        ),
                        _buildRulesSection(),
                        _buildReferralCodeShareSection(),
                      ],
                    ),
                  ),
                ),
    );
  }

  _buildReferralCodeShareSection() {
    return ReferralCodeShareCopyWidget(referralDataEntity: _referralEntity!);
  }

  _buildTitleSection() {
    return Column(
      children: [
        Image.asset(
          Assets.rewardGiftImg,
          height: Values.v60,
          width: Values.v60,
        ),
        Container(
          // decoration: BoxDecoration(border: Border.all(color: Colors.red)),
          child: Text(
            'Share & Win Rewards',
            style: TextStyle(
              fontSize: FontSize.s24.sp,
              fontFamily: FontConstants.poppinsFontFamily,
              fontWeight: FontWeight.w600,
              // height: 1.0
            ),
          ),
        ),
      ],
    );
  }

  _buildRulesSection() {
    var widgetList = _referralEntity!.referralRules.map((eachRule) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [Text('• '), Expanded(child: Text(eachRule))],
      );
    }).toList();

    return Padding(
      padding: const EdgeInsets.all(10),
      child: Column(
        children: widgetList,
      ),
    );
  }
}

class ReferralCodeShareCopyWidget extends StatefulWidget {
  const ReferralCodeShareCopyWidget(
      {super.key, required this.referralDataEntity});
  final ReferralDataEntity referralDataEntity;

  @override
  State<ReferralCodeShareCopyWidget> createState() =>
      _ReferralCodeShareCopyWidgetState();
}

class _ReferralCodeShareCopyWidgetState
    extends State<ReferralCodeShareCopyWidget> {
  ReferralCodeEntity? _referralCodeEntity;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    String userId = context.read<SharedBloc>().userProfile!.id; // get userid
    _isLoading = true;
    BlocProvider.of<ReferralBloc>(context).add(GetReferralCode(
        userId: userId, referralId: widget.referralDataEntity.referralId));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ReferralBloc, ReferralState>(
      listener: (context, state) {
        if (state is GetReferralCodeSuccess) {
          setState(() {
            _referralCodeEntity = state.referralCodeEntity;
            _isLoading = false;
          });
        }

        if (state is GetReferralCodeFail) {
          setState(() {
            _isLoading = false;
          });

          AppToast.showToast(
              message: 'No referral code fount', gravity: ToastGravity.BOTTOM);
        }
      },
      child: (_isLoading)
          ? const SizedBox(
              height: Values.v200,
              child: Center(
                child: CircularProgressIndicator(
                  color: UIColors.primary,
                ),
              ),
            )
          : (_referralCodeEntity == null)
              ? const SizedBox(
                  height: Values.v200,
                  child: Center(
                    child: Text('No referral code available'),
                  ),
                )
              : Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Divider(
                      height: 20,
                      indent: 20,
                      endIndent: 20,
                      color: AppColors.greyscale100,
                    ),
                    _buildReferralCodeSection(),
                    Divider(
                      height: 20,
                      indent: 20,
                      endIndent: 20,
                      color: AppColors.greyscale100,
                    ),
                    _buildShareCodeSection()
                  ],
                ),
    );
  }

  _buildReferralCodeSection() {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Column(
            children: [
              Text(
                'Your referral code is',
                style: AppTypography.poppinsRegular16(
                    color: AppColors.greyscale500),
              ),
              Text(
                _referralCodeEntity!.referralCode,
                style: AppTypography.poppinsSemiBold24(
                    color: UIColors.primaryGreen950),
              ),
              const SizedBox(height: Values.v10,),
              _buildCopyReferralCodeButton(),
            ],
          ),
        ],
      ),
    );
  }

  _buildShareCodeSection() {
    return Container(
      child: Column(children: [
        _buildShareCodeButton(context),
        SizedBox(
          height: Values.v10,
        ),
        _buildShareImg()
      ]),
    );
  }

  _buildShareImg() {
    return GestureDetector(
      onTap: () async {
        await _shareReferralCode();
      },
      child: Padding(
        padding: const EdgeInsets.only(top: Values.v10, bottom: Values.v20),
        child: Image.asset(
          Assets.referralShareImg,
          height: Values.v30,
        ),
      ),
    );
  }

  _buildCopyReferralCodeButton() {
    // return Button(
    //   background: UIColors.primaryGreen900,
    //   width: Values.v304.w,
    //   label: 'Copy Referral Code',
    //   textStyle: const TextStyle(
    //     fontSize: FontSize.s20,
    //     fontFamily: FontConstants.poppinsFontFamily,
    //     fontWeight: FontWeight.w500,
    //     color: Colors.white,
    //   ),
    //   onPressed: () async {
    //     Log.debug('copy referral code');
    //   },
    // );

    return ElevatedButton(
      onPressed: () {
        Log.debug('copy referral code pressed');
        _copyToClipboard();
      },
      style: const ButtonStyle(
        backgroundColor: WidgetStatePropertyAll(UIColors.primaryGreen900),
        padding: WidgetStatePropertyAll(
            EdgeInsets.symmetric(vertical: 10, horizontal: 50)),
      ),
      child: Text(
        'Copy Referral Code',
        style: AppTypography.poppinsSemiBold16(color: UIColors.white),
      ),
    );
  }

  _copyToClipboard() {
    Clipboard.setData(ClipboardData(text: _referralCodeEntity!.referralCode))
        .then((_) {
      AppToast.showToast(message: 'Copied to clipboard');
    });
  }

  _buildShareCodeButton(BuildContext context) {
    return ElevatedButton(
      onPressed: () async {
        Log.debug('share rules pressed');
        await _shareReferralCode();
      },
      style: const ButtonStyle(
        backgroundColor: WidgetStatePropertyAll(UIColors.primaryGreen900),
        padding: WidgetStatePropertyAll(
            EdgeInsets.symmetric(vertical: 10, horizontal: 50)),
      ),
      child: Text(
        'Share Referral Code',
        style: AppTypography.poppinsSemiBold16(color: UIColors.white),
      ),
    );
  }

  Future<void> _shareReferralCode() async {
    ShareResult result = await SharePlus.instance.share(
      ShareParams(
          text:
              '${widget.referralDataEntity.referralTitle} \nEarn Points: ${widget.referralDataEntity.totalPoint} \nCode: ${_referralCodeEntity!.referralCode}'),
    );

    Log.debug('Share result: ${result.status}');
  }
}
