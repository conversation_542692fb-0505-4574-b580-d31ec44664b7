import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_point_rank_entity.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/presentation/bloc/reward_leaderboard_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class UserRankDetailWidget extends StatefulWidget {
  const UserRankDetailWidget({super.key, required this.userId});
  final String userId;

  @override
  State<UserRankDetailWidget> createState() => _UserRankDetailWidgetState();
}

class _UserRankDetailWidgetState extends State<UserRankDetailWidget> {
  RewardPointRankEntity? _pointRankEntity;
  bool _isLoading = false;

  @override
  void initState() {
    // _pointRankEntity = testPointRankEntity;

    _isLoading = true;
    BlocProvider.of<RewardLeaderboardBloc>(context)
        .add(GetUserCurrentRank(userId: widget.userId));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RewardLeaderboardBloc, RewardLeaderboardState>(
      listener: (context, state) {
        if (state is GetUserCurrentRankSuccess) {
          setState(() {
            _pointRankEntity = state.rankEntity;
            _isLoading = false;
          });
        }

        if (state is GetUserCurrentRankFail) {
          setState(() {
            _isLoading = false;
            _pointRankEntity = null;
          });
        }
      },
      child: (_isLoading)
          ? Center(
              child: CircularProgressIndicator(
                color: AppColors.primaryGreen,
              ),
            )
          : (_pointRankEntity == null)
              ? const SizedBox.shrink()
              : _buildTotalPointCard(),
    );
  }

  _buildTotalPointCard() {
    return Container(
      // padding: EdgeInsets.all(Values.v20),

      decoration: BoxDecoration(
        gradient: _backgroundGradienRedColor(),
        borderRadius: BorderRadius.circular(Values.v12.r),
      ),
      child: Stack(
        alignment: AlignmentDirectional.center,
        children: [
          _buildImageLayerSection(),
          _buildRankPointSection(),
        ],
      ),
    );
  }

  _backgroundGradienRedColor() {
    return LinearGradient(colors: [UIColors.black, AppColors.red]);
  }

  _buildImageLayerSection() {
    return Container(
      // color: UIColors.primary,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Image.asset(
            Assets.rewardRankImg,
          )
        ],
      ),
    );
  }

  _buildRankPointSection() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildRankSection(),
          Divider(
            color: AppColors.greyscale200,
            endIndent: 150,
            height: 20,
          ),
          _buildUserPointSection(),
        ],
      ),
    );
  }

  _buildUserPointSection() {
    return Row(
      children: [
        _buildUserImage(),
        SizedBox(
          width: Values.v10,
        ),
        _buildPointSection(),
      ],
    );
  }

  _buildPointSection() {
    final String userName =
        BlocProvider.of<SharedBloc>(context).userProfile!.name;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${userName}',
          style: AppTypography.poppinsMedium14(color: UIColors.white),
        ),
        _buildPoint(),
      ],
    );
  }

  _buildCoinImage() {
    return Image.asset(
      Assets.rewardPointImg,
      width: Values.v24,
      height: Values.v24,
    );
  }

  _buildUserImage() {
    String? userImageUrl =
        BlocProvider.of<SharedBloc>(context).userProfile!.getUsersImage();

    return Container(
      // decoration: BoxDecoration(
      //     borderRadius: BorderRadius.circular(Values.v100),
      //     border: Border.all(width: 2, color: UIColors.white)),
      child: ImageContainer.circularImage(
        image: userImageUrl,
        radius: Values.v30,
        showBorder: false,
      ),
    );
  }

  _buildRankSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Rank',
          style: AppTypography.poppinsMedium14(color: UIColors.white),
        ),
        _buildUserRankText(),
      ],
    );
  }

  _buildUserRankText() {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        text:
            '${_pointRankEntity!.userRank == 0 ? '-' : '#${_pointRankEntity!.userRank}'}',
        style: AppTypography.poppinsSemiBold32(color: UIColors.yellow400),
        children: [
          TextSpan(
              text: '/${_pointRankEntity!.totalParticipant}',
              style: AppTypography.poppinsSemiBold18(color: UIColors.yellow400))
        ],
      ),
    );
  }

  _buildPoint() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCoinImage(),
        SizedBox(
          width: Values.v5,
        ),
        Text('${_pointRankEntity!.totalPoints}',
            style: AppTypography.poppinsSemiBold16(color: UIColors.white)),
      ],
    );
  }
}
