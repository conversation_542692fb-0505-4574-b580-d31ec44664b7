part of 'chat_history_bloc.dart';

class ChatHistoryState extends Equatable {
  @override
  List<Object?> get props => [];
}

class ChatHistoryInitial extends ChatHistoryState {}

class ChatHistoryLoading extends ChatHistoryState {}

class Chat<PERSON><PERSON>orySuc<PERSON> extends ChatH<PERSON>oryState {
  final List<ChatHistoryEntity> chatHistoryEntity;

  ChatHistorySuccess({required this.chatHistoryEntity});

  @override
  List<Object?> get props => chatHistoryEntity;
}

class ChatHistoryFailure extends ChatHistoryState {
  final String errorMessage;

  ChatHistoryFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}
