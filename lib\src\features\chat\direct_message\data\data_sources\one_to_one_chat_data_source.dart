import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class OneToOneChatDataSource {
  Future<Response> getChatHistory(
      {required String userId, int? limit, int? offset});
}

class OneToOneChatDataSourceImpl implements OneToOneChatDataSource {
  const OneToOneChatDataSourceImpl({required this.restClient});

  final RestClient restClient;

  @override
  Future<Response> getChatHistory(
      {required String userId, int? limit, int? offset}) async {
    final response = await restClient.get(
        APIType.PROTECTED, API.getChatHistory(userId: userId), data: {
      if (limit != null) "limit": limit,
      if (offset != null) "offset": offset
    });

    return response;
  }
}
