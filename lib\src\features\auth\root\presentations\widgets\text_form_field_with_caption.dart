import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TextFormFieldWithCaption extends StatefulWidget {
  const TextFormFieldWithCaption({
    required this.caption,
    this.captionColor = AppColors.white,
    this.fillColor,
    this.captionPadding = Values.v5,
    required this.controller,
    this.requiredSuffixIcon = false,
    this.decoration,
    required this.keyboardType,
    required this.validator,
    this.obscureText = false,
    Key? key,
  }) : super(key: key);

  final String caption;
  final Color captionColor;
  final Color? fillColor;
  final double captionPadding;
  final bool obscureText;
  final bool requiredSuffixIcon;
  final TextEditingController controller;
  final InputDecoration? decoration;
  final TextInputType keyboardType;
  final String? Function(String?)? validator;

  @override
  State<TextFormFieldWithCaption> createState() =>
      _TextFormFieldWithCaptionState();
}

class _TextFormFieldWithCaptionState extends State<TextFormFieldWithCaption> {
  bool obscureText = true;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.caption,
          style: AppTypography.regular14(
            color: widget.captionColor,
          ),
        ),
        SizedBox(height: widget.captionPadding.h),
        TextFormField(
          controller: widget.controller,
          keyboardType: widget.keyboardType,
          cursorColor: AppColors.black,
          autocorrect: false,
          decoration: InputDecoration(
            contentPadding: EdgeInsets.only(
              left: Values.v10.w,
            ),
            suffixIconColor: AppColors.white,
            suffixIcon: widget.requiredSuffixIcon
                ? GestureDetector(
                    onTap: () {
                      setState(() {
                        obscureText = !obscureText;
                      });
                    },
                    child: !obscureText
                        ? Icon(Icons.visibility, color: AppColors.greyDark)
                        : Icon(Icons.visibility_off, color: AppColors.greyDark),
                  )
                : const SizedBox.shrink(),
            fillColor: widget.fillColor ?? AppColors.gallery_06,
            filled: true,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(Values.v8),
              borderSide: BorderSide.none,
            ),
            errorBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: AppColors.error,
                width: Values.v1.r,
              ),
              borderRadius: BorderRadius.all(Radius.circular(Values.v8.r)),
            ),
            errorStyle:
                AppTypography.regular14(color: AppColors.error),
            errorMaxLines: 2,
          ),
          validator: widget.validator,
          obscureText: widget.obscureText ? obscureText : false,
        ),
      ],
    );
  }
}
