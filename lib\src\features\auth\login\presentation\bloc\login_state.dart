part of 'login_bloc.dart';

enum LoginType { EMAIL_PASSWORD, GOOGLE, FACEBOOK, APPLE }

abstract class LoginState extends Equatable {
  const LoginState();

  @override
  List<Object?> get props => [];
}

class LoginInitialState extends LoginState {}

class RememberedCredentialState extends LoginState {
  const RememberedCredentialState();
}

class LoginLoadingState extends LoginState {
  final LoginType type;

  const LoginLoadingState({required this.type});
}

class LoginErrorState extends LoginState {
  const LoginErrorState({required this.message});

  final String message;
}

class LoginSuccessState extends LoginState {
  final String token;

  const LoginSuccessState({required this.token});

  @override
  List<Object?> get props => [token];
}

class LoginWithPhoneNumberState extends LoginState {}

class LoginWithEmailState extends LoginState {}
