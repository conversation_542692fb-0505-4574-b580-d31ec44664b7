import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/club/dashboard/presentation/bloc/club_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/club/club_bloc.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/club_members/club_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/nearby_clubs_members/nearby_clubs_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/entities/club_entity.dart';
import 'package:fitsomnia_app/src/features/settings/about/presentation/widget/webivew_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MyClubWidget extends StatefulWidget {
  const MyClubWidget({
    Key? key,
    required this.club,
  }) : super(key: key);

  final ClubEntity club;

  @override
  State<MyClubWidget> createState() => _MyClubWidgetState();
}

class _MyClubWidgetState extends State<MyClubWidget> {
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ClubBloc, ClubState>(
      listener: (context, state) {
        if (state is LeaveClubSuccessState) {
          context.read<NearbyClubsMembersCubit>().resetState();
          context.read<ClubMembersCubit>().resetState();
          context.read<ClubDashboardBloc>().add(ClubMembershipCheckEvent());
        } else if (state is ClubErrorState) {
          setState(() => isLoading = false);
        }
      },
      buildWhen: (previous, current) {
        return (current is ClubLoadedState ||
            (current is ClubErrorState &&
                current.event == ClubEventType.MY_CLUB) ||
            (current is ClubLoadingState &&
                current.event == ClubEventType.MY_CLUB));
      },
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildClubCover(widget.club),
            // _buildClubLogo(club),
            _buildClubName(widget.club),
            SizedBox(height: Values.v12.h),
          ],
        );
      },
    );
  }

  /// DO NOT REMOVE! Might need this later.
  Widget _buildClubLogo(ClubEntity club) {
    return CircleAvatar(
      radius: 30,
      backgroundColor: AppColors.alto_08,
      backgroundImage: NetworkImage(club.image.logo),
      onBackgroundImageError: (exception, stackTrace) {
        Container(
          color: AppColors.alto_08,
          height: Values.v192.h,
        );
      },
    );
  }

  Widget _buildClubName(ClubEntity club) {
    return Container(
      padding: EdgeInsets.only(
        left: 16.w,
        top: 16.h,
        bottom: 16.h,
      ),
      color: AppColors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              club.name,
              style: AppTypography.semiBold24(
                color: AppColors.black,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              _navigateToWebView();
            },
            child: const Icon(
              Icons.info_outline_rounded,
              color: AppColors.blue,
            ),
          ),
          PopupMenuButton(
            offset: const Offset(0, 40),
            onSelected: (value) {
              _showLeaveAlert(context, widget.club.id);
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry>[
              const PopupMenuItem(
                value: 1,
                child: Text(
                  'Leave Club',
                  style: TextStyle(
                    color: AppColors.red,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildClubCover(ClubEntity club) {
    return ImageContainer.rectangularImage(
      image: club.image.cover.contains('/original/')
          ? club.image.cover.replaceFirst('/original/', '/thumbnail/original/')
          : club.image.cover,
      width: double.infinity,
      fit: BoxFit.cover,
    );
  }

  _showLeaveAlert(BuildContext context, String clubId) {
    showDialog(
        context: context,
        builder: (context) {
          return CupertinoAlertDialog(
            title: Column(
              children: const [
                Text("Alert"),
              ],
            ),
            content: const Text("Are you sure you want to leave this club?"),
            actions: [
              CupertinoDialogAction(
                child: const Text("Yes"),
                onPressed: () {
                  Navigator.of(context).pop();
                  _onPressed(clubId);
                },
              ),
              CupertinoDialogAction(
                child: const Text("No"),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });
  }

  void _onPressed(String clubId) {
    setState(() => isLoading = true);
    context.read<ClubBloc>().add(LeaveClubEvent(clubId: clubId));
  }

  Future<void> _navigateToWebView() {
    return Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => const WebViewPage(
          url: 'https://fitsomnia.com/clubs/disclaimer',
          fullUrl: true,
        ),
      ),
    );
  }
}
