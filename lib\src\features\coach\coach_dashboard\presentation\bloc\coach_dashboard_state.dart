part of 'coach_dashboard_bloc.dart';

sealed class CoachDashboardState extends Equatable {
  const CoachDashboardState();
  
  @override
  List<Object> get props => [];
}

final class CoachDashboardInitial extends CoachDashboardState {

}

class CoachDashboardLoading extends CoachDashboardState {

}

class GetCoachOwnProfileSuccess extends CoachDashboardState {
  final CoachEntity coachEntity;

  const GetCoachOwnProfileSuccess({required this.coachEntity});
  
  @override
  List<Object> get props => [coachEntity];
}

class GetCoachOwnProfileFail<T> extends CoachDashboardState {
  final T? data;
  const GetCoachOwnProfileFail({required this.data});
}


class GetCoachOwnProgramsSuccess extends CoachDashboardState {
  final List<CoachProgramEntity> programs;

  const GetCoachOwnProgramsSuccess({required this.programs});
  
  @override
  List<Object> get props => [programs];
}

class GetCoachOwnProgramsFail<T> extends CoachDashboardState {
  final T? data;
  const GetCoachOwnProgramsFail({required this.data});
}

class GetCoachProgramEnrollersSuccess extends CoachDashboardState {
  final List<CoachProgramEnrollmentEntity> userSubscriptionHistoryEntity;

  const GetCoachProgramEnrollersSuccess({required this.userSubscriptionHistoryEntity});

  @override
  List<Object> get props => [userSubscriptionHistoryEntity];
}

class GetCoachProgramEnrollersFail<T> extends CoachDashboardState {
  final T? data;
  const GetCoachProgramEnrollersFail({required this.data});
}

class GetCoachProgramSingleEnrollerSuccess extends CoachDashboardState {
  final CoachProgramEnrollmentEntity enrolledProgram;

  const GetCoachProgramSingleEnrollerSuccess({required this.enrolledProgram});

  @override
  List<Object> get props => [enrolledProgram];
}

class GetCoachProgramSingleEnrollerFail<T> extends CoachDashboardState {
  final T? data;
  const GetCoachProgramSingleEnrollerFail({required this.data});
}

class ProgramSubscriptionCancelByCoachSuccess extends CoachDashboardState {
  final CoachProgramEnrollmentEntity enrolledProgram;

  const ProgramSubscriptionCancelByCoachSuccess({required this.enrolledProgram});

  @override
  List<Object> get props => [enrolledProgram];
}

class ProgramSubscriptionCancelByCoachFail<T> extends CoachDashboardState {
  final T? data;

  const ProgramSubscriptionCancelByCoachFail({required this.data});
}

class GetCoachIncomeInfoSuccess extends CoachDashboardState {
  final CoachIncomeEntity coachIncomeEntity;

  const GetCoachIncomeInfoSuccess({required this.coachIncomeEntity});
  
  @override
  List<Object> get props => [coachIncomeEntity];
}

class GetCoachIncomeInfoFail<T> extends CoachDashboardState {
  final T? data;
  const GetCoachIncomeInfoFail({required this.data});
}

class GetCoachProgramEnrollerHistorySuccess extends CoachDashboardState {
  final List<CoachProgramEnrollmentEntity> userSubscriptionHistoryEntity;

  const GetCoachProgramEnrollerHistorySuccess({required this.userSubscriptionHistoryEntity});

  @override
  List<Object> get props => [userSubscriptionHistoryEntity];
}

class GetCoachProgramEnrollerHistoryFail<T> extends CoachDashboardState {
  final T? data;
  const GetCoachProgramEnrollerHistoryFail({required this.data});
}

class GetCoachProgramPaymentHistorySuccess extends CoachDashboardState {
  final List<CoachProgramEnrollmentEntity> userSubscriptionHistoryEntity;

  const GetCoachProgramPaymentHistorySuccess({required this.userSubscriptionHistoryEntity});

  @override
  List<Object> get props => [userSubscriptionHistoryEntity];
}

class GetCoachProgramPaymentHistoryFail<T> extends CoachDashboardState {
  final T? data;
  const GetCoachProgramPaymentHistoryFail({required this.data});
}
