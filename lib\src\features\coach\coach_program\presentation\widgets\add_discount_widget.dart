import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/validators/input_validators.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';

class AddDiscountWidget extends StatefulWidget {
  const AddDiscountWidget({Key? key, required this.callback, this.priceStr}): super(key: key);
  final Function(int?) callback;
  final String? priceStr;

  @override
  State<AddDiscountWidget> createState() => _AddDiscountWidgetState();
}

class _AddDiscountWidgetState extends State<AddDiscountWidget> {
  bool isSelected = false;

  late TextEditingController priceController;

  @override
  void initState() {
    super.initState();
    priceController = TextEditingController();

    if(widget.priceStr != null && widget.priceStr != '') {
      isSelected = true;
      priceController.text = widget.priceStr!;
      Log.debug('price befre discount: ${widget.priceStr}');
  
    }

    priceController.addListener(
      () {
        int? price = int.tryParse(priceController.text);
        widget.callback(price);
      },
    );
  }

  @override
  void dispose() {
    priceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          _buildAddDuscountCheckbox(),
          if (isSelected) _buildPreviousPriceSection(),
        ],
      ),
    );
  }

  _buildAddDuscountCheckbox() {
    return GestureDetector(
      onTap: () {
        setState(() {
          isSelected = !isSelected;
          if(!isSelected) {
            priceController.text = '';
          }
        });
      },
      child: Row(
        children: [
          _buildCheckBox(),
          SizedBox(
            width: 5,
          ),
          _buildItemLabel(),
        ],
      ),
    );
  }

  _buildItemLabel() {
    return Text(
      'Add Discount',
      style: AppTypography.poppinsMedium14(color: UIColors.primaryGreen950),
    );
  }

  _buildCheckBox() {
    return Checkbox(
      value: isSelected,
      onChanged: null,
      activeColor: UIColors.primary,
      checkColor: AppColors.white,
      fillColor:
          (isSelected) ? WidgetStatePropertyAll(AppColors.primaryGreen) : null,
      side: const BorderSide(color: UIColors.primary),
    );
  }

  _buildPreviousPriceSection() {
    return _buildEditTextInfo(
      name: 'Previous Price (Higher than Set Price)',
      text: priceController.text,
      controller: priceController,
      keyboardType: TextInputType.number,
      validator: InputValidators.name,
    );
  }

  _buildEditTextInfo(
      {required String name,
      required String? text,
      required TextEditingController controller,
      required String? Function(String?)? validator,
      int maxLine = 1,
      TextInputType? keyboardType = null}) {
    if (text != null) controller.text = text;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "$name",
          style:
              AppTypography.poppinsRegular16(color: UIColors.primaryGreen950),
        ),
        SizedBox(
          height: Values.v5,
        ),
        TextFormField(
          cursorColor: AppColors.black,
          controller: controller,
          maxLines: maxLine,
          minLines: maxLine,
          autofocus: false,
          autocorrect: false,
          decoration: _buildInputDecoration(),
          validator: validator,
          keyboardType: keyboardType,
          autovalidateMode: AutovalidateMode.onUserInteraction,
        )
      ],
    );
  }

  InputDecoration _buildInputDecoration() {
    var inputBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(Values.v10),
      borderSide: BorderSide(color: AppColors.greyscale50, width: Values.v2),
    );

    return InputDecoration(
      enabledBorder: inputBorder,
      focusedBorder: inputBorder,
      // hintText: 'Write a short bio?',
      hintStyle: AppTypography.regular18(
        color: AppColors.silver,
      ),
      contentPadding: EdgeInsets.all(Values.v16),
      // filled: true,
      // fillColor: AppColors.alto.withOpacity(0.2),
    );
  }
}
