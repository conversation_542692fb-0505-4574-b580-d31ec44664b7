part of '../pages/login_page.dart';

class _SocialLoginBuilder extends StatelessWidget {
  const _SocialLoginBuilder({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    LoginBloc bloc = context.read<LoginBloc>();

    return Column(
      children: [
        CenterText(
          text: TextConstants.otherLogins,
          textStyle: AppTypography.poppinsRegular16(),
        ),
        SizedBox(height: 25.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildGoogleLoginButton(bloc),
            Sized<PERSON>ox(width: Values.v35.w),
            _buildFacebookLoginButton(bloc),
            <PERSON><PERSON><PERSON><PERSON>(width: Values.v35.w),
            _buildAppleLoginInButton(bloc)
          ],
        ),
      ],
    );
  }

  Widget _buildGoogleLoginButton(LoginBloc bloc) {
    return SocialIconContainer(
      onTap: () {
        bloc.add(LoginWithGoogleEvent());
      },
      icon: Assets.googleIcon,
    );
  }

  Widget _buildFacebookLoginButton(LoginBloc bloc) {
    return SocialIconContainer(
      onTap: () {
        bloc.add(LoginWithFacebookEvent());
      },
      icon: Assets.facebookIcon,
    );
  }

  Widget _buildAppleLoginInButton(LoginBloc bloc) {
    return SocialIconContainer(
      onTap: () {
        bloc.add(LoginWithAppleEvent());
      },
      icon: Assets.appleIcon,
    );
  }
}
