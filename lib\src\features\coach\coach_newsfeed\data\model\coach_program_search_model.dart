import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_program_search_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';

class CoachProgramSearchModel extends CoachProgramSearchEntity {
  CoachProgramSearchModel({
    required super.programId,
    required super.coachName,
    required super.coachId,
    required super.description,
    required super.images,
    required super.programRating,
    required super.title,
  });

  factory CoachProgramSearchModel.fromJson(Map<String, dynamic> json) {
    return CoachProgramSearchModel(
      programId: json['id'],
      coachName: json['userName'],
      coachId: json['coachId'],
      title: json['title'],
      description: json['description'],
      images: List<CoachMediaFile>.from(json['media']!.map((x) => CoachMediaFile.fromJson(x))),
      programRating: json['rating'] ?? 0,
    );
  }
}
