class FoodScanImageRequest {
  final String filePath;
  final String featureName;
  String? mediaType;
  String? fileExtention;

  FoodScanImageRequest({
    required this.filePath,
    required this.featureName,
    this.mediaType,
    this.fileExtention,
  });

  @override
  String toString() => 'FoodScanImageRequest(imagePath: $filePath)';
}

class FoodScanFeedbackRequest {
  final String feedback;
  final String featureName;

  FoodScanFeedbackRequest({
    required this.feedback,
    required this.featureName,
  });

  @override
  String toString() => 'FoodScanFeedbackRequest(feedback: $feedback)';
}
