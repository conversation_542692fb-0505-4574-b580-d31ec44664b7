import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/repository/group_repository.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/entity/group_chat_entity.dart';

class GetGroupHistoryUseCase {
  const GetGroupHistoryUseCase({required this.groupRepository});

  final GroupRepository groupRepository;

  Future<Either<String, List<GroupChatEntity>>> call(
      {int? limit, int? offset, required String groupId}) async {
    return await groupRepository.getGroupHistory(
      groupId: groupId,
      limit: limit,
      offset: offset,
    );
  }
}
