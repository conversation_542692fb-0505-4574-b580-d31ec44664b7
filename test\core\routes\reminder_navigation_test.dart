import 'package:flutter_test/flutter_test.dart';
import 'package:fitsomnia_app/src/core/routes/global_navigation.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';

void main() {
  group('ReminderFeatureType', () {
    test('should parse feature strings correctly', () {
      expect(ReminderFeatureType.fromString('rewards'), ReminderFeatureType.rewards);
      expect(ReminderFeatureType.fromString('referrals'), ReminderFeatureType.referrals);
      expect(ReminderFeatureType.fromString('daily-tasks'), ReminderFeatureType.dailyTasks);
      expect(ReminderFeatureType.fromString('challenges'), ReminderFeatureType.challenges);
      expect(ReminderFeatureType.fromString('diet'), ReminderFeatureType.diet);
      expect(ReminderFeatureType.fromString('leaderboard'), ReminderFeatureType.leaderboard);
      expect(ReminderFeatureType.fromString('other'), ReminderFeatureType.other);
    });

    test('should handle case insensitive parsing', () {
      expect(ReminderFeatureType.fromString('REWARDS'), ReminderFeatureType.rewards);
      expect(ReminderFeatureType.fromString('Diet'), ReminderFeatureType.diet);
      expect(ReminderFeatureType.fromString('DAILY-TASKS'), ReminderFeatureType.dailyTasks);
    });

    test('should return other for null or empty strings', () {
      expect(ReminderFeatureType.fromString(null), ReminderFeatureType.other);
      expect(ReminderFeatureType.fromString(''), ReminderFeatureType.other);
    });

    test('should return other for unknown feature types', () {
      expect(ReminderFeatureType.fromString('unknown'), ReminderFeatureType.other);
      expect(ReminderFeatureType.fromString('invalid'), ReminderFeatureType.other);
    });
  });

  group('ReminderNavigationHelper', () {
    test('should return correct routes for each feature type', () {
      expect(ReminderNavigationHelper.getRouteForFeature(ReminderFeatureType.rewards), 
             Routes.rewardDashboardPage);
      expect(ReminderNavigationHelper.getRouteForFeature(ReminderFeatureType.referrals), 
             Routes.referralListPage);
      expect(ReminderNavigationHelper.getRouteForFeature(ReminderFeatureType.dailyTasks), 
             Routes.dailyTaskPage);
      expect(ReminderNavigationHelper.getRouteForFeature(ReminderFeatureType.challenges), 
             Routes.allMonthlyChallenges);
      expect(ReminderNavigationHelper.getRouteForFeature(ReminderFeatureType.diet), 
             Routes.dietDashboard);
      expect(ReminderNavigationHelper.getRouteForFeature(ReminderFeatureType.leaderboard), 
             Routes.leaderboard);
      expect(ReminderNavigationHelper.getRouteForFeature(ReminderFeatureType.other), 
             null);
    });

    test('should return correct arguments for feature types', () {
      // Diet feature should have specific arguments
      expect(ReminderNavigationHelper.getArgumentsForFeature(ReminderFeatureType.diet), 
             [false, false]);
      
      // Other features should have null arguments
      expect(ReminderNavigationHelper.getArgumentsForFeature(ReminderFeatureType.rewards), 
             null);
      expect(ReminderNavigationHelper.getArgumentsForFeature(ReminderFeatureType.referrals), 
             null);
      expect(ReminderNavigationHelper.getArgumentsForFeature(ReminderFeatureType.dailyTasks), 
             null);
      expect(ReminderNavigationHelper.getArgumentsForFeature(ReminderFeatureType.challenges), 
             null);
      expect(ReminderNavigationHelper.getArgumentsForFeature(ReminderFeatureType.leaderboard), 
             null);
      expect(ReminderNavigationHelper.getArgumentsForFeature(ReminderFeatureType.other), 
             null);
    });
  });

  group('Integration Tests', () {
    test('should handle complete flow from feature string to route', () {
      // Test rewards flow
      final rewardsFeature = ReminderFeatureType.fromString('rewards');
      final rewardsRoute = ReminderNavigationHelper.getRouteForFeature(rewardsFeature);
      final rewardsArgs = ReminderNavigationHelper.getArgumentsForFeature(rewardsFeature);
      
      expect(rewardsRoute, Routes.rewardDashboardPage);
      expect(rewardsArgs, null);

      // Test diet flow
      final dietFeature = ReminderFeatureType.fromString('diet');
      final dietRoute = ReminderNavigationHelper.getRouteForFeature(dietFeature);
      final dietArgs = ReminderNavigationHelper.getArgumentsForFeature(dietFeature);
      
      expect(dietRoute, Routes.dietDashboard);
      expect(dietArgs, [false, false]);

      // Test unknown feature flow
      final unknownFeature = ReminderFeatureType.fromString('unknown');
      final unknownRoute = ReminderNavigationHelper.getRouteForFeature(unknownFeature);
      final unknownArgs = ReminderNavigationHelper.getArgumentsForFeature(unknownFeature);
      
      expect(unknownRoute, null);
      expect(unknownArgs, null);
    });
  });
}
