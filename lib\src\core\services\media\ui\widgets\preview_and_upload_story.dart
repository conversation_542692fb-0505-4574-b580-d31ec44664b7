import 'dart:io';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/services/media/ui/widgets/trim_thumbnail_generator.dart';
import 'package:fitsomnia_app/src/core/widgets/loading_overlay.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/challenge_details/presentation/page/preview_recorded_video_page.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/story_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PreviewAndUploadStory extends StatefulWidget {
  const PreviewAndUploadStory({
    Key? key,
    required this.filePath,
  }) : super(key: key);

  final List<String?> filePath;

  @override
  State<PreviewAndUploadStory> createState() => _PreviewAndUploadStoryState();
}

class _PreviewAndUploadStoryState extends State<PreviewAndUploadStory> {
  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      child: PopScope(
        canPop: true,
        onPopInvokedWithResult: (didPop, result) {
          if (!didPop) {
            Navigator.pop(
              context,
              widget.filePath.isNotEmpty ? true : false,
            );
          }
        },
        child: Scaffold(
          appBar: _buildAppBar(),
          body: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 1.0,
              mainAxisSpacing: 4,
              crossAxisSpacing: 4,
            ),
            padding: EdgeInsets.symmetric(
              horizontal: 10.w,
              vertical: 5.h,
            ),
            itemCount: widget.filePath.length,
            itemBuilder: (BuildContext ctx, index) {
              return _createGridTileWidget(widget.filePath[index] ?? '');
            },
          ),
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text('View and upload'),
      elevation: 1,
      actions: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 8.w),
          child: BlocConsumer<StoryBloc, StoryState>(
            listener: (context, state) {
              if (state is StorySuccessState) {
                Navigator.popUntil(context, (route) => route.isFirst);
              } else if (state is StoryFailedState) {
                AppToast.showToast(message: state.message);
                setState(() {
                  state.filePath?.forEach((path) {
                    widget.filePath.removeWhere((element) => element != path);
                  });
                });
                LoadingOverlay.of(context).hide();
              }
            },
            builder: (context, state) {
              return ElevatedButton(
                onPressed: state is StoryLoadingState || widget.filePath.isEmpty
                    ? null
                    : () {
                        LoadingOverlay.of(context).show();
                        for (var file in widget.filePath) {
                          context
                              .read<StoryBloc>()
                              .add(CreateStoryEvent(files: [File(file!)]));
                        }
                      },
                child: Text(
                  'Upload',
                  style: AppTypography.bold16(color: AppColors.white),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _createGridTileWidget(String path) => Builder(
        builder: (context) => InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PreviewRecordedVideoPage(
                  path: path,
                  removeFloatingIcon: true,
                ),
              ),
            );
          },
          onLongPress: () {
            _buildDialog(context, path);
          },
          child: ClipRRect(
            borderRadius: BorderRadius.circular(Values.v4),
            child: TrimThumbnailGenerator(
              videoUrl: path,
            ),
          ),
        ),
      );

  void _buildDialog(BuildContext context, String path) {
    showDialog(
      context: context,
      builder: (context) {
        return CupertinoAlertDialog(
          title: const Text('Delete video'),
          content: const Text('Sure to delete this video'),
          actions: [
            TextButton(
              onPressed: () {
                setState(() {
                  widget.filePath.remove(path);
                });
                Navigator.pop(context);
              },
              child: Text(
                'Yes',
                style: AppTypography.regular16(
                  color: AppColors.black,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context); //close Dialog
              },
              child: Text(
                'No',
                style: AppTypography.regular16(
                  color: AppColors.black,
                ),
              ),
            )
          ],
        );
      },
    );
  }
}
