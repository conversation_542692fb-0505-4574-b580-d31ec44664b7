import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/exception/network_exception.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/club/details/data/models/club_members_model.dart';
import 'package:fitsomnia_app/src/features/club/details/data/models/live_members_reponse_model.dart';
import 'package:fitsomnia_app/src/features/club/details/data/models/people_from_nearby_clubs_model.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/entities/club_members_entity.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/entities/live_member_model.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/entities/nearby_clubs_members_entity.dart';
import 'package:fitsomnia_app/src/features/club/root/data/data_source/club_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/club/root/data/models/club_model.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/entities/club_entity.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/repository/club_repository.dart';

class ClubRepositoryImpl implements ClubRepository {
  const ClubRepositoryImpl({required this.clubRemoteDataSource});

  final ClubRemoteDataSource clubRemoteDataSource;

  @override
  Future<Either<ErrorResponseModel, ClubEntity>> myClub() async {
    Response? response;
    try {
      response = await clubRemoteDataSource.myClub();
      ClubModel model = ClubModel.fromJson(response.data['data']);

      return Right(model);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel);
    }
  }

  @override
  Future<Either<ErrorResponseModel, List<NearbyClubsMemberEntity>>>
      nearbyClubsMembers(
    Map<String, dynamic> map,
    String id,
    int? offset,
  ) async {
    try {
      final Response response = await clubRemoteDataSource.nearbyClubsMembers(
        map,
        id,
        offset,
      );
      final data = response.data['data'];

      List<PeopleFromNearbyClubsModel> models = data
          .map<PeopleFromNearbyClubsModel>(
              (json) => PeopleFromNearbyClubsModel.fromJson(json))
          .toList();

      return Right(models);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel);
    }
  }

  @override
  Future<Either<ErrorResponseModel, List<ClubMemberEntity>>> clubMembers(
    String id,
    int? offset,
  ) async {
    try {
      final Response response =
          await clubRemoteDataSource.clubMembers(id, offset);
      final data = response.data['data'];

      List<ClubMembersModel> models = data
          .map<ClubMembersModel>(
            (json) => ClubMembersModel.fromJson(json),
          )
          .toList();

      return Right(models);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel);
    }
  }

  @override
  Future<Either<ErrorResponseModel, List<ClubEntity>>> findNearbyClubs({
    double? long,
    double? lat,
    int? offset,
  }) async {
    Response? response;
    try {
      response = await clubRemoteDataSource.findNearbyClubs(
          long: long, lat: lat, offset: offset);
      List<ClubModel> nearbyClubList = (response.data['data'] as List<dynamic>)
          .map(
            (e) => ClubModel.fromJson(e),
          )
          .toList();

      return Right(nearbyClubList);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel);
    }
  }

  @override
  Future<Either<ErrorResponseModel, String>> joinClub(String clubId) async {
    Response? response;
    try {
      response = await clubRemoteDataSource.joinClub(clubId);

      return Right(response.data['data']['message']);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel);
    }
  }

  @override
  Future<Either<ErrorResponseModel, String>> leaveAndJoinClub(
      String clubId) async {
    Response? response;
    try {
      response = await clubRemoteDataSource.leaveAndJoinClub(clubId);

      return Right(response.data['data']['message']);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel);
    }
  }

  @override
  Future<Either<ErrorResponseModel, String>> leaveClub(String clubId) async {
    Response? response;
    try {
      response = await clubRemoteDataSource.leaveClub(clubId);

      return Right(response.data['data']['message']);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel);
    }
  }

  @override
  Future<Either<ErrorModel, List<LiveMemberModel>>> liveMembersNearMe(
    Map<String, dynamic> query,
  ) async {
    Response? response;
    try {
      response = await clubRemoteDataSource.liveMembersNearMe(query);

      LiveMembersResponseModel model = LiveMembersResponseModel.fromJson(
        response.data,
      );

      List<LiveMemberModel> liveMembers = model.data;

      return Right(liveMembers);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel.error!);
    }
  }
}
