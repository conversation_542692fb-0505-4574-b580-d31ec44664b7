import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class DailyTaskDataSource {
  Future<Response> getDailyTasks(
      {required int? offset,
      required int? limit,
      required String? taskTypeFilter,
      required String? taskStatusFilter});
}

class DailyTaskDataSourceImpl extends DailyTaskDataSource {
  final RestClient restClient;

  DailyTaskDataSourceImpl({required this.restClient});

  @override
  Future<Response> getDailyTasks(
      {required int? offset,
      required int? limit,
      required String? taskTypeFilter,
      required String? taskStatusFilter}) async {
    
    String path = '${API.dailyTask}?offset=${(offset == null) ? 0 : offset}&limit=${(limit == null) ? 100 : limit}';
    if(taskTypeFilter != null) {
      path += '&type=$taskTypeFilter';
    }

    if(taskStatusFilter != null) {
      path += '&status=$taskStatusFilter';
    }

    final response = await restClient.get(APIType.PROTECTED, path);

    return response;
  }
}
