import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/domain/entity/group_response_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/repository/group_repository.dart';

class CreateGroupUseCase {
  const CreateGroupUseCase({required this.groupRepository});

  final GroupRepository groupRepository;

  Future<Either<String, GroupResponseEntity>> call({
    required Map<String, dynamic> mapData,
  }) async {
    return await groupRepository.createGroup(mapData: mapData);
  }
}
