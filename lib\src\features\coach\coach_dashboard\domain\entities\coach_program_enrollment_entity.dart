import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/model/coach_program_model.dart';

class CoachProgramEnrollmentEntity {
  final String subscriptionId;
  final String programId;
  final String programTitle;
  final int? programDurationCount;
  final String? programDurationTerm;
  final bool isPaymentNeeded;
  final bool isCompleted;
  final DateTime? subscriptionDate;
  String? coachId;
  String? coachUserId;
  String? userId;
  String? userName;
  String? userImage;
  String? coachName;
  List<CoachMediaFile> coachImages;
  String? description;
  final String? cancelStatus;
  final String? refundStatus;
  final int? userTotalPaid;
  final bool? isFirstPaymentDone;
  final DateTime? nextPaymentDate;
  final String? coachReviewId;
  final String? programReviewId;
  final String? paymentTerm;

  final CoachProgramPaymentInfo? oneTimePrice;
  final CoachProgramPaymentInfo? monthlyPrice;

  CoachProgramEnrollmentEntity({
    required this.subscriptionId,
    required this.programId,
    required this.programTitle,
    required this.programDurationCount,
    required this.programDurationTerm,
    required this.isPaymentNeeded,
    required this.isCompleted,
    required this.coachId,
    required this.coachUserId,
    required this.userId,
    required this.userName,
    required this.userImage,
    required this.coachName,
    required this.coachImages,
    required this.subscriptionDate,
    required this.description,
    this.paymentTerm,
    this.cancelStatus,
    this.refundStatus,
    this.userTotalPaid,
    this.isFirstPaymentDone,
    this.nextPaymentDate,
    this.coachReviewId,
    this.programReviewId,
    this.oneTimePrice,
    this.monthlyPrice,
  });

  double get actualPrice {
    if (paymentTerm == null) {
      return 0;
    } else {
      if (paymentTerm == CoachProgramSubscriptionPaymentTerm.ONE_TIME.key()) {
        return oneTimePrice!.actualPrice!;
      } else if (paymentTerm ==
          CoachProgramSubscriptionPaymentTerm.MONTHLY.key()) {
        return monthlyPrice!.actualPrice!;
      }

      return 0;
    }
  }

  double get discountPrice {
    if (paymentTerm == null) {
      return 0;
    } else {
      if (paymentTerm == CoachProgramSubscriptionPaymentTerm.ONE_TIME.key()) {
        return oneTimePrice!.discountedPrice!;
      } else if (paymentTerm ==
          CoachProgramSubscriptionPaymentTerm.MONTHLY.key()) {
        return monthlyPrice!.discountedPrice!;
      }

      return 0;
    }
  }

  int get nextPaymentAfterNDays {
    if (!isPaymentNeeded) {
      return 0;
    }
    if (nextPaymentDate == null) {
      return 0;
    }

    int nDays = nextPaymentDate!.difference(DateTime.now()).inDays;
    if (nDays < 0) nDays = 0;

    return nDays;
  }

  DateTime? get programCompleteAtDate {
    if (subscriptionDate == null ||
        programDurationTerm == null ||
        programDurationCount == null ||
        programDurationCount! < 1) {
      return null;
    }
    int programInDays = 0;
    if (programDurationTerm == 'days') {
      programInDays = programDurationCount!;
    } else if (programDurationTerm == 'months') {
      programInDays = (programDurationCount! * 30 ) ;
    } else if (programDurationTerm == 'weeks') {
      programInDays = programDurationCount! * 7;
    }

    DateTime programCompletionDate =
        subscriptionDate!.add(Duration(days: programInDays));
    
    return programCompletionDate;
  }

  bool get canRequestRefund {
    if(subscriptionDate == null) return false;

    DateTime refundRequestLastDate = subscriptionDate!.add(const Duration(days: 7));

    return DateTime.now().isBefore(refundRequestLastDate);

  }
}

/// subscription cancel status
/// canceled_by_user
/// canceled_by_coach
/// refund status
/// refund_requested
/// refunnd_successful
/// refund_rejected

var testProgramEnrollmentEntity = CoachProgramEnrollmentEntity(
  subscriptionId: "c913ea5b-9829-4c56-b6c8-fc19356988e9",
  programId: "programId",
  programTitle: "Lose 10 Kgs In 30 Days Without Changing Your Diet",
  programDurationCount: 6,
  programDurationTerm: 'months',
  isPaymentNeeded: true,
  isCompleted: true,
  coachId: "c578fb71-509d-4da0-922d-c533800bac7e",
  coachUserId: "60ed012b-ac63-4e3e-bc17-9575200d8251",
  userId: "60ed012b-ac63-4e3e-bc17-9575200d8251",
  userName: "Mahmudul Hasan",
  userImage:
      'https://dev-public-cdn.fitsomnia.com/original/coach/2025/1/28/6293a7cb-9788-4cc1-9cf3-12eb56ff687e/938925_1738071637314B9A405711.jpg',
  coachName: 'Ashraful Haque',
  coachImages: [
    CoachMediaFile(
        mediaType: 'image',
        url:
            'https://dev-public-cdn.fitsomnia.com/original/coach/2025/1/28/6293a7cb-9788-4cc1-9cf3-12eb56ff687e/938925_1738071637314B9A405711.jpg')
  ],
  subscriptionDate: DateTime.tryParse('2025-01-09T12:36:27.454Z'),
  nextPaymentDate: DateTime.tryParse('2025-01-09T12:36:27.454Z'),
  description:
      'I will assess the physical and health conditions of clients, create appropriate exercise plans, and monitor their improvement.',
  cancelStatus: 'canceled_by_coach',
  refundStatus: null,
  paymentTerm: 'monthly',
);
