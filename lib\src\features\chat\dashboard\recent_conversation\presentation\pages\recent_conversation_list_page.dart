import 'dart:async';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/services/socket/scoket_service.dart';
import 'package:fitsomnia_app/src/core/services/socket/socket_enum.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/loading_widget.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/domain/entity/chat_list_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/presentation/bloc/recent_conversation_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/presentation/pages/conversation_card.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RecentConversation extends StatefulWidget {
  const RecentConversation({Key? key}) : super(key: key);

  @override
  State<RecentConversation> createState() => _RecentConversationState();
}

class _RecentConversationState extends State<RecentConversation> {
  final ScrollController _scrollController = ScrollController();

  StreamController<ChatListEntity> streamController =
      StreamController<ChatListEntity>.broadcast();

  int _offset = 0;
  final int _limit = 10;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    context.read<RecentConversationBloc>().recentConversationList.clear();
    _loadConversation();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        _loadConversation();
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _scrollController.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (mounted) _initiateSocketListener();
  }

  Future<void> _loadConversation() async {
    if (_isLoading) {
      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    if (context
        .read<RecentConversationBloc>()
        .recentConversationList
        .isNotEmpty) {
      _offset =
          context.read<RecentConversationBloc>().recentConversationList.length;
    }

    BlocProvider.of<RecentConversationBloc>(context).add(
      GetRecentConversationListEvent(
        limit: _limit,
        offset: _offset,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RecentConversationBloc, RecentConversationState>(
      listener: (context, state) {
        if (state is RecentConversationSuccess) {
          BlocProvider.of<RecentConversationBloc>(context).add(
            UpdateRecentConversationListEvent(
              chatListEntities: state.chatListEntity,
            ),
          );

          clearStreamAndAddNewValues();

          if (mounted) {
            setState(() {
              _isLoading = false;
            });
          }
        }
      },
      builder: (context, state) {
        return StreamBuilder<ChatListEntity>(
            stream: streamController.stream,
            builder: (context, snapshot) {
              return recentConversationThread();
            });
      },
    );
  }

  Widget recentConversationThread() {
    var conversationList =
        context.read<RecentConversationBloc>().recentConversationList;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: Values.v16.r),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(Values.v10.r),
      ),
      child: ListView.builder(
        controller: _scrollController,
        itemCount: conversationList.length + (_isLoading ? 1 : 0),
        itemBuilder: (context, index) {
          // print('conversation list created at ${conversationList[index].toViewModel().createdAt}');

          return index == conversationList.length
              ? const LoadingIndicator()
              : ConversationCard(
                  key: Key(conversationList[index].toViewModel().id),
                  chat: conversationList[index].toViewModel(),
                );
        },
      ),
    );
  }

  Widget noRecentConversationOrLoading({
    String text = "",
    bool? progressIndicator,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.only(top: Values.v150),
        child: progressIndicator != null
            ? CircularProgressIndicator(color: AppColors.primaryGreen)
            : Text(
                text,
                style: AppTypography.light14(),
              ),
      ),
    );
  }

  void _initiateSocketListener() {
    SocketService.instance.on(
        ServerToClient.DIRECT_SERVER_TO_CLIENT_RECEIVE_MESSAGE.name, (data) {
      if (mounted) {
        BlocProvider.of<RecentConversationBloc>(context).add(
          DeleteAndInsertAtFirstEvent(
              data, BlocProvider.of<SharedBloc>(context).userProfile!),
        );
        clearStreamAndAddNewValues();
      }
    });
  }

  void clearStreamAndAddNewValues() {
    context
        .read<RecentConversationBloc>()
        .recentConversationList
        .forEach((element) {
      streamController.sink.add(element);
    });
  }
}
