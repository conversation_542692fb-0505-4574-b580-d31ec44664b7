class RewardLeaderboardDataEntity {
  final String userId;
  final String userName;
  final String userImage;
  final int totalPoints;
  int? rank;

  RewardLeaderboardDataEntity({
    required this.userId,
    required this.userName,
    required this.userImage,
    required this.totalPoints,
    this.rank,
  });
}

var testLeaderBoardEntity = RewardLeaderboardDataEntity(
  userId: 'user-id',
  userName: 'Mahm<PERSON><PERSON> Islam',
  userImage: "https://dev-public-cdn.fitsomnia.com/original/user/2024/12/24/397b152c-fb18-47b8-a7a4-63af3b34d08f/473224_17350187829151000000023.jpg",
  totalPoints: 1000,
);
