import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/use_cases/leave_group_use_case.dart';

part 'leave_group_event.dart';
part 'leave_group_state.dart';

class LeaveGroupBloc extends Bloc<LeaveGroupEvent, LeaveGroupState> {
  LeaveGroupBloc({required this.leaveGroupUseCase})
      : super(LeaveGroupInitial()) {
    on<LeaveGroupEvent>(_onLeaveGroupEvent);
  }

  late LeaveGroupUseCase leaveGroupUseCase;

  Future<void> _onLeaveGroupEvent(
    LeaveGroupEvent event,
    Emitter<LeaveGroupState> emit,
  ) async {
    emit(LeaveGroupLoading());

    try {
      final response = await leaveGroupUseCase.call(
        groupId: event.groupId,
      );

      response.fold(
        (l) => emit(
          LeaveGroupFailure(
            errorMessage: l.toString(),
          ),
        ),
        (r) => emit(LeaveGroupSuccess(message: r)),
      );
    } catch (_) {
      emit(
        LeaveGroupFailure(
          errorMessage: TextConstants.unexpectedError,
        ),
      );
    }
  }
}
