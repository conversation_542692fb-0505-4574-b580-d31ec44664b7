import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:input_form_field/input_form_field.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/core/validators/input_validators.dart';
import 'package:fitsomnia_app/src/core/widgets/show_snack_bar_message.dart';
import 'package:fitsomnia_app/src/features/auth/root/presentations/widgets/center_text.dart';
import 'package:fitsomnia_app/src/features/auth/reset_password/bloc/reset_password/reset_password_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/reset_password/bloc/forgot_password/forgot_password_bloc.dart';

class ResetPasswordPage extends StatelessWidget {
  ResetPasswordPage({Key? key}) : super(key: key);

  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: UIColors.background,
      appBar: AppBar(
        backgroundColor: UIColors.background,
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: Padding(
            padding: _buildPadding(),
            child: Column(
              children: [
                CenterText(
                  text: TextConstants.resetPassword,
                  textStyle: AppTypography.regular16(
                    color: AppColors.black,
                  ),
                ),
                SizedBox(height: Values.v25.h),
                _buildNewPassword(context),
                _buildConfirmPassword(context),
                const Spacer(),
                _buildChangePasswordButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNewPassword(BuildContext context) {
    return InputFormField(
      textEditingController: context.read<ResetPasswordBloc>().passwordField,
      labelText: TextConstants.password,
      password: EnabledPassword(),
      validator: InputValidators.password,
    );
  }

  Widget _buildConfirmPassword(BuildContext context) {
    ResetPasswordBloc resetPasswordBloc = context.read<ResetPasswordBloc>();

    return InputFormField(
      textEditingController: resetPasswordBloc.confirmPasswordField,
      labelText: TextConstants.confirmPassword,
      password: EnabledPassword(),
      validator: (String? value) {
        if (value != resetPasswordBloc.passwordField.text) {
          return TextConstants.passwordDoNotMatch;
        }

        return null;
      },
    );
  }

//ignore: long-method
  Widget _buildChangePasswordButton() {
    return BlocConsumer<ResetPasswordBloc, BaseState>(
      listener: (context, state) {
        if (state is SuccessState) {
          context.read<ForgotPasswordBloc>().emailOrPhoneField.clear();
          Navigator.pop(context);
        } else if (state is ErrorState) {
          ShowSnackBarMessage.showErrorSnackBar(
            message: state.data,
            context: context,
          );
        }
      },
      builder: (context, state) {
        return Button.filled(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              BlocProvider.of<ResetPasswordBloc>(context).add(
                ResetPasswordWithEmailAndPasswordEvent(
                  email: context
                          .read<ForgotPasswordBloc>()
                          .emailOrPhoneField
                          .text
                          .trim()
                          .contains('@')
                      ? context
                          .read<ForgotPasswordBloc>()
                          .emailOrPhoneField
                          .text
                          .trim()
                      : null,
                  phone: !context
                          .read<ForgotPasswordBloc>()
                          .emailOrPhoneField
                          .text
                          .trim()
                          .contains('@')
                      ? context
                          .read<ForgotPasswordBloc>()
                          .emailOrPhoneField
                          .text
                          .trim()
                      : null,
                ),
              );
            }
          },
          isLoading: state is LoadingState,
          label: TextConstants.resetPassword,
        );
      },
    );
  }

  EdgeInsets _buildPadding() {
    return EdgeInsets.only(
      top: Values.v10.h,
      left: Values.v16.w,
      right: Values.v16.w,
    );
  }
}
