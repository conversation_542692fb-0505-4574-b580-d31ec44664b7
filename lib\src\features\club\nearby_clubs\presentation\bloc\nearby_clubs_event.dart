part of 'nearby_clubs_bloc.dart';

abstract class NearbyClubsEvent extends Equatable {
  const NearbyClubsEvent();
}

class FindNearbyClubsEvent extends NearbyClubsEvent {
  final bool pullToRefresh;

  const FindNearbyClubsEvent({required this.pullToRefresh});
  @override
  List<Object?> get props => [pullToRefresh];
}

class JoinClubEvent extends NearbyClubsEvent {
  const JoinClubEvent({required this.clubId});

  final String clubId;

  @override
  List<Object?> get props => [];
}

class LeaveAndJoinClubEvent extends NearbyClubsEvent {
  const LeaveAndJoinClubEvent({required this.clubId});

  final String clubId;

  @override
  List<Object?> get props => [];
}
