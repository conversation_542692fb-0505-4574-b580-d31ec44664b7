part of 'app_storage_imp.dart';

class _SharedPreference {
  static const String _hasRunBefore = 'hasRunBefore';
  static const String _spotProfileAlertTime = 'spotProfileAlertTime';
  static const String _isSpotProfileExist = 'isSpotProfileExist';
  static const String _isHowToWinPointsAlertShownBefore = 'isHowToWinPointsAlertShownBefore';
  static const String _isShowWelcomeToFitsomniaBefore = 'isShowWelcomeToFitsomnia';

  static Future<SharedPreferences> getPrefs() async {
    return await SharedPreferences.getInstance();
  }

  static Future<bool> hasRunBefore() async {
    SharedPreferences prefs = await getPrefs();

    return prefs.getBool(_hasRunBefore) ?? false;
  }

  static Future<void> setHasRunBefore() async {
    SharedPreferences prefs = await getPrefs();

    prefs.setBool(_hasRunBefore, true);
  }

  static Future<void> storeNewsFeed(List<String> jsonList) async {
    SharedPreferences prefs = await getPrefs();

    prefs.setStringList(AppStorageImp._keyNewsFeed, jsonList);
  }

  static Future<List<FeedEntity>?> retrieveNewsFeed() async {
    SharedPreferences prefs = await getPrefs();
    final List<String>? jsonList = prefs.getStringList(AppStorageImp._keyNewsFeed);
    List<FeedEntity>? models = jsonList
        ?.map<FeedModel>((element) => FeedModel.fromJson(jsonDecode(element)))
        .toList();

    return models;
  }

  static Future<String?> getSpotProfileAlertLastShowTime() async {
    SharedPreferences prefs = await getPrefs();

    return prefs.getString(_spotProfileAlertTime);
  }

  static Future<void> setSpotProfileAlertLastShowTime() async {
    SharedPreferences prefs = await getPrefs();
    await prefs.setString(_spotProfileAlertTime, DateTime.now().toString());
  } 

  static Future<void> setSpotProfileExistStatus(bool status) async {
    SharedPreferences prefs = await getPrefs();
    await prefs.setBool(_isSpotProfileExist, status);
  }

  static Future<bool> isSpotProfileExist() async {
    SharedPreferences prefs = await getPrefs();

    return prefs.getBool(_isSpotProfileExist) ?? false;
  }

  static Future<void> setHowToWinPointsAlertStatus(bool status) async {
    SharedPreferences prefs = await getPrefs();
    await prefs.setBool(_isHowToWinPointsAlertShownBefore, status);
  }

  static Future<bool> isHowToWinPointsAlertShown() async {
    SharedPreferences prefs = await getPrefs();

    return prefs.getBool(_isHowToWinPointsAlertShownBefore) ?? false;
  }

  static Future<void> setWelcomeToFitsomniaAlertStatus(bool status) async {
    SharedPreferences prefs = await getPrefs();
    await prefs.setBool(_isShowWelcomeToFitsomniaBefore, status);
  }

  static Future<bool> isWelcomeToFitsomniaShown() async {
    SharedPreferences prefs = await getPrefs();

    return prefs.getBool(_isShowWelcomeToFitsomniaBefore) ?? false;
  }
}
