import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/package_info_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/auth/logout/presentation/cubit/logout_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AppDrawer extends StatefulWidget {
  final int index;

  const AppDrawer({Key? key, required this.index}) : super(key: key);

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  final List<_DrawerItemModel> drawerItems = [
    _DrawerItemModel(
      name: TextConstants.hallOfWeight,
      route: Routes.hallOfWeight,
      icon: Assets.hallOfWeight,
    ),
    _DrawerItemModel(
      name: TextConstants.training,
      route: Routes.training,
      icon: Assets.train,
    ),
    _DrawerItemModel(
      name: 'Shop',
      route: Routes.fitMarketDashboard,
      icon: Assets.navFitMarketIcon,
    ),
    _DrawerItemModel(
      name: TextConstants.planner,
      route: Routes.planner,
      icon: Assets.planner,
    ),
    // _DrawerItemModel(
    //   name: TextConstants.club,
    //   route: Routes.club,
    //   icon: Assets.region,
    // ),
    _DrawerItemModel(
      name: TextConstants.orderHistory,
      route: Routes.orderHistory,
      icon: Assets.history,
    ),
    _DrawerItemModel(
      name: TextConstants.settings,
      route: Routes.settings,
      icon: Assets.settingsGreen,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: UIColors.fitBlack,
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(10.sp),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCloseIcon(context),
              SizedBox(height: Values.v20.h),
              const Divider(color: UIColors.strokeGrey),
              // SizedBox(height: Values.v20.h),
              // Container(
              //   width: 124.w,
              //   height: 36.h,
              //   decoration: BoxDecoration(
              //     color: UIColors.primary,
              //     borderRadius: BorderRadius.circular(50.0),
              //   ),
              //   child: Center(
              //     child: Text(
              //       'Fitpoints: 200',
              //       style: AppTypography.poppinsRegular14(),
              //     ),
              //   ),
              // ),
              SizedBox(height: Values.v48.h),
              ...drawerItems.map(
                (item) => _buildDrawerItem(context, item),
              ),
              _buildLogoutButton(context),
              SizedBox(height: Values.v16.h),
              // const Divider(color: UIColors.strokeGrey),
              // SizedBox(height: Values.v48.h),
              // _buildDrawerItem(
              //   context,
              //   _DrawerItemModel(
              //     name: 'Language',
              //     icon: Assets.language,
              //   ),
              // ),
              // _buildDrawerItem(
              //   context,
              //   _DrawerItemModel(
              //     name: 'Region: Australia',
              //     icon: Assets.region,
              //   ),
              // ),
              const Spacer(),
              _buildCurrentVersionText(),
              SizedBox(height: 32.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCloseIcon(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            SvgPicture.asset(
              Assets.drawerLogo,
              height: 60.h,
              width: 60.h,
            ),
            SizedBox(
              width: 20.w,
            ),
            Text(
              'Fitsomnia',
              style: AppTypography.medium24(
                color: UIColors.primary,
              ),
            )
          ],
        ),
        IconButton(
          onPressed: () => Navigator.pop(context),
          icon: SvgPicture.asset(
            Assets.drawerMenu,
            height: 36.h,
            width: 36.h,
          ),
        ),
      ],
    );
  }

  Widget _buildDrawerItem(BuildContext context, _DrawerItemModel item) {
    return Container(
      margin: EdgeInsets.only(bottom: Values.v30.h),
      child: InkWell(
        onTap: item.route != null
            ? item.route == Routes.dietDashboard
                ? () => Navigator.pushNamed(
                      context,
                      item.route!,
                      arguments: [false, false],
                    )
                : () => Navigator.pushNamed(context, item.route!)
            : null,
        child: Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            SizedBox(
              height: 24.h,
              width: 24.h,
              child: SvgPicture.asset(
                item.icon,
                colorFilter: const ColorFilter.mode(UIColors.primary, BlendMode.srcIn),
              ),
            ),
            SizedBox(width: 10.w),
            Text(
              item.name,
              style: AppTypography.semiBold20(
                color: UIColors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutButton(BuildContext context) {
    return InkWell(
      onTap: () {
        _showLogOutAlert(context);
      },
      child: Padding(
        padding: EdgeInsets.only(bottom: Values.v30.h),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            SizedBox(
              height: 24.h,
              width: 24.h,
              child: SvgPicture.asset(
                Assets.logout,
                colorFilter: const ColorFilter.mode(UIColors.primary, BlendMode.srcIn),
                
              ),
            ),
            SizedBox(width: 10.w),
            Text(
              TextConstants.logout,
              style: AppTypography.semiBold20(
                color: UIColors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentVersionText() {
    String version = PackageInfoService.instance.version ?? '';
    String buildNumber = PackageInfoService.instance.buildNumber ?? '';

    return Text(
      buildNumber != '' && version != '' ? "$version ($buildNumber)" : '',
      style: AppTypography.semiBold20(
        color: UIColors.white,
      ),
    );
  }

  _showLogOutAlert(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return CupertinoAlertDialog(
            title: Column(
              children: const [
                Text("Log Out"),
              ],
            ),
            content:
                const Text("Are you sure you want to log out from Fitsomnia?"),
            actions: [
              CupertinoDialogAction(
                child: const Text("Yes"),
                onPressed: () async {
                  BlocProvider.of<LogoutCubit>(context).logout();
                },
              ),
              CupertinoDialogAction(
                child: const Text("No"),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });
  }
}

class _DrawerItemModel {
  _DrawerItemModel({
    required this.name,
    required this.icon,
    this.route,
  });

  final String name;
  final String? route;
  final String icon;
}
