part of 'coach_program_bloc.dart';

sealed class CoachProgramEvent extends Equatable {
  const CoachProgramEvent();

  @override
  List<Object> get props => [];
}

class AddCoachProgramEvent extends CoachProgramEvent {
  final String coachId;
  final CoachProgramEntity programEntity;
  const AddCoachProgramEvent(
      {required this.coachId, required this.programEntity});

  @override
  List<Object> get props => [coachId, programEntity];
}

class UpdateCoachProgramEvent extends CoachProgramEvent {
  final String coachId;
  final String programId;
  final CoachProgramEntity programEntity;
  const UpdateCoachProgramEvent(
      {required this.coachId,
      required this.programId,
      required this.programEntity});

  @override
  List<Object> get props => [coachId, programEntity];
}

class GetCoachOwnProgramByIdEvent extends CoachProgramEvent {
  final String coachId;
  final String programId;
  const GetCoachOwnProgramByIdEvent(
      {required this.coachId, required this.programId});

  @override
  List<Object> get props => [coachId, programId];
}

class DeleteCoachOwnProgramById extends Coach<PERSON>rogramEvent {
  final String coachId;
  final String programId;

  const DeleteCoachOwnProgramById(
      {required this.coachId, required this.programId});

  @override
  List<Object> get props => [coachId, programId];
}

class SubscribeCoachProgramEvent extends CoachProgramEvent {
  final String coachId;
  final String programId;

  const SubscribeCoachProgramEvent({
    required this.coachId,
    required this.programId,
  });

  @override
  List<Object> get props => [coachId, programId];
}

class GetPaymentGetwayUrlEvent extends CoachProgramEvent {
  final String subscriptionId;
  final String paymentTerm;

  const GetPaymentGetwayUrlEvent({
    required this.subscriptionId,
    required this.paymentTerm,
  });

  @override
  List<Object> get props => [subscriptionId, paymentTerm];
}
