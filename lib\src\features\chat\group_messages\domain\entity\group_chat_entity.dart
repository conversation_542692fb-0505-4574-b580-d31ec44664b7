import 'package:equatable/equatable.dart';

class GroupChatEntity extends Equatable {
  const GroupChatEntity({
    required this.id,
    required this.senderId,
    required this.groupId,
    required this.type,
    required this.content,
    required this.createdAt,
    required this.senderName,
    required this.senderImage,
  });

  final String id;
  final String senderId;
  final String groupId;
  final String type;
  final String content;
  final DateTime createdAt;
  final String senderName;
  final String? senderImage;

  @override
  List<Object?> get props => [
    senderName,
    createdAt,
    content,
    type,
    groupId,
    senderId,
    id,
    senderImage,
  ];
}