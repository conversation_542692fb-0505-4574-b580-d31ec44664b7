import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/services/firebase/firebase_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/features/club/dashboard/presentation/bloc/club_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/club/club_bloc.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/pages/club_page.dart';
import 'package:fitsomnia_app/src/features/club/nearby_clubs/presentation/bloc/nearby_clubs_bloc.dart';
import 'package:fitsomnia_app/src/features/club/nearby_clubs/presentation/pages/nearby_clubs_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ClubDashboardPage extends StatefulWidget {
  const ClubDashboardPage({Key? key}) : super(key: key);

  @override
  State<ClubDashboardPage> createState() => _ClubDashboardPageState();
}

class _ClubDashboardPageState extends State<ClubDashboardPage> {
  @override
  void initState() {
    super.initState();
    BlocProvider.of<ClubDashboardBloc>(context).add(ClubMembershipCheckEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ClubDashboardBloc, BaseState>(
      listener: (context, state) {
        if (state is ClubMembershipFoundState) {
          BlocProvider.of<ClubBloc>(context).add(MyClubEvent(club: state.club));
        } else if (state is ClubMembershipNotFoundState) {
          BlocProvider.of<NearbyClubsBloc>(context)
              .add(const FindNearbyClubsEvent(pullToRefresh: true));
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              TextConstants.club,
              style: AppTypography.bold24(),
            ),
            scrolledUnderElevation: 0,
            centerTitle: true,
            automaticallyImplyLeading: false,
            leading: IconButton(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: SvgPicture.asset(
                Assets.backButton,
              ),
            ),
          ),
          body: state is ClubMembershipNotFoundState
              ? const NearbyClubsPage()
              : const ClubPage(),
        );
      },
    );
  }
}
