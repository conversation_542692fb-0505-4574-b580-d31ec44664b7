import 'package:dio/dio.dart';

abstract class ClubRemoteDataSource {
  Future<Response> myClub();

  Future<Response> findNearbyClubs({double? long, double? lat, int? offset});

  Future<Response> joinClub(String clubId);

  Future<Response> leaveAndJoinClub(String clubId);

  Future<Response> leaveClub(String clubId);

  /// AKA People From Your Club
  Future<Response> clubMembers(String id, int? offset);

  Future<Response> nearbyClubsMembers(
    Map<String, dynamic> map,
    String id,
    int? offset,
  );

  Future<Response> liveMembersNearMe(Map<String, dynamic> query);
}
