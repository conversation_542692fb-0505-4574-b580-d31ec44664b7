import 'dart:collection';

import 'package:fitsomnia_app/src/core/routes/routes.dart';

enum CoachSkillLevel {
  BEGINNER,
  MID_LEVEL,
  PROFESSIONAL,
  EXPERT;

  String getKey() {
    switch (this) {
      case CoachSkillLevel.BEGINNER:
        return 'beginner';
      case CoachSkillLevel.MID_LEVEL:
        return 'mid_level';
      case CoachSkillLevel.PROFESSIONAL:
        return 'professional';
      case CoachSkillLevel.EXPERT:
        return 'expert';
      default:
        return 'beginner';
    }
  }

  String getTitle() {
    switch (this) {
      case CoachSkillLevel.BEGINNER:
        return 'Beginner';
      case CoachSkillLevel.MID_LEVEL:
        return 'Mid Level';
      case CoachSkillLevel.PROFESSIONAL:
        return 'Professional';
      case CoachSkillLevel.EXPERT:
        return 'Expert';
      default:
        return 'Beginner';
    }
  }
}

const List<CoachSkillLevel> coachSkills = CoachSkillLevel.values;
List<String> coachSkillKeys =
    CoachSkillLevel.values.map((v) => v.getKey()).toList();
List<String> coachSkillTitles =
    CoachSkillLevel.values.map((v) => v.getTitle()).toList();

CoachSkillLevel getSkillEnumFromKey(String key) {
  if (key == CoachSkillLevel.BEGINNER.getKey()) {
    return CoachSkillLevel.BEGINNER;
  } else if (key == CoachSkillLevel.MID_LEVEL.getKey()) {
    return CoachSkillLevel.MID_LEVEL;
  } else if (key == CoachSkillLevel.MID_LEVEL.getKey()) {
    return CoachSkillLevel.MID_LEVEL;
  } else if (key == CoachSkillLevel.PROFESSIONAL.getKey()) {
    return CoachSkillLevel.PROFESSIONAL;
  } else if (key == CoachSkillLevel.EXPERT.getKey()) {
    return CoachSkillLevel.EXPERT;
  } else {
    return CoachSkillLevel.BEGINNER;
  }
}

enum CoachProfileFilterType {
  CATEGORY,
  SUBCATEGORY,
  BEST,
  NAME;

  String getValue() {
    switch (this) {
      case CoachProfileFilterType.CATEGORY:
        return 'category';
      case CoachProfileFilterType.SUBCATEGORY:
        return 'sub-category';
      case CoachProfileFilterType.NAME:
        return 'name';
      case CoachProfileFilterType.BEST:
        return 'best';
      default:
        return 'best';
    }
  }
}

enum CoachProfileOptionItem {
  COACH_PROFILE,
  STUDENT_PROFILE,
  CREATE_COACH_PROFILE,
  CREATE_STUDENT_PROFILE;

  String name() {
    switch (this) {
      case CoachProfileOptionItem.COACH_PROFILE:
        return 'Coach Profile';
      case CoachProfileOptionItem.STUDENT_PROFILE:
        return 'Learner Profile';
      case CoachProfileOptionItem.CREATE_COACH_PROFILE:
        return 'Coach Registration';
      case CoachProfileOptionItem.CREATE_STUDENT_PROFILE:
        return 'View All Programs';
      default:
        return 'Coach';
    }
  }

  String route() {
    switch (this) {
      case CoachProfileOptionItem.COACH_PROFILE:
        return Routes.coachDashboardPage;
      case CoachProfileOptionItem.STUDENT_PROFILE:
        return Routes.studentEnrollmentDashboardPage;
      case CoachProfileOptionItem.CREATE_COACH_PROFILE:
        return Routes.coachIntroductionPage;
      case CoachProfileOptionItem.CREATE_STUDENT_PROFILE:
        return Routes.studentEnrollmentInitialPage;
      default:
        return Routes.coachOptionsDashboardPage;
    }
  }
}

enum CoachProgramSubscriptionRefundStatusInfo {
  NO_REFUND_REQUESTED,
  REFUND_REQUESTED,
  REFUND_SUCCESS,
  REFUND_REJECTED;

  String description() {
    switch (this) {
      case CoachProgramSubscriptionRefundStatusInfo.NO_REFUND_REQUESTED:
        return '';
      case CoachProgramSubscriptionRefundStatusInfo.REFUND_REQUESTED:
        return 'Refund In Progress';
      case CoachProgramSubscriptionRefundStatusInfo.REFUND_SUCCESS:
        return 'Refund Approved';
      case CoachProgramSubscriptionRefundStatusInfo.REFUND_REJECTED:
        return 'Refund request rejected';
      default:
        return '';
    }
  }
}

CoachProgramSubscriptionRefundStatusInfo getCoachProgramRefundStatusEnumValue(
    String key) {
  switch (key) {
    case 'refund_requested':
      return CoachProgramSubscriptionRefundStatusInfo.REFUND_REQUESTED;
    case 'refund_successful':
      return CoachProgramSubscriptionRefundStatusInfo.REFUND_SUCCESS;
    case 'refund_rejected':
      return CoachProgramSubscriptionRefundStatusInfo.REFUND_REJECTED;
    default:
      return CoachProgramSubscriptionRefundStatusInfo.NO_REFUND_REQUESTED;
  }
}

enum CoachProgramSubscriptionCancelInfo {
  NOT_CANCEL,
  CANCEL_BY_USER,
  CANCEL_BY_COACH;

  String description() {
    switch (this) {
      case CoachProgramSubscriptionCancelInfo.NOT_CANCEL:
        return '';
      case CoachProgramSubscriptionCancelInfo.CANCEL_BY_USER:
        return 'Subscription Canceled';
      case CoachProgramSubscriptionCancelInfo.CANCEL_BY_COACH:
        return 'Subscription Canceled By Coach';
      default:
        return 'Not Canceled';
    }
  }
}

CoachProgramSubscriptionCancelInfo getCoachProgramCancelStatusEnumValue(
    String key) {
  switch (key) {
    case 'canceled_by_user':
      return CoachProgramSubscriptionCancelInfo.CANCEL_BY_USER;
    case 'canceled_by_coach':
      return CoachProgramSubscriptionCancelInfo.CANCEL_BY_COACH;
    default:
      return CoachProgramSubscriptionCancelInfo.NOT_CANCEL;
  }
}

enum CoachProgramSubscriptionPaymentTerm {
  ONE_TIME,
  DAILY,
  WEEKLY,
  MONTHLY;

  String name() {
    switch (this) {
      case CoachProgramSubscriptionPaymentTerm.ONE_TIME:
        return 'One time';
      case CoachProgramSubscriptionPaymentTerm.DAILY:
        return 'Daily';
      case CoachProgramSubscriptionPaymentTerm.WEEKLY:
        return 'Weekly';
      case CoachProgramSubscriptionPaymentTerm.MONTHLY:
        return 'Monthly';
      default:
        return 'Unknown';
    }
  }

  String key() {
    switch (this) {
      case CoachProgramSubscriptionPaymentTerm.ONE_TIME:
        return 'one_time';
      case CoachProgramSubscriptionPaymentTerm.DAILY:
        return 'daily';
      case CoachProgramSubscriptionPaymentTerm.WEEKLY:
        return 'weekly';
      case CoachProgramSubscriptionPaymentTerm.MONTHLY:
        return 'monthly';
      default:
        return 'one_time';
    }
  }
}

CoachProgramSubscriptionPaymentTerm getPaymentTermEnumFromKey(String key) {
  if (key == CoachProgramSubscriptionPaymentTerm.ONE_TIME.key()) {
    return CoachProgramSubscriptionPaymentTerm.ONE_TIME;
  } else if (key == CoachProgramSubscriptionPaymentTerm.DAILY.key()) {
    return CoachProgramSubscriptionPaymentTerm.DAILY;
  } else if (key == CoachProgramSubscriptionPaymentTerm.WEEKLY.key()) {
    return CoachProgramSubscriptionPaymentTerm.WEEKLY;
  } else if (key == CoachProgramSubscriptionPaymentTerm.MONTHLY.key()) {
    return CoachProgramSubscriptionPaymentTerm.MONTHLY;
  } else {
    return CoachProgramSubscriptionPaymentTerm.ONE_TIME;
  }
}

CoachProgramSubscriptionPaymentTerm getPaymentTermEnumFromName(String name) {
  if (name == CoachProgramSubscriptionPaymentTerm.ONE_TIME.name()) {
    return CoachProgramSubscriptionPaymentTerm.ONE_TIME;
  } else if (name == CoachProgramSubscriptionPaymentTerm.DAILY.name()) {
    return CoachProgramSubscriptionPaymentTerm.DAILY;
  } else if (name == CoachProgramSubscriptionPaymentTerm.WEEKLY.name()) {
    return CoachProgramSubscriptionPaymentTerm.WEEKLY;
  } else if (name == CoachProgramSubscriptionPaymentTerm.MONTHLY.name()) {
    return CoachProgramSubscriptionPaymentTerm.MONTHLY;
  } else {
    return CoachProgramSubscriptionPaymentTerm.ONE_TIME;
  }
}

List<CoachProgramSubscriptionPaymentTerm> avialablePaymentTerms = [
  CoachProgramSubscriptionPaymentTerm.ONE_TIME,
  // CoachProgramSubscriptionPaymentTerm.DAILY,
  // CoachProgramSubscriptionPaymentTerm.WEEKLY,
  CoachProgramSubscriptionPaymentTerm.MONTHLY,
];

enum CoachProgramDurationTerm {
  DAYS,
  WEEKS,
  MONTHS;

  String name() {
    switch (this) {
      case CoachProgramDurationTerm.DAYS:
        return 'Days';
      case CoachProgramDurationTerm.MONTHS:
        return 'Months';
      case CoachProgramDurationTerm.WEEKS:
        return 'Weeks';
      default:
        return 'months';
    }
  }

  String key() {
    switch (this) {
      case CoachProgramDurationTerm.DAYS:
        return 'days';
      case CoachProgramDurationTerm.MONTHS:
        return 'months';
      case CoachProgramDurationTerm.WEEKS:
        return 'weeks';
      default:
        return 'months';
    }
  }
}

CoachProgramDurationTerm getProgramDurationTermEnumFromKey(String name) {
  if (name == CoachProgramDurationTerm.MONTHS.key()) {
    return CoachProgramDurationTerm.MONTHS;
  } else if (name == CoachProgramDurationTerm.DAYS.key()) {
    return CoachProgramDurationTerm.DAYS;
  } else {
    return CoachProgramDurationTerm.MONTHS;
  }
}

enum JerseySize {
  Small,
  Medium,
  Large,
  LargeXL,
  Large2XL,
  Large3XL,
  Large4XL;

  String name() {
    switch (this) {
      case JerseySize.Small:
        return 'S';

      case JerseySize.Medium:
        return 'M';

      case JerseySize.Large:
        return 'L';

      case JerseySize.LargeXL:
        return 'XL';

      case JerseySize.Large2XL:
        return '2XL';

      case JerseySize.Large3XL:
        return '3XL';

      case JerseySize.Large4XL:
        return '4XL';
    }
  }

  String key() {
    switch (this) {
      case JerseySize.Small:
        return 'S';

      case JerseySize.Medium:
        return 'M';

      case JerseySize.Large:
        return 'L';

      case JerseySize.LargeXL:
        return 'XL';

      case JerseySize.Large2XL:
        return 'XXL';

      case JerseySize.Large3XL:
        return '3XL';

      case JerseySize.Large4XL:
        return '4XL';
    }
  }
}

enum StudySession {
  Session15_16,
  Session16_17,
  Session17_18,
  Session18_19,
  Session19_20,
  Session20_21,
  Session21_22,
  Session22_23,
  Session23_24,
  SessionOthers;

  String name() {
    switch (this) {
      case StudySession.Session15_16:
        return '15-16';

      case StudySession.Session16_17:
        return '16-17';

      case StudySession.Session17_18:
        return '17-18';

      case StudySession.Session18_19:
        return '18-19';

      case StudySession.Session19_20:
        return '19-20';

      case StudySession.Session20_21:
        return '20-21';
      
      case StudySession.Session21_22:
        return '21-22';

      case StudySession.Session22_23:
        return '22-23';
      
      case StudySession.Session23_24:
        return '23-24';

      case StudySession.SessionOthers:
        return 'Others';
    }
  }

  String key() {
      switch (this) {
      case StudySession.Session15_16:
        return '15-16';

      case StudySession.Session16_17:
        return '16-17';

      case StudySession.Session17_18:
        return '17-18';

      case StudySession.Session18_19:
        return '18-19';

      case StudySession.Session19_20:
        return '19-20';

      case StudySession.Session20_21:
        return '20-21';
      
      case StudySession.Session21_22:
        return '21-22';

      case StudySession.Session22_23:
        return '22-23';
      
      case StudySession.Session23_24:
        return '23-24';

      case StudySession.SessionOthers:
        return 'others';
    }
  }
}

enum DailyTaskFilter {
  ACTIVE,
  COMPLETED,
  DAILY,
  SPECIAL;

  String key(){
    switch (this) {
      case DailyTaskFilter.ACTIVE:
        return 'active';

      case DailyTaskFilter.COMPLETED:
        return 'completed';

      case DailyTaskFilter.DAILY:
        return 'daily';
      
      case DailyTaskFilter.SPECIAL:
        return 'special';
        
    }
  }
}
