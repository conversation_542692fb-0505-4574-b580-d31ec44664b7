import 'package:camera/camera.dart';
import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/services/camera/app_camera_service.dart';
import 'package:fitsomnia_app/src/core/services/camera/bloc/camera_bloc.dart';
import 'package:fitsomnia_app/src/core/services/camera/bloc/camera_cubit.dart';
import 'package:fitsomnia_app/src/core/services/camera/ui/camera_footer_widget.dart';
import 'package:fitsomnia_app/src/core/services/camera/ui/camera_header_widget.dart';
import 'package:fitsomnia_app/src/core/services/food_scan_camera/ui/food_camera_footer_widget.dart';
import 'package:fitsomnia_app/src/core/services/food_scan_camera/ui/food_camera_header_widget.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class FoodCameraServiceView extends StatelessWidget {
  const FoodCameraServiceView({
    Key? key,
    this.maxRecordingTimeInSecond = 0,
    this.onMediaIconTap,
    this.showMediaIcon = true,
    this.onlyRecord = false,
  }) : super(key: key);
  final int maxRecordingTimeInSecond;
  final Function? onMediaIconTap;
  final bool showMediaIcon;
  final bool onlyRecord;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => CameraCubit(),
        ),
        BlocProvider(
          create: (context) => CameraBloc(
            maxRecordingTimeInSecond: maxRecordingTimeInSecond,
            onMediaIconTap: onMediaIconTap,
          ),
        ),
      ],
      child: FoodCameraPreview(),
    );
  }
}

class FoodCameraPreview extends StatefulWidget {
  const FoodCameraPreview({
    Key? key,
    this.showMediaIcon = true,
    this.onlyRecord = false,
  }) : super(key: key);

  final bool showMediaIcon;
  final bool onlyRecord;

  @override
  State<FoodCameraPreview> createState() => _CreateFoodCameraPageState();
}

class _CreateFoodCameraPageState extends State<FoodCameraPreview>
    with WidgetsBindingObserver {
  final CameraController _cameraController =
      AppCameraService.instance.selectNewCamera(CameraLensDirection.back);
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    context.read<CameraBloc>().add(ChangeCameraEvent(_cameraController));
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    if (_cameraController.value.isInitialized) _cameraController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (!_cameraController.value.isInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      _cameraController.dispose();
    } else if (state == AppLifecycleState.resumed) {
      CameraController newController =
          AppCameraService.instance.selectNewCamera();
      if (mounted) {
        context.read<CameraBloc>().add(ChangeCameraEvent(newController));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          alignment: Alignment.center,
          children: [
            // _videoBackgroudnPreview(),
            // _blurBackground(),
            _buildCameraPreview(),
            Container(
              // height: MediaQuery.of(context).size.height,
              child: Column(
                // mainAxisSize: MainAxisSize.min,
                children: [
                  const FoodCameraHeaderWidget(),
                  Expanded(
                    child: _buildFooSelectionBox(),
                  ),
                  FoodCameraFooterWidget(
                    showMediaIcon: widget.showMediaIcon,
                    onlyRecord: widget.onlyRecord,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCameraPreview() {
    return BlocBuilder<CameraBloc, CameraState>(
      builder: (context, state) {
        return state.cameraController == null
            ? const SizedBox()
            : FutureBuilder(
                future: state.cameraController!.initialize(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.done && state.cameraController != null && state.cameraController!.value.isInitialized) {
                    return Container(
                      // decoration: BoxDecoration(
                      //   image: DecorationImage(
                      //     image: AssetImage(Assets.coachBannerBuildMuscleImg),
                      //     fit: BoxFit.cover,
                      //     // opacity: 0.3,
                      //   ),
                      // ),
                      child: Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 0, vertical: 0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              _buildCameraSquare(
                                  cameraController: state.cameraController!,
                                  aspectRatio: state
                                      .cameraController!.value.aspectRatio),
                              // _buildPlaceFoodInsideSquare()
                            ],
                          ),
                        ),
                      ),
                    );
                  }

                  return const SizedBox();
                },
              );
      },
    );
  }

  _buildCameraSquare(
      {required CameraController cameraController,
      required double aspectRatio}) {
    return Container(
      // height: Values.v400,
      // width: Values.v400,
      // decoration: BoxDecoration(
      //     border: Border.all(
      //       color: Colors.white,
      //     ),
      //     borderRadius: BorderRadius.circular(Values.v10)),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(Values.v10),
        child: AspectRatio(
          aspectRatio: 1 / aspectRatio,
          child: CameraPreview(cameraController),
        ),
      ),
    );
  }

  _buildPlaceFoodInsideSquare() {
    return Padding(
      padding: const EdgeInsets.only(top: 0),
      child: Text(
        'Place the food inside the square',
        style: AppTypography.poppinsRegular16(
          color: Colors.white,
        ),
      ),
    );
  }

  _videoBackgroudnPreview() {
    return BlocBuilder<CameraBloc, CameraState>(
      builder: (context, state) {
        return state.cameraController == null
            ? const SizedBox()
            : FutureBuilder(
                future: state.cameraController!.initialize(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.done) {
                    return Container(
                      height: double.infinity,
                      width: double.infinity,
                      child: AspectRatio(
                        aspectRatio:
                            1 / state.cameraController!.value.aspectRatio,
                        child: CameraPreview(state.cameraController!),
                      ),
                    );
                  }

                  return const SizedBox();
                },
              );
      },
    );
  }

  _blurBackground() {
    return Container(
      height: double.infinity,
      width: double.infinity,
      color: UIColors.black,
    );
  }

  _buildFooSelectionBox() {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          child: SvgPicture.asset(
            Assets.foodScannerImageSelectionBox,
            // color: UIColors.black.withOpacity(0.6),
            // colorFilter: ColorFilter.mode(UIColors.white, BlendMode.srcIn),
            fit: BoxFit.cover,
          ),
        ),
        // Positioned(left: Values.v80.w, bottom: Values.v120.h, child: _buildPlaceFoodInsideSquare())

        Align(
            alignment: AlignmentDirectional.bottomCenter,
            child: Padding(
              padding: EdgeInsets.only(
                bottom: Values.v100,
              ),
              child: _buildPlaceFoodInsideSquare(),
            )),
      ],
    );
  }
}
