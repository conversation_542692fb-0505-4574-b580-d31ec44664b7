import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/domain/entity/all_users_entity.dart';

class AllUsersModel extends AllUsersEntity {
  const AllUsersModel({
    required super.id,
    required super.name,
    required super.image,
  });

  factory AllUsersModel.fromJson(Map<String, dynamic> json) => AllUsersModel(
        id: json["id"],
        name: json["name"],
        image: json["image"] == null
            ? null
            : AllUsersImage.fromJson(json["image"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image": image?.toJson(),
      };
}

class AllUsersModelWithoutImageProfile extends AllUsersEntityWithoutProfile {
  const AllUsersModelWithoutImageProfile({
    required super.id,
    required super.name,
    required super.image,
  });

  factory AllUsersModelWithoutImageProfile.fromJson(
          Map<String, dynamic> json) =>
      AllUsersModelWithoutImageProfile(
        id: json["id"],
        name: json["name"],
        image: json["image"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image": image,
      };
}
