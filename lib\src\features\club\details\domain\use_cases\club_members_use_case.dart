import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/entities/club_members_entity.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/repository/club_repository.dart';

class ClubMembersUseCase {
  const ClubMembersUseCase({
    required this.repository,
  });

  final ClubRepository repository;

  Future<Either<ErrorResponseModel, List<ClubMemberEntity>>> clubMembers(
    String id,
    int? offset,
  ) async {
    return await repository.clubMembers(id, offset);
  }
}
