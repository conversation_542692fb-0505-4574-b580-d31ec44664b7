import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class CoachProgramReviewDataSource {
  // coach program review
  Future<Response> getCoachProgramReviews({required String programId});
  Future<Response> getCoachProgramReviewById({required String reviewId});
  Future<Response> reviewCoachProgram(
      {required String programId, required Map<String, dynamic> data});
  Future<Response> deleteCoachProgramReview({required String reviewId});
  Future<Response> updateCoachProgramReview(
      {required String reviewId, required Map<String, dynamic> data});

  // coach profile review
  Future<Response> getCoachProfileReviews({required String coachId});
  Future<Response> getCoachProfileReviewById({required String reviewId});
  Future<Response> reviewCoachProfile(
      {required String coachId, required Map<String, dynamic> data});
  Future<Response> deleteCoachProfileReview({required String reviewId});
  Future<Response> updateCoachProfileReview(
      {required String reviewId, required Map<String, dynamic> data});
}

class CoachProgramReviewDataSourceImpl extends CoachProgramReviewDataSource {
  final RestClient restClient;

  CoachProgramReviewDataSourceImpl({required this.restClient});

  @override
  Future<Response> getCoachProgramReviewById({required String reviewId}) async {
    final response = await restClient.get(
        APIType.PROTECTED, '${API.coachProgramSingleRateing}/$reviewId');

    return response;
  }

  @override
  Future<Response> getCoachProgramReviews({required String programId}) async {
    final response = await restClient.get(
        APIType.PROTECTED, '${API.coachProgramMultipleRating}/$programId');

    return response;
  }

  @override
  Future<Response> reviewCoachProgram(
      {required String programId, required Map<String, dynamic> data}) async {
    final response = await restClient.post(
        APIType.PROTECTED, '${API.coachProgramSingleRateing}/$programId', data);

    return response;
  }

  @override
  Future<Response> deleteCoachProgramReview({required String reviewId}) async {
    final response = await restClient.delete(
        APIType.PROTECTED, '${API.coachProgramSingleRateing}/$reviewId');

    return response;
  }

  @override
  Future<Response> updateCoachProgramReview(
      {required String reviewId, required Map<String, dynamic> data}) async {
    final response = await restClient.patch(
        APIType.PROTECTED, '${API.coachProgramSingleRateing}/$reviewId', data);

    return response;
  }

  @override
  Future<Response> deleteCoachProfileReview({required String reviewId}) async {
    final response = await restClient.delete(
        APIType.PROTECTED, API.coachProfileReview(reviewId));

    return response;
  }

  @override
  Future<Response> getCoachProfileReviewById({required String reviewId}) async {
    final response = await restClient.get(
        APIType.PROTECTED, API.coachProfileReview(reviewId));

    return response;
  }

  @override
  Future<Response> getCoachProfileReviews({required String coachId}) async {
    final response = await restClient.get(
        APIType.PROTECTED, API.coachProfileReviews(coachId));

    return response;
  }

  @override
  Future<Response> reviewCoachProfile({
    required String coachId,
    required Map<String, dynamic> data,
  }) async {
    final response = await restClient.post(
        APIType.PROTECTED, API.coachProfileReview(coachId), data);

    return response;
  }

  @override
  Future<Response> updateCoachProfileReview({
    required String reviewId,
    required Map<String, dynamic> data,
  }) async {
    final response = await restClient.patch(
        APIType.PROTECTED, API.coachProfileReview(reviewId), data);

    return response;
  }
}
