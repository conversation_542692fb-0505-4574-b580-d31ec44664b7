import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class CoachWithdrawPaymentDatasource {
  Future<Response> withdrawMoney({
    required String coachId,
    required Map<String, dynamic> data,
  });
  Future<Response> addPhoneNumber({
    required String coachId,
    required Map<String, dynamic> data,
  });
  Future<Response> updatePhoneNumber({
    required String coachId,
    required Map<String, dynamic> data,
  });
}

class CoachWithdrawPaymentDatasourceImpl
    implements CoachWithdrawPaymentDatasource {
  final RestClient restClient;

  CoachWithdrawPaymentDatasourceImpl({required this.restClient});

  @override
  Future<Response> addPhoneNumber({
    required String coachId,
    required Map<String, dynamic> data,
  }) async {
    //TODO: need to update
    final response = await restClient.post(
        APIType.PROTECTED, API.coachUpdatePhoneNumber(coachId), data);

    return response;
  }

  @override
  Future<Response> updatePhoneNumber({
    required String coachId,
    required Map<String, dynamic> data,
  }) async {
    final response = await restClient.patch(
        APIType.PROTECTED, API.coachUpdatePhoneNumber(coachId), data);

    return response;
  }

  @override
  Future<Response> withdrawMoney({
    required String coachId,
    required Map<String, dynamic> data,
  }) async {
    final response = await restClient.post(
        APIType.PROTECTED, API.coachWithdrawMoney(coachId), data);

    return response;
  }
}
