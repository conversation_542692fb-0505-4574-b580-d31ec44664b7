part of 'coach_profile_bloc.dart';

sealed class CoachPro<PERSON>leEvent extends Equatable {
  const CoachProfileEvent();

  @override
  List<Object> get props => [];
}

class CreateCoachProfileEvent extends CoachProfileEvent {
  final CoachEntity coachRegistrationEntity;

  const CreateCoachProfileEvent({required this.coachRegistrationEntity});

  @override
  List<Object> get props => [coachRegistrationEntity];
}

class UpdateCoachProfileEvent extends CoachProfileEvent {
  final String coachId;
  final CoachEntity coachEntity;

  const UpdateCoachProfileEvent({required this.coachId, required this.coachEntity});

  @override
  List<Object> get props => [coachId, coachEntity];
}

class UpdateCoachProfilePictureLocally extends CoachProfileEvent {
  final List<CoachMediaFile> profilePictures;

  const UpdateCoachProfilePictureLocally({required this.profilePictures});

  @override
  List<Object> get props => [profilePictures];
}

class UpdateCoachCredentialDocLocally extends Coach<PERSON>ro<PERSON>leEvent {
  final List<CoachMediaFile> credentials;

  const UpdateCoachCredentialDocLocally({required this.credentials});

  @override
  List<Object> get props => [credentials];
}

class UpdateCoachIdentificationDocLocally extends CoachProfileEvent {
  final List<CoachMediaFile> files;

  const UpdateCoachIdentificationDocLocally({required this.files});

  @override
  List<Object> get props => [files];
}
