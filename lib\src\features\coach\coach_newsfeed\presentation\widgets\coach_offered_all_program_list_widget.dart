import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_offered_program_view_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

class CoachOfferedAllProgramsWidget extends StatefulWidget {
  const CoachOfferedAllProgramsWidget({super.key, required this.coachId});
  final String coachId;

  @override
  State<CoachOfferedAllProgramsWidget> createState() =>
      _CoachOfferedAllProgramsWidgetState();
}

class _CoachOfferedAllProgramsWidgetState
    extends State<CoachOfferedAllProgramsWidget> {
  List<CoachProgramEntity> _allOfferedPrograms = [];
  List<CoachProgramEntity> testPrograms = List.generate(5, (index) {
    return testCoachProgram;
  });

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _isLoading = true;
    BlocProvider.of<CoachBloc>(context)
        .add(GetCoachOfferedAllPrograms(coachId: widget.coachId));
    // _allOfferedPrograms = testPrograms; //TODO: for testing
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachBloc, CoachState>(
      listener: (context, state) {
        if (state is GetCoachOfferedProgramsSuccess) {
          Log.debug('get coach offered program success');
          setState(() {
            '';
            _allOfferedPrograms = state.programs; //TODO disable for testing
            _isLoading = false;
          });
        }

        if (state is GetCoachOfferedProgramsFail) {
          Log.debug('get coach offered program fail');
          AppToast.showToast(message: 'Get coach offered program failed');
          setState(() {
            _isLoading = false;
          });
        }
      },
      child: Container(
        margin: EdgeInsets.only(top: Values.v40),
        child: Column(
          children: [
            _buildProgramSectionHeader(
              Assets.coachProgramEnrolledIcon,
              'All Programs',
              showCount: true,
              iconColor: UIColors.primaryGreen100,
              textColor: UIColors.primary,
            ),
            _buildStudentProgramsEnrollmentList(),
          ],
        ),
      ),
    );
  }

  _buildProgramSectionHeader(
    String icon,
    String title, {
    required bool showCount,
    Color? iconColor,
    Color? textColor,
  }) {
    return Row(
      children: [
        // Container(
        //   margin: const EdgeInsets.only(right: Values.v4),
        //   decoration: BoxDecoration(
        //     color: (iconColor != null) ? iconColor : UIColors.primaryGreen100,
        //     borderRadius: BorderRadius.circular(Values.v36),
        //   ),
        //   child: Center(
        //     child: SvgPicture.asset(
        //       icon,
        //       height: Values.v36,
        //       width: Values.v36,
        //     ),
        //   ),
        // ),
        Text(
          '$title ${(showCount && _allOfferedPrograms.isNotEmpty) ? '(${_allOfferedPrograms.length})' : ''}',
          style: AppTypography.poppinsSemiBold20(
              color:
                  (textColor != null) ? textColor : UIColors.primaryGreen950),
        ),
      ],
    );
  }

  _buildStudentProgramsEnrollmentList() {
    return (_isLoading)
        ? SizedBox(
            height: 100,
            child: Center(
              child: CircularProgressIndicator(
                color: UIColors.primary,
              ),
            ),
          )
        : (_allOfferedPrograms.isEmpty)
            ? Container(
                height: 100,
                width: double.infinity,
                child: Center(
                  child: Text(
                    'No program found',
                    style: AppTypography.poppinsRegular16(
                        color: AppColors.greyscale400),
                  ),
                ),
              )
            : Column(
                children: _allOfferedPrograms.map<Widget>((program) {
                  return CoachOfferedProgramViewWidget(
                    programEntity: program,
                    isBestProgram: false,
                  );
                }).toList(),
              );
  }
}
