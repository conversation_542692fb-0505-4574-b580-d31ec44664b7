part of 'coach_program_review_bloc.dart';

sealed class Coach<PERSON>rogramReviewState extends Equatable {
  const CoachProgramReviewState();

  @override
  List<Object> get props => [];
}

final class CoachProgramReviewInitial extends CoachProgramReviewState {}

class CoachRatingLoading extends CoachProgramReviewState {}

// coach program review

class GetCoachProgramReviewsSuccess extends CoachProgramReviewState {
  final List<UserReviewEntity> reviews;

  const GetCoachProgramReviewsSuccess({required this.reviews});
  @override
  List<Object> get props => [];
}

class GetCoachProgramReviewsFail<T> extends CoachProgramReviewState {
  final T? data;

  const GetCoachProgramReviewsFail({required this.data});
}

class ReviewCoachProgramSuccess extends CoachProgramReviewState {
  final UserReviewEntity review;

  const ReviewCoachProgramSuccess({required this.review});
  @override
  List<Object> get props => [review];
}

class ReviewCoachProgramFail<T> extends CoachProgramReviewState {
  final T? data;

  const ReviewCoachProgramFail({required this.data});
}

class UpdateCoachProgramReviewSuccess extends CoachProgramReviewState {
  final UserReviewEntity review;

  const UpdateCoachProgramReviewSuccess({required this.review});
  @override
  List<Object> get props => [review];
}

class UpdateCoachProgramReviewFail<T> extends CoachProgramReviewState {
  final T? data;

  const UpdateCoachProgramReviewFail({required this.data});
}


class GetCoachProgramSingleReviewSuccess extends CoachProgramReviewState {
  final UserReviewEntity review;

  const GetCoachProgramSingleReviewSuccess({required this.review});
  @override
  List<Object> get props => [review];
}

class GetCoachProgramSingleReviewFail<T> extends CoachProgramReviewState {
  final T? data;

  const GetCoachProgramSingleReviewFail({required this.data});
}


// coach profile review
class GetCoachProfileReviewsSuccess extends CoachProgramReviewState {
  final List<UserReviewEntity> reviews;

  const GetCoachProfileReviewsSuccess({required this.reviews});
  @override
  List<Object> get props => [];
}

class GetCoachProfileReviewsFail<T> extends CoachProgramReviewState {
  final T? data;

  const GetCoachProfileReviewsFail({required this.data});
}

class ReviewCoachProfileSuccess extends CoachProgramReviewState {
  final UserReviewEntity review;

  const ReviewCoachProfileSuccess({required this.review});
  @override
  List<Object> get props => [review];
}

class ReviewCoachProfileFail<T> extends CoachProgramReviewState {
  final T? data;

  const ReviewCoachProfileFail({required this.data});
}

class UpdateCoachProfileReviewSuccess extends CoachProgramReviewState {
  final UserReviewEntity review;

  const UpdateCoachProfileReviewSuccess({required this.review});
  @override
  List<Object> get props => [review];
}

class UpdateCoachProfileReviewFail<T> extends CoachProgramReviewState {
  final T? data;

  const UpdateCoachProfileReviewFail({required this.data});
}

class GetCoachProfileSingleReviewSuccess extends CoachProgramReviewState {
  final UserReviewEntity review;

  const GetCoachProfileSingleReviewSuccess({required this.review});
  @override
  List<Object> get props => [review];
}

class GetCoachProfileSingleReviewFail<T> extends CoachProgramReviewState {
  final T? data;

  const GetCoachProfileSingleReviewFail({required this.data});
}





