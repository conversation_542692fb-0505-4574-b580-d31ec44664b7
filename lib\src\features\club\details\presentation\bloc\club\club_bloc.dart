import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/services/location_service/location_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/use_cases/leave_club_use_case.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/use_cases/my_club_use_case.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/entities/club_entity.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'club_event.dart';
part 'club_state.dart';

class ClubBloc extends Bloc<ClubEvent, ClubState> {
  ClubBloc({
    required this.locationService,
    required this.myClubUseCase,
    required this.leaveClubUseCase,
  }) : super(ClubInitialState()) {
    on<MyClubEvent>(_onMyClubEvent);
    on<LeaveClubEvent>(_onLeaveClubEvent);
  }

  late LocationService locationService;
  late MyClubUseCase myClubUseCase;
  late LeaveClubUseCase leaveClubUseCase;

  Future<void> _onMyClubEvent(
    MyClubEvent event,
    Emitter<ClubState> emit,
  ) async {
    emit(ClubLoadedState(club: event.club));
  }

  Future<void> _onLeaveClubEvent(
    LeaveClubEvent event,
    Emitter<ClubState> emit,
  ) async {
    try {
      final response = await leaveClubUseCase.call(event.clubId);

      response.fold(
        (l) => emit(
          ClubErrorState(
            message: l.error!.message!,
            event: ClubEventType.LEAVE_CLUB,
            userId: '',
          ),
        ),
        (r) => emit(const LeaveClubSuccessState()),
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());
      emit(
        const ClubErrorState(
          message: TextConstants.pleaseTryAgain,
          event: ClubEventType.LEAVE_CLUB,
          userId: '',
        ),
      );
    }
  }
}
