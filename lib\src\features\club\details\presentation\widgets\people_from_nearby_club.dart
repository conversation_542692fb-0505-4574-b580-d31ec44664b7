import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/di/injection_container.dart' as di;
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/firebase/firebase_service.dart';
import 'package:fitsomnia_app/src/core/services/location_service/location_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_info_widget.dart';
import 'package:fitsomnia_app/src/core/widgets/enable_location_service_or_permission.dart';
import 'package:fitsomnia_app/src/core/widgets/loading_widget.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/entities/nearby_clubs_members_entity.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/nearby_clubs_members/nearby_clubs_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/widgets/caption_and_view.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/widgets/club_member_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PeopleFromNearbyClub extends StatelessWidget {
  const PeopleFromNearbyClub({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NearbyClubsMembersCubit, NearbyClubsMembersState>(
      builder: (context, state) {
        switch (state.status) {
          case NearbyClubsMembersStatus.initial:
          case NearbyClubsMembersStatus.loading:
            return _buildLoading();
          case NearbyClubsMembersStatus.success:
            return _buildMembersSection(context, state);
          case NearbyClubsMembersStatus.error:
            return Container(
              height: 200,
              color: AppColors.grey6,
              child: InfoWidget(message: state.error),
            );
        }
      },
    );
  }

  Widget _buildLoading() {
    return Column(
      children: [
        const CaptionAndViewAll(
          caption: TextConstants.peopleFromYourClub,
          onPressed: null,
        ),
        Container(
          height: 200,
          color: AppColors.grey6,
          child: const LoadingIndicator(),
        ),
      ],
    );
  }

  //ignore: long-method
  Widget _buildMembersSection(
    BuildContext context,
    NearbyClubsMembersState state,
  ) {
    LocationService locationService = di.sl<LocationService>();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CaptionAndViewAll(
          caption: TextConstants.peopleFromNearbyClubs,
          onPressed: state.data.length <= 3
              ? null
              : () => _navigateToAllNearbyClubsMembersPage(context),
        ),
        SizedBox(height: Values.v13.h),
        !locationService.isGranted
            ? EnableLocationServiceOrPermission(
                message: TextConstants.nearbyClubsLocationPermissionFeedback,
                locationService: locationService,
                onEnabled: null,
              )
            : SizedBox(
                height: Values.v190.h,
                child: state.data.isEmpty
                    ? const Center(
                        child: Text("No Members Found"),
                      )
                    : ListView.builder(
                        padding: EdgeInsets.zero,
                        scrollDirection: Axis.horizontal,
                        cacheExtent: 10,
                        itemCount:
                            state.data.length >= 3 ? 3 : state.data.length,
                        itemBuilder: (BuildContext context, int index) {
                          NearbyClubsMemberEntity model = state.data[index];

                          return ClubMemberCard(
                            id: model.userId,
                            name: model.userName,
                            image: model.image,
                            status: model.relationStatus,
                            callback: (status) {
                              context
                                  .read<NearbyClubsMembersCubit>()
                                  .updateRelationship(
                                    index: index,
                                    relationship: status,
                                  );
                            },
                          );
                        },
                      ),
              ),
      ],
    );
  }

  void _navigateToAllNearbyClubsMembersPage(BuildContext context) {
    Navigator.pushNamed(
      context,
      Routes.peopleFromNearByClub,
    );

    FirebaseService().logFeatureUsage('club', 'club_people_nearby', '');
  }
}
