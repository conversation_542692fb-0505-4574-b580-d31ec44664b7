part of 'club_dashboard_bloc.dart';

abstract class ClubDashboardEvent extends Equatable {
  const ClubDashboardEvent();
}

/// To check if user is already a member of a club or not.
/// If user is not a member of a club we need to show him nearby clubs. But if
/// the user is member of club need to show club details page
class ClubMembershipCheckEvent extends ClubDashboardEvent {

  @override
  List<Object?> get props => [];
}
