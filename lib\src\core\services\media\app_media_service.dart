import 'package:fitsomnia_app/src/core/services/media/model/media_model.dart';
import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';

import 'ui/pages/show_media_page.dart';

class AppMediaService {
  final size = 30;
  Future<bool> requestPermission() async {
    final result = await PhotoManager.requestPermissionExtend();

    return result.isAuth;
  }

  Future<List<AssetEntity>> loadAlbums() async {
    List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(onlyAll: true);

    return await albums[0].getAssetListPaged(page: 0, size: size);
  }

  Future<List<MediaModel>> openMediaView(
    BuildContext context, {
    bool selectMultipleMedia = true,
    int maxSelectionLimit = 5,
    Function? onCloseButtonTap,
  }) async {
    return await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MediaServiceView(
          selectMultipleMedia: selectMultipleMedia,
          maxSelectionLimit: maxSelectionLimit,
          onCloseButtonTap: onCloseButtonTap,
        ),
      ),
    );
  }
}
