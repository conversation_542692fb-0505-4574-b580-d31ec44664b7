import 'dart:math';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/domain/entities/user_review_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/presentation/bloc/coach_program_review_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/presentation/widgets/coach_single_rating_view_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CoachProgramReviewsWidget extends StatefulWidget {
  const CoachProgramReviewsWidget(
      {super.key, required this.programId, this.isShowAllRating = false});
  final String programId;
  final bool isShowAllRating;

  @override
  State<CoachProgramReviewsWidget> createState() => _CoachProgramReviewsWidgetState();
}

class _CoachProgramReviewsWidgetState extends State<CoachProgramReviewsWidget> {
  List<UserReviewEntity> reviews = [];

  @override
  void initState() {
    super.initState();

    reviews = List.generate(10, (index) => ratingSample);

    BlocProvider.of<CoachProgramReviewBloc>(context)
        .add(GetCoachProgramReviews(programId: widget.programId));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachProgramReviewBloc, CoachProgramReviewState>(
      listener: (context, state) {
        if (state is GetCoachProgramReviewsSuccess) {
          Log.debug('get coach program reviews success');
          setState(() {
            reviews = state.reviews;
          });
        }

        if (state is GetCoachProgramReviewsFail) {
          Log.debug('get coach program reviews fail');
          AppToast.showToast(message: 'Failed to load coach review');
        }
      },
      child: (reviews.isEmpty)
          ? const Center(
              child: Text('No review available'),
            )
          : Column(
              children: [
                _buildCoachReviewSection(),
                if (!widget.isShowAllRating) _buildSeeAllSection(),
              ],
            ),
    );
  }

  _buildCoachReviewSection() {
    return ListView.builder(
        shrinkWrap: true,
        itemCount:
            (widget.isShowAllRating) ? reviews.length : min(2, reviews.length),
        itemBuilder: (context, index) {
          return CoachSingleRatingViewWidget(coachRatingEntity: reviews[index]);
        });
  }

  _buildSeeAllSection() {
    return GestureDetector(
        onTap: () {
          Navigator.of(context)
              .pushNamed(Routes.coachRatigsPage, arguments: [widget.programId]);
        },
        child: Text(
          'See All Reviews',
          style: AppTypography.poppinsMedium16(color: AppColors.blueRibbon),
        ));
  }
}
