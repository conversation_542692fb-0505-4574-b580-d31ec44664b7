import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/di/injection_container.dart' as di;
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/services/location_service/location_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/widgets/enable_location_service_or_permission.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/live_members/live_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/live_members/live_members_state.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/pages/dashboard/others_profile_page.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class GoogleMapWidget extends StatefulWidget {
  const GoogleMapWidget({
    Key? key,
  }) : super(key: key);

  @override
  State<GoogleMapWidget> createState() => _GoogleMapWidgetState();
}

class _GoogleMapWidgetState extends State<GoogleMapWidget>
    with WidgetsBindingObserver {
  late GoogleMapController _mapController;
  double initialZoomLevel = 18;
  double _radius = 48; // Mile

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    Log.debug(state.toString());
    if (state == AppLifecycleState.resumed) {
      _getLiveMembers();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<LiveMembersCubit>();
    LocationService locationService = di.sl<LocationService>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Live Members Near You',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 10),
        if (locationService.isServiceEnabled && locationService.isGranted) ...[
          _buildMap(bloc, locationService),
        ] else ...[
          EnableLocationServiceOrPermission(
            message: TextConstants.liveLocationPermissionFeedback,
            locationService: locationService,
            onEnabled: () {
              _getLiveMembers();
            },
          ),
        ],
      ],
    );
  }

  // ignore: long-method
  BlocBuilder<LiveMembersCubit, LiveMembersState> _buildMap(
    LiveMembersCubit bloc,
    LocationService locationService,
  ) {
    Position? location = locationService.userLocation;

    return BlocBuilder<LiveMembersCubit, LiveMembersState>(
      buildWhen: (previous, current) {
        if (previous.status != current.status) {
          //ignore: no-empty-block
          setState(() {});

          return true;
        }

        return false;
      },
      builder: (context, state) {
        return Column(
          children: [
            Container(
              height: 350,
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppColors.grey50,
                  width: 1,
                ),
              ),
              child: Stack(
                alignment: Alignment.bottomRight,
                children: [
                  GoogleMap(
                    onMapCreated: (GoogleMapController controller) {
                      _mapController = controller;
                    },
                    rotateGesturesEnabled: false,
                    tiltGesturesEnabled: false,
                    // zoomGesturesEnabled: false,
                    zoomControlsEnabled: false,
                    myLocationEnabled: true,
                    myLocationButtonEnabled: true,
                    gestureRecognizers: {
                      Factory<OneSequenceGestureRecognizer>(
                        () => EagerGestureRecognizer(),
                      )
                    },
                    initialCameraPosition: CameraPosition(
                      target: LatLng(
                        location!.latitude,
                        location.longitude,
                      ),
                      zoom: initialZoomLevel,
                    ),
                    circles: {
                      Circle(
                        circleId: const CircleId('1'),
                        center: LatLng(
                          location.latitude,
                          location.longitude,
                        ),
                        radius: _radius * 1609.34,
                        strokeWidth: 1,
                        strokeColor: AppColors.transparent,
                        fillColor: AppColors.transparent,
                      ),
                    },
                    markers: {
                      if (state.status == LiveMembersStatus.success)
                        ...state.data.map(
                          (e) {
                            return Marker(
                              markerId: MarkerId(e.id.toString()),
                              position: LatLng(
                                e.location.coordinates[1],
                                e.location.coordinates[0],
                              ),
                              icon: BitmapDescriptor.defaultMarkerWithHue(
                                BitmapDescriptor.hueGreen,
                              ),
                              infoWindow: InfoWindow(
                                title: e.name,
                                snippet: e.club?.name,
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (_) => OthersProfilePage(
                                        userId: e.userId,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                        ).toSet(),
                    },
                  ),
                  Positioned(
                    bottom: 0,
                    left: 0,
                    child: SliderTheme(
                      data: SliderThemeData(
                        valueIndicatorColor: AppColors.primaryGreen,
                        valueIndicatorTextStyle: const TextStyle(
                          color: AppColors.white,
                        ),
                        activeTrackColor:
                            AppColors.primaryGreen.withOpacity(.5),
                        activeTickMarkColor: AppColors.white,
                        inactiveTickMarkColor: AppColors.primaryGreen,
                        thumbColor: AppColors.primaryGreen,
                      ),
                      child: Slider(
                        value: _radius,
                        min: 10,
                        max: 200,
                        divisions: 5,
                        label: '${_radius.toStringAsFixed(0)} miles',
                        inactiveColor: AppColors.primaryGreen10,
                        onChanged: (double value) {
                          setState(() => _radius = value);
                          bloc.getLiveMembers(
                            maxDistance: _radius,
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (state.status == LiveMembersStatus.loading &&
                state.data.isNotEmpty)
              Row(
                children: const [
                  Padding(
                    padding: EdgeInsets.only(top: 16),
                    child: Text("Please Wait"),
                  ),
                ],
              )
            else
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    "Showing ${state.data.length} results.",
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                  ),
                  TextButton(
                    onPressed: state.hasReachedMax
                        ? null
                        : () {
                            bloc.getLiveMembers(
                              maxDistance: _radius,
                              offset: state.data.length,
                            );
                          },
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.primaryGreen,
                      textStyle: const TextStyle(
                        fontSize: 14,
                      ),
                    ),
                    child: const Text("Load More"),
                  ),
                ],
              )
          ],
        );
      },
    );
  }

  void _getLiveMembers() {
    context.read<LiveMembersCubit>().getLiveMembers(
          maxDistance: _radius,
        );
  }
}
