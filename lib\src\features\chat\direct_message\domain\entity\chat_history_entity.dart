import 'package:equatable/equatable.dart';

class ChatHistoryEntity extends Equatable {
  const ChatHistoryEntity({
    required this.senderId,
    required this.receiverId,
    required this.type,
    required this.content,
    required this.isLastSeen,
    required this.id,
    required this.createdAt,
  });

  final String senderId;
  final String receiverId;
  final String type;
  final String content;
  final bool? isLastSeen;
  final String id;
  final DateTime createdAt;

  @override
  List<Object?> get props => [
        senderId,
        receiverId,
        type,
        content,
        isLastSeen,
        id,
        createdAt,
      ];
}
