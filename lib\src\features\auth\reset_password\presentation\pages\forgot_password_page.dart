import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/validators/input_validators.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/auth/reset_password/bloc/forgot_password/forgot_password_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/root/presentations/widgets/authentication_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:input_form_field/input_form_field.dart';

class ForgotPasswordPage extends StatelessWidget {
  ForgotPasswordPage({Key? key}) : super(key: key);

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return ScrollableWrapper(
      appBar: AppBar(
        backgroundColor: UIColors.background,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppColors.black,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildForgotPasswordTitle(),
          SizedBox(height: Values.v8.h),
          _buildForgotPasswordDetails(),
          SizedBox(height: Values.v30.h),
          Form(key: _formKey, child: _buildEmailField(context)),
          SizedBox(height: Values.v215.h),
          _buildContinueButton(context),
        ],
      ),
    );
  }

  Widget _buildForgotPasswordTitle() {
    return Align(
      alignment: Alignment.center,
      child: Text(
        TextConstants.forgotPasswordCaption,
        style: AppTypography.regular16(
          color: AppColors.black,
        ).copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildForgotPasswordDetails() {
    return Align(
      alignment: Alignment.center,
      child: Text(
        "Enter your email address or phone number associated with your account and we'll send you a code to verify your identity.",
        style: AppTypography.regular12(
          color: AppColors.black,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildEmailField(BuildContext context) {
    return InputFormField(
      textEditingController:
          context.read<ForgotPasswordBloc>().emailOrPhoneField,
      labelText: TextConstants.enterEmailOrPhone,
      keyboardType: TextInputType.emailAddress,
      autocorrect: false,
      validator: InputValidators.emailOrPhone,
    );
  }

  Widget _buildContinueButton(BuildContext context) {
    return BlocConsumer<ForgotPasswordBloc, BaseState>(
      listener: (context, state) {
        if (state is SuccessState) {
          Navigator.pushReplacementNamed(
            context,
            Routes.identityVerification,
          );
        } else if (state is ErrorState<ErrorModel>) {
          AppToast.showToast(
            message: state.data!.message!,
            gravity: ToastGravity.BOTTOM,
            backgroundColor: AppColors.error,
          );
        }
      },
      builder: (context, state) {
        return Button(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              context
                  .read<ForgotPasswordBloc>()
                  .add(const SendOTPToEmailEvent());
            }
          },
          isLoading: state is LoadingState,
          label: TextConstants.continueText,
        );
      },
    );
  }
}
