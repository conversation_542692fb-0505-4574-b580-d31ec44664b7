import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/validators/input_validators.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/list_multi_selection_widget.dart';
import 'package:fitsomnia_app/src/core/widgets/list_single_selection_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/pages/create_coach_program_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/widgets/add_discount_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/widget/dropdown_selection_menu.dart';
import 'package:fitsomnia_app/src/features/coach/root/domain/entities/coach_program_category_entity.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/presentation/widgets/custom_dropdown_menu.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CreateCoachProgramBasicInfoWidget extends StatefulWidget {
  const CreateCoachProgramBasicInfoWidget({Key? key, this.programEntity})
      : super(key: key);

  final CoachProgramEntity? programEntity;

  @override
  State<CreateCoachProgramBasicInfoWidget> createState() =>
      CreateCoachProgramBasicInfoWidgetState();
}

class CreateCoachProgramBasicInfoWidgetState
    extends State<CreateCoachProgramBasicInfoWidget>
    with AutomaticKeepAliveClientMixin<CreateCoachProgramBasicInfoWidget> {
  ValueNotifier<MenuItem> programCategoryNotifier =
      ValueNotifier(MenuItem(label: '', name: ''));

  ValueNotifier<MenuItem> programSubCategorieNotifier =
      ValueNotifier(MenuItem(name: '', label: ''));

  TextEditingController programTitleController = TextEditingController();

  ValueNotifier<MenuItem> programDurationNotifier = ValueNotifier(MenuItem(
      name: CoachProgramDurationTerm.MONTHS.key(),
      label: CoachProgramDurationTerm.MONTHS.name()));

  TextEditingController programDescController = TextEditingController();
  List<ChoiceOption> selectedGuarantees = [];

  TextEditingController programPriceController = TextEditingController();
  TextEditingController discountPriceController =
      TextEditingController(text: '');

  ValueNotifier<ChoiceOption?> selectedPaymentTerm = ValueNotifier(null);

  List<MenuItem> categories = [];
  List<MenuItem> subCategories = [];
  List<CoachProgramCategoryEntity> _categoryMap = [];
  Map<String, List<CoachProgramCategoryEntity>> categoryIdToSubcategoryMap = {};

  @override
  void initState() {
    super.initState();

    _setPreviousValue();
    // BlocProvider.of<CoachBloc>(context).add(GetCoachCategoryMap());
  }

  @override
  void dispose() {
    programTitleController.dispose();
    programDescController.dispose();
    programPriceController.dispose();
    discountPriceController.dispose();
    durationCountController.dispose();

    super.dispose();
  }

  List<ChoiceOption> choiceOptions = [];
  _setPreviousValue() {
    programTitleController.text =
        (widget.programEntity == null) ? '' : widget.programEntity!.title;

    programDescController.text =
        (widget.programEntity == null) ? '' : widget.programEntity!.desc;

    _initCategorySubCategory();

    MenuItem initialItem = programDurations.last;
    if (widget.programEntity != null) {
      for (MenuItem durationItem in programDurations) {
        if (durationItem.name ==
            widget.programEntity!.durationCount.toString()) {
          initialItem = durationItem;
          break;
        }
      }
    }

    programDurationNotifier.value = initialItem;

    selectedGuarantees = (widget.programEntity == null)
        ? []
        : widget.programEntity!.guarantees.map((name) {
            return ChoiceOption(name: name, label: name);
          }).toList();

    Log.debug('selected guarantees: ${selectedGuarantees.length}');

    programPriceController.text = (widget.programEntity == null)
        ? ''
        : widget.programEntity!.payments.first.discountedPrice.toString();

    if (widget.programEntity != null) {
      if (widget.programEntity!.payments.first.actualPrice! > 0) {
        discountPriceController.text =
            widget.programEntity!.payments.first.actualPrice.toString();
      }
    }

    choiceOptions = avialablePaymentTerms.map((paymentTerm) {
      return ChoiceOption(name: paymentTerm.key(), label: paymentTerm.name());
    }).toList();

    if (widget.programEntity != null) {
      durationCountController.text = widget.programEntity!.durationCount.toString();
    }
    
  }

  /// create category menu list, subcategory menu list

  _initCategorySubCategory() {
    List<CoachProgramCategoryEntity> categoryList =
        context.read<CoachBloc>().categoryList;
    categoryIdToSubcategoryMap =
        context.read<CoachBloc>().subcategoriesByCategoryId;

    categories = categoryList
        .map<MenuItem>((item) => MenuItem(name: item.id, label: item.title))
        .toList();

    if (widget.programEntity != null) {
      String categoryId = widget.programEntity!.categoryId;
      String subCategoryId = widget.programEntity!.subCategoryId;

      subCategories =
          categoryIdToSubcategoryMap[categoryId]!.map<MenuItem>((item) {
        return MenuItem(name: item.id, label: item.title);
      }).toList();

      programCategoryNotifier.value =
          categories.firstWhere((item) => (item.name == categoryId));
      programSubCategorieNotifier.value =
          subCategories.firstWhere((item) => (item.name == subCategoryId));
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPageHeader(),
          _buildProgramIntroSection(),
        ],
      ),
    );
  }

  _buildPageHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _completeCoachProfileTitle(),
        Text(
          'Provide information to introduce your program',
          style: AppTypography.poppinsMedium14(color: AppColors.greyscale400),
        ),
        const SizedBox(
          height: 24,
        ),
      ],
    );
  }

  Widget _completeCoachProfileTitle() {
    return RichText(
      text: TextSpan(
        style: AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
        children: [
          TextSpan(
            text: 'Hi ! ',
            style: AppTypography.poppinsSemiBold24(color: UIColors.primary),
          ),
          const TextSpan(text: 'Add your program details'),
        ],
      ),
    );
  }

  _buildProgramIntroSection() {
    return Form(
      key: programBasicInfoFormValidationKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildProgramTitleSection(),
          const SizedBox(
            height: 20,
          ),
          _buildCoachCategoryMenuSection(),
          const SizedBox(
            height: 20,
          ),
          _buildCoachSubCategorySection(),
          const SizedBox(
            height: 20,
          ),
          _buildProgramDescriptioSection(),
          const SizedBox(
            height: 20,
          ),
          _buildProgramDurationSection(),
          const SizedBox(
            height: 20,
          ),
          _buildSelectGuaranteesSection(),
          const SizedBox(
            height: 20,
          ),
          // _buildProgramPriceSection(),
          // // const SizedBox(
          // //   height: 1,
          // // ),
          // _buildAddDiscountSection(),
          // const SizedBox(
          //   height: 20,
          // ),
          // _buildPreviousPriceSection(),
        ],
      ),
    );
  }

  // List<MenuItem> coachCategories = [
  //   MenuItem(name: "Fitness", label: "Fitness"),
  //   MenuItem(name: "Cricket", label: "Cricket"),
  //   MenuItem(name: "Football", label: "Football"),
  //   MenuItem(name: "Badminton", label: "Badmintor"),
  // ];

  _buildCoachCategoryMenuSection() {
    return DropdownSelectionMenu(
      title: 'Select Category',
      lists: categories,
      initialSelectedItem: programCategoryNotifier.value,
      valuteNotifier: programCategoryNotifier,
    );
  }

  // List<MenuItem> coachSubCategories = [
  //   MenuItem(name: "Running", label: "Running"),
  //   MenuItem(name: "Bench", label: "Bench"),
  //   MenuItem(name: "Squat", label: "Squat"),
  //   MenuItem(name: "Pushup", label: "Pushup"),
  // ];

  _buildCoachSubCategorySection() {
    return ValueListenableBuilder(
        valueListenable: programCategoryNotifier,
        builder: (context, selectedCategory, child) {
          subCategories =
              categoryIdToSubcategoryMap[selectedCategory.name] != null
                  ? categoryIdToSubcategoryMap[selectedCategory.name]!
                      .map((item) => MenuItem(name: item.id, label: item.title))
                      .toList()
                  : [];

          return DropdownSelectionMenu(
            title: 'Select Sub-categories',
            lists: subCategories,
            initialSelectedItem: programSubCategorieNotifier.value,
            valuteNotifier: programSubCategorieNotifier,
          );
        });
  }

  List<MenuItem> programDurations = [
    MenuItem(
        name: CoachProgramDurationTerm.DAYS.key(),
        label: CoachProgramDurationTerm.DAYS.name()),
    MenuItem(
        name: CoachProgramDurationTerm.WEEKS.key(),
        label: CoachProgramDurationTerm.WEEKS.name()),
    MenuItem(
        name: CoachProgramDurationTerm.MONTHS.key(),
        label: CoachProgramDurationTerm.MONTHS.name()),
  ];

  _buildProgramDurationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DropdownSelectionMenu(
          title: 'Program Duration Type',
          lists: programDurations,
          initialSelectedItem: programDurationNotifier.value,
          valuteNotifier: programDurationNotifier,
        ),
        SizedBox(
          height: 20,
        ),
        _buildDurationCount(),
      ],
    );
  }

  TextEditingController durationCountController =
      TextEditingController(text: '1');

  _buildDurationCount() {
    return ValueListenableBuilder(
        valueListenable: programDurationNotifier,
        builder: (BuildContext context, MenuItem selectedDurationTypeItem,
            Widget? child) {
          return _buildEditTextInfo(
            name: 'Program Duration in ${selectedDurationTypeItem.label}',
            text: durationCountController.text,
            controller: durationCountController,
            validator: InputValidators.name,
            keyboardType: TextInputType.number,
            isRequired: true,
          );
        });
  }

  _buildProgramTitleSection() {
    return _buildEditTextInfo(
      name: 'Program Title',
      text: programTitleController.text,
      controller: programTitleController,
      validator: InputValidators.name,
      isRequired: true,
    );
  }

  _buildProgramDescriptioSection() {
    return _buildEditTextInfo(
      name: 'Program Description',
      text: programDescController.text,
      controller: programDescController,
      validator: InputValidators.name,
      maxLine: 5,
      isRequired: true,
    );
  }

  _buildEditTextInfo(
      {required String name,
      required String? text,
      required TextEditingController controller,
      required String? Function(String?)? validator,
      int maxLine = 1,
      TextInputType? keyboardType = null,
      bool isRequired = false}) {
    if (text != null) controller.text = text;

    // return TextFormField(
    //     cursorColor: AppColors.black,
    //     controller: controller,
    //     maxLines: 5,
    //     minLines: 5,
    //     autofocus: true,
    //     autocorrect: false,
    //     decoration: _buildInputDecoration(),

    //   );

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        (isRequired)
            ? RichText(
                text: TextSpan(
                  text: name,
                  style: AppTypography.poppinsRegular16(
                      color: UIColors.primaryGreen950),
                  children: const <TextSpan>[
                    TextSpan(
                      text: '*',
                      style: TextStyle(color: Colors.red),
                    ),
                  ],
                ),
              )
            : Text(
                name,
                style: AppTypography.poppinsRegular16(
                    color: UIColors.primaryGreen950),
              ),
        SizedBox(
          height: Values.v5,
        ),
        TextFormField(
          cursorColor: AppColors.black,
          controller: controller,
          maxLines: maxLine,
          minLines: maxLine,
          autofocus: false,
          autocorrect: false,
          decoration: _buildInputDecoration(),
          validator: validator,
          keyboardType: keyboardType,
          autovalidateMode: AutovalidateMode.onUserInteraction,
        )
      ],
    );
  }

  InputDecoration _buildInputDecoration() {
    var inputBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(Values.v10),
      borderSide: BorderSide(color: AppColors.greyscale50, width: Values.v2),
    );

    return InputDecoration(
      enabledBorder: inputBorder,
      focusedBorder: inputBorder,
      // hintText: 'Write a short bio?',
      hintStyle: AppTypography.regular18(
        color: AppColors.silver,
      ),
      contentPadding: EdgeInsets.all(Values.v16),
      // filled: true,
      // fillColor: AppColors.alto.withOpacity(0.2),
    );
  }

  List<ChoiceOption> guarantees = [
    ChoiceOption(name: 'Money Back Guarantees', label: 'Money Back Guarantees'),
    ChoiceOption(name: 'Gym Support', label: 'Gym Support'),
    ChoiceOption(name: '24/7 Support', label: '24/7 Support'),
  ];

  _buildSelectGuaranteesSection() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Select Guarantees",
          style:
              AppTypography.poppinsRegular16(color: UIColors.primaryGreen950),
        ),
        const SizedBox(
          height: Values.v5,
        ),
        _buildGuaranteesMultiSelectionOption(),
      ],
    );
  }

  _buildGuaranteesMultiSelectionOption() {
    return ListMultiSelectionWidget(
      key: UniqueKey(),
      options: guarantees,
      preSelectedOptions: selectedGuarantees,
      onSelectCallback: (List<ChoiceOption> selectedItems) {
        selectedGuarantees = selectedItems;

        Log.debug('selected item: ');
        for (ChoiceOption item in selectedItems) {
          Log.debug(item.name);
        }
      },
    );
  }

  String prevPrice = '';
  _buildProgramPriceSection() {
    return _buildEditTextInfo(
      name: 'Set Price (BDT)',
      text: programPriceController.text,
      controller: programPriceController,
      validator: InputValidators.name,
      keyboardType: TextInputType.number,
    );
  }

  _buildAddDiscountSection() {
    return AddDiscountWidget(
        key: UniqueKey(),
        priceStr: discountPriceController.text,
        callback: (int? price) {
          Log.debug('discount price: $price');
          discountPriceController.text = price.toString();
        });
  }

  @override
  bool get wantKeepAlive => true;
}
