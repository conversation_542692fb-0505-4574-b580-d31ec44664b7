part of 'leave_group_bloc.dart';

class LeaveGroupState extends Equatable {
  @override
  List<Object?> get props => [];
}

class LeaveGroupInitial extends LeaveGroupState {}

class LeaveGroupLoading extends LeaveGroupState {}

class LeaveGroupSuccess extends LeaveGroupState {
  final String message;

  LeaveGroupSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class LeaveGroupFailure extends LeaveGroupState {
  final String errorMessage;

  LeaveGroupFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}
