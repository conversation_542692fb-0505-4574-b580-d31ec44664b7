import 'dart:io';

class AdHelper {
  static String get nativeAd {
    return Platform.isAndroid
        ? 'ca-app-pub-5624599308242385/6153968162'
        : 'ca-app-pub-5624599308242385/4102367932';
  }
}

class AdHelperTest {
  static String get fixedSizeBannerAd {
    return Platform.isAndroid
        ? 'ca-app-pub-3940256099942544/6300978111'
        : 'ca-app-pub-3940256099942544/2934735716';
  }

  static String get nativeAd {
    return Platform.isAndroid
        ? 'ca-app-pub-3940256099942544/2247696110'
        : 'ca-app-pub-3940256099942544/3986624511';
  }

  static String get nativeVideoAd {
    return Platform.isAndroid
        ? 'ca-app-pub-3940256099942544/1044960115'
        : 'ca-app-pub-3940256099942544/2521693316';
  }

  
  static String workingNativeAdTest(){
    return '/21775744923/example/native';
  }

  static String workingNativeVideoAdTest(){
    return '/21775744923/example/native-video';
  }
}
