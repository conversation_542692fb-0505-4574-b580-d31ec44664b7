import 'dart:ffi';

import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/program_subscription_fee_payment_info.dart';

class ProgramSubscriptionFeePaymentModel
    extends ProgramSubscriptionFeePaymentInfo {
  ProgramSubscriptionFeePaymentModel({
    required super.programId,
    required super.subscriptionId,
    required super.userId,
    required super.paymentTerm,
    required super.paymentStatus,
    required super.paymentAmount,
    required super.paymentDate,
    required super.paymentGateway,
    required super.paymentUrl,
  });

  factory ProgramSubscriptionFeePaymentModel.fromJson(
      Map<String, dynamic> json) {
    return ProgramSubscriptionFeePaymentModel(
      programId: json['id'] ?? '',
      subscriptionId: json['subscriptionId'] ?? '',
      userId: json['userId'] ?? '',
      paymentTerm: json['paymentTerm'] ?? '',
      paymentStatus: json['paymentStatus'] ?? '',
      paymentAmount:
          (json['paymentAmount'] is Double) ? json['paymentAmount'] : 0,
      paymentDate: DateTime.tryParse(json['paymentDate']) ?? DateTime.now(),
      paymentGateway: json['paymentGateway'],
      paymentUrl: json['paymentUrl'],
    );
  }
}
