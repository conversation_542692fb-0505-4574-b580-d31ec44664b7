import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/presentation/widget/leaderboard_top_users_widget.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/presentation/widget/user_rank_detail_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RewardLeaderboardPage extends StatefulWidget {
  const RewardLeaderboardPage({super.key});

  @override
  State<RewardLeaderboardPage> createState() => _RewardLeaderboardPageState();
}

class _RewardLeaderboardPageState extends State<RewardLeaderboardPage> {
  late final String userId;

  @override
  void initState() {
    super.initState();
    userId = context.read<SharedBloc>().userProfile!.id;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SafeArea(
          child: Padding(
        padding: const EdgeInsets.all(Values.v10),
        child: SingleChildScrollView(child: _buildRewardLeaderboaard()),
      )),
    );
  }

  _buildRewardLeaderboaard() {
    return Column(
      children: [
        _buildTotalPointSection(),
        SizedBox(
          height: Values.v20,
        ),
        _buildTopUserSection(),
      ],
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        'Leaderboard',
        style: TextStyle(
          color: UIColors.primaryGreen950,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: true,
      elevation: 0,
    );
  }

  _buildTotalPointSection() {
    return UserRankDetailWidget(
      userId: userId,
    );
  }

  _buildTopUserSection() {
    return LeaderboardTopUsersWidget();
  }
}
