import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/validators/input_validators.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/image_viewer.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/widget/dropdown_selection_menu.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/presentation/widgets/custom_dropdown_menu.dart';
import 'package:fitsomnia_app/src/features/event/domain/entities/event_entity.dart';
import 'package:fitsomnia_app/src/features/event/domain/entities/event_join_entity.dart';
import 'package:fitsomnia_app/src/features/event/presentation/bloc/event_bloc.dart';
import 'package:fitsomnia_app/src/features/event/presentation/widgets/event_joiner_file_upload_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:input_form_field/input_form_field.dart';
import 'package:page_transition/page_transition.dart';

class EventRegistrationPage extends StatefulWidget {
  const EventRegistrationPage({
    super.key,
    required this.eventEntity,
  });
  final EventEntity eventEntity;

  @override
  State<EventRegistrationPage> createState() => _EventRegistrationPageState();
}

class _EventRegistrationPageState extends State<EventRegistrationPage> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneNumberController = TextEditingController();
  final TextEditingController _bkashNumberController = TextEditingController();
  final TextEditingController _transectionIdController =
      TextEditingController();

  ValueNotifier<bool> _sendMeEmailCheckedNotifier = ValueNotifier(true);

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // _eventEntity = widget.eventEntity;
    // _isAlreadyJoined = widget.eventEntity.isRegistered;
    // _isLoading = true;
    // registeredUsers = [];
    // BlocProvider.of<EventBloc>(context)
    //     .add(GetSingleEvent(eventId: widget.eventId));

    _isLoading = false;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneNumberController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildEventRegistrationPage(),
    );
  }

  _buildAppBar() {
    return AppBar(
      title: Text(
        'Join Event',
        style: AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen900),
      ),
      centerTitle: true,
    );
  }

  _buildEventRegistrationPage() {
    return BlocListener<EventBloc, EventState>(
      listener: (context, eventState) {
        if (eventState is JoinEventSuccess) {
          if (eventState.eventJoinLeaveResponseEntity.eventId ==
              widget.eventEntity.eventId) {
            // setState(() {
            //   _isAlreadyJoined = true;
            // });

            // get event registered user information
            // BlocProvider.of<EventBloc>(context).add(GetEventRegisteredUser(
            //   eventId: _eventEntity.eventId,
            //   offset: null,
            //   limit: null,
            // ));

            AppToast.showToast(message: 'Event Regestration successful');
            setState(() {
              _isLoading = false;
            });

            Navigator.of(context).pop(true);
          }
        }

        if (eventState is JoinEventError) {
          AppToast.showToast(
              message: 'Event Regestration failed. Try again later');

          setState(() {
            _isLoading = false;
          });

          Navigator.of(context).pop(false);
        }
      },
      child: (_isLoading)
          ? const Center(
              child: CircularProgressIndicator(
                color: UIColors.primary,
              ),
            )
          : Padding(
              padding: const EdgeInsets.all(Values.v20),
              child: Stack(
                children: [
                  SingleChildScrollView(
                    child: Column(
                      children: [
                        _buildEventRegistrationSection(),
                        SizedBox(
                          height: Values.v150.h,
                        ),
                      ],
                    ),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [_buildJoinLeaveButtonSection()],
                  )
                ],
              ),
            ),
    );
  }

  _buildEventRegistrationSection() {
    return Column(
      children: [
        Text(
          'Registration',
          style:
              AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen900),
        ),
        SizedBox(
          height: Values.v30,
        ),
        _buildRegistrationForm(),
        _buildSendMeEmailSection(),
      ],
    );
  }

  _buildJoinLeaveButtonSection() {
    return Container(
      color: AppColors.white,
      child: Row(
        children: [
          _buildRegistrationButton(),
        ],
      ),
    );
  }

  _buildRegistrationForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          _buildNameField(),
          _buildStudySessionField(),
          _buildPhoneField(),
          _buildEmailField(),
          _buildBkashNumberField(),
          _buildTransectionIdField(),
          _buildIdField(),
          SizedBox(
            height: Values.v10,
          ),
          _buildJerseySizeField(),
        ],
      ),
    );
  }

  Widget _buildNameField() {
    return InputFormField(
      textEditingController: _nameController,
      label: _buildInputFieldLable(lableText: 'Name', isRequired: true),
      hintText: 'Your Name',
      autocorrect: false,
      keyboardType: TextInputType.emailAddress,
      validator: InputValidators.name,
    );
  }

  Widget _buildPhoneField() {
    return InputFormField(
      textEditingController: _phoneNumberController,
      label:
          _buildInputFieldLable(lableText: 'Contact Number', isRequired: true),
      hintText: TextConstants.phone,
      keyboardType: TextInputType.phone,
      autocorrect: false,
      validator: InputValidators.phone,
      // bottomMargin: 5.h,
    );
  }

  Widget _buildEmailField() {
    return InputFormField(
      textEditingController: _emailController,
      label:
          _buildInputFieldLable(lableText: 'Email Address', isRequired: true),
      hintText: '<EMAIL>',
      autocorrect: false,
      keyboardType: TextInputType.emailAddress,
      validator: InputValidators.email,
    );
  }

  // List<String> sessions = StudySession.values.map((e) => e.name()).toList();
  // Widget _buildStudySessionField() {
  //   return Padding(
  //     padding: const EdgeInsets.only(bottom: Values.v20),
  //     child: DropdownFormField(
  //       labelText: "DU Session",
  //       isRequired: true,
  //       labelStyle:
  //           AppTypography.poppinsRegular18(color: UIColors.primaryGreen950),
  //       dropdownItems: sessions,
  //       onChanged: (String? value) {
  //         _sessionYearTextController.text = value ?? 'Others';

  //         return true;
  //       },
  //     ),
  //   );
  // }

  List<MenuItem> sessions = StudySession.values
      .map((e) => MenuItem(name: e.key(), label: e.name()))
      .toList();
  ValueNotifier<MenuItem> studySessionValueNotifier =
      ValueNotifier(MenuItem(name: '', label: ''));

  Widget _buildStudySessionField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: Values.v20),
      child: DropdownSelectionMenu(
        title: 'DU Session',
        lists: sessions,
        valuteNotifier: studySessionValueNotifier,
      ),
    );
  }

  Widget _buildBkashNumberField() {
    return InputFormField(
      textEditingController: _bkashNumberController,
      label: _buildBkashNumberFieldLable(),
      hintText: TextConstants.phone,
      keyboardType: TextInputType.phone,
      autocorrect: false,
      validator: InputValidators.phone,
      // bottomMargin: 5.h,
    );
  }

  Widget _buildTransectionIdField() {
    return InputFormField(
      textEditingController: _transectionIdController,
      label:
          _buildInputFieldLable(lableText: 'Transaction ID', isRequired: true),
      hintText: 'Payment Transaction Id',
      autocorrect: false,
      validator: InputValidators.name,
      // bottomMargin: 5.h,
    );
  }

  Widget _buildIdField() {
    return EventJoinerFileUploadWidget(
      featureName: 'event',
      fieldName: 'id',
      title: 'Student ID / Faculty ID (image)',
      initialFiles: [],
    );
  }

  List<MenuItem> jerseySizes = JerseySize.values
      .map((e) => MenuItem(name: e.key(), label: e.name()))
      .toList();
  ValueNotifier<MenuItem> jerseySizeValueNotifier =
      ValueNotifier(MenuItem(name: '', label: ''));

  Widget _buildJerseySizeField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: Values.v20),
      child: Column(
        children: [
          DropdownSelectionMenu(
            title: 'Jersey Size',
            lists: jerseySizes,
            valuteNotifier: jerseySizeValueNotifier,
          ),
          Row(
            children: [_buildSizeChartSection()],
          )
        ],
      ),
    );
  }

  Widget _buildRegistrationButton() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(5.0),
        child: ElevatedButton(
          onPressed: () {
            Log.debug('on press join button');
            _sendEventJoinRequest();
          },
          child: Container(
            height: Values.v60,
            child: Center(
              child: Text(
                'Registration',
                style: AppTypography.poppinsMedium20(color: UIColors.white),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSendMeEmailSection() {
    return Row(
      // crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        _buildSendMeEmailCheckbox(),
        Flexible(
          child: Text(
            'Send me information about this event via email',
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
            style:
                AppTypography.poppinsRegular14(color: UIColors.primaryGreen900),
          ),
        ),
      ],
    );
  }

  Widget _buildSendMeEmailCheckbox() {
    return ValueListenableBuilder(
      valueListenable: _sendMeEmailCheckedNotifier,
      builder: (context, value, _) {
        return Checkbox(
          value: value,
          onChanged: (isChecked) {
            _sendMeEmailCheckedNotifier.value = isChecked ?? false;
          },
          materialTapTargetSize: MaterialTapTargetSize.padded,
          activeColor: UIColors.primary,
          checkColor: AppColors.white,
          side: BorderSide(color: UIColors.primary),
        );
      },
    );
  }

  void _sendEventJoinRequest() {
    if (widget.eventEntity.registrationCloseTime != null &&
        widget.eventEntity.registrationCloseTime!.compareTo(DateTime.now()) <=
            0) {
      Log.debug('Event Registration closed');
      AppToast.showToast(
        message: 'Event Registration closed',
        backgroundColor: UIColors.primary,
        gravity: ToastGravity.BOTTOM,
      );

      return;
    }
    if (_formKey.currentState!.validate() == false) {
      return;
    }
    if (studySessionValueNotifier.value.name == '') {
      AppToast.showToast(
          message: 'Session is missing.', gravity: ToastGravity.BOTTOM);

      return;
    }
    if (_phoneNumberController.text == '') {
      AppToast.showToast(
          message: 'Contact Number is missing.', gravity: ToastGravity.BOTTOM);

      return;
    }
    if (_bkashNumberController.text == '') {
      AppToast.showToast(
          message: 'bkash number is missing.', gravity: ToastGravity.BOTTOM);

      return;
    }
    List<String> idFiles = context.read<EventBloc>().eventJoinerFile;
    if (idFiles.isEmpty) {
      AppToast.showToast(
          message: 'ID is missing.', gravity: ToastGravity.BOTTOM);

      return;
    }

    if (jerseySizeValueNotifier.value.name == '') {
      AppToast.showToast(
          message: 'Jersey Size is missing.', gravity: ToastGravity.BOTTOM);

      return;
    }
    Log.debug('send join request to server');
    BlocProvider.of<EventBloc>(context).add(JoinEvent(
        eventJoinRequestEntity: EventJoinRequestEntity(
      eventId: widget.eventEntity.eventId,
      userId: 'no-user-id',
      userName: _nameController.text,
      userMobile: _phoneNumberController.text,
      userEmail: _emailController.text,
      notifyMeUsingEmail: _sendMeEmailCheckedNotifier.value,
      studySession: studySessionValueNotifier.value.name,
      bkashNumber: _bkashNumberController.text,
      contactNumber: _phoneNumberController.text,
      transectionId: _transectionIdController.text,
      studentIdImage: context.read<EventBloc>().eventJoinerFile.first,
      jerseySize: jerseySizeValueNotifier.value.name,
    )));

    setState(() {
      _isLoading = true;
    });
  }

  Widget _buildBkashNumberFieldLable() {
    return RichText(
      text: TextSpan(
        text: 'Bkash Number',
        style: AppTypography.poppinsRegular16(color: UIColors.primaryGreen950),
        children: <TextSpan>[
          TextSpan(
            text: '*',
            style: TextStyle(color: Colors.red),
          ),
          TextSpan(
            text: '\nFrom which the money was sent',
            style: AppTypography.poppinsRegular14(color: UIColors.fitGrey),
          ),
        ],
      ),
    );
  }

  Widget _buildInputFieldLable(
      {required String lableText, bool isRequired = false}) {
    return (isRequired)
        ? RichText(
            text: TextSpan(
              text: lableText,
              style: AppTypography.poppinsRegular16(
                  color: UIColors.primaryGreen950),
              children: const <TextSpan>[
                TextSpan(
                  text: '*',
                  style: TextStyle(color: Colors.red),
                ),
              ],
            ),
          )
        : Text(
            lableText,
            style:
                AppTypography.poppinsRegular16(color: UIColors.primaryGreen950),
          );
  }

  _buildSizeChartSection() {
    return IconButton(
      onPressed: () {
        Log.debug('view size chart');
        _previewMediaFile(mediaFileUrl: Assets.jerseySizeChartImg);
      },
      icon: Container(
        // padding: EdgeInsets.all(10),
        // decoration: BoxDecoration(
        //   color: UIColors.primaryGreen100,
        //   borderRadius: BorderRadius.circular(Values.v50),
        //   // border: Border.all(
        //   //   color: UIColors.primaryGreen600,
        //   // ),
        // ),
        child: Row(
          children: [
            // Icon(Icons.shopping_bag_outlined),
            Text(
              'Size Chart',
              style: TextStyle(
                  fontSize: FontSize.s14.sp,
                  fontFamily: FontConstants.poppinsFontFamily,
                  fontWeight: FontWeight.w500,
                  color: UIColors.primary,
                  decoration: TextDecoration.underline,
                  decorationColor: UIColors.primary),
            )
          ],
        ),
      ),
    );
  }

  void _previewMediaFile({required String mediaFileUrl}) {
    Future.delayed(const Duration(milliseconds: 300), () {
      Navigator.push(
        context,
        PageTransition(
          type: PageTransitionType.scale,
          alignment: Alignment.bottomCenter,
          child: ImageAssetViewer(
            image: mediaFileUrl,
          ),
        ),
      );
    });
  }
}
