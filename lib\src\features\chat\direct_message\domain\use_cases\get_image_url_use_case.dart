import 'dart:io';

import 'package:fitsomnia_app/src/features/chat/direct_message/domain/repositories/image_upload_repository.dart';
import 'package:fitsomnia_app/src/features/file_upload/domain/entities/upload_pre_signed_url_entity.dart';

class GetImageUrlUseCase {
  const GetImageUrlUseCase({required this.imageUploadRepository});

  final ImageUploadRepository imageUploadRepository;

  Future<UploadPreSignedUrlEntity?> call({
    required String filePath,
    required File file,
    String? featureName,
  }) async {
    return await imageUploadRepository.getImageUrl(
      filePath: filePath,
      file: file,
      featureName: featureName,
    );
  }
}
