<?xml version="1.0" encoding="utf-8"?>
<com.google.android.gms.ads.nativead.NativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_gravity="start"
        android:background="#FFFFFF">

<!--        header section-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_gravity="start"
            android:gravity="center">

            <androidx.cardview.widget.CardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:layout_marginEnd="8dp"
                app:cardCornerRadius="20dp"
                >
                <ImageView
                    android:id="@+id/ad_app_icon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:adjustViewBounds="true"
                    android:src="@drawable/com_facebook_profile_picture_blank_square"
                    android:contentDescription="icon"/>

            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="start"
                android:layout_gravity="center"
                android:layout_marginStart="8dp">

                <TextView
                    android:id="@+id/ad_advertiser"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:hint="Advertiser"
                    android:visibility="visible"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/black"/>
                <TextView
                    android:id="@+id/ad_ad"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Ad"
                    android:textStyle="bold"
                    android:textColor="@color/gnt_gray"
                    android:textSize="14sp"/>
<!--                <RatingBar-->
<!--                    android:id="@+id/ad_stars"-->
<!--                    style="@style/Widget.AppCompat.RatingBar.Small"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:isIndicator="true"-->
<!--                    android:numStars="5"-->
<!--                    android:stepSize="0.5"-->
<!--                    android:visibility="visible"/>-->

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/ad_headline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:hint="Native ads are ad assets that are presented to users via UI components"
            android:visibility="visible"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginBottom="5dp"/>


        <!--            MediaView and Body text-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="center">

            <com.google.android.gms.ads.nativead.MediaView
                android:id="@+id/ad_media"
                android:layout_width="match_parent"
                android:layout_height="175dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:visibility="visible" />
            <TextView
                android:id="@+id/ad_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:textSize="14sp"
                android:textStyle="normal"
                android:hint="Using platform specific UI components, these ads can be formatted to match"
                android:layout_gravity="start"
                android:gravity="start"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:paddingBottom="10dp"
            android:paddingTop="10dp">

            <TextView
                android:id="@+id/ad_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="5dp"
                android:paddingStart="5dp"
                android:paddingRight="5dp"
                android:paddingEnd="5dp"
                android:textSize="12sp"
                android:visibility="gone"
                android:hint="Product Price"/>

            <TextView
                android:id="@+id/ad_store"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="5dp"
                android:paddingStart="5dp"
                android:paddingRight="5dp"
                android:paddingEnd="5dp"
                android:textSize="12sp"
                android:visibility="gone"
                android:hint="Store name"/>

            <Button
                android:id="@+id/ad_call_to_action"
                android:layout_width="300dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_gravity="center"
                android:textSize="14sp"
                android:textStyle="bold"
                android:background="@color/primaryGreen"
                android:textColor="@color/white"
                android:visibility="visible"
                android:hint="Shop Now"/>
        </LinearLayout>
        

        
    </LinearLayout>

</com.google.android.gms.ads.nativead.NativeAdView>