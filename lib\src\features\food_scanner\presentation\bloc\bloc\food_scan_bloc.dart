import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/use_cases/get_image_url_use_case.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/entities/food_info_entity.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/entities/food_scan_image_request.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/use_case/food_scan_usecase.dart';
import 'package:fitsomnia_app/src/features/food_scanner/presentation/pages/food_scanner_page.dart';

part 'food_scan_event.dart';
part 'food_scan_state.dart';

class FoodScanBloc extends Bloc<FoodScanEvent, FoodScanState> {
  FoodScanBloc({required this.getImageUrlUseCase, required this.usecase})
      : super(FoodScanInitial()) {
    on<SendFoodScanImageEvent>(_sendFoodScanImage);
    on<SendFoodScanFeedbackEvent>(_sendFoodScanFeedback);
  }

  final FoodScanUseCase usecase;
  final GetImageUrlUseCase getImageUrlUseCase;

  Future<void> _sendFoodScanImage(
    SendFoodScanImageEvent event,
    Emitter<FoodScanState> emit,
  ) async {
    emit(FoodScanLoading());
    try {
      // // upload food image
      // final imageUploadResponse = await getImageUrlUseCase.call(
      //     filePath: event.imagePath,
      //     file: File(event.imagePath),
      //     featureName: 'diet');
      // make for info query using imaage path

      final response = await usecase.getFoodInfoFromImage(
          request: FoodScanImageRequest(
        filePath: event.imagePath,
        featureName: "food-scanner",
      ));
      response.fold((l) {
        emit(FoodScanFailure(data: l));
      }, (r) {
        emit(FoodScanSuccess(foodInfo: r));
      });
    } catch (error) {
      Log.debug(error.toString());
      emit(FoodScanFailure(data: error));
    }
  }

  Future<void> _sendFoodScanFeedback(
    SendFoodScanFeedbackEvent event,
    Emitter<FoodScanState> emit,
  ) async {
    emit(FoodScanLoading());
    try {
      // make for info query using imaage path
      final response = await usecase.getFoodInfoFromFeedback(
          request: FoodScanFeedbackRequest(
        feedback: event.feedback,
        featureName: 'diet',
      ));
      response.fold((l) {
        emit(FoodScanFailure(data: l));
      }, (r) {
        emit(FoodScanSuccess(foodInfo: r));
      });
    } catch (error) {
      Log.debug(error.toString());
      emit(FoodScanFailure(data: error));
    }
  }
}
