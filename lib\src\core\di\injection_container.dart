import 'package:fitsomnia_app/src/core/connection_info.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';
import 'package:fitsomnia_app/src/core/services/location_service/location_service.dart';
import 'package:fitsomnia_app/src/features/auth/login/presentation/bloc/login_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/logout/data/data_source/logout_data_source.dart';
import 'package:fitsomnia_app/src/features/auth/logout/data/repository/logout_repository_impl.dart';
import 'package:fitsomnia_app/src/features/auth/logout/domain/repository/logout_repository.dart';
import 'package:fitsomnia_app/src/features/auth/logout/domain/use_case/logout_use_case.dart';
import 'package:fitsomnia_app/src/features/auth/logout/presentation/cubit/logout_cubit.dart';
import 'package:fitsomnia_app/src/features/auth/reset_password/bloc/forgot_password/forgot_password_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/reset_password/bloc/identity_verification/identity_verification_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/reset_password/bloc/reset_password/reset_password_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/root/data/data_sources/auth_data_source.dart';
import 'package:fitsomnia_app/src/features/auth/root/data/data_sources/auth_remote_data_source_imp.dart';
import 'package:fitsomnia_app/src/features/auth/root/data/repositories/auth_repository_imp.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/repositories/auth_repository.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/forgot_password_use_case.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/identity_verification_use_case.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/login_use_case.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/login_with_apple_use_case.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/login_with_facebook_use_case.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/login_with_google_use_case.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/registration_use_case.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/reset_password_use_case.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/verification_use_case.dart';
import 'package:fitsomnia_app/src/features/auth/sign_up/presentation/bloc/sign_up_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/verification/presentation/bloc/verification_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/data/data_source/active_user_data_source.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/data/repository/active_user_repository_impl.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/domain/repository/active_user_repository.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/domain/use_case/active_user_list_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/presentation/bloc/online_fit_buddy_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/domain/use_case/create_group_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/presentation/bloc/create_group_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/data/data_source/gorup_data_source.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/data/repository/group_repository_impl.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/repository/group_repository.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/use_case/get_group_list_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/presentation/bloc/group_list_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/data/data_source/all_users_data_source.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/data/repository/all_users_repository_impl.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/domain/repository/all_users_repository.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/domain/use_case/get_all_users_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/presentation/bloc/all_users_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/data/data_source/recent_conversation_data_source.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/data/repository/chat_list_repository_impl.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/domain/repository/chat_list_repository.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/domain/use_case/recent_chat_list_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/presentation/bloc/recent_conversation_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/data/data_sources/delete_message_data_source.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/data/data_sources/one_to_one_chat_data_source.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/data/repositories/delete_message_repository_impl.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/data/repositories/image_picking_repository_impl.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/data/repositories/image_upload_repository_impl.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/data/repositories/one_to_one_chat_repository_impl.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/repositories/delete_message_repository.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/repositories/image_picking_repository.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/repositories/image_upload_repository.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/repositories/one_to_one_chat_repository.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/use_cases/delete_one_to_one_message_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/use_cases/get_chat_history_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/use_cases/get_image_url_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/use_cases/pick_image_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/chat_history/chat_history_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/delete_message/delete_message_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/image_picker/image_picker_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/image_upload/image_upload_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/use_cases/add_member_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/use_cases/delete_group_message_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/use_cases/get_group_history_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/use_cases/get_group_members_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/use_cases/leave_group_use_case.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/add_member_bloc/add_member_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/group_chat/group_chat_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/group_members/group_members_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/leave_group/leave_group_bloc.dart';
import 'package:fitsomnia_app/src/features/club/dashboard/domain/use_case/membersip_status_use_case.dart';
import 'package:fitsomnia_app/src/features/club/dashboard/presentation/bloc/club_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/use_cases/club_members_use_case.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/use_cases/leave_club_use_case.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/use_cases/live_member_use_case.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/use_cases/my_club_use_case.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/use_cases/nearby_clubs_members_use_case.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/club/club_bloc.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/club_members/club_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/live_members/live_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/nearby_clubs_members/nearby_clubs_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/spot_not/spot_not_cubit.dart';
import 'package:fitsomnia_app/src/features/club/nearby_clubs/domain/use_case/join_club_use_case.dart';
import 'package:fitsomnia_app/src/features/club/nearby_clubs/domain/use_case/narby_club_use_case.dart';
import 'package:fitsomnia_app/src/features/club/nearby_clubs/presentation/bloc/nearby_clubs_bloc.dart';
import 'package:fitsomnia_app/src/features/club/root/data/data_source/club_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/club/root/data/data_source/club_remote_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/club/root/data/repository/club_repository_impl.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/repository/club_repository.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/data/data_source/coach_personal_data_source.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/data/repositories/coach_personal_repository_impl.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/repositories/coach_personal_repository.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/use_case/coach_personal_use_case.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/bloc/coach_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/data/data_source/coach_newsfeed_data_source.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/data/repositories/coach_newsfeed_repository_impl.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/repositories/coach_newsfeed_repository.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/use_case/coach_newsfeed_use_case.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/data_source/coach_program_data_source.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/repository/coach_program_repository_impl.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/repositories/coach_program_repository.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/use_case/coach_program_use_case.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/bloc/coach_program_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/data_source/coach_profile_data_source.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/data_source/coach_profile_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/repositories/coach_profile_repository_impl.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/repositories/coach_profile_repository.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/use_cases/coach_profile_use_case.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/bloc/coach_profile_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/data/data_source/coach_program_review_data_source.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/data/repositories/coach_program_review_repository_impl.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/domain/repositories/coach_program_review_repository.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/domain/use_case/coach_program_review_use_case.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/presentation/bloc/coach_program_review_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_withdraw_payment/data/data_source/coach_withdraw_payment_datasource.dart';
import 'package:fitsomnia_app/src/features/coach/coach_withdraw_payment/data/repositories/coach_withdraw_payment_repository_impl.dart';
import 'package:fitsomnia_app/src/features/coach/coach_withdraw_payment/domain/repositories/coach_withdraw_payment_repository.dart';
import 'package:fitsomnia_app/src/features/coach/coach_withdraw_payment/domain/use_case/coach_withdraw_payment_usecase.dart';
import 'package:fitsomnia_app/src/features/coach/coach_withdraw_payment/presentation/bloc/withdraw_payment_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/root/data/data_source/coach_data_source.dart';
import 'package:fitsomnia_app/src/features/coach/root/data/repositories/coach_repository_impl.dart';
import 'package:fitsomnia_app/src/features/coach/root/domain/repositories/coach_repository.dart';
import 'package:fitsomnia_app/src/features/coach/root/domain/use_case/coach_use_case.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/student_dashboard/data/data_source/user_program_subscription_data_source.dart';
import 'package:fitsomnia_app/src/features/coach/student_dashboard/data/repositories/user_program_subscription_repository_impl.dart';
import 'package:fitsomnia_app/src/features/coach/student_dashboard/domain/repositories/user_program_subscription_repository.dart';
import 'package:fitsomnia_app/src/features/coach/student_dashboard/domain/use_case/user_program_subscription_use_case.dart';
import 'package:fitsomnia_app/src/features/coach/student_dashboard/presentation/bloc/student_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/bloc/dashboard_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/domain/use_case/activity_level_use_case.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/presentation/bloc/activity_level/activity_level_bloc.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/presentation/bloc/diet_plan/diet_plan_bloc.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/presentation/bloc/ideal_calories/ideal_calories_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/presentation/bloc/weight_unit/weight_unit_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/domain/use_case/food_consumption_use_case.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/domain/use_case/recent_foods_use_case.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/bloc/delete_consumed_food/delete_consumed_food_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/bloc/food_consumption/food_consumption_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/bloc/recent_foods/recent_foods_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/food/personalised_food/domain/use_case/create_food_use_case.dart';
import 'package:fitsomnia_app/src/features/diet/food/personalised_food/domain/use_case/my_foods_use_case.dart';
import 'package:fitsomnia_app/src/features/diet/food/personalised_food/presentation/bloc/create_food_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/food/personalised_food/presentation/bloc/my_foods_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/food/root/data/data_source/food_data_source.dart';
import 'package:fitsomnia_app/src/features/diet/food/root/data/data_source/food_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/diet/food/root/data/repository/food_repository.dart';
import 'package:fitsomnia_app/src/features/diet/food/root/domain/repository/food_repository_impl.dart';
import 'package:fitsomnia_app/src/features/diet/food/root/domain/use_case/diet_plan_use_case.dart';
import 'package:fitsomnia_app/src/features/diet/root/data/data_source/diet_data_source.dart';
import 'package:fitsomnia_app/src/features/diet/root/data/data_source/diet_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/diet/root/data/repository/diet_repository.dart';
import 'package:fitsomnia_app/src/features/diet/root/domain/repository/diet_repository_impl.dart';
import 'package:fitsomnia_app/src/features/diet/settings/update_diet_plan/presentation/bloc/update_diet_plan_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/track_diet/domain/use_case/diet_history_use_case.dart';
import 'package:fitsomnia_app/src/features/diet/track_diet/domain/use_case/ideal_calories_use_case.dart';
import 'package:fitsomnia_app/src/features/diet/track_diet/domain/use_case/water_consumption_use_case.dart';
import 'package:fitsomnia_app/src/features/diet/track_diet/presentation/bloc/diet_history/diet_history_bloc.dart';
import 'package:fitsomnia_app/src/features/diet/track_diet/presentation/bloc/water_consumption/water_cubit.dart';
import 'package:fitsomnia_app/src/features/event/data/data_source/event_data_source.dart';
import 'package:fitsomnia_app/src/features/event/data/data_source/event_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/event/data/repositories/event_repository_impl.dart';
import 'package:fitsomnia_app/src/features/event/domain/repositories/event_repository.dart';
import 'package:fitsomnia_app/src/features/event/domain/use_cases/event_use_case.dart';
import 'package:fitsomnia_app/src/features/event/presentation/bloc/event_bloc.dart';
import 'package:fitsomnia_app/src/features/file_upload/data/data_sources/file_upload_remote_datasource.dart';
import 'package:fitsomnia_app/src/features/file_upload/data/data_sources/file_upload_remote_datasource_impl.dart';
import 'package:fitsomnia_app/src/features/file_upload/data/repositories/file_upload_repository_impl.dart';
import 'package:fitsomnia_app/src/features/file_upload/domain/repositories/file_upload_repository.dart';
import 'package:fitsomnia_app/src/features/file_upload/domain/use_cases/file_upload_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/add_new_address/data/data_source/validate_address_data_source.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/add_new_address/data/repository/validate_address_repository_impl.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/add_new_address/domain/repository/validate_address_repository.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/add_new_address/domain/use_case/validate_address_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/add_new_address/presentation/bloc/add_billing_address_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/add_new_address/presentation/bloc/add_shipping_address_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/add_new_address/presentation/bloc/validate_address/validate_address_cubit.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/root/data/data_sources/billing_address_data_source.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/root/data/data_sources/shipping_address_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/root/data/data_sources/shipping_address_remote_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/root/data/repositories/billing_address_repository_impl.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/root/data/repositories/shipping_address_repository_imp.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/root/domain/repositories/billing_address_repository.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/root/domain/repositories/shipping_address_repositories.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/root/domain/use_cases/billing_address_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/root/domain/use_cases/shipping_address_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/select_address/bloc/billing_address_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/select_address/bloc/select_shipping_address_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/update_address/bloc/update_billing_address_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/update_address/bloc/update_shipping_address_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/cart/data/data_sources/cart_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/fit_market/cart/data/data_sources/cart_remote_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/fit_market/cart/data/repositories/cart_repository_impl.dart';
import 'package:fitsomnia_app/src/features/fit_market/cart/domain/repositories/cart_repositories.dart';
import 'package:fitsomnia_app/src/features/fit_market/cart/domain/use_cases/cart_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/cart/presentations/bloc/cart_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/confirm/data/data_source/order_summary_data_source.dart';
import 'package:fitsomnia_app/src/features/fit_market/confirm/data/repository/order_summary_repository_impl.dart';
import 'package:fitsomnia_app/src/features/fit_market/confirm/domain/repository/order_summary_repository.dart';
import 'package:fitsomnia_app/src/features/fit_market/confirm/domain/use_case/get_payment_methods_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/confirm/domain/use_case/order_summary_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/confirm/presentation/bloc/order_summary_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/confirm/presentation/bloc/payment_methods_cubit.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/data/repositories/fit_market_dashboard_repository_impl.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/domain/repositories/fit_market_dashboard_reposity.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/domain/use_cases/fitmarket_dashboard_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/presentation/bloc/fit_market_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/data/data_source/wishlist_data_source.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/data/repository/wishlist_repository_impl.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/domain/repository/wishlist_repository.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/domain/use_case/add_to_wishlist_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/domain/use_case/get_wishlist_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/domain/use_case/remove_from_wishlist_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/presentation/bloc/add_wishlist/add_to_wishlist_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/presentation/bloc/get_wishlist/get_wishlist_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/presentation/bloc/remove_from_wishlist/remove_from_wishlist_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/order/data/data_source/order_data_source.dart';
import 'package:fitsomnia_app/src/features/fit_market/order/data/repository/order_repository_impl.dart';
import 'package:fitsomnia_app/src/features/fit_market/order/domain/repository/order_repository.dart';
import 'package:fitsomnia_app/src/features/fit_market/order/domain/use_case/create_order_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/order/domain/use_case/get_order_details_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/order/domain/use_case/order_history_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/order/presentation/bloc/create_order/create_order_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/order/presentation/bloc/order_details/order_details_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/order/presentation/bloc/order_history/order_history_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/payment/data/data_source/payment_data_source.dart';
import 'package:fitsomnia_app/src/features/fit_market/payment/data/repository/payment_repository_impl.dart';
import 'package:fitsomnia_app/src/features/fit_market/payment/domain/repository/payment_repository.dart';
import 'package:fitsomnia_app/src/features/fit_market/payment/domain/use_case/create_payment_by_points_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/payment/domain/use_case/create_payment_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/payment/presentation/bloc/make_payment/make_payment_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/product_details/data/data_source/product_details_data_source.dart';
import 'package:fitsomnia_app/src/features/fit_market/product_details/data/data_source/product_details_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/fit_market/product_details/domain/repositories/product_details_repository.dart';
import 'package:fitsomnia_app/src/features/fit_market/product_details/domain/use_cases/product_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/product_details/presentation/bloc/product_details_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/review/data/data_source/review_data_source.dart';
import 'package:fitsomnia_app/src/features/fit_market/review/data/repository/review_repository_impl.dart';
import 'package:fitsomnia_app/src/features/fit_market/review/domain/repository/review_repository.dart';
import 'package:fitsomnia_app/src/features/fit_market/review/domain/use_case/create_review_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/review/domain/use_case/delete_review_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/review/domain/use_case/edit_review_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/review/domain/use_case/get_review_use_case.dart';
import 'package:fitsomnia_app/src/features/fit_market/review/presentation/bloc/create_review/create_review_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/review/presentation/bloc/edit_review/edit_review_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/review/presentation/bloc/get_review/get_review_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/root/data/source/fitmarket_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/fitbot/fitbot_chat/data/data_source/fitbot_chat_data_source.dart';
import 'package:fitsomnia_app/src/features/fitbot/fitbot_chat/data/repositories/fitbot_chat_repository_imp.dart';
import 'package:fitsomnia_app/src/features/fitbot/fitbot_chat/domain/repositories/fitbot_chat_repository.dart';
import 'package:fitsomnia_app/src/features/fitbot/fitbot_chat/domain/use_cases/fitbot_chat_use_case.dart';
import 'package:fitsomnia_app/src/features/fitbot/fitbot_chat/presentation/bloc/fitbot_chat_bloc.dart';
import 'package:fitsomnia_app/src/features/food_scanner/data/data_source/food_scan_datasource.dart';
import 'package:fitsomnia_app/src/features/food_scanner/data/repositories/food_scan_repository_impl.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/repositories/food_scan_repository.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/use_case/food_scan_usecase.dart';
import 'package:fitsomnia_app/src/features/food_scanner/presentation/bloc/bloc/food_scan_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/challenge_details/domain/use_case/upload_challenge_video_use_case.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/challenge_details/presentation/bloc/upload_challenge_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/dashboard/data/data_source/points_data_source.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/dashboard/data/repository/points_repository_impl.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/dashboard/domain/repository/points_repository.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/dashboard/domain/use_case/accept_challenge_use_case.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/dashboard/domain/use_case/get_points_use_case.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/dashboard/presentation/bloc/accept_challenge/accept_challenge_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/dashboard/presentation/bloc/points/points_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/history/points_history/domain/use_case/get_points_history_use_case.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/history/points_history/presentation/bloc/points_history_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/leaderboard/domain/use_case/get_leaderboard_use_case.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/leaderboard/presentation/bloc/leaderboard_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/monthly_challenges/data/data_source/monthly_challenge_data_source.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/monthly_challenges/data/repository/monthly_challenge_repository_impl.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/monthly_challenges/domain/repository/monthly_challenge_repository.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/monthly_challenges/domain/use_cases/get_challenge_history_use_case.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/monthly_challenges/domain/use_cases/get_monthly_challenges_use_case.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/monthly_challenges/presentation/bloc/challenge_history/challenge_history_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/monthly_challenges/presentation/bloc/monthly_challenge_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/data_sources/comment_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/data_sources/comment_remote_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/repositories/comment_repository_impl.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/repositories/comment_reposity.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/use_cases/add_comment_use_case.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/use_cases/get_all_comments_use_case.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/comment_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/data_sources/create_post_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/data_sources/create_post_remote_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/repositories/crteate_post_repository_impl.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/repositories/crteate_post_repository.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/use_cases/create_post_usecase.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/create_post/create_post_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/search_location/search_location_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/bloc/edit_post_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/data_source/feed_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/data_source/feed_remote_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/repositories/feed_repository_imp.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/repositories/feed_repository.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/use_cases/delete_post_use_case.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/use_cases/edit_post_use_case.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/use_cases/get_feeds_use_case.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/use_cases/like_post_use_case.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/feed_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/data_sources/likers_info_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/data_sources/likers_info_remote_data_source_imp.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/repositories/likers_info_repository_impl.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/repositories/likers_info_repository.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/use_cases/likers_info_use_case.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/likers_info_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/data_sources/story_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/data_sources/story_remote_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/repositories/story_repository_impl.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/repositories/story_reposity.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/use_cases/create_story_use_case.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/use_cases/delete_story_use_case.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/use_cases/get_stories_use_case.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/use_cases/mark_story_as_viewed_use_case.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/use_cases/my_story_use_case.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/story_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/data_sources/story_viewers_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/data_sources/story_viewers_remote_data_source_imp.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/repositories/story_viewers_repository_impl.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/repositories/story_viewers_repositories.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/use_cases/story_viewers_use_case.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/story_viewers_bloc.dart';
import 'package:fitsomnia_app/src/features/notification/data/data_sources/notification_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/notification/data/data_sources/notification_remote_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/notification/data/repositories/notification_repository_imp.dart';
import 'package:fitsomnia_app/src/features/notification/domain/repositories/notification_repository.dart';
import 'package:fitsomnia_app/src/features/notification/domain/use_cases/all_notification_use_case.dart';
import 'package:fitsomnia_app/src/features/notification/domain/use_cases/mark_all_notification_as_read_use_case.dart';
import 'package:fitsomnia_app/src/features/notification/domain/use_cases/spot_list_use_case.dart';
import 'package:fitsomnia_app/src/features/notification/presentation/bloc/notification_bloc.dart';
import 'package:fitsomnia_app/src/features/notification/presentation/cubit/update_fcm_token_cubit.dart';
import 'package:fitsomnia_app/src/features/on_boarding/data/data_source/shared_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/on_boarding/data/repositories/shared_repository_imp.dart';
import 'package:fitsomnia_app/src/features/on_boarding/domain/repositories/shared_repository.dart';
import 'package:fitsomnia_app/src/features/on_boarding/domain/use_cases/app_info_use_case.dart';
import 'package:fitsomnia_app/src/features/on_boarding/domain/use_cases/send_location_to_server_user_case.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:fitsomnia_app/src/features/planner/data/data_sources/planner_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/planner/data/repositories/planner_repository_imp.dart';
import 'package:fitsomnia_app/src/features/planner/domain/repositories/planner_repository.dart';
import 'package:fitsomnia_app/src/features/planner/domain/use_cases/create_note_use_case.dart';
import 'package:fitsomnia_app/src/features/planner/domain/use_cases/delete_note_use_case.dart';
import 'package:fitsomnia_app/src/features/planner/domain/use_cases/get_note_details_use_case.dart';
import 'package:fitsomnia_app/src/features/planner/domain/use_cases/get_notes_use_case.dart';
import 'package:fitsomnia_app/src/features/planner/domain/use_cases/update_note_use_case.dart';
import 'package:fitsomnia_app/src/features/planner/presentation/bloc/planner_bloc.dart';
import 'package:fitsomnia_app/src/features/polls/data/data_sours/poll_data_source.dart';
import 'package:fitsomnia_app/src/features/polls/data/data_sours/poll_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/polls/data/repositories/team_poll_repository_impl.dart';
import 'package:fitsomnia_app/src/features/polls/domain/repositories/team_poll_repository.dart';
import 'package:fitsomnia_app/src/features/polls/domain/use_case/team_poll_use_case.dart';
import 'package:fitsomnia_app/src/features/polls/presentation/bloc/team_poll_bloc/bloc/team_poll_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/data/data_source/post_details_data_source.dart';
import 'package:fitsomnia_app/src/features/profile/data/data_source/profile_feed_data_source.dart';
import 'package:fitsomnia_app/src/features/profile/data/data_source/profile_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/profile/data/data_source/spot_back_data_source.dart';
import 'package:fitsomnia_app/src/features/profile/data/repositories/post_details_repository_impl.dart';
import 'package:fitsomnia_app/src/features/profile/data/repositories/profile_feed_repository_impl.dart';
import 'package:fitsomnia_app/src/features/profile/data/repositories/profile_repository_impl.dart';
import 'package:fitsomnia_app/src/features/profile/data/repositories/spot_back_repository_impl.dart';
import 'package:fitsomnia_app/src/features/profile/domain/repositories/post_details_repository.dart';
import 'package:fitsomnia_app/src/features/profile/domain/repositories/profile_feed_repository.dart';
import 'package:fitsomnia_app/src/features/profile/domain/repositories/profile_repository.dart';
import 'package:fitsomnia_app/src/features/profile/domain/repositories/spot_back_repository.dart';
import 'package:fitsomnia_app/src/features/profile/domain/usecase/block_fit_buddy_use_case.dart';
import 'package:fitsomnia_app/src/features/profile/domain/usecase/get_fit-buddies_use_case.dart';
import 'package:fitsomnia_app/src/features/profile/domain/usecase/get_post_details_use_case.dart';
import 'package:fitsomnia_app/src/features/profile/domain/usecase/get_profile_feed_photos_use_case.dart';
import 'package:fitsomnia_app/src/features/profile/domain/usecase/get_profile_feed_videos_use_case.dart';
import 'package:fitsomnia_app/src/features/profile/domain/usecase/get_profile_use_case.dart';
import 'package:fitsomnia_app/src/features/profile/domain/usecase/get_timeline_use_case.dart';
import 'package:fitsomnia_app/src/features/profile/domain/usecase/spot_back_use_case.dart';
import 'package:fitsomnia_app/src/features/profile/domain/usecase/unfriend_fit_buddy_use_case.dart';
import 'package:fitsomnia_app/src/features/profile/domain/usecase/update_profile_info_use_case.dart';
import 'package:fitsomnia_app/src/features/profile/domain/usecase/update_profile_picture_use_case.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/bench_squat_unit/bench_squat_unit_cubit.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/block_fit_buddy/block_fit_buddy_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/feed_image/feed_image_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/feed_video/feed_video_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/fit_buddies/fit_buddies_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/like_unlike_post/like_or_unlike_cubit.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/others_profile/others_profile_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/post_comments/post_comments_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/post_details/post_details_cubit.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/spot_back/spot_back_cubit.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/timeline/timeline_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/unfriend_fit_buddy/unfriend_fit_buddy_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/user_profile/user_profile_bloc.dart';
import 'package:fitsomnia_app/src/features/qr_code/scan_qr_code/data/data_sources/scan_qr_code_data_source.dart';
import 'package:fitsomnia_app/src/features/qr_code/scan_qr_code/data/repositories/qr_code_repository_impl.dart';
import 'package:fitsomnia_app/src/features/qr_code/scan_qr_code/domain/repositories/qr_code_repository.dart';
import 'package:fitsomnia_app/src/features/qr_code/scan_qr_code/domain/use_cases/scan_qr_code_use_case.dart';
import 'package:fitsomnia_app/src/features/qr_code/scan_qr_code/presentation/bloc/scan_qr_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/data/data_source/daily_task_data_source.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/data/repositories/daily_task_repository_impl.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/domain/repositories/daily_task_repository.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/domain/use_case/daily_task_use_case.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/presentation/bloc/daily_task_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/data/data_source/reward_leaderboard_data_source.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/data/repository/reward_leaderboard_repository_impl.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/repository/reward_leaderboard_repository.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/use_case/reward_leaderboard_use_case.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/presentation/bloc/reward_leaderboard_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/data/data_source/referral_data_source.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/data/repositories/referral_repository_impl.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/repositories/referral_repository.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/use_case/referral_use_case.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/presentation/bloc/referral_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/data/data_source/reward_point_data_source.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/data/repositories/reward_point_repository_impl.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/repositories/reward_point_repository.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/use_case/reward_point_use_case.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/presentation/bloc/reward_point_bloc.dart';
import 'package:fitsomnia_app/src/features/search/data/data_source/search_data_source.dart';
import 'package:fitsomnia_app/src/features/search/data/repository/search_repository_imp.dart';
import 'package:fitsomnia_app/src/features/search/domain/repository/search_repository.dart';
import 'package:fitsomnia_app/src/features/search/domain/use_case/club_search_use_case.dart';
import 'package:fitsomnia_app/src/features/search/domain/use_case/people_search_use_case.dart';
import 'package:fitsomnia_app/src/features/search/domain/use_case/product_search_use_case.dart';
import 'package:fitsomnia_app/src/features/search/presentation/bloc/club/club_search_cubit.dart';
import 'package:fitsomnia_app/src/features/search/presentation/bloc/people/people_search_cubit.dart';
import 'package:fitsomnia_app/src/features/search/presentation/bloc/product/product_search_cubit.dart';
import 'package:fitsomnia_app/src/features/search/presentation/bloc/search/search_view_cubit.dart';
import 'package:fitsomnia_app/src/features/settings/notifications/presentation/bloc/notification_bloc.dart';
import 'package:fitsomnia_app/src/features/settings/privacy/block_list/data/data_source/block_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/settings/privacy/block_list/data/repository/block_repository_impl.dart';
import 'package:fitsomnia_app/src/features/settings/privacy/block_list/domain/repository/block_repository.dart';
import 'package:fitsomnia_app/src/features/settings/privacy/block_list/domain/use_case/block_use_case.dart';
import 'package:fitsomnia_app/src/features/settings/privacy/block_list/presentation/bloc/block_bloc.dart';
import 'package:fitsomnia_app/src/features/settings/privacy/profile_visibility/bloc/profile_visibility_bloc.dart';
import 'package:fitsomnia_app/src/features/settings/root/data/data_source/preference_data_source.dart';
import 'package:fitsomnia_app/src/features/settings/root/data/repository/preference_repository_impl.dart';
import 'package:fitsomnia_app/src/features/settings/root/domain/repository/preference_repository.dart';
import 'package:fitsomnia_app/src/features/settings/root/domain/use_case/preference_use_case.dart';
import 'package:fitsomnia_app/src/features/settings/security/delete_account/data/data_source/delete_account_data_source.dart';
import 'package:fitsomnia_app/src/features/settings/security/delete_account/data/repository/delete_account_repository_impl.dart';
import 'package:fitsomnia_app/src/features/settings/security/delete_account/domain/repository/delete_account_repository.dart';
import 'package:fitsomnia_app/src/features/settings/security/delete_account/domain/use_case/delete_account_use_case.dart';
import 'package:fitsomnia_app/src/features/settings/security/delete_account/presentation/bloc/delete_account_cubit.dart';
import 'package:fitsomnia_app/src/features/settings/security/email/data/data_source/change_email_data_source.dart';
import 'package:fitsomnia_app/src/features/settings/security/email/data/repository/change_email_repository_impl.dart';
import 'package:fitsomnia_app/src/features/settings/security/email/domain/repository/change_email_repository.dart';
import 'package:fitsomnia_app/src/features/settings/security/email/domain/use_case/change_email_use_case.dart';
import 'package:fitsomnia_app/src/features/settings/security/email/presentation/bloc/change_email_bloc.dart';
import 'package:fitsomnia_app/src/features/settings/security/password/data/data_source/change_password_data_source.dart';
import 'package:fitsomnia_app/src/features/settings/security/password/data/repository/change_password_repository_impl.dart';
import 'package:fitsomnia_app/src/features/settings/security/password/domain/repository/change_password_repository.dart';
import 'package:fitsomnia_app/src/features/settings/security/password/domain/use_case/change_password_use_case.dart';
import 'package:fitsomnia_app/src/features/settings/security/password/presentation/bloc/change_password_bloc.dart';
import 'package:fitsomnia_app/src/features/settings/subscription/data/data_sources/subscriptions_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/settings/subscription/data/data_sources/subscriptions_remote_data_source_imp.dart';
import 'package:fitsomnia_app/src/features/settings/subscription/data/repositories/subscriptions_repository_imp.dart';
import 'package:fitsomnia_app/src/features/settings/subscription/domain/repositories/subscriptions_repositories.dart';
import 'package:fitsomnia_app/src/features/settings/subscription/domain/use_cases/my_subscription_package_use_case.dart';
import 'package:fitsomnia_app/src/features/settings/subscription/domain/use_cases/subscribe_package_use_case.dart';
import 'package:fitsomnia_app/src/features/settings/subscription/domain/use_cases/subscriptions_use_case.dart';
import 'package:fitsomnia_app/src/features/settings/subscription/presentation/bloc/subscription_bloc.dart';
import 'package:fitsomnia_app/src/features/spot_not/delete_profile/data/data_source/delete_spot_profile_data_source.dart';
import 'package:fitsomnia_app/src/features/spot_not/delete_profile/data/repository/delete_spot_profile_repository_impl.dart';
import 'package:fitsomnia_app/src/features/spot_not/delete_profile/domain/repository/delete_spot_profile_repository.dart';
import 'package:fitsomnia_app/src/features/spot_not/delete_profile/domain/use_case/delete_spot_profile_use_case.dart';
import 'package:fitsomnia_app/src/features/spot_not/delete_profile/presentation/bloc/delete_spot_profile_cubit.dart';
import 'package:fitsomnia_app/src/features/spot_not/near_by_user_profile/presentation/bloc/near_by_user_profile_bloc.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/data/data_sources/spot_not_data_source.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/data/data_sources/spot_not_remote_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/data/repositories/spot_not_repository_impl.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/domain/repositories/spot_not_repository.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/domain/use_cases/get_fit_buddy_status_use_case.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/domain/use_cases/suggested_user_list_use_case.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/presentation/bloc/spot_bloc/spot_bloc.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/presentation/bloc/spot_not_bloc.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/data/data_source/spot_me_data_source.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/data/repository/spot_me_repository_impl.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/domain/repository/spot_me_repository.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/domain/use_case/create_spot_me_profile_use_case.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/domain/use_case/get_others_spot_me_use_case.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/domain/use_case/get_spot_me_profile_use_case.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/domain/use_case/get_spot_profile_use_case.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/domain/use_case/update_spot_me_profile_use_case.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/presentation/bloc/spot_me_bloc.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_not_filter/presentation/bloc/spot_not_filter_bloc.dart';
import 'package:fitsomnia_app/src/features/spot_not_request/data/data_sources/spot_not_request_data_source.dart';
import 'package:fitsomnia_app/src/features/spot_not_request/data/data_sources/spot_not_request_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/spot_not_request/data/repositories/spot_not_request_repository_impl.dart';
import 'package:fitsomnia_app/src/features/spot_not_request/domain/repositories/spot_not_request_repository.dart';
import 'package:fitsomnia_app/src/features/spot_not_request/domain/use_cases/cancel_spot_request_use_case.dart';
import 'package:fitsomnia_app/src/features/spot_not_request/domain/use_cases/not_request_use_case.dart';
import 'package:fitsomnia_app/src/features/spot_not_request/domain/use_cases/send_spot_request_use_case.dart';
import 'package:fitsomnia_app/src/features/spot_not_request/domain/use_cases/update_request_use_case.dart';
import 'package:fitsomnia_app/src/features/training/body_building_program_details/domain/use_case/get_body_building_program_details_use_case.dart';
import 'package:fitsomnia_app/src/features/training/body_building_program_details/presentation/bloc/intermediate_training_bloc.dart';
import 'package:fitsomnia_app/src/features/training/body_building_program_selection/data/data_source/body_building_program_data_source.dart';
import 'package:fitsomnia_app/src/features/training/body_building_program_selection/data/repository/body_building_program_repository_impl.dart';
import 'package:fitsomnia_app/src/features/training/body_building_program_selection/domain/repository/body_building_program_repository.dart';
import 'package:fitsomnia_app/src/features/training/body_building_program_selection/domain/use_case/get_body_building_program_use_case.dart';
import 'package:fitsomnia_app/src/features/training/body_building_program_selection/presentation/bloc/body_building_bloc.dart';
import 'package:fitsomnia_app/src/features/training/exercise_category/data/data_sources/exercise_category_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/training/exercise_category/data/data_sources/exercise_category_remote_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/training/exercise_category/data/repositories/exercise_category_repository_impl.dart';
import 'package:fitsomnia_app/src/features/training/exercise_category/domain/repositories/exercise_category_reposity.dart';
import 'package:fitsomnia_app/src/features/training/exercise_category/domain/use_cases/get_exercise_category_use_case.dart';
import 'package:fitsomnia_app/src/features/training/exercise_category/presentation/bloc/exercise_category_bloc.dart';
import 'package:fitsomnia_app/src/features/training/exercise_list/data/data_sources/training_remote_data_source.dart';
import 'package:fitsomnia_app/src/features/training/exercise_list/data/data_sources/training_remote_data_source_impl.dart';
import 'package:fitsomnia_app/src/features/training/exercise_list/data/repositories/training_repository_impl.dart';
import 'package:fitsomnia_app/src/features/training/exercise_list/domain/repositories/training_reposity.dart';
import 'package:fitsomnia_app/src/features/training/exercise_list/domain/use_cases/get_training_egercise_list_use_case.dart';
import 'package:fitsomnia_app/src/features/training/exercise_list/presentation/bloc/exercise_list_bloc.dart';
import 'package:fitsomnia_app/src/features/training/muscle_group_selection/data/data_source/muscle_group_data_source.dart';
import 'package:fitsomnia_app/src/features/training/muscle_group_selection/data/repository/muscle_group_repository_impl.dart';
import 'package:fitsomnia_app/src/features/training/muscle_group_selection/domain/repository/muscle_group_repository.dart';
import 'package:fitsomnia_app/src/features/training/muscle_group_selection/domain/use_case/get_muscle_group_use_case.dart';
import 'package:fitsomnia_app/src/features/training/muscle_group_selection/presentation/bloc/muscle_group_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

import '../../features/fit_market/product_details/data/repositories/product_details_repository_impl.dart';

part 'bloc.dart';
part 'data_source.dart';
part 'repository.dart';
part 'use_case.dart';

final sl = GetIt.instance;

Future<void> init() async {
  /// BLOC
  await _initBlocs();

  /// Use Case
  await _initUseCases();

  /// Repository
  await _initRepositories();

  /// DataSource
  await _initDataSources();

  /// Externals
  final restClient = RestClient();
  final internetConnectionChecker = InternetConnection();
  final locationService = LocationService();

  sl.registerLazySingleton(() => restClient);
  sl.registerLazySingleton<ConnectionInfo>(() => ConnectionInfoImpl(sl()));
  sl.registerLazySingleton(() => internetConnectionChecker);
  sl.registerFactory(() => locationService);
}
