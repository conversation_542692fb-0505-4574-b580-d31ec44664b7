import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/domain/entity/active_user_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/presentation/bloc/online_fit_buddy_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/pages/chat_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ActiveUserListPage extends StatefulWidget {
  const ActiveUserListPage({Key? key}) : super(key: key);

  @override
  State<ActiveUserListPage> createState() => _ActiveUserListPageState();
}

class _ActiveUserListPageState extends State<ActiveUserListPage> {
  @override
  void initState() {
    super.initState();
    BlocProvider.of<OnlineFitBuddyBloc>(context).add(
      const OnlineFitBuddyEvent(limit: 1000, offset: 0),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: _buildAppBar(),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "Active Now",
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: AppColors.black54,
              ),
            ),
            Expanded(
              child: BlocBuilder<OnlineFitBuddyBloc, OnlineFitBuddyState>(
                builder: (context, state) {
                  return state is OnlineFitBuddySuccess
                      ? ListView.builder(
                          itemCount: state.activeUserEntity.length,
                          itemBuilder: (context, index) {
                            return _buildActiveUserCard(
                              context,
                              state.activeUserEntity[index],
                            );
                          },
                        )
                      : const SizedBox.shrink();
                },
              ),
            )
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        "People",
        style: TextStyle(
          color: AppColors.black,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevation: 0,
      iconTheme: const IconThemeData(color: AppColors.black),
      backgroundColor: AppColors.white,
    );
  }

  Widget _buildActiveUserCard(
    BuildContext context,
    ActiveUserEntity activeUserEntity,
  ) {
    return GestureDetector(
      onTap: () => _navigateToChatPage(context, activeUserEntity),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 10),
        child: Row(
          children: [
            _buildUserAvatar(activeUserEntity.userInfo!.image),
            const SizedBox(width: 16),
            _buildUserName(activeUserEntity.userInfo!.name!),
          ],
        ),
      ),
    );
  }

  Widget _buildUserAvatar(ActiveUserImage? image) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        ImageContainer.circularImage(
          image: image != null ? image.profile ?? "" : "",
          radius: Values.v22,
        ),
        Positioned(
          right: 1,
          bottom: 2,
          child: Container(
            height: Values.v10,
            width: Values.v10,
            decoration: BoxDecoration(
              color: AppColors.apple,
              shape: BoxShape.circle,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUserName(String name) {
    return Expanded(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: AppColors.black10),
          ),
        ),
        child: Text(
          name,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
            color: AppColors.black,
          ),
        ),
      ),
    );
  }

  void _navigateToChatPage(
    BuildContext context,
    ActiveUserEntity activeUserEntity,
  ) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(
          title: activeUserEntity.userInfo!.name!,
          fitBuddyId: activeUserEntity.userInfo!.id!,
          image: activeUserEntity.getActiveUsersImage(),
        ),
      ),
    );
  }
}
