import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/best_coach_list_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_category_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_newsfeed_promotional_banners.dart';
import 'package:flutter/material.dart';

class CoachNewsfeedProgramsPage extends StatefulWidget {
  const CoachNewsfeedProgramsPage({super.key});

  @override
  State<CoachNewsfeedProgramsPage> createState() =>
      _CoachNewsfeedProgramsPageState();
}

class _CoachNewsfeedProgramsPageState extends State<CoachNewsfeedProgramsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SafeArea(
        child: _buildCoachProgramPageBody(),
      ),
    );
  }

  _buildCoachProgramPageBody() {
    return Padding(
      padding: const EdgeInsets.only(top: 20,),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSearchBar(),
            _buildSuggestedCoachSliderSection(),
            _buildCoachCategorySection(),
            _buildBestCoachSection(),
            SizedBox(height: Values.v20,)
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        'Coaching Program',
        style: TextStyle(
          color: UIColors.primaryGreen950,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      elevation: 0,
    );
  }

  _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.only(left: Values.v20, right: Values.v20, bottom: Values.v20),
      child: GestureDetector(
        onTap: () {
          Log.debug('search button tap');
          // Navigator.of(context).pushNamed(Routes.coachSearchListPage, arguments: [null, null, null]);
          Navigator.of(context).pushNamed(Routes.coachSearchPage);
        },
        child: Container(
          // padding: EdgeInsets.all(Values.v20),
          decoration: BoxDecoration(
              border: Border.all(color: AppColors.greyscale50),
              borderRadius: BorderRadius.circular(Values.v10)),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 10,
              ),
              Icon(Icons.search_rounded),
              // Text('Search coach', style: AppTypography.poppinsRegular16(color: AppColors.greyscale400),),
              Expanded(
                child: TextFormField(
                  onChanged: (_) {
                    Log.debug('searching...');
                  },
                  controller: null,
                  decoration: InputDecoration(
                    // contentPadding:
                    //     const EdgeInsets.fromLTRB(12.0, 0.0, 12.0, 12.0),
                    border: InputBorder.none,
                    hintText: 'Search coach',
                    hintStyle: AppTypography.poppinsRegular16(
                        color: AppColors.greyscale400),
                  ),
                  enabled: false,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _buildSuggestedCoachSliderSection() {
    return CoachNewsfeedPromotionalBanner();
  }

  _buildCoachCategorySection() {
    return CoachCategoryWidget();
  }

  _buildBestCoachSection() {
    return BestCoachSectionWidget();
  }
}
