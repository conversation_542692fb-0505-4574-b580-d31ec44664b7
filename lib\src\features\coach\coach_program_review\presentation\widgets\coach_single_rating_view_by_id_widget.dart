import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/domain/entities/user_review_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/presentation/bloc/coach_program_review_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class CoachSingleRatingViewByIdWidget extends StatefulWidget {
  const CoachSingleRatingViewByIdWidget(
      {super.key, required this.reviewId, this.isProgramReview = false});
  final bool isProgramReview;
  final String reviewId;

  @override
  State<CoachSingleRatingViewByIdWidget> createState() =>
      _CoachSingleRatingViewByIdWidgetState();
}

class _CoachSingleRatingViewByIdWidgetState
    extends State<CoachSingleRatingViewByIdWidget> {
  UserReviewEntity? coachRatingEntity;
  bool _isLoading = false;
  bool _showDetailsComment = false;

  @override
  void initState() {
    super.initState();
    _showDetailsComment = false;
    if (widget.isProgramReview)
      BlocProvider.of<CoachProgramReviewBloc>(context)
          .add(GetCoachProgramSingleReview(reviewId: widget.reviewId));
    else
      BlocProvider.of<CoachProgramReviewBloc>(context)
          .add(GetCoachProfileSingleReview(reviewId: widget.reviewId));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachProgramReviewBloc, CoachProgramReviewState>(
      listener: (context, state) {
        if (state is GetCoachProgramSingleReviewSuccess) {
          if (widget.reviewId == state.review.reviewId) {
            Log.debug('get program rating success');
            setState(() {
              coachRatingEntity = state.review;
            });
          }
        }

        if (state is GetCoachProgramSingleReviewFail) {
          Log.debug('get program rating fail');
          // AppToast.showToast(message: 'Failed to load coach review');
        }

         if (state is GetCoachProfileSingleReviewSuccess) {
          if (widget.reviewId == state.review.reviewId) {
            Log.debug('get profile rating success');
            setState(() {
              coachRatingEntity = state.review;
            });
          }
        }

        if (state is GetCoachProfileSingleReviewFail) {
          Log.debug('get profile rating fail');
          // AppToast.showToast(message: 'Failed to load coach review');
        }
      },
      child: (coachRatingEntity == null)
          ? SizedBox.shrink()
          : Container(
              // margin: EdgeInsets.all(Values.v10),
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _showDetailsComment = !_showDetailsComment;
                  });
                },
                child: Container(
                  padding: EdgeInsets.all(Values.v15),
                  decoration: BoxDecoration(border: Border.all(color: AppColors.greyscale100), borderRadius: BorderRadius.circular(Values.v10)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildRatingUserSection(),
                      const SizedBox(
                        height: Values.v10,
                      ),
                      _buildRatingCommentSection(),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  _buildRatingUserSection() {
    return Row(
      children: [
        // _buildUserImage(),
        // const SizedBox(
        //   width: Values.v5,
        // ),
        Expanded(child: _buildUserInfo()),
      ],
    );
  }

  _buildUserImage() {
    return ImageContainer.circularImage(
        image: coachRatingEntity!.userImage, radius: Values.v20);
  }

  _buildUserInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // _buildNameDateSection(),
        _buildRatingSection(),
      ],
    );
  }

  _buildRatingCommentSection() {
    return Text(
      coachRatingEntity!.comment,
      maxLines: (_showDetailsComment) ? 100 : 2,
      overflow: TextOverflow.ellipsis,
      style: AppTypography.poppinsRegular14(color: UIColors.primaryGreen900),
    );
  }

  _buildNameDateSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          coachRatingEntity!.userName,
          style: AppTypography.semiBold16(color: UIColors.primaryGreen900),
        ),
        Text(
          DateFormat.yMMMd().format(coachRatingEntity!.updatedAt),
          style: AppTypography.regular14(color: AppColors.greyscale400),
        )
      ],
    );
  }

  _buildRatingSection() {
    return Row(
      children: [
        const Icon(
          Icons.star,
          color: UIColors.primaryGreen400,
        ),
        Text(
          '${coachRatingEntity!.rating} Stars',
          style:
              AppTypography.poppinsRegular14(color: UIColors.primaryGreen900),
        )
      ],
    );
  }
}
