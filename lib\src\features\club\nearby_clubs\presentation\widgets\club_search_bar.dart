part of '../pages/nearby_clubs_page.dart';

class _ClubSearchBar extends StatelessWidget {
  const _ClubSearchBar({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _onPressed(context),
      child: Container(
        margin: EdgeInsets.only(
          left: 16.w,
          right: 16.w,
          top: 6.h,
          bottom: 20.h,
        ),
        padding: const EdgeInsets.all(12),
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: AppColors.grey6,
          borderRadius: BorderRadius.circular(30.r),
        ),
        child: Row(
          children: [
            Icon(
              Icons.search,
              color: AppColors.primaryGreen,
            ),
            SizedBox(width: 13.w),
            Text(
              TextConstants.searchClubYouWantToJoin,
              style: TextStyle(
                color: AppColors.grey2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onPressed(BuildContext context) {
    context.read<SearchViewCubit>().initialTabIndex = 1;
    Navigator.of(context).pushNamed(Routes.search);
    FirebaseService().logFeatureUsage('club', 'club_search', '');
  }
}
