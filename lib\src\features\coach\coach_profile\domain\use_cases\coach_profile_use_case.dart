import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/repositories/coach_profile_repository.dart';
import 'package:fitsomnia_app/src/features/coach/root/domain/repositories/coach_repository.dart';

class CoachProfileUseCase {
  final CoachProfileRepository coachRegistrationRepository;

  CoachProfileUseCase({required this.coachRegistrationRepository});

  Future<Either<ErrorModel, CoachEntity>> creaeCoachProfile(
      {required CoachEntity registrationEntity}) async {
    return await coachRegistrationRepository.createCoachProfile(
        registrationEntity: registrationEntity);
  }
   Future<Either<ErrorModel, CoachEntity>> updateCoachProfile({ required String coachId, required CoachEntity coachEntity}) async {
    return await coachRegistrationRepository.updateCoachProfile(coachId: coachId, coachEntity: coachEntity);
   }
}
