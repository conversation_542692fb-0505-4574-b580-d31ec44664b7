import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_chat_consultation_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_offered_all_program_list_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_offered_best_program_list_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CoachOfferedProgramsPage extends StatefulWidget {
  const CoachOfferedProgramsPage({
    super.key,
    required this.coachId,
  });
  final String? coachId; //18828a45-3111-4a37-96df-40470b5d48c8

  @override
  State<CoachOfferedProgramsPage> createState() =>
      _CoachOfferedProgramsPageState();
}

class _CoachOfferedProgramsPageState extends State<CoachOfferedProgramsPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SafeArea(
          child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(Values.v20),
          child: Column(
            children: [
              CoachProfileWidget(coachId: widget.coachId!),
              _buildBestProgramsSection(),
              _buildAllProgramsSection(),
            ],
          ),
        ),
      )),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        'Programs',
        style: TextStyle(
          color: UIColors.primaryGreen950,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: true,
      elevation: 0,
    );
  }

  _buildBestProgramsSection() {
    return CoachOfferedBestProgramsWidget(coachId: widget.coachId!);
  }

  _buildAllProgramsSection() {
    return CoachOfferedAllProgramsWidget(coachId: widget.coachId!);
  }
}

class CoachProfileWidget extends StatefulWidget {
  const CoachProfileWidget({super.key, required this.coachId});
  final String coachId;

  @override
  State<CoachProfileWidget> createState() => _CoachProfileWidgetState();
}

class _CoachProfileWidgetState extends State<CoachProfileWidget> {
  CoachEntity? _coachEntity;

  @override
  void initState() {
    super.initState();
    BlocProvider.of<CoachNewsfeedBloc>(context)
        .add(GetSingleCoachProfileEvent(coachId: widget.coachId));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachNewsfeedBloc, CoachNewsfeedState>(
      listener: (context, state) {
        if (state is GetSingleCoachProfileSuccess) {
          Log.debug('get coach profile success');

          setState(() {
            _coachEntity = state.coachProfile;
          });
        }

        if (state is GetSingleCoachProfileFail) {
          Log.debug('get coach profile fail');
        }
      },
      child: (_coachEntity == null)
          ? const SizedBox.shrink()
          : Container(
              margin: EdgeInsets.only(top: Values.v20),
              padding: EdgeInsets.all(Values.v10),
              decoration: BoxDecoration(
                  border: Border.all(color: UIColors.primaryGreen200),
                  borderRadius: BorderRadius.circular(10),
                  gradient: _buildLinearGradientGreenColor()),
              child: Row(
                children: [
                  _buildCoachProfileImage(),
                  SizedBox(
                    width: Values.v10,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _coachEntity!.name,
                          overflow: TextOverflow.clip,
                          maxLines: 2,
                          style: AppTypography.poppinsSemiBold24(
                              color: UIColors.primaryGreen950),
                          // style: TextStyle(fontSize: FontSize.s24, fontFamily: FontConstants.poppinsFontFamily, fontWeight: FontWeight.w600, height: 1.0),
                        ),
                        Text(
                          'Subscribe to my programs now !',
                          style: AppTypography.poppinsMedium14(
                              color: UIColors.primaryGreen950),
                        ),
                      ],
                    ),
                  ),
                  _buildChatOption(),
                ],
              ),
            ),
    );
  }

  _buildChatOption() {
    return IconButton(
        onPressed: () {
          Log.debug('show consultation message');
          _showCoachConsultationAlert();
        },
        icon: CircleAvatar(
          radius: Values.v25,
          backgroundColor: UIColors.primaryGreen900,
          child: Image.asset(
            Assets.messageIconNew,
            color: UIColors.white,
            height: Values.v30,
            width: Values.v30,
          ),
        ));
  }

  _buildCoachProfileImage() {
    if (_coachEntity!.media == null || _coachEntity!.media!.isEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(Values.v100),
        child: SizedBox(
          height: Values.v64,
          width: Values.v64,
          child: Image.asset(
            Assets.spotMeNoImage,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    if (_coachEntity!.media!.first.url == 'string') {
      return ClipRRect(
        borderRadius: BorderRadius.circular(Values.v100),
        child: SizedBox(
          height: Values.v64,
          width: Values.v64,
          child: Image.asset(
            Assets.spotMeNoImage,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    return ImageContainer.circularImage(
      image: _coachEntity!.media!.first.url,
      radius: Values.v32,
      showBorder: false,
    );
  }

  LinearGradient _buildLinearGradientGreenColor() {
    return const LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        UIColors.primaryGreenGradientStart,
        UIColors.primaryGreenGradientEnd,
      ],
    );
  }

  void _showCoachConsultationAlert() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(Values.v10))),
          contentPadding: const EdgeInsets.all(1),
          // titlePadding: EdgeInsets.all(1),
          // actionsPadding: EdgeInsets.all(1),
          // buttonPadding: EdgeInsets.all(1),
          insetPadding: const EdgeInsets.all(20),
          content: CoachChatColsultationWidget(
            coachEntity: _coachEntity!,
          ),
        );
      },
      barrierDismissible: false,
    );
  }
}
