import 'package:camera/camera.dart';
import 'package:fitsomnia_app/src/core/services/food_scan_camera/ui/food_camera_page.dart';
import 'package:flutter/material.dart';

import 'ui/camera_page.dart';

class AppCameraService {
  AppCameraService._(this.cameras);

  List<CameraDescription>? cameras;
  static AppCameraService? _instance;
  late CameraController _cameraController;

  CameraController selectNewCamera(
      [CameraLensDirection direction = CameraLensDirection.front]) {
    CameraDescription cameraDescription = cameras!.last;
    cameraDescription =
        direction == CameraLensDirection.back ? cameras![0] : cameras![1];

    _cameraController = CameraController(
      cameraDescription,
      ResolutionPreset.max,
      imageFormatGroup: ImageFormatGroup.bgra8888,
    );

    return _cameraController;
  }

  CameraController get cameraController => _cameraController;

  static AppCameraService get instance => _instance!;

  static init() async {
    List<CameraDescription> cameras = await availableCameras();
    _instance = AppCameraService._(cameras);
  }

  Future<XFile?> openCameraView(
    BuildContext context, {
    int maxRecordingTimeInSecond = 0,
    Function? onMediaIconTap,
    bool showMediaIcon = true,
  }) async {
    return await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CameraServiceView(
          maxRecordingTimeInSecond: maxRecordingTimeInSecond,
          onMediaIconTap: onMediaIconTap,
          showMediaIcon: showMediaIcon,
        ),
      ),
    );
  }

  Future<XFile?> openCameraRecordingView(
    BuildContext context, {
    int maxRecordingTimeInSecond = 20,
  }) async {
    return await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CameraServiceView(
          maxRecordingTimeInSecond: maxRecordingTimeInSecond,
          onlyRecord: true,
        ),
      ),
    );
  }
}
