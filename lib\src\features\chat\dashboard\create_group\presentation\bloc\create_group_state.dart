part of 'create_group_bloc.dart';

class CreateGroupState extends Equatable {
  @override
  List<Object?> get props => [];
}

class CreateGroupInitial extends CreateGroupState {}

class CreateGroupLoading extends CreateGroupState {}

class CreateGroupSuccess extends CreateGroupState {
  final GroupResponseEntity groupResponseEntity;

  CreateGroupSuccess({required this.groupResponseEntity});

  @override
  List<Object?> get props => [groupResponseEntity];
}

class CreateGroupFailure extends CreateGroupState {
  final String errorMessage;

  CreateGroupFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}
