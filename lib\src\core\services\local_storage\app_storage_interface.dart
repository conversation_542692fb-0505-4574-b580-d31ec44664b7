import 'package:fitsomnia_app/src/features/home/<USER>/domain/entities/feed_entity.dart';

abstract class AppStorageI {
  Future<void> setOnBoardingStatus(String status);
  Future<String?> getBoardingStatus();
  Future<void> storeBearerToken(String token);
  Future<String?> retrieveBearerToken();
  Future<void> storeFcmToken(String token);
  Future<String?> retrieveFcmToken();
  Future<void> storeCredentials(Map<String, dynamic> credentials);
  Future<Map<String, dynamic>?> retrieveCredentials();
  Future<void> clearCredentials();
  Future<void> clear();
  Future<void> clearSecureStorageOnReinstall();

  /// News Feed
  Future<void> storeNewsFeed(List<String> jsonData);
  Future<List<FeedEntity>?> retrieveNewsFeed();

  //spot alert
  Future<bool> isSpotProfileExist();
  Future<void> setSpotProfileExistStatus(bool status);
  Future<String?> getSpotProfileCreateAlertShowTime();
  Future<void> setSpotProfileCreateAlertShowTime();

  /// referral points
  Future<void> setHowToWinPointAlertShowStatus(bool status);
  Future<bool> getHowToWinPointAlertShowStatus();

  Future<void> setWelcomeToFitsomniaAlertShowStatus(bool status);
  Future<bool> getWelcomeToFitsomniaAlertShowStatus();

}
