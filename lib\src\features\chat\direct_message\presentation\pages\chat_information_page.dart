import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';

class ChatInformationPage extends StatelessWidget {
  const ChatInformationPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const SizedBox(height: 20),
            _buildAvatar(),
            const SizedBox(height: 10),
            _buildName(),
            const SizedBox(height: 5),
            _buildSubTitle(),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildInfoCard("Fit Buddy"),
                  const SizedBox(width: 20),
                  _buildInfoCard("Message"),
                ],
              ),
            ),
            const SizedBox(height: 40),

            /// Actions
            _buildActionCard("View Profile", null),
            _buildActionCard("Delete Chat", null),
            _buildActionCard("Block", null),
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: AppColors.white,
      iconTheme: const IconThemeData(color: AppColors.black),
    );
  }

  Stack _buildAvatar() {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        CircleAvatar(
          radius: Values.v50,
          backgroundColor: AppColors.black10,
        ),
        Positioned(
          right: Values.v10,
          bottom: 7,
          child: Container(
            height: Values.v10,
            width: Values.v10,
            decoration: BoxDecoration(
              color: AppColors.apple,
              shape: BoxShape.circle,
            ),
          ),
        ),
      ],
    );
  }

  Text _buildName() {
    return const Text(
      "Azfar Inan",
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: AppColors.black,
      ),
    );
  }

  Text _buildSubTitle() {
    return const Text(
      "Trainer, Planet Fitness Gym",
      style: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w600,
        color: AppColors.black54,
      ),
    );
  }

  Expanded _buildInfoCard(String title) {
    return Expanded(
      child: Card(
        elevation: 0,
        color: AppColors.apple,
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Center(
            child: Text(
              title,
              style: const TextStyle(
                color: AppColors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionCard(String title, void Function()? onTap) {
    const double tempValue = 0.15;

    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: Values.v10,
        shadowColor: AppColors.apple.withOpacity(tempValue),
        margin: const EdgeInsets.only(bottom: 10),
        child: ListTile(
          visualDensity: VisualDensity.compact,
          title: Text(title),
          trailing: const Icon(
            Icons.arrow_forward_ios,
            size: 16,
          ),
        ),
      ),
    );
  }
}
