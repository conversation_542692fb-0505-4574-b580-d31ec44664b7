import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/entity/chat_history_entity.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/use_cases/get_chat_history_use_case.dart';

part 'chat_history_event.dart';
part 'chat_history_state.dart';

class ChatHistoryBloc extends Bloc<ChatHistoryEvent, ChatHistoryState> {
  ChatHistoryBloc({required this.getChatHistoryUseCase})
      : super(ChatHistoryInitial()) {
    on<GetChatHistoryEvent>(_onGetChatHistoryEvent);
    on<UpdateChatHistoryEvent>(_onUpdateChatHistoryEvent);
    on<AddChatHistoryEvent>(_onAddChatHistoryEvent);
    on<RemoveFromChatHistoryListEvent>(_onRemoveFromChatHistoryListEvent);
    on<GetMoreChatHistoryEvent>(_onGetMoreChatHistoryEvent);
  }

  late GetChatHistoryUseCase getChatHistoryUseCase;
  List<ChatHistoryEntity> conversationList = [];
  bool hasReachedMaximum = false;

  Future<void> _onGetChatHistoryEvent(
    GetChatHistoryEvent event,
    Emitter<ChatHistoryState> emit,
  ) async {
    emit(ChatHistoryLoading());

    try {
      final response = await getChatHistoryUseCase.call(
        userId: event.userId,
        offset: event.offset,
        limit: event.limit,
      );

      if (event.offset == 0) {
        conversationList.clear();
        hasReachedMaximum = false;
      }

      response.fold(
        (l) => emit(
          ChatHistoryFailure(
            errorMessage: l.toString(),
          ),
        ),
        (r) => emit(ChatHistorySuccess(chatHistoryEntity: r)),
      );
    } catch (_) {
      emit(
        ChatHistoryFailure(
          errorMessage: TextConstants.failedToLoadData,
        ),
      );
    }
  }

  Future<void> _onGetMoreChatHistoryEvent(
    GetMoreChatHistoryEvent event,
    Emitter<ChatHistoryState> emit,
  ) async {
    if (!hasReachedMaximum) {
      try {
        final response = await getChatHistoryUseCase.call(
          userId: event.userId,
          offset: event.offset,
          limit: event.limit,
        );

        response.fold(
          (l) => Log.error("Error Loading Data"),
          (r) {
            if (r.isEmpty) {
              hasReachedMaximum = true;
            }

            emit(ChatHistorySuccess(chatHistoryEntity: r));
          },
        );
      } catch (_) {
        emit(
          ChatHistoryFailure(
            errorMessage: TextConstants.failedToLoadData,
          ),
        );
      }
    }
  }

  Future<void> _onUpdateChatHistoryEvent(
    UpdateChatHistoryEvent event,
    Emitter<ChatHistoryState> emit,
  ) async {
    conversationList.insertAll(0, event.chatHistoryEntities);
  }

  Future<void> _onAddChatHistoryEvent(
    AddChatHistoryEvent event,
    Emitter<ChatHistoryState> emit,
  ) async {
    conversationList.add(event.chatHistoryEntity);
  }

  Future<void> _onRemoveFromChatHistoryListEvent(
    RemoveFromChatHistoryListEvent event,
    Emitter<ChatHistoryState> emit,
  ) async {
    int index =
        conversationList.indexWhere((element) => element.id == event.messageId);
    conversationList.removeAt(index);
  }
}
