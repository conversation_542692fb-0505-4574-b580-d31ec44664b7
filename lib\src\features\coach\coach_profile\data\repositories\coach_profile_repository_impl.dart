import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/exception/network_exception.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/data_source/coach_profile_data_source.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/repositories/coach_profile_repository.dart';

class CoachProfileRepositoryImpl extends CoachProfileRepository {
  final CoachProfileDataSource dataSource;

  CoachProfileRepositoryImpl({required this.dataSource});

  @override
  Future<Either<ErrorModel, CoachEntity>> createCoachProfile(
      {required CoachEntity registrationEntity}) async {
    try {
      final response = await dataSource.createCoachProfile(
          coachProfile: registrationEntity.toModel().toJson());
      final data = response.data['data'];
      CoachEntity coachProfile = CoachModel.fromJson(data) as CoachEntity;

      return Right(coachProfile);
    } catch (e, stackTrace) {
      Log.info(e.toString());
      Log.info(stackTrace.toString());

      return Left((e is NetworkExceptionV2)
          ? e.errorModel.error!
          : ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, CoachEntity>> updateCoachProfile(
      {required String coachId, required CoachEntity coachEntity}) async {
    try {
      final response = await dataSource.updateCoachProfile(
        coachId: coachId,
        coachProfile: coachEntity.toModel().toJson(),
      );
      final data = response.data['data'];
      CoachEntity coachProfile = CoachModel.fromJson(data) as CoachEntity;

      return Right(coachProfile);
    } catch (e, stackTrace) {
      Log.info(e.toString());
      Log.info(stackTrace.toString());

      return Left((e is NetworkExceptionV2)
          ? e.errorModel.error!
          : ErrorModel(message: e.toString()));
    }
  }
}
