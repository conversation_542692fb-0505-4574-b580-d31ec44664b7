import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class UserAvatar extends StatelessWidget {
  const UserAvatar({
    required this.image,
    this.isActive,
    Key? key,
  }) : super(key: key);

  final String image;
  final bool? isActive;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: Values.v52.r,
          height: Values.v52.r,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(
              Values.v100.r,
            ),
          ),
          child: ImageContainer.circularImage(
            image: image,
            radius: Values.v26,
          ),
        ),
        isActive != null
            ? Positioned(
                bottom: 0,
                right: Values.v3.h,
                child: CircleAvatar(
                  backgroundColor:
                      isActive! ? AppColors.primaryGreen : AppColors.softYellow,
                  radius: Values.v5.r,
                ),
              )
            : const SizedBox.shrink(),
      ],
    );
  }
}
