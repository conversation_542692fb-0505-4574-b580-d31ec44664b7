import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/domain/entities/daily_task.dart';

abstract class DailyTaskRepository {
  Future<Either<ErrorModel, List<DailyTask>>> getDailyTasks(
      {required int? offset,
      required int? limit,
      required String? taskTypeFilter,
      required String? taskStatusFilter});
}
