import 'package:camera/camera.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'camera_event.dart';
part 'camera_state.dart';

class CameraBloc extends Bloc<CameraEvent, CameraState> {
  CameraBloc({this.maxRecordingTimeInSecond = 0, this.onMediaIconTap})
      : super(const CameraState(null)) {
    on<ChangeCameraEvent>(_onChangeCameraEvent);
  }
  final int maxRecordingTimeInSecond;
  final Function? onMediaIconTap;

  void _onChangeCameraEvent(
    ChangeCameraEvent event,
    Emitter<CameraState> emit,
  ) {
    emit(
      CameraState(event.cameraController),
    );
  }
}
