library globals;

import 'package:flutter/material.dart';

final ValueNotifier<int> notificationCount = ValueNotifier<int>(0);
final ValueNotifier<int> chattingCount = ValueNotifier<int>(0);
final ValueNotifier<bool> errorScreenActive = ValueNotifier<bool>(false);
final ValueNotifier<bool> noInternetScreenActive = ValueNotifier<bool>(false);
final ValueNotifier<bool> showPermissionAlert = ValueNotifier<bool>(false);
final ValueNotifier<bool> socketAlwaysActive = ValueNotifier<bool>(false);
final ValueNotifier<bool> showGlobalLoader = ValueNotifier<bool>(false);
final ValueNotifier<bool> chatScreenActive = ValueNotifier<bool>(false);
