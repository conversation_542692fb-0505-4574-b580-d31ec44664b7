class UserReviewEntity {
  final String reviewId;
  final String userId;
  final String userName;
  final String userImage;
  final String coachId;
  final double rating;
  final String comment;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? programId;

  UserReviewEntity({
    required this.reviewId,
    required this.userId,
    required this.userImage,
    required this.coachId,
    required this.rating,
    required this.comment,
    required this.createdAt,
    required this.updatedAt,
    required this.userName,
    this.programId,
  });
}

class UserReviewRequestEntity {
  final double rating;
  final String comment;

  UserReviewRequestEntity({required this.rating, required this.comment});

  Map<String, dynamic> toJson() {
    return {'rating': rating, 'comment': comment};
  }
}

final ratingSample = UserReviewEntity(
  reviewId: 'review-id',
  userId: 'user-id',
  userName: 'Mahmud<PERSON>',
  userImage:
      "https://dev-public-cdn.fitsomnia.com/original/profile/2024/3/22/4f5ac2c0-a536-4e07-b3c7-7e93244d1554/138453_171113494179237998895320652217971482273516284040001133459n.jpg",
  coachId: 'coachId',
  rating: 5.0,
  comment:
      'Strength is the product of struggle and you must do what others don\'t to achieve what others won\'t. Strength is the product of struggle and you must do what others don\'t to achieve what others won\'t. Strength is the product of struggle and you must do what others don\'t to achieve what others won\'t.',
  createdAt: DateTime.now(),
  updatedAt: DateTime.now(),
);
