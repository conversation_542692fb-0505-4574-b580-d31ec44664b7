class GroupEntity {
  GroupEntity({
    required this.name,
    required this.description,
    required this.image,
    required this.members,
  });

  final String name;
  final String? description;
  final String? image;
  final List<Member> members;

  factory GroupEntity.fromJson(Map<String, dynamic> json) => GroupEntity(
    name: json["name"],
    description: json["description"] ?? "",
    image: json["image"] ?? "",
    members: List<Member>.from(json["members"].map((x) => Member.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "name": name ,
    "description": description ?? "",
    "image": image ?? "",
    "members": List<dynamic>.from(members.map((x) => x.toJson())),
  };
}

class Member {
  Member({
    required this.userId,
  });

  final String userId;

  factory Member.fromJson(Map<String, dynamic> json) => Member(
    userId: json["userId"],
  );

  Map<String, dynamic> toJson() => {
    "userId": userId,
  };
}