part of '../../../verification/presentation/bloc/verification_bloc.dart';

class VerificationState extends Equatable {
  const VerificationState();

  @override
  // TODO: implement props
  List<Object?> get props => [];
}

class VerificationInitial extends VerificationState {}

class VerificationLoadingState extends VerificationState {}

class VerificationErrorState extends VerificationState {
  const VerificationErrorState({required this.message});

  final String message;
}

class VerificationSuccessState extends VerificationState {}

class VerificationTimeCountDownState extends VerificationState {
  const VerificationTimeCountDownState({required this.duration});

  final int duration;
}
