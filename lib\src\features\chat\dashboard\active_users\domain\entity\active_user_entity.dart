import 'package:equatable/equatable.dart';

class ActiveUserEntity extends Equatable {
  const ActiveUserEntity({
    required this.userInfo,
  });

  final UserInfo? userInfo;

  String getActiveUsersImage() {
    return userInfo!.image != null
        ? userInfo!.image!.profile != null
            ? userInfo!.image!.profile!
            : ""
        : "";
  }

  @override
  List<Object?> get props => [userInfo];
}

class UserInfo {
  UserInfo({
    required this.id,
    required this.name,
    required this.image,
  });

  final String? id;
  final String? name;
  final ActiveUserImage? image;

  factory UserInfo.fromJson(Map<String, dynamic> json) => UserInfo(
        id: json["id"],
        name: json["name"],
        image: json["image"] != null
            ? ActiveUserImage.fromJson(json["image"])
            : null,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image": image != null ? image!.toJson() : null,
      };
}

class ActiveUserImage {
  ActiveUserImage({
    required this.profile,
  });

  final String? profile;

  factory ActiveUserImage.fromJson(Map<String, dynamic> json) =>
      ActiveUserImage(
        profile: json["profile"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "profile": profile,
      };
}
