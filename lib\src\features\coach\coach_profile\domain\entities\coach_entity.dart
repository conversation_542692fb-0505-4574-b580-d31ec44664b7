import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';

class CoachEntity {
  final String? coachId;
  final String? userId;
  final String name;
  final String profileName;
  final String coachCategoryId;
  final String? coachCategoryName;
  final String expertise;
  final String highlights;
  final String skillLevel;
  final String about;
  final String experienceInYear;
  final List<String> achievements;
  List<CoachMediaFile>? media;
  List<CoachMediaFile>? credentials;
  List<CoachMediaFile>? identifications;
  String? reviewStatus;
  String? reviewComment;
  int? subscriptionCount;
  double? currentRating;
  int? reviewCount;
  bool? isStarCoach;
  String? userEmail;


  CoachEntity({
    required this.coachId,
    required this.userId,
    required this.name,
    required this.expertise,
    required this.highlights,
    required this.skillLevel,
    required this.about,
    required this.experienceInYear,
    required this.achievements,
    required this.media,
    required this.credentials,
    required this.identifications,
    required this.reviewStatus,
    required this.reviewComment,
    required this.profileName,
    required this.coachCategoryId,
    this.reviewCount,
    this.currentRating,
    this.coachCategoryName,
    this.subscriptionCount,
    this.isStarCoach,
    this.userEmail,
  });

  CoachModel toModel() {
    return CoachModel(
      name: name,
      expertise: expertise,
      highlights: highlights,
      skillLevel: skillLevel,
      about: about,
      experienceInYear: experienceInYear,
      achievements: achievements,
      media: media,
      credentials: credentials,
      identifications: identifications,
      profileName: profileName,
      coachCategoryId: coachCategoryId,
      coachCategoryName: coachCategoryName,
      userEmail: userEmail,
    );
  }
}

final testCoachProfileEntity = CoachEntity(
  name: 'Mahmudul Hasan',
  // categories: [
  //   CoachCategory(
  //       categoryId: '31c52628-3a6c-4f3e-a43d-4d3da43b9212',
  //       subCategoryId: '1eff1b45-de7a-44bb-aaf1-7d4979514aec')
  // ],
  profileName: '@mahmudul',
  coachCategoryId: '31c52628-3a6c-4f3e-a43d-4d3da43b9212',
  coachCategoryName: 'Boller',
  expertise: 'Bolling',
  highlights: 'Bolling pro',
  skillLevel: 'Beginner',
  about: 'coach about',
  experienceInYear: '5',
  achievements: ['Mr batsman 2023'],
  media: [
    CoachMediaFile(
      url:
          "https://dev-public-cdn.fitsomnia.com/original/coach/2025/1/28/6293a7cb-9788-4cc1-9cf3-12eb56ff687e/938925_1738071637314B9A405711.jpg",
      mediaType: 'image',
    ),
    CoachMediaFile(
      url:
          "https://dev-public-cdn.fitsomnia.com/original/coach/2025/1/28/6293a7cb-9788-4cc1-9cf3-12eb56ff687e/938925_1738071637314B9A405711.jpg",
      mediaType: 'image',
    ),
    CoachMediaFile(
      url:
          "https://dev-public-cdn.fitsomnia.com/original/coach/2025/1/28/6293a7cb-9788-4cc1-9cf3-12eb56ff687e/938925_1738071637314B9A405711.jpg",
      mediaType: 'image',
    ),
  ],
  credentials: [
    CoachMediaFile(
        url:
            "https://dev-public-cdn.fitsomnia.com/original/coach/2025/1/28/6293a7cb-9788-4cc1-9cf3-12eb56ff687e/938925_1738071637314B9A405711.jpg",
        mediaType: 'image')
  ],
  identifications: [
    CoachMediaFile(
        url:
            "https://dev-public-cdn.fitsomnia.com/original/coach/2025/1/28/6293a7cb-9788-4cc1-9cf3-12eb56ff687e/938925_1738071637314B9A405711.jpg",
        mediaType: 'image')
  ],
  coachId: '0c29b89b-58c8-4bcc-9873-c98316b298df',
  userId: '60ed012b-ac63-4e3e-bc17-9575200d8251',
  reviewStatus: 'approved',
  reviewComment: 'no comment',
  reviewCount: 10,
  subscriptionCount: 5,
  currentRating: 4,
);

final testCoachPendingProfileEntity = testCoachProfileEntity;
