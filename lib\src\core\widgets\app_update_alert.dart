import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:flutter/material.dart';

class AppUpdateAlert extends StatelessWidget {
  const AppUpdateAlert(
      {super.key,
      required this.appCurrentVersion,
      required this.appLatestVersion});
  final String appCurrentVersion;
  final String appLatestVersion;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Column(
        children: [
          Container(
            // decoration: BoxDecoration(border: Border.all(color: Colors.red)),
            child: Text(
              'Update App?',
              style: AppTypography.poppinsBold20(color: UIColors.black),
            ),
          ),
          SizedBox(height: 10,),
          Text(
            'A new version of Upgrade is available! Version ${appLatestVersion} is available now. You have ${appCurrentVersion}',
            style: AppTypography.poppinsRegular14(color: AppColors.greyscale500),
          ),
          SizedBox(height: 10,),
          Text(
            'Would you like to update it now?',
            style: AppTypography.poppinsRegular14(color: AppColors.greyscale500),
          ),
        ],
      ),
    );
  }
}
