import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/entity/group_list_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/use_case/get_group_list_use_case.dart';

part 'group_list_event.dart';
part 'group_list_state.dart';

class GroupListBloc extends Bloc<GroupListEvent, GroupListState> {
  GroupListBloc({required this.getGroupListUseCase})
      : super(GroupListInitial()) {
    on<GetGroupListEvent>(_onGetGroupListEvent);
    on<UpdateGroupListEvent>(_onUpdateGroupListEvent);
    on<AddGroupToListEvent>(_onAddGroupToListEvent);
    on<RemoveGroupFromListEvent>(_onRemoveGroupFromListEvent);
  }

  late GetGroupListUseCase getGroupListUseCase;
  List<GroupListEntity> groupList = [];

  Future<void> _onGetGroupListEvent(
    GetGroupListEvent event,
    Emitter<GroupListState> emit,
  ) async {
    emit(GroupListLoading());

    try {
      final response = await getGroupListUseCase.call(event.limit);
      // print(response);

      response.fold(
        (l) => emit(
          GroupListFailure(
            errorMessage: l.toString(),
          ),
        ),
        (r) => emit(GroupListSuccess(groupList: r)),
      );
    } catch (_) {
      emit(
        GroupListFailure(
          errorMessage: TextConstants.failedToLoadData,
        ),
      );
    }
  }

  Future<void> _onUpdateGroupListEvent(
    UpdateGroupListEvent event,
    Emitter<GroupListState> emit,
  ) async {
    groupList = event.groupListEntities;
  }

  Future<void> _onAddGroupToListEvent(
    AddGroupToListEvent event,
    Emitter<GroupListState> emit,
  ) async {
    groupList.insert(0, event.groupListEntity);
  }

  Future<void> _onRemoveGroupFromListEvent(
    RemoveGroupFromListEvent event,
    Emitter<GroupListState> emit,
  ) async {
    groupList.removeWhere(
        (element) => element.groupId == event.groupListEntity.groupId);
  }
}
