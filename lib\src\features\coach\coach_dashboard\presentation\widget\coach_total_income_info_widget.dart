import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_income_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/bloc/coach_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/story_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http_parser/http_parser.dart';
import 'package:intl/intl.dart';

class CoachTotalIncomeInfoWidget extends StatefulWidget {
  const CoachTotalIncomeInfoWidget({super.key, required this.coachEntity});
  final CoachEntity coachEntity;

  @override
  State<CoachTotalIncomeInfoWidget> createState() =>
      _CoachTotalIncomeInfoWidgetState();
}

class _CoachTotalIncomeInfoWidgetState
    extends State<CoachTotalIncomeInfoWidget> {
  CoachIncomeEntity? coachIncomeEntity;
  bool _isLoading = false;

  @override
  void initState() {
    //TODO call total coach withdraw api
    super.initState();

    BlocProvider.of<CoachDashboardBloc>(context)
        .add(GetCoachIncomeEvent(coachId: widget.coachEntity.coachId!));
    _isLoading = true;
  }

  @override
  Widget build(BuildContext context) {
    return _buildCoachIncomeSection();
  }

  _buildCoachIncomeSection() {
    return BlocListener<CoachDashboardBloc, CoachDashboardState>(
      listener: (context, state) {
        if (state is GetCoachIncomeInfoSuccess) {
          setState(() {
            coachIncomeEntity = state.coachIncomeEntity;
            _isLoading = false;
          });
        }

        if (state is GetCoachIncomeInfoFail) {
          Log.debug('coach incom info failed');
          setState(() {
            _isLoading = false;
          });
        }
      },
      child: (_isLoading || coachIncomeEntity == null)
          ? const SizedBox.shrink()
          : GestureDetector(
              onTap: () {
                // if(coachIncomeEntity!.totalSubscriptions < 1) {
                //   AppToast.showToast(message: 'No subscribtion information');
                  
                //   return;
                // }
                Navigator.of(context).pushNamed(
                    Routes.coachProgramEnrollmentHistoryPage,
                    arguments: [widget.coachEntity]);
              },
              child: _buildCoachIncomeInfo(),
            ),
    );
  }

  _buildCoachPorgramsTotalEnrollmentInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildEnrolledStudentImageStack(),
        Text(
          'Total Enrollment: ${coachIncomeEntity!.totalSubscriptions}',
          style: AppTypography.poppinsMedium20(color: UIColors.primaryGreen950),
        ),
        Text(
          'You have new ${coachIncomeEntity!.lastWeekSubscriptionCount} enrollments this week',
          style: AppTypography.poppinsMedium14(color: AppColors.greyscale400),
        ),
      ],
    );
  }

  _buildEnrolledStudentImageStack() {
    return SizedBox.shrink();
  }

  _buildCoachIncomeInfo() {
    return Container(
      margin: EdgeInsets.only(top: Values.v20, bottom: Values.v20),
      width: double.infinity,
      padding:
          EdgeInsets.symmetric(vertical: Values.v20, horizontal: Values.v16),
      decoration: BoxDecoration(
          color: UIColors.orange50,
          border: Border.all(color: UIColors.orange400),
          borderRadius: BorderRadius.circular(10)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'My Income',
            style:
                AppTypography.poppinsMedium20(color: UIColors.primaryGreen950),
          ),
          Text(
              'Last Income: ${(coachIncomeEntity!.lastIncomeDate != null) ? DateFormat.yMMMMd('en_US').format(coachIncomeEntity!.lastIncomeDate!) : ''}',
              style: AppTypography.poppinsMedium14(
                  color: AppColors.greyscale400)),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Text(
              'BDT ${coachIncomeEntity!.currentAccountBalance.toStringAsFixed(0)}',
              style: AppTypography.poppinsSemiBold32(
                  color: UIColors.primaryGreen950),
            ),
          ),
          _buildCoachPorgramsTotalEnrollmentInfoSection(),
        ],
      ),
    );
  }
}
