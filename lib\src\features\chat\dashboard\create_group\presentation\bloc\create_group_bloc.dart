import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/domain/entity/group_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/domain/entity/group_response_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/domain/use_case/create_group_use_case.dart';

part 'create_group_event.dart';
part 'create_group_state.dart';

class CreateGroupBloc extends Bloc<CreateGroupEvent, CreateGroupState> {
  CreateGroupBloc({required this.createGroupUseCase})
      : super(CreateGroupInitial()) {
    on<CreateGroupChatEvent>(_onCreateGroupChatEvent);
  }

  late CreateGroupUseCase createGroupUseCase;

  Future<void> _onCreateGroupChatEvent(
    CreateGroupChatEvent event,
    Emitter<CreateGroupState> emit,
  ) async {
    emit(CreateGroupLoading());

    try {
      List<Member> members = [];

      for (var element in event.listOfUserId) {
        members.add(Member(userId: element));
      }

      GroupEntity groupEntity = GroupEntity(
        name: event.groupName,
        description: "",
        image: "",
        members: members,
      );

      final response = await createGroupUseCase.call(
        mapData: groupEntity.toJson(),
      );

      response.fold(
        (l) => emit(
          CreateGroupFailure(
            errorMessage: l.toString(),
          ),
        ),
        (r) => emit(CreateGroupSuccess(groupResponseEntity: r)),
      );
    } catch (_) {
      emit(
        CreateGroupFailure(
          errorMessage: TextConstants.failedToCreateData,
        ),
      );
    }
  }
}
