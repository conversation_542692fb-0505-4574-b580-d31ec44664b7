import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/model/coach_program_model.dart';

class CoachProgramEntity {
  final String? programId;
  final String? coachId;
  final String title;
  final String desc;
  final String categoryId;
  final String subCategoryId;
  final CoachProgramPaymentInfo? oneTimePrice;
  final CoachProgramPaymentInfo? dailyPrice;
  final CoachProgramPaymentInfo? weeklyPrice;
  final CoachProgramPaymentInfo? monthlyPrice;
  final List<CoachProgramFeature> features;
  final List<String> guarantees;
  final int? totalSubscription;
  final int totalIncome;
  final bool isBestProgram;
  final bool? hasSubscription;
  final String durationType;
  final int durationCount;

  CoachProgramEntity({
    required this.programId,
    required this.coachId,
    required this.title,
    required this.desc,
    required this.features,
    required this.guarantees,
    required this.totalSubscription,
    required this.totalIncome,
    required this.isBestProgram,
    required this.categoryId,
    required this.subCategoryId,
    this.oneTimePrice,
    this.dailyPrice,
    this.weeklyPrice,
    this.monthlyPrice,
    this.hasSubscription,
    required this.durationType,
    required this.durationCount,
  });

  List<CoachProgramPaymentInfo> get payments {
    List<CoachProgramPaymentInfo> paymetntOptions = [];

    if (oneTimePrice != null) {
      oneTimePrice!.paymentTerm = CoachProgramSubscriptionPaymentTerm.ONE_TIME.key();
      paymetntOptions.add(oneTimePrice!);
    }
    // if (dailyPrice != null) {
    //   dailyPrice!.paymentTerm = CoachProgramSubscriptionPaymentTerm.DAILY.key();
    //   paymetntOptions.add(dailyPrice!);
    // }
    // if(weeklyPrice != null) {
    //   weeklyPrice!.paymentTerm = CoachProgramSubscriptionPaymentTerm.WEEKLY.key();
    //   paymetntOptions.add(weeklyPrice!);
    // }
    if (monthlyPrice != null) {
      monthlyPrice!.paymentTerm = CoachProgramSubscriptionPaymentTerm.MONTHLY.key();
      paymetntOptions.add(monthlyPrice!);
    }

    return paymetntOptions;
  }
}

class CoachProgramFeature {
  String featureTitle;
  String featureDescription;

  CoachProgramFeature({
    required this.featureTitle,
    required this.featureDescription,
  });

  factory CoachProgramFeature.fromJson(Map<String, dynamic> json) {
    return CoachProgramFeature(
      featureTitle: json['title'],
      featureDescription: json['description'],
    );
  }

  Map<String, dynamic> toJson() => {
        'title': featureTitle,
        'description': featureDescription,
      };
}

final testCoachProgram = CoachProgramEntity(
  programId: 'program-id',
  coachId: 'coach-id',
  title: 'Lose 10 Kgs In 30 Days Without Changing Your Diet',
  desc: 'program description',
  categoryId: '',
  subCategoryId: '',
  features: [
    CoachProgramFeature(
        featureTitle: 'feature 01',
        featureDescription: 'feature 01 description'),
    CoachProgramFeature(
        featureTitle: 'feature 02',
        featureDescription: 'feature 02 description')
  ],
  guarantees: ['guarantees sample 01'],
  totalSubscription: 210,
  totalIncome: 30000,
  isBestProgram: false,
  durationType: 'weeks',
  durationCount: 10,
  // categories: [CoachCategory(categoryId: 'category-id', subCategoryId: 'sub-category-id')],
);
