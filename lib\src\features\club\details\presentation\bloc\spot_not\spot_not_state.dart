part of 'spot_not_cubit.dart';

enum SpotNotStatus {
  initial,
  loading,
  success,
  error,
}

enum SpotRequestStatus {
  SENT,
  ACCEPTED,
  CANCELLED,
  REJECTED,
  REMOVED,
}

class SpotNotState<T> extends Equatable {
  const SpotNotState({
    this.id,
    this.status = SpotNotStatus.initial,
    this.data,
    this.spotRequestStatus,
    this.error,
  });

  final String? id;
  final SpotNotStatus status;
  final T? data;
  final SpotRequestStatus? spotRequestStatus;
  final String? error;

  SpotNotState copyWith({
    String? id,
    SpotNotStatus? status,
    T? data,
    SpotRequestStatus? spotRequestStatus,
    String? error,
  }) {
    return SpotNotState(
      id: id ?? this.id,
      status: status ?? this.status,
      data: data ?? this.data,
      spotRequestStatus: spotRequestStatus ?? this.spotRequestStatus,
      error: error ?? this.error,
    );
  }

  @override
  String toString() =>
      'SpotNotSuccessState(id: $id, stats: $status, data: $data, spotRequestStatus: $spotRequestStatus, error: $error)';

  @override
  List<Object?> get props => [
        id,
        status,
        data,
        spotRequestStatus,
        error,
      ];
}
