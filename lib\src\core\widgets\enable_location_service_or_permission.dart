import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/services/location_service/location_service.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

class EnableLocationServiceOrPermission extends StatelessWidget {
  const EnableLocationServiceOrPermission({
    Key? key,
    required this.message,
    required this.locationService,
    required this.onEnabled,
  }) : super(key: key);

  final String message;
  final LocationService locationService;
  final Function? onEnabled;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.grey50,
          width: 1,
        ),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 10,
      ),
      child: RichText(
        text: TextSpan(
          text: message,
          style: const TextStyle(
            color: AppColors.grey,
            fontSize: 14,
          ),
          children: [
            TextSpan(
              text: ' Click Here',
              style: TextStyle(
                color: AppColors.primaryGreen,
                fontSize: 14,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () async {
                  await locationService.requestPermission();
                  await locationService.requestService();
                  if (locationService.isServiceEnabled &&
                      locationService.isGranted) {
                    if (onEnabled != null) onEnabled!();
                  }
                },
            ),
          ],
        ),
      ),
    );
  }
}
