import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/entities/user.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/repositories/auth_repository.dart';

class LoginUseCase {
  const LoginUseCase({required this.authRepository});

  final AuthRepository authRepository;

  Future<Either<ErrorModel, User>> call(Map<String, dynamic> map) async {
    return await authRepository.login(map);
  }
}
