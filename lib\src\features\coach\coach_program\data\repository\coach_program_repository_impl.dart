import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/exception/network_exception.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/data/model/coach_program_enrollment_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/program_subscription_fee_payment_info.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/data_source/coach_program_data_source.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/model/coach_program_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/model/program_subscription_fee_payment_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/repositories/coach_program_repository.dart';

class CoachProgramRepositoryImpl extends CoachProgramRepository {
  final CoachProgramDataSource dataSource;

  CoachProgramRepositoryImpl({required this.dataSource});

  @override
  Future<Either<ErrorModel, CoachProgramEntity>> createCoachProgram(
      {required String coachId,
      required CoachProgramEntity programEntity}) async {
    try {
      final response = await dataSource.createCoachProgram(
          coachId: coachId,
          data: CoachProgramModel.fromEntity(programEntity).toJson());
      final data = response.data['data'];
      CoachProgramEntity responseProgramEntity =
          CoachProgramModel.fromJson(data);

      return Right(responseProgramEntity);
    } catch (e, stackTrace) {
      Log.info(e.toString());
      Log.info(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, CoachProgramEntity>> deleteCoachProgramById(
      {required String coachId, required String programId}) async {
    try {
      final response = await dataSource.deleteCoachProgramById(
          coachId: coachId, programId: programId);
      final data = response.data['data'];
      CoachProgramEntity responseProgramEntity =
          CoachProgramModel.fromJson(data);

      return Right(responseProgramEntity);
    } catch (e, stackTrace) {
      Log.info(e.toString());
      Log.info(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, CoachProgramEntity>> getCoachProgramById(
      {required String coachId, required String programId}) async {
    try {
      final response = await dataSource.getCoachProgramById(
          coachId: coachId, programId: programId);
      final data = response.data['data'];
      CoachProgramEntity responseProgramEntity =
          CoachProgramModel.fromJson(data);

      return Right(responseProgramEntity);
    } catch (e, stackTrace) {
      Log.info(e.toString());
      Log.info(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, CoachProgramEntity>> updateCoachProgram({
    required String coachId,
    required String programId,
    required CoachProgramEntity programEntity,
  }) async {
    try {
      final response = await dataSource.updateCoachProgram(
          coachId: coachId,
          programId: programId,
          data: CoachProgramModel.fromEntity(programEntity).toJson());
      final data = response.data['data'];
      CoachProgramEntity responseProgramEntity =
          CoachProgramModel.fromJson(data);

      return Right(responseProgramEntity);
    } catch (e, stackTrace) {
      Log.info(e.toString());
      Log.info(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, ProgramSubscriptionFeePaymentInfo>>
      paySubscriptionFee({
    required String subscriptionId,
    required String paymentTerm,
  }) async {
    try {
      final response = await dataSource.paySubscriptionFee(
        subscriptionId: subscriptionId,
        data: {'paymentTerm': paymentTerm},
      );
      final responseData = response.data['data'];
      ProgramSubscriptionFeePaymentInfo feePaymentResponse =
          ProgramSubscriptionFeePaymentModel.fromJson(responseData);

      return Right(feePaymentResponse);
    } catch (e, stackTrace) {
      Log.info(e.toString());
      Log.info(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, CoachProgramEnrollmentEntity>> subscribeProgram({
    required String coachId,
    required String programId,
  }) async {
    try {
      final response = await dataSource.subscribeProgram(
        coachId: coachId,
        programId: programId,
      );
      final data = response.data['data'];
      CoachProgramEnrollmentEntity programEnrollmentEntity =
          CoachProgramEnrollmentModel.fromJson(data);

      return Right(programEnrollmentEntity);
    } catch (e, stackTrace) {
      Log.info(e.toString());
      Log.info(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }
}
