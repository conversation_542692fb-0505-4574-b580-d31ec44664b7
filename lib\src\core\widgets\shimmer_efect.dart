import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerEffect {
  static circle([double? radius]) {
    return Shimmer.fromColors(
      baseColor: AppColors.grey.withOpacity(Values.v0_5),
      highlightColor: Colors.grey[100]!,
      child: CircleAvatar(
        radius: radius ?? Values.v40.r,
        backgroundColor: AppColors.grey,
      ),
    );
  }

  static rectangle({
    required double width,
    required double height,
    required double radius,
  }) {
    return Shimmer.fromColors(
      baseColor: AppColors.grey.withOpacity(Values.v0_5),
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width.w,
        height: height.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(radius.r),
          color: AppColors.grey,
        ),
      ),
    );
  }

  static text({required String text}) {
    return Shimmer.fromColors(
      baseColor: AppColors.grey.withOpacity(Values.v0_5),
      highlightColor: Colors.grey[100]!,
      child: Text(
        text,
        style: AppTypography.regular16(
          color: AppColors.grey,
        ),
      ),
    );
  }
}
