part of 'daily_task_bloc.dart';

sealed class DailyTaskState extends Equatable {
  const DailyTaskState();
  
  @override
  List<Object> get props => [];
}

final class DailyTaskInitial extends DailyTaskState {}

class DailyTaskLoading extends DailyTaskState {

}

class GetDailyTasksSuccess extends DailyTaskState {
  final List<DailyTask> dailyTasks;

  const GetDailyTasksSuccess({required this.dailyTasks});

  @override
  List<Object> get props => [dailyTasks];
}

class GetDailyTasksFail<T> extends DailyTaskState {
  final T? data;
  const GetDailyTasksFail({required this.data});
}
