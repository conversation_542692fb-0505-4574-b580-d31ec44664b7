import 'dart:convert';

import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/services/local_storage/app_storage_interface.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/data/models/feed_model.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/domain/entities/feed_entity.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'shared_preference.dart';

class AppStorageImp extends AppStorageI {
  final storage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
  );

  static const String _keyBearerToken = 'bearer-token';
  static const String _keyFcmToken = 'fcm-token';
  static const String _keyOnBoardingStatus = 'on-boarding-status';
  static const String _keyCredentials = 'credentials';
  static const String _keyNewsFeed = 'news-feed';
  static const String _keyHowToWinPoints = 'how-to-win-points';

  /// keys which should be clear after logout
  final List _keys = [_keyBearerToken, _keyFcmToken, _keyNewsFeed];

  @override
  Future<String?> getBoardingStatus() async {
    return await storage.read(key: _keyOnBoardingStatus);
  }

  @override
  Future<String?> retrieveBearerToken() async {
    return await storage.read(key: _keyBearerToken);
  }

  @override
  Future<String?> retrieveFcmToken() async {
    return await storage.read(key: _keyFcmToken);
  }

  @override
  Future<void> setOnBoardingStatus(String status) async {
    await storage.write(key: _keyOnBoardingStatus, value: status);

    return;
  }

  @override
  Future<void> storeBearerToken(String token) async {
    await storage.write(key: _keyBearerToken, value: token);

    return;
  }

  @override
  Future<void> storeFcmToken(String token) async {
    // Delete existing token if any before storing new one
    String? existingToken = await retrieveFcmToken();
    if (existingToken != null) {
      await _delete(key: _keyFcmToken);
    }
    
    // Store the new token
    await storage.write(key: _keyFcmToken, value: token);

    return;
  }

  
  Future<void> _delete({required String key}) async {
    await storage.delete(key: _keyFcmToken);
    
    return;
  }

  @override
  Future<Map<String, dynamic>?> retrieveCredentials() async {
    String? credentials = await storage.read(key: _keyCredentials);
    if (credentials != null) {
      return json.decode(credentials);
    }

    return null;
  }

  @override
  Future<void> clearCredentials() async {
    await storage.delete(key: _keyCredentials);

    return;
  }

  @override
  Future<void> clear() async {
    for (String key in _keys) {
      await storage.delete(key: key);
    }

    return;
  }

  @override
  Future<void> storeCredentials(Map<String, dynamic> credentials) async {
    storage.write(key: _keyCredentials, value: json.encode(credentials));
  }

  @override
  Future<void> clearSecureStorageOnReinstall() async {
    bool hasRunBefore = await _SharedPreference.hasRunBefore();
    Log.debug('hasRunBefore: $hasRunBefore');

    if (!hasRunBefore) {
      await storage.deleteAll();
      await DefaultCacheManager().emptyCache();
      await _SharedPreference.setHasRunBefore();
    }
  }

  @override
  Future<void> storeNewsFeed(List<String> jsonData) async {
    await _SharedPreference.storeNewsFeed(jsonData);
  }

  @override
  Future<List<FeedEntity>?> retrieveNewsFeed() async {
    return await _SharedPreference.retrieveNewsFeed();
  }
  
  @override
  Future<bool> isSpotProfileExist() async {
    return await _SharedPreference.isSpotProfileExist();
  }
  
  @override
  Future<void> setSpotProfileExistStatus(bool status) async {
    await _SharedPreference.setSpotProfileExistStatus(status);
  }
  
  @override
  Future<String?> getSpotProfileCreateAlertShowTime() async {
    return await _SharedPreference.getSpotProfileAlertLastShowTime();
  }
  
  @override
  Future<void> setSpotProfileCreateAlertShowTime() async {
    await _SharedPreference.setSpotProfileAlertLastShowTime();
  }
  
  @override
  Future<bool> getHowToWinPointAlertShowStatus() async {
    return await _SharedPreference.isHowToWinPointsAlertShown();
  }
  
  @override
  Future<void> setHowToWinPointAlertShowStatus(bool status) async {
    await _SharedPreference.setHowToWinPointsAlertStatus(status);
  }
  
  @override
  Future<bool> getWelcomeToFitsomniaAlertShowStatus() async {
    return await _SharedPreference.isWelcomeToFitsomniaShown();
  }
  
  @override
  Future<void> setWelcomeToFitsomniaAlertShowStatus(bool status) async {
    await _SharedPreference.setWelcomeToFitsomniaAlertStatus(status);
  }
  
}
