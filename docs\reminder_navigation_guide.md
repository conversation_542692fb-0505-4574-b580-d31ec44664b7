# Reminder Notification Dynamic Navigation Guide

## Overview

The Fitsomnia app now supports dynamic navigation for reminder notifications based on the `feature` field from the Reminder API. This replaces the previous hardcoded navigation to the diet dashboard.

## API Structure

The Reminder API now includes a `feature` field that determines which feature page to navigate to:

```json
{
  "title": "Daily Workout Reminder",
  "message": "Time to do your daily workout! Stay fit with Fitsomnia!",
  "time": "08:00",
  "feature": "diet",
  "isActive": true,
  "isRepeating": true,
  "repeatDays": [
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
    "saturday",
    "sunday"
  ]
}
```

## Supported Feature Types

The following feature types are supported:

| Feature Value | Navigation Route | Description |
|---------------|------------------|-------------|
| `rewards` | `Routes.rewardDashboardPage` | Navigate to rewards dashboard |
| `referrals` | `Routes.referralListPage` | Navigate to referrals page |
| `daily-tasks` | `Routes.dailyTaskPage` | Navigate to daily tasks page |
| `challenges` | `Routes.allMonthlyChallenges` | Navigate to challenges page |
| `diet` | `Routes.dietDashboard` | Navigate to diet dashboard (with specific arguments) |
| `leaderboard` | `Routes.leaderboard` | Navigate to leaderboard page |
| `other` | No navigation | No specific navigation action |

## Implementation Details

### 1. Feature Type Enum

```dart
enum ReminderFeatureType {
  rewards('rewards'),
  referrals('referrals'),
  dailyTasks('daily-tasks'),
  challenges('challenges'),
  diet('diet'),
  leaderboard('leaderboard'),
  other('other');
}
```

### 2. Navigation Helper

The `ReminderNavigationHelper` class provides methods to:
- Get the appropriate route for a feature type
- Get the required arguments for specific routes (e.g., diet dashboard)

### 3. Push Notification Handling

When a push notification is tapped (app closed/background):
```dart
// Extract feature from notification data
String? feature = data['feature']?.toString();

// Determine feature type and navigate
ReminderFeatureType featureType = ReminderFeatureType.fromString(feature);
String? route = ReminderNavigationHelper.getRouteForFeature(featureType);
dynamic arguments = ReminderNavigationHelper.getArgumentsForFeature(featureType);

if (route != null) {
  navigatorKey?.currentState?.pushNamed(route, arguments: arguments);
}
```

### 4. In-App Notification Handling

When a notification is tapped from the notification list:
```dart
void _navigateBasedOnFeature(String? feature) {
  ReminderFeatureType featureType = ReminderFeatureType.fromString(feature);
  String? route = ReminderNavigationHelper.getRouteForFeature(featureType);
  dynamic arguments = ReminderNavigationHelper.getArgumentsForFeature(featureType);
  
  if (route != null) {
    Navigator.pushNamed(context, route, arguments: arguments);
  }
}
```

## Usage Examples

### Example 1: Diet Reminder
```json
{
  "title": "Time for your meal!",
  "message": "Don't forget to log your lunch",
  "feature": "diet"
}
```
**Result**: Navigates to `Routes.dietDashboard` with arguments `[false, false]`

### Example 2: Daily Task Reminder
```json
{
  "title": "Complete your daily tasks",
  "message": "You have 3 tasks remaining today",
  "feature": "daily-tasks"
}
```
**Result**: Navigates to `Routes.dailyTaskPage`

### Example 3: Rewards Reminder
```json
{
  "title": "Check your rewards",
  "message": "You have new reward points available",
  "feature": "rewards"
}
```
**Result**: Navigates to `Routes.rewardDashboardPage`

### Example 4: Unknown Feature
```json
{
  "title": "General reminder",
  "message": "This is a general notification",
  "feature": "unknown"
}
```
**Result**: No navigation action (treated as 'other')

## Backward Compatibility

The implementation maintains backward compatibility:
- If the `feature` field is missing or null, it defaults to `other` (no navigation)
- Existing reminder detection logic still works
- Non-reminder notifications continue to work as before

## Testing

Comprehensive tests are available in `test/core/routes/reminder_navigation_test.dart` covering:
- Feature type parsing (case-insensitive)
- Route mapping for all feature types
- Argument handling
- Integration flow testing
- Edge cases (null, empty, unknown values)

## Migration Notes

### For Backend Developers
- Add the `feature` field to reminder notification payloads
- Use the supported feature values listed above
- Ensure the field is included in both push notifications and in-app notification APIs

### For Frontend Developers
- The `Notification` model now includes an optional `feature` field
- Old hardcoded diet navigation has been replaced with dynamic navigation
- No changes needed for non-reminder notifications
