import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/use_cases/coach_profile_use_case.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/widget/coach_profile_file_upload_widget.dart';
import 'package:fitsomnia_app/src/features/coach/root/domain/use_case/coach_use_case.dart';

part 'coach_profile_event.dart';
part 'coach_profile_state.dart';

class CoachProfileBloc
    extends Bloc<CoachProfileEvent, CoachProfileState> {
  CoachProfileBloc({ required this.coachRegistrationUseCase, required this.coachUseCase}) : super(CoachRegistrationInitial()) {
    on<CoachProfileEvent>((event, emit) {
      // TODO: implement event handler
    });
    on<CreateCoachProfileEvent>(_onCreateCoachProfileEvent);
    on<UpdateCoachIdentificationDocLocally>(
        _onUpdateCoachIdentificationDocLocally);
    on<UpdateCoachProfilePictureLocally>(_onUpdateCoachProfilePictureLocally);
    on<UpdateCoachCredentialDocLocally>(_onUpdateCoachCredentialDocLocally);
    on<UpdateCoachProfileEvent>(_onUpdateCoachProfileEvent);
  }

  final CoachUseCase coachUseCase;
  final CoachProfileUseCase coachRegistrationUseCase;

  List<CoachMediaFile> identificationFiles = [];
  List<CoachMediaFile> profilePictures = [];
  List<CoachMediaFile> credentialFiles = [];

  FutureOr<void> _onCreateCoachProfileEvent(
    CreateCoachProfileEvent event,
    Emitter<CoachProfileState> emit,
  ) async {
    try {
      emit(CoachRegistrationLoading());
      
      Log.debug('create coach profile');
      event.coachRegistrationEntity.media = profilePictures;
      event.coachRegistrationEntity.credentials = credentialFiles;
      event.coachRegistrationEntity.identifications = identificationFiles;
      
      final response = await coachRegistrationUseCase.creaeCoachProfile(registrationEntity: event.coachRegistrationEntity);
      response.fold((l){
        emit(CoachRegistrationFail(data: l));
      }, (r){
        emit(CoachRegistrationSuccess(registrationEntity: r));

      });

    } catch (e) {
      Log.info(e.toString());
      emit(CoachRegistrationFail(data: e));
    }
  }

  FutureOr<void> _onUpdateCoachIdentificationDocLocally(
    UpdateCoachIdentificationDocLocally event,
    Emitter<CoachProfileState> emit,
  ) {
    Log.debug('update identification files');
    identificationFiles = event.files;
  }

  FutureOr<void> _onUpdateCoachProfilePictureLocally(
    UpdateCoachProfilePictureLocally event,
    Emitter<CoachProfileState> emit,
  ) {
    Log.debug('update profile pictures');
    profilePictures = event.profilePictures;
  }

  FutureOr<void> _onUpdateCoachCredentialDocLocally(
    UpdateCoachCredentialDocLocally event,
    Emitter<CoachProfileState> emit,
  ) {
    Log.debug('update coach credential files');
    credentialFiles = event.credentials;
  }

  Future<void> _onUpdateCoachProfileEvent(UpdateCoachProfileEvent event, Emitter<CoachProfileState> emit) async {
    try {
      emit(CoachRegistrationLoading());
      
      Log.debug('update coach profile');
      event.coachEntity.media = profilePictures;
      event.coachEntity.credentials = credentialFiles; // do not allow user to update credentials
      event.coachEntity.identifications = []; // do not allow user to update credentials
      
      final response = await coachRegistrationUseCase.updateCoachProfile(coachId: event.coachId, coachEntity: event.coachEntity);
      response.fold((l){
        emit(UpdateCoachProfileFail(data: l));
      }, (r){
        emit(UpdateCoachProfileSuccess(coachEntity: r));

      });

    } catch (e) {
      Log.info(e.toString());
      emit(UpdateCoachProfileFail(data: e));
    }
  }
}
