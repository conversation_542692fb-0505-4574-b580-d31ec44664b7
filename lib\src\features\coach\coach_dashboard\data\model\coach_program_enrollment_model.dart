import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/model/coach_program_model.dart';

class CoachProgramEnrollmentModel extends CoachProgramEnrollmentEntity {
  CoachProgramEnrollmentModel({
    required super.subscriptionId,
    required super.programId,
    required super.programTitle,
    required super.programDurationCount,
    required super.programDurationTerm,
    required super.isPaymentNeeded,
    required super.isCompleted,
    required super.coachId,
    required super.coachUserId,
    required super.userId,
    required super.userName,
    required super.userImage,
    required super.coachName,
    required super.coachImages,
    required super.subscriptionDate,
    required super.description,
    required super.paymentTerm,
    super.cancelStatus,
    super.refundStatus,
    super.userTotalPaid,
    super.isFirstPaymentDone,
    super.nextPaymentDate,
    super.coachReviewId,
    super.programReviewId,
    super.oneTimePrice,
    super.monthlyPrice,
  });

  factory CoachProgramEnrollmentModel.fromJson(Map<String, dynamic> json) {
    return CoachProgramEnrollmentModel(
      subscriptionId: json['id'],
      programId: json['programId'],
      programTitle: json['programName'] ?? '(empty)',
      programDurationCount: json['programDurationCount'] ?? 0,
      programDurationTerm: json['programDurationTerm'],
      isPaymentNeeded: json['isPaymentNeeded'] ?? false,
      isCompleted: json['isCompleted'] ?? false,
      coachId: json['coachId'],
      coachUserId: json['coachUserId'],
      userId: json['userId'],
      userName: json['userName'],
      userImage: (json['userImage'] == null) ? null : json['userImage'],
      coachName: json['coachName'],
      coachImages: (json['coachImage'] == null)
          ? []
          : List<CoachMediaFile>.from(json['coachImage']!
              .map((x) => CoachMediaFile.fromJson(x))), //TODO change in format
      subscriptionDate: (json['subscriptionDate'] == null)
          ? DateTime.now()
          : DateTime.tryParse(json['subscriptionDate']),
      description: '',
      oneTimePrice: (json['oneTimePrice'] == null)
          ? null
          : CoachProgramPaymentInfo.fromJson(json['oneTimePrice']),
      monthlyPrice: (json['monthlyPrice'] == null)
          ? null
          : CoachProgramPaymentInfo.fromJson(json['monthlyPrice']),
      cancelStatus: json['cancelStatus'],
      refundStatus: json['refundStatus'],
      userTotalPaid: json['userTotalPaid'] ?? 0,
      isFirstPaymentDone: json['isFirstPaymentDone'],
      nextPaymentDate: DateTime.tryParse(json['nextPaymentDate']),
      coachReviewId: json['coachRatingId'],
      programReviewId: json['programRatingId'],
      paymentTerm: json['paymentTerm'],
    );
  }
  CoachProgramEnrollmentEntity toEntity() => CoachProgramEnrollmentEntity(
        subscriptionId: subscriptionId,
        programId: programId,
        programTitle: programTitle,
        programDurationCount: programDurationCount,
        programDurationTerm: programDurationTerm,
        isPaymentNeeded: isPaymentNeeded,
        isCompleted: isCompleted,
        coachId: coachId,
        coachUserId: coachUserId,
        userId: userId,
        userName: userName,
        userImage: userImage,
        coachName: coachName,
        coachImages: coachImages,
        subscriptionDate: subscriptionDate,
        description: description,
        cancelStatus: cancelStatus,
        refundStatus: refundStatus,
        userTotalPaid: userTotalPaid,
        isFirstPaymentDone: isFirstPaymentDone,
        nextPaymentDate: nextPaymentDate,
        coachReviewId: coachReviewId,
        programReviewId: programReviewId,
        paymentTerm: paymentTerm,
        oneTimePrice: oneTimePrice,
        monthlyPrice: monthlyPrice,
      );
}

//  "data": [
//     {
//       "id": "string",
//       "programId": "string",
//       "userId": "string",
//       "userName": "string",
//       "userImage": "string",
//       "title": "string",
//       "durationInDays": 0,
//       "needPayment": true,
//       "isCompleted": true
//     }
//   ]
