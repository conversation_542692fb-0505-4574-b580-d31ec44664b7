import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/domain/entity/active_user_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/domain/use_case/active_user_list_use_case.dart';

part 'online_fit_buddy_state.dart';
part 'online_fit_buddy_event.dart';

class OnlineFitBuddyBloc
    extends Bloc<OnlineFitBuddyEvent, OnlineFitBuddyState> {
  OnlineFitBuddyBloc({required this.activeUserListUseCase})
      : super(OnlineFitBuddyInitial()) {
    on<OnlineFitBuddyEvent>(
      _onGetOnlineFitBuddies,
    );
  }

  late final ActiveUserListUseCase activeUserListUseCase;

  Future<void> _onGetOnlineFitBuddies(
    OnlineFitBuddyEvent event,
    Emitter<OnlineFitBuddyState> emit,
  ) async {
    emit(OnlineFitBuddyLoading());

    try {
      final response = await activeUserListUseCase.call(
        limit: event.limit,
        offset: event.offset,
      );

      response.fold(
        (l) => emit(
          OnlineFitBuddyFailure(
            errorMessage: l.toString(),
          ),
        ),
        (r) => emit(OnlineFitBuddySuccess(activeUserEntity: r)),
      );
    } catch (_) {
      emit(
        OnlineFitBuddyFailure(
          errorMessage: TextConstants.failedToLoadData,
        ),
      );
    }
  }
}
