import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/presentation/pages/groups.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/presentation/page/message_to_page.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/presentation/pages/active_users.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/presentation/pages/recent_conversation_list_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

part '../widget/people_search_bar.dart';

class ChatDashboardPage extends StatelessWidget {
  const ChatDashboardPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: _buildAppBar(context),
      body: Column(
        children: const [
          _PeopleSearchBar(),
          ActiveUsers(),
          Groups(),
          Expanded(
            flex: 1,
            child: RecentConversation(),
          ),
        ],
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.white,
      elevation: 0,
      leading: GestureDetector(
        onTap: () => Navigator.pop(context),
        child: Icon(
          Icons.arrow_back_ios,
          color: AppColors.greyDark,
        ),
      ),
      title: const Text(
        TextConstants.chat,
        style: TextStyle(
          color: AppColors.black,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      actions: [
        // _buildNewChatIcon(context),
      ],
    );
  }

  Widget _buildNewChatIcon(context) {
    return GestureDetector(
      onTap: () {
        _navigateToMessageToPage(context);
      },
      child: Container(
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          color: AppColors.apple.withOpacity(Values.v0_25),
          shape: BoxShape.circle,
        ),
        child: Padding(
          padding: const EdgeInsets.all(6.0),
          child: Icon(
            Icons.edit,
            color: AppColors.apple,
            size: Values.v18,
          ),
        ),
      ),
    );
  }

  void _navigateToMessageToPage(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MessageToPage(),
      ),
    );
  }
}
