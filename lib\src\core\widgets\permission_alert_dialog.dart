import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionAlertDialog extends StatelessWidget {
  const PermissionAlertDialog({
    required this.context,
    this.title = 'images, videos and microphone',
  }) : super();

  final BuildContext context;
  final String title;

  @override
  Widget build(BuildContext context) {
    return CupertinoAlertDialog(
      content: Container(
        padding: EdgeInsets.symmetric(vertical: 10.h),
        child: Text(
          "Open Settings, then tap Permissions and turn on $title",
          style: AppTypography.semiBold14(
            color: AppColors.grey,
          ),
          textAlign: TextAlign.center,
        ),
      ),
      actions: [
        CupertinoDialogAction(
          child: const Text(
            "Open Settings",
            style: TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          onPressed: () {
            openAppSettings();
            Navigator.pop(context);
          },
        ),
      ],
    );
  }
}
