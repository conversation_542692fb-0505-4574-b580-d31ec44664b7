import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/exception/network_exception.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/data/data_source/coach_personal_data_source.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/data/model/coach_income_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/data/model/coach_program_enrollment_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_income_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/repositories/coach_personal_repository.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/model/coach_program_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';

class CoachPersonalRepositoryImpl extends CoachPersonalRepository {
  final CoachPersonalDataSource dataSource;

  CoachPersonalRepositoryImpl({required this.dataSource});

  @override
  Future<Either<ErrorModel, CoachEntity>> getCoachOwnProfile(
      {required String coachId}) async {
    try {
      final response = await dataSource.getCoachOwnProfile(coachId: coachId);
      final data = response.data['data'];
      CoachEntity coachEntity = CoachModel.fromJson(data);

      return Right(coachEntity);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, CoachProgramEntity>> getCoachProgram({
    required String coachId,
    required String programId,
  }) async {
    try {
      final response = await dataSource.getCoachProgram(
          coachId: coachId, programId: programId);
      final data = response.data['data'];
      CoachProgramEntity programEntity = CoachProgramModel.fromJson(data);

      return Right(programEntity);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, List<CoachProgramEntity>>> getCoachPrograms(
      {required String coachId}) async {
    try {
      final response = await dataSource.getCoachPrograms(coachId: coachId);
      final data = response.data['data'];
      List<CoachProgramEntity> coachPrograms = List<CoachProgramModel>.from(
          data.map((x) => CoachProgramModel.fromJson(x)));

      return Right(coachPrograms);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, CoachProgramEnrollmentEntity>> getProgramEnroller(
      {required String coachId, required String subscriptionId}) async {
    try {
      // return Right(testProgramEnrollmentEntity);

      final response = await dataSource.getEnrollerById(
          coachId: coachId, subscriptionId: subscriptionId);
      final data = response.data['data'];
      CoachProgramEnrollmentEntity programEnroller =
          CoachProgramEnrollmentModel.fromJson(data).toEntity();

      return Right(programEnroller);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, List<CoachProgramEnrollmentEntity>>>
      getProgramEnrollers({required String coachId}) async {
    try {
      final response = await dataSource.getEnrollers(coachId: coachId);
      final data = response.data['data'];
      List<CoachProgramEnrollmentEntity> programsEnrollers =
          List<CoachProgramEnrollmentModel>.from(
                  data.map((x) => CoachProgramEnrollmentModel.fromJson(x)))
              .map((x) => x.toEntity())
              .toList();

      return Right(programsEnrollers);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, CoachProgramEnrollmentEntity>>
      cancelProgramSubscriptionByCoach({
    required String coachId,
    required String subscriptionId,
    required String cancelReason,
  }) async {
    try {
      final response = await dataSource.cancelProgramSubscriptionByCoach(
        coachId: coachId,
        subscriptionId: subscriptionId,
        data: {'reason': cancelReason},
      );
      final data = response.data['data'];
      CoachProgramEnrollmentEntity cancelResponseEntity =
          CoachProgramEnrollmentModel.fromJson(data);

      return Right(cancelResponseEntity);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, CoachIncomeEntity>> getCoachIncomeInfo(
      {required String coachId}) async {
    try {
      final response = await dataSource.getCoachIncomeAmount(coachId: coachId);
      final data = response.data['data'];
      CoachIncomeEntity coachIncomeEntity = CoachIncomeModel.fromJson(data);

      return Right(coachIncomeEntity);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, List<CoachProgramEnrollmentEntity>>>
      getProgramEnrollerHistory({
    required String coachId,
    required String programId,
  }) async {
    try {
      final response = await dataSource.getProgramEnrollerHistory(coachId: coachId, programId: programId);
      final data = response.data['data'];
      List<CoachProgramEnrollmentEntity> programsEnrollers =
          List<CoachProgramEnrollmentModel>.from(
                  data.map((x) => CoachProgramEnrollmentModel.fromJson(x)))
              .map((x) => x.toEntity())
              .toList();

      return Right(programsEnrollers);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, List<CoachProgramEnrollmentEntity>>>
      getProgramPaymentHistory({
    required String coachId,
    required String programId,
  }) async {
    try {
      final response = await dataSource.getProgramPaymentHistory(coachId: coachId, programId: programId);
      final data = response.data['data'];
      List<CoachProgramEnrollmentEntity> programsEnrollers =
          List<CoachProgramEnrollmentModel>.from(
                  data.map((x) => CoachProgramEnrollmentModel.fromJson(x)))
              .map((x) => x.toEntity())
              .toList();

      return Right(programsEnrollers);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }
}
