import 'dart:convert';

GroupResponseEntity groupResponseEntityFromJson(String str) => GroupResponseEntity.fromJson(json.decode(str));

String groupResponseEntityToJson(GroupResponseEntity data) => json.encode(data.toJson());

class GroupResponseEntity {
  GroupResponseEntity({
    required this.id,
    required this.name,
    required this.description,
    required this.image,
  });

  final String id;
  final String name;
  final String? description;
  final String? image;

  factory GroupResponseEntity.fromJson(Map<String, dynamic> json) => GroupResponseEntity(
    id: json["id"],
    name: json["name"],
    description: json["description"] ?? "",
    image: json["image"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "description": description ?? "",
    "image": image ?? "",
  };
}
