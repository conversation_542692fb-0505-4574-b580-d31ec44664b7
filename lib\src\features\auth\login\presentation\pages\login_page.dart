import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/validators/input_validators.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/auth/login/presentation/bloc/login_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/root/presentations/widgets/authentication_wrapper.dart';
import 'package:fitsomnia_app/src/features/auth/root/presentations/widgets/center_text.dart';
import 'package:fitsomnia_app/src/features/auth/root/presentations/widgets/social_icon_container.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:input_form_field/input_form_field.dart';

part '../widgets/checkbox.dart';
part '../widgets/form.dart';
part '../widgets/social_login.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  bool _isLoadingEssentialData = false;

  @override
  void initState() {
    super.initState();
    context.read<LoginBloc>().add(LoginInitialEvent());
  }

  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<LoginBloc, LoginState>(
          listener: (context, state) {
            if (state is LoginSuccessState) {
              //TODO testing
              // Log.info('Server access token: ${state.token}');
              // _showToastMessage('Server access token: ${state.token}');
              Log.info('User Login: success');

              _onLoginSuccessState();
            } else if (state is LoginErrorState) {
              _onLoginErrorState(state);
            } else if (state is LoginWithPhoneNumberState) {
              _showAlert();
            } else if (state is LoginWithEmailState) {
              context
                  .read<LoginBloc>()
                  .add(const LoginWithEmailAndPasswordEvent());
            }
          },
        ),
        BlocListener<SharedBloc, SharedState>(
          listener: (context, state) {
            if (state is EssentialDataLoaded) {
              Log.debug('EssentialDataLoaded received in login page');
              //TODO testing
              // _showToastMessage('Essential data is received from server');
              if (mounted && _isLoadingEssentialData) {
                _navigateToDashboardPage();

                setState(() {
                  _isLoadingEssentialData = false;
                });
              }
            }

            // TODO testing
            if (state is SharedErrorState) {
              // _showToastMessage('(SharedErrorState): User profile data error: ${state.message}');
              Log.error('UserProfile data error: ${state.message}');
              _onEssentialDataErrorState(state);

              if (mounted) {
                setState(() {
                  _isLoadingEssentialData = false;
                });
              }
            }
          },
        ),
      ],
      child: ScrollableWrapper(
        // appBar: AppBar(
        //   backgroundColor: UIColors.background,
        //   // elevation: 0,
        //   // title: Text(
        //   //   'Sign In',
        //   //   style: AppTypography.medium30(),
        //   // ),
        //   // centerTitle: true,
        // ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: Values.v20.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // SizedBox(height: 15.h),
                Center(
                  child: Image.asset(
                    Assets.workout,
                    width: Values.v70.w,
                    height: Values.v70.h,
                  ),
                ),
                CenterText(
                  text: 'Welcome to',
                  textStyle: TextStyle(
                    color: UIColors.primaryGreen900,
                    fontSize: FontSize.s32.sp,
                    fontWeight: FontWeight.w300,
                    fontFamily: FontConstants.promptFontFamily,
                    height: 1.0,
                  ),
                ),
                CenterText(
                  text: 'Fitsomnia',
                  textStyle: TextStyle(
                    color: UIColors.primaryGreen900,
                    fontSize: FontSize.s48.sp,
                    fontWeight: FontWeight.w500,
                    fontFamily: FontConstants.promptFontFamily,
                    height: 1.0,
                  ),
                ),
                // SizedBox(height: 30.h),
                // CenterText(
                //   text: 'Get Everything in One Place',
                //   textStyle: AppTypography.regular16(
                //     color: UIColors.fitGrey,
                //   ),
                // ),
                SizedBox(height: Values.v80.h),
                Form(
                  key: _formKey,
                  child: const _LoginFormBuilder(),
                ),
                SizedBox(height: Values.v20.h),
                _buildLoginButton(),
                SizedBox(height: Values.v20.h),
                _buildRememberMeAndForgotPassword(),
                SizedBox(height: Values.v100.h),
                const _SocialLoginBuilder(),
                SizedBox(height: Values.v40.h),
                _buildDoNotHaveAnAccountOption(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRememberMeAndForgotPassword() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            const _CheckboxBuilder(),
            Text(
              TextConstants.rememberMe,
              style: AppTypography.poppinsRegular14(),
            )
          ],
        ),
        const SizedBox(),
        GestureDetector(
          onTap: _navigateToForgotPasswordPage,
          child: Text(
            TextConstants.forgotPassword,
            style: AppTypography.poppinsRegular14(),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildLoginButton() {
    return BlocBuilder<SharedBloc, SharedState>(
      builder: (sharedContext, sharedState) {
        return BlocBuilder<LoginBloc, LoginState>(
          builder: (context, state) {
            return Button.filled(
              onPressed: _onLoginButtonPressed,
              isLoading: state is LoginLoadingState ||
                  sharedState is SharedLoadingState,
              label: TextConstants.login,
            );
          },
        );
      },
    );
  }

  Widget _buildDoNotHaveAnAccountOption() {
    return Wrap(
      // mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          TextConstants.doNotHaveAccount,
          style: AppTypography.poppinsRegular16(),
        ),
        GestureDetector(
          onTap: _navigateToSignUpPage,
          child: Text(
            TextConstants.createAccount,
            style: TextStyle(
                fontSize: FontSize.s16.sp,
                fontFamily: FontConstants.poppinsFontFamily,
                fontWeight: FontWeight.w400,
                color: UIColors.primary,
                decoration: TextDecoration.underline,
                decorationColor: UIColors.primary),
          ),
        ),
      ],
    );
  }

  void _onLoginSuccessState() {
    // _showToastMessage('Send userProfile request');
    Log.debug('EssentialDataLoaded requested from LoginPage');
    Log.info('Send request for UserProfile information');
    if (mounted) {
      setState(() {
        _isLoadingEssentialData = true;
      });
      context.read<SharedBloc>().add(LoadEssentialDataEvent());
    }
  }

  void _onLoginErrorState(LoginErrorState state) {
    AppToast.showToast(
      message: state.message,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: AppColors.error,
      toastLength: Toast.LENGTH_LONG,
    );
  }

  void _onEssentialDataErrorState(SharedErrorState errorState) {
    AppToast.showToast(
      message: errorState.message,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: AppColors.error,
      toastLength: Toast.LENGTH_LONG,
    );
  }

  void _showToastMessage(String message) {
    AppToast.showToast(
      message: message,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: AppColors.success,
    );
  }

  void _onLoginButtonPressed() {
    if (_formKey.currentState!.validate()) {
      context.read<LoginBloc>().add(CheckLoginMethodEvent());
    }
  }

  void _navigateToDashboardPage() async {
    // _showToastMessage('Route to dashboard page'); //Testing
    Log.debug(
        'EssentialDataLoaded requested from LoginPage for dashboard navigation');
    Log.info('Got user data successfully. Move to dashboard page');
    // Navigator.pushReplacementNamed(context, Routes.dashboard);

    Navigator.of(context).pop();

    await Navigator.of(context).pushNamedAndRemoveUntil(
      Routes.dashboard,
      (route) => false,
    );
  }

  void _navigateToForgotPasswordPage() {
    Navigator.pushNamed(context, Routes.forgotPassword);
  }

  void _navigateToSignUpPage() {
    Navigator.pushNamed(context, Routes.signUp);
  }

  _showAlert() {
    showDialog(
      context: context,
      builder: (context) {
        return CupertinoAlertDialog(
          title: Column(
            children: const [
              Text("Disclaimer"),
            ],
          ),
          content: const Text(
            'We\'re enhancing our security!\nPlease add an email address.\nTo add an email address goto:\n Settings > Security > Email Address.',
            textAlign: TextAlign.center,
          ),
          actions: [
            CupertinoDialogAction(
              child: const Text("Continue"),
              onPressed: () {
                context
                    .read<LoginBloc>()
                    .add(const LoginWithEmailAndPasswordEvent());
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
}
