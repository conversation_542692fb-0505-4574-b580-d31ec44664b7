import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/di/injection_container.dart' as di;
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/services/location_service/location_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/entities/nearby_clubs_members_entity.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/use_cases/nearby_clubs_members_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';

part 'nearby_clubs_members_state.dart';

class NearbyClubsMembersCubit extends Cubit<NearbyClubsMembersState> {
  NearbyClubsMembersCubit({
    required this.useCase,
  }) : super(const NearbyClubsMembersState());

  late NearbyClubsMembersUseCase useCase;

  String? clubId;

  //ignore: long-method
  Future<void> getNearbyClubsMembers({
    String? clubId,
    bool refresh = false,
  }) async {
    if (state.status == NearbyClubsMembersStatus.loading && !refresh) return;
    if (state.hasReachedMax && !refresh) return;
    if (refresh) emit(state.copyWith(data: []));

    emit(state.copyWith(
      status: NearbyClubsMembersStatus.loading,
      error: null,
    ));

    try {
      if (clubId != null) this.clubId = clubId;

      Position? locationData = await di.sl<LocationService>().getLocation();

      Map<String, dynamic> map = {
        'latitude': locationData?.latitude,
        'longitude': locationData?.longitude,
      };

      final result = await useCase.nearbyClubsMembers(
        map,
        this.clubId!,
        state.data.length,
      );

      result.fold(
        (l) => emit(
          state.copyWith(
            status: NearbyClubsMembersStatus.error,
            error: l.error!.message,
          ),
        ),
        (r) {
          emit(
            r.isEmpty
                ? state.copyWith(
                    status: NearbyClubsMembersStatus.success,
                    hasReachedMax: true,
                  )
                : state.copyWith(
                    status: NearbyClubsMembersStatus.success,
                    data: List.of(state.data)..addAll(r),
                    hasReachedMax: false,
                  ),
          );
        },
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      emit(
        state.copyWith(
          status: NearbyClubsMembersStatus.error,
          error: TextConstants.somethingWentWrong,
        ),
      );
    }
  }

  void updateRelationship({
    required int index,
    required String relationship,
  }) {
    emit(
      state.copyWith(
        data: List.of(state.data)
          ..removeAt(index)
          ..insert(
            index,
            state.data[index].copyWith(relationStatus: relationship),
          ),
      ),
    );
  }

  void resetState() {
    emit(const NearbyClubsMembersState());
  }
}
