import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_leaderboard_data_entity.dart';

class RewardLeaderboardDataModel extends RewardLeaderboardDataEntity {
  RewardLeaderboardDataModel({
    required super.userId,
    required super.userName,
    required super.userImage,
    required super.totalPoints,
  });

  factory RewardLeaderboardDataModel.fromJson(Map<String, dynamic> json) {
    return RewardLeaderboardDataModel(
      userId: json['userId'],
      userName: json['name'],
      userImage: (json['image'] == null) ? '' : json['image'],
      totalPoints: json['points'],
    );
  }

  RewardLeaderboardDataEntity toEntity() {
    return RewardLeaderboardDataModel(
      userId: userId,
      userName: userName,
      userImage: userImage,
      totalPoints: totalPoints,
    );
  }
}
