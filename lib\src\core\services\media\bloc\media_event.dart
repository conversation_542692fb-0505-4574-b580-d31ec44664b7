part of 'media_bloc.dart';

abstract class MediaEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class LoadPostsEvent extends MediaEvent {}

class LoadMediaFilesEvent extends MediaEvent {}

class PreviewSelectedImageEvent extends MediaEvent {
  final Uint8List previewedFile;
  final List<AssetEntity> mediaFiles;
  PreviewSelectedImageEvent(this.previewedFile, this.mediaFiles);
}
