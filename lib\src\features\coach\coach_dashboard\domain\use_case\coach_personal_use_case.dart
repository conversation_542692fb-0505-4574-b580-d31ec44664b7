import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_income_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/repositories/coach_personal_repository.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';

class CoachPersonalUseCase {
  final CoachPersonalRepository repository;

  CoachPersonalUseCase({required this.repository});

  Future<Either<ErrorModel, CoachEntity>> getCoachOwnProfile(
      {required String coachId}) async {
    return await repository.getCoachOwnProfile(coachId: coachId);
  }

  Future<Either<ErrorModel, List<CoachProgramEntity>>> getCoachPrograms(
      {required String coachId}) async {
    return await repository.getCoachPrograms(coachId: coachId);
  }

  Future<Either<ErrorModel, CoachProgramEntity>> getCoachProgram(
      {required String coachId, required String programId}) async {
    return await repository.getCoachProgram(
        coachId: coachId, programId: programId);
  }

  Future<Either<ErrorModel, List<CoachProgramEnrollmentEntity>>>
      getProgramEnrollers({required String coachId}) async {
    return await repository.getProgramEnrollers(coachId: coachId);
  }

  Future<Either<ErrorModel, CoachProgramEnrollmentEntity>> getProgramEnroller(
      {required String coachId, required String subscriptionId}) async {
    return await repository.getProgramEnroller(
        coachId: coachId, subscriptionId: subscriptionId);
  }

  Future<Either<ErrorModel, CoachProgramEnrollmentEntity>>
      cancelProgramSubscriptionByCoach({
    required String coachId,
    required String subscriptionId,
    required String cancelReason,
  }) async {
    return await repository.cancelProgramSubscriptionByCoach(
      coachId: coachId,
      subscriptionId: subscriptionId,
      cancelReason: cancelReason,
    );
  }

  Future<Either<ErrorModel, CoachIncomeEntity>> getCoachIncomeInfo(
      {required String coachId}) async {
    return await repository.getCoachIncomeInfo(coachId: coachId);
  }

  Future<Either<ErrorModel, List<CoachProgramEnrollmentEntity>>>
      getProgramEnrollerHistory({
    required String coachId,
    required String programId,
  }) async {
    return await repository.getProgramEnrollerHistory(
        coachId: coachId, programId: programId);
  }

  Future<Either<ErrorModel, List<CoachProgramEnrollmentEntity>>>
      getProgramPaymentHistory({
    required String coachId,
    required String programId,
  }) async {
    return await repository.getProgramPaymentHistory(
        coachId: coachId, programId: programId);
  }
}
