import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/data/model/group_list_model.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/entity/group_list_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/data/data_source/gorup_data_source.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/data/model/all_users_model.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/domain/entity/all_users_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/domain/entity/group_response_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/repository/group_repository.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/data/model/group_conversation_model.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/entity/group_chat_entity.dart';

class GroupRepositoryImpl implements GroupRepository {
  const GroupRepositoryImpl({required this.groupDataSource});

  final GroupDataSource groupDataSource;

  @override
  Future<Either<String, GroupResponseEntity>> createGroup(
      {required Map<String, dynamic> mapData}) async {
    try {
      final Response response =
          await groupDataSource.createGroup(mapData: mapData);
      final data = response.data['data'];

      GroupResponseEntity groupResponseEntity =
          GroupResponseEntity.fromJson(data);

      return Right(groupResponseEntity);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, List<GroupListEntity>>> getGroupList(int? limit) async {
    try {
      final Response response = await groupDataSource.getGroupList(limit);
      final data = response.data['data'];

      List<GroupListEntity> groupList = data
          .map<GroupListEntity>((groups) => GroupListModel.fromJson(groups))
          .toList();

      return Right(groupList);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, List<GroupChatEntity>>> getGroupHistory({
    required String groupId,
    int? limit,
    int? offset,
  }) async {
    try {
      final Response response = await groupDataSource.getGroupHistory(
        groupId: groupId,
        limit: limit,
        offset: offset,
      );
      final data = response.data['data'];

      List<GroupChatEntity> groupChat = data
          .map<GroupChatEntity>(
              (groups) => GroupConversationModel.fromJson(groups))
          .toList();

      List<GroupChatEntity> reversedList = List.from(groupChat.reversed);

      return Right(reversedList);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, List<AllUsersEntityWithoutProfile>>>
      getGroupMembersList({
    required String groupId,
    int? limit,
    int? offset,
  }) async {
    try {
      final Response response = await groupDataSource.getGroupMembersList(
        groupId: groupId,
        limit: limit,
        offset: offset,
      );
      final data = response.data['data'];

      List<AllUsersModelWithoutImageProfile> models = data
          .map<AllUsersModelWithoutImageProfile>(
              (users) => AllUsersModelWithoutImageProfile.fromJson(users))
          .toList();

      return Right(models);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, String>> leaveGroup({required String groupId}) async {
    try {
      final Response response =
          await groupDataSource.leaveGroup(groupId: groupId);
      final data = response.data['data'];

      return Right(data);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, String>> addNewMember(
      {required String groupId, required String memberId}) async {
    try {
      await groupDataSource.addNewMember(groupId: groupId, memberId: memberId);

      return const Right("Success");
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left(e.toString());
    }
  }
}
