import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_subcategory_view_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/coach/root/data/model/coach_program_category_model.dart';
import 'package:fitsomnia_app/src/features/coach/root/domain/entities/coach_program_category_entity.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CoachCategoryWidget extends StatefulWidget {
  const CoachCategoryWidget({super.key});

  @override
  State<CoachCategoryWidget> createState() => _CoachCategoryWidgetState();
}

class _CoachCategoryWidgetState extends State<CoachCategoryWidget> {
  Map<String, List<CoachProgramCategoryEntity>> _subcategoriesByCategoryId = {};
  List<CoachProgramCategoryEntity> _coachCategories = [];
  List<CoachProgramCategoryEntity> _subCategories = [];
  List<CoachProgramCategoryEntity> _allSubcateories = [];

  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    BlocProvider.of<CoachBloc>(context).add(GetCoachCategoryMap());
    _coachCategories = context.read<CoachBloc>().categoryList;
    _subcategoriesByCategoryId = context.read<CoachBloc>().subcategoriesByCategoryId;
    _allSubcateories = context.read<CoachBloc>().subcategoryList;
    _subCategories = _allSubcateories;
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachBloc, CoachState>(
      listener: (context, state) {
        if (state is CoachCategoryLoading) {
          setState(() {
            isLoading = true;
          });
        }
        if (state is GetCoachCategoryMapSuccess) {
          setState(() {
            isLoading = false;
            _coachCategories = state.categories;
          });
        }

        if (state is GetCoachCategoryMapFail) {
          setState(() {
            isLoading = false;
          });
        }
      },
      child: Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCategoriesTitle(),
            SizedBox(height: Values.v20,),
            _buildCategorySelectionMenu(),
            SizedBox(height: Values.v20,),
            _buildSubCategorySection(),
          ],
        ),
      ),
    );
  }

  _buildCategoriesTitle() {
    return Padding(
      padding: EdgeInsets.only(left: Values.v10),
      child: Text('Categories', style: AppTypography.poppinsSemiBold16(color: UIColors.primaryGreen900),),
    );
  }

  _buildCategorySelectionMenu() {
    List<CoachCategoryMenuItem> lists = _coachCategories.asMap().map((index, categoryEntity){
      return MapEntry(index, CoachCategoryMenuItem(id: categoryEntity.id, name: categoryEntity.title, index: index));
    }).values.toList();

    lists.insert(0, CoachCategoryMenuItem(id: 'all-menu-item-id', name: 'All', index: lists.length + 1));



    return CoachCategorySelectionMenu(
      categories: lists,
      callback: (p0) {
        Log.debug('selected category :${p0.name} ${p0.index}');
        setState(() {
          '';
          // set selected sub categories
          if(p0.index == lists.length) {
            // 'All' menu item is selected
            _subCategories = _allSubcateories;
          } else {
            _subCategories = _coachCategories[p0.index].subcategories ?? [];
          }
          
        });
      },
    );
  }

  _buildSubCategorySection() {
    // _subCategories = List.generate(5, (index) {
    //   return testCoachCategory;
    // });

    return Container(
      height: Values.v200.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        itemCount: _subCategories.length,
        itemBuilder: (BuildContext context, int index) {
          return CoachSubcategoryViewWidget(
              subcategoryEnrity: _subCategories[index]);
        },
      ),
    );
  }
}

class CoachCategorySelectionMenu extends StatefulWidget {
  const CoachCategorySelectionMenu(
      {super.key, this.callback, required this.categories});
  final List<CoachCategoryMenuItem> categories;
  final void Function(CoachCategoryMenuItem)? callback;

  @override
  State<CoachCategorySelectionMenu> createState() =>
      _CoachCategorySelectionMenuState();
}

class _CoachCategorySelectionMenuState
    extends State<CoachCategorySelectionMenu> {
  int _selectedItemIndex = 0;
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Values.v50.h,
      width: double.maxFinite,
      // color: Colors.grey,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.categories.length,
        itemBuilder: (BuildContext context, int index) {
          return Row(
            children: [
              if (index == 0)
                SizedBox(
                  width: Values.v20.w,
                ),
              ElevatedButton(
                  onPressed: () {
                    Log.debug(
                        'selected category: ${widget.categories[index].name}');
                    widget.callback!(widget.categories[index]);
                    setState(() {
                      _selectedItemIndex = index;
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: (_selectedItemIndex == index)
                        ? AppColors.primaryGreen
                        : AppColors.greyscale50,
                    
                    // elevation: 3,
                    // shape: RoundedRectangleBorder(
                    //   borderRadius: BorderRadius.circular(100.r),
                    // ),
                    // padding: const EdgeInsets.symmetric(
                    //   horizontal: 40.0,
                    //   vertical: 16.0,
                    // ), // Button padding
                  ),
                  child: Row(
                    children: [
                      Text(
                        widget.categories[index].name,
                        style: AppTypographyGreen.poppinsSemiBold14(
                            color: (_selectedItemIndex == index)
                                ? UIColors.white
                                : UIColors.primaryGreen950),
                      ),
                    ],
                  )),
              SizedBox(
                width: Values.v8.w,
              )
            ],
          );
        },
      ),
    );
  }
}

class CoachCategoryMenuItem {
  final String id;
  final String name;
  final int index;

  CoachCategoryMenuItem(
      {required this.id, required this.name, required this.index});
}

CoachProgramCategoryEntity testCoachCategory = CoachProgramCategoryEntity(
  id: 'coach-cateogy-id',
  title: 'Fitness',
  desc: 'fitness desc',
  mediaList: [
    CoachCategoryMediaFile(
        urlPath:
            'https://dev-public-cdn.fitsomnia.com/original/coach/2025/1/28/6293a7cb-9788-4cc1-9cf3-12eb56ff687e/938925_1738071637314B9A405711.jpg',
        mediaType: 'image')
  ],
  subcategories: [],
  totalCoach: 100,
);
