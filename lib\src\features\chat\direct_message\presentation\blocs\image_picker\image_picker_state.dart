part of 'image_picker_bloc.dart';

class ImagePickerState extends Equatable {
  @override
  List<Object?> get props => [];
}

class ImagePickerInitial extends ImagePickerState {}

class ImagePickerSuccess extends ImagePickerState {
  final XFile image;
  final String? featureName;
  final String? fieldName;

  ImagePickerSuccess({required this.image, this.featureName, this.fieldName});

  @override
  List<Object?> get props => [image];
}

class ImagePickerFailure extends ImagePickerState {
  final String errorMessage;
  final String? featureName;
  final String? fieldName;

  ImagePickerFailure(this.errorMessage, {this.featureName, this.fieldName});

  @override
  List<Object?> get props => [errorMessage];
}
