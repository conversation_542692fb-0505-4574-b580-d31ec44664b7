import 'dart:math';

import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/widget/process_steps_timeline_widget.dart';
import 'package:flutter/material.dart';

class CoachRegistrationIntroductionPage extends StatefulWidget {
  const CoachRegistrationIntroductionPage({super.key});

  @override
  State<CoachRegistrationIntroductionPage> createState() =>
      _CoachRegistrationIntroductionPageState();
}

class _CoachRegistrationIntroductionPageState
    extends State<CoachRegistrationIntroductionPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Be A Coach'),
        centerTitle: true,
        titleTextStyle:
            AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen900),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Stack(
            children: [
              _createCoachRegistrationIntroInfoSection(),
              Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  _createCoachProfileButton(),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  _createCoachRegistrationIntroInfoSection() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _coachBenefitSection(),
          _coachRegistrationProcessSection(),
          SizedBox(
            height: 100,
          ),
        ],
      ),
    );
  }

  _coachBenefitSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(coachBenefitTitle),
        Column(
          children: coachBenefits.map<Widget>((benefit) {
            return _buildBenefit(benefit.title, benefit.desc);
          }).toList(),
        ),
      ],
    );
  }

  _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(
        top: 1,
      ),
      child: Text(
        title,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        style: AppTypography.poppinsSemiBold20(color: UIColors.primary),
      ),
    );
  }

  _buildBenefit(String benefitName, String benifitDesc) {
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // const Icon(
          //   Icons.check_circle_outline_rounded,
          //   color: UIColors.primary,
          // ),

          Padding(
            padding: const EdgeInsets.all(4.0),
            child: Image.asset(
              Assets.coachProgramOfferIcon,
              height: 20,
              width: 20,
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Expanded(
              child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBenefitHeader(benefitName),
              _buildBenefitBody(benifitDesc),
            ],
          )),
        ],
      ),
    );
  }

  _buildBenefitHeader(String benefitName) {
    return Text(
      benefitName,
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
      style: AppTypography.poppinsSemiBold16(color: UIColors.primaryGreen950),
    );
  }

  _buildBenefitBody(String desc) {
    return Text(
      desc,
      style: AppTypography.poppinsSemiBold14(color: AppColors.greyscale400),
    );
  }

  _coachRegistrationProcessSection() {
    return ProcessStepsTimelineWidget(
      title: coachBenefitTitle,
      steps: coachRegistrationSteps,
    );
  }

  _createCoachProfileButton() {
    return Container(
      color: AppColors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Button.filled(
                label: 'Create Coach Profile',
                onPressed: () {
                  Log.debug('create coach profile pressed');
                  Navigator.of(context)
                      .pushNamed(Routes.coachRegistrationFormPage);
                }),
          ),
        ],
      ),
    );
  }
}

class CoachBenefit {
  const CoachBenefit({required this.title, required this.desc});

  final String title;
  final String desc;
}

String coachBenefitTitle = 'What You\'ll Get';
List<CoachBenefit> coachBenefits = [
  const CoachBenefit(
    title: 'Guaranteed Income',
    desc:
        'Say goodbye to income stress. We offer a reliable, consistent paycheck so you can focus on what you do best—coaching. All these for only 30% platform fee !',
  ),
  const CoachBenefit(
    title: 'Just add the program',
    desc:
        'You bring your knowledge, we provide the platform. Simply upload your coaching program and let our system take care of delivery, scheduling, and client management.',
  ),
  const CoachBenefit(
    title: 'We will do the marketing',
    desc:
        'We’ll promote you through our channels, target the right audience, and bring clients directly to your program—so you can skip the hustle and grow your impact faster.',
  ),
];

class CoachRegistrationStep extends ProcessStep {
  CoachRegistrationStep({super.icon, required super.stepDesc});
}

String coachRegistrationProcessTitle = 'How It Works?';
List<CoachRegistrationStep> coachRegistrationSteps = [
  CoachRegistrationStep(stepDesc: 'Provide your information'),
  CoachRegistrationStep(stepDesc: 'Add your program details and images'),
  CoachRegistrationStep(stepDesc: 'Add your certificates and credentials'),
  CoachRegistrationStep(stepDesc: 'Give a competitive price for your program'),
  CoachRegistrationStep(stepDesc: 'Now you’re ready to earn!'),
];
