import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/services/location_service/location_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/club/nearby_clubs/domain/use_case/join_club_use_case.dart';
import 'package:fitsomnia_app/src/features/club/nearby_clubs/domain/use_case/narby_club_use_case.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/entities/club_entity.dart';
import 'package:geolocator/geolocator.dart';

part 'nearby_clubs_event.dart';
part 'nearby_clubs_state.dart';

class NearbyClubsBloc extends Bloc<NearbyClubsEvent, BaseState> {
  NearbyClubsBloc({
    required this.nearbyClubUseCase,
    required this.joinClubUseCase,
    required this.locationService,
  }) : super(InitialState()) {
    on<FindNearbyClubsEvent>(_onNearbyClubEvent);
    on<JoinClubEvent>(_onJoinClubEvent);
    on<LeaveAndJoinClubEvent>(_onLeaveAndJoinClubEvent);
  }

  late NearbyClubUseCase nearbyClubUseCase;
  late JoinClubUseCase joinClubUseCase;
  late LocationService locationService;

  List<ClubEntity> clubs = [];

  Future<void> _onNearbyClubEvent(
    FindNearbyClubsEvent event,
    Emitter<BaseState> emit,
  ) async {
    if (event.pullToRefresh) clubs.clear();

    if (clubs.isEmpty) emit(const LoadingState());

    try {
      Position? userLocation = await locationService.getLocation();

      double? long = userLocation!.longitude;
      double? lat = userLocation.latitude;

      final response = await nearbyClubUseCase.call(
        lat: lat,
        long: long,
        offset: clubs.length,
      );

      response.fold(
        (l) => emit(ErrorState(data: l.error!.message!)),
        (r) {
          if (clubs.isEmpty) {
            clubs = r;
          } else {
            clubs.addAll(r);
          }
          emit(NearbyClubsSuccessState(clubs: List.of(clubs)));
        },
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());
      emit(
        const ErrorState(data: TextConstants.defaultErrorMessage),
      );
    }
  }

  Future<void> _onJoinClubEvent(
    JoinClubEvent event,
    Emitter<BaseState> emit,
  ) async {
    try {
      final response = await joinClubUseCase.call(event.clubId);

      response.fold(
        (l) => emit(ErrorState(data: l.error!.message!)),
        (r) => emit(const JoinClubSuccessState()),
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());
      emit(const ErrorState(data: TextConstants.pleaseTryAgain));
    }
  }

  Future<void> _onLeaveAndJoinClubEvent(
    LeaveAndJoinClubEvent event,
    Emitter<BaseState> emit,
  ) async {
    try {
      emit(const LoadingState());

      await Future.delayed(const Duration(seconds: 2));

      final response = await joinClubUseCase.leaveAndJoinClub(event.clubId);

      response.fold(
        (l) => emit(ErrorState(data: l.error!.message!)),
        (r) => emit(const JoinClubSuccessState()),
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());
      emit(const ErrorState(data: TextConstants.pleaseTryAgain));
    }
  }
}
