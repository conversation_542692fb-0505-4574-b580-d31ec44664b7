part of 'reward_point_bloc.dart';

sealed class RewardPointEvent extends Equatable {
  const RewardPointEvent();

  @override
  List<Object> get props => [];
}

class GetRewardPoints extends RewardPointEvent {
  @override
  List<Object> get props => [];
}

class GetUserWeeklyLoginActivity extends RewardPointEvent {
  final String userId;

  GetUserWeeklyLoginActivity({
    required this.userId,
  });

  @override
  List<Object> get props => [userId];
}

class GetRewardPointHistory extends RewardPointEvent {
  final int? offset;
  final int? limit;
  final String? historyFilter;

  const GetRewardPointHistory({
    required this.offset,
    required this.limit,
    required this.historyFilter,
  });

  @override
  List<Object> get props => [];
}
