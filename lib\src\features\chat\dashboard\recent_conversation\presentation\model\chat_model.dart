class ChatListViewModel {
  ChatListViewModel({
    required this.id,
    required this.name,
    required this.image,
    required this.lastMessage,
    required this.isLastSeen,
    required this.senderId,
    required this.type,
    required this.createdAt,
  });

  String id;
  String name;
  String? image;
  String lastMessage;
  bool isLastSeen;
  String senderId;
  String type;
  DateTime? createdAt;

  bool isMessageAlreadySeenByUser(String userId) {
    return userId == senderId ? true : isLastSeen;
  }
}
