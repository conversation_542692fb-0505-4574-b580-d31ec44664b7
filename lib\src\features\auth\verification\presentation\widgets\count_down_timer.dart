import 'dart:async';

import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/features/auth/sign_up/presentation/bloc/sign_up_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CountdownTimer extends StatefulWidget {
  const CountdownTimer({
    Key? key,
  }) : super(key: key);

  @override
  State<CountdownTimer> createState() => _CountdownTimerState();
}

class _CountdownTimerState extends State<CountdownTimer> {
  late Timer _timer;

  int _secondsRemaining = 180;
  bool showResendButton = false;

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  @override
  Widget build(BuildContext context) {
    String minutes =
        (_secondsRemaining / 60).floor().toString().padLeft(2, '0');
    String seconds = (_secondsRemaining % 60).toString().padLeft(2, '0');

    return BlocConsumer<SignUpBloc, BaseState>(
      listener: (context, state) {
        if (state is SuccessState && !_timer.isActive) {
          _startTimer();
          showResendButton = false;
        }
        if (state is ErrorState) {
          showResendButton = true;
          AppToast.showToast(
            message: state.data,
            backgroundColor: Colors.red,
            gravity: ToastGravity.BOTTOM,
          );
        }
      },
      builder: (context, state) {
        return GestureDetector(
          onTap: () async {
            if (state is LoadingState) return;

            if (showResendButton) {
              setState(() {
                _resetTimer();
                showResendButton = false;
                BlocProvider.of<SignUpBloc>(context).add(
                  SignUpWithEmailAndPasswordEvent(),
                );
              });
            }
          },
          child: Container(
            margin: const EdgeInsets.only(top: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.primaryGreen,
              borderRadius: BorderRadius.circular(25),
            ),
            child: Text(
              state is LoadingState
                  ? "Please Wait"
                  : showResendButton
                      ? 'Resend'
                      : '$minutes:$seconds',
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.white,
              ),
            ),
          ),
        );
      },
    );
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (Timer timer) {
      setState(() {
        if (_secondsRemaining <= 0) {
          timer.cancel();
          showResendButton = true;
        } else {
          _secondsRemaining--;
        }
      });
    });
  }

  void _resetTimer() {
    _timer.cancel();
    _secondsRemaining = 60;
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }
}
