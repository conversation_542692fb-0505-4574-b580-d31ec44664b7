import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/core/widgets/image_viewer.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/delete_message/delete_message_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/entity/group_chat_entity.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/group_chat/group_chat_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/pages/dashboard/others_profile_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:page_transition/page_transition.dart';
import 'package:fitsomnia_app/src/core/extensions/extensions.dart';

class GroupMessageBubble extends StatefulWidget {
  const GroupMessageBubble({
    required this.text,
    required this.isSentByMe,
    required this.data,
  });

  final String text;
  final bool isSentByMe;
  final GroupChatEntity data;

  @override
  State<GroupMessageBubble> createState() => _GroupMessageBubbleState();
}

class _GroupMessageBubbleState extends State<GroupMessageBubble> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 7.5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment:
            widget.isSentByMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          Visibility(
            visible: !widget.isSentByMe,
            child: Container(
              margin: const EdgeInsets.only(right: 16.0),
              child: GestureDetector(
                onTap: () {
                  _navigateToOthersProfile();
                },
                child: ImageContainer.circularImage(
                  image: widget.data.senderImage ?? '',
                  radius: Values.v22,
                ),
              ),
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment:  CrossAxisAlignment.end,         
            children: [
              Flexible(
                child: GestureDetector(
                  onLongPress: () {
                    if (widget.isSentByMe) {
                      _showDeleteAlert(context);
                    }
                  },
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth:
                          MediaQuery.of(context).size.width * Values.v0_75,
                    ),
                    decoration: BoxDecoration(
                      color: widget.data.type == 'image'
                          ? Colors.transparent
                          : widget.isSentByMe
                              ? AppColors.black5
                              : AppColors.primaryGreen50,
                      borderRadius: !widget.isSentByMe
                          ? const BorderRadius.only(
                              topLeft: Radius.circular(10),
                              topRight: Radius.circular(10),
                              bottomRight: Radius.circular(10),
                            )
                          : const BorderRadius.only(
                              topLeft: Radius.circular(10),
                              topRight: Radius.circular(10),
                              bottomLeft: Radius.circular(10),
                            ),
                    ),
                    padding: widget.data.type == 'image'
                        ? const EdgeInsets.all(0.0)
                        : const EdgeInsets.all(10.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        /// Sender
                        Visibility(
                          visible: !widget.isSentByMe,
                          child: Text(
                            widget.data.senderName,
                            style: const TextStyle(
                              fontSize: 14.0,
                              fontWeight: FontWeight.bold,
                              color: AppColors.black,
                            ),
                          ),
                        ),
                        if (widget.data.type == 'text')
                          Text(
                            widget.text,
                            style: TextStyle(
                              color: widget.isSentByMe
                                  ? AppColors.black
                                  : AppColors.black,
                            ),
                          ),
                        if (widget.data.type == 'image') SizedBox(height: 5.h),
                        if (widget.data.type == 'image')
                          GestureDetector(
                            onTap: () {
                              Navigator.push(
                                context,
                                PageTransition(
                                  type: PageTransitionType.scale,
                                  alignment: Alignment.bottomCenter,
                                  child: ImageViewer(
                                    image: widget.text,
                                  ),
                                ),
                              );
                            },
                            child: ImageContainer.rectangularImage(
                                image: widget.text,
                                height: 280.h,
                                width: 235.w,
                                fit: BoxFit.contain,
                                useMedium: true,),
                          )
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(height: 5.h),
              _buildDateStamp(widget.data.createdAt),
            ],
          ),
        ],
      ),
    );
  }

  _showDeleteAlert(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return BlocListener<DeleteMessageBloc, DeleteMessageState>(
            listener: (context, state) {
              if (state is DeleteMessageSuccess) {
                BlocProvider.of<GroupChatBloc>(context).emit(
                  GroupChatInitial(),
                );
                Navigator.of(context).pop();
              }
            },
            child: CupertinoAlertDialog(
              title: const Column(
                children: [
                  Text("Alert"),
                ],
              ),
              content:
                  const Text("Are you sure you want to delete this message?"),
              actions: [
                CupertinoDialogAction(
                  child: const Text("Yes"),
                  onPressed: () {
                    BlocProvider.of<DeleteMessageBloc>(context).add(
                      DeleteGroupChatMessageEvent(messageId: widget.data.id),
                    );
                  },
                ),
                CupertinoDialogAction(
                  child: const Text("No"),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            ),
          );
        });
  }

  Widget _buildDateStamp(DateTime dateTime) {
    // print('time ${dateTime.timeAgo()}');

    // var currentDate = DateTime.now();

    // if (today(currentDate, dateTime)) {
    //   var justTime = DateFormat('hh:mm a').format(dateTime);

    //   return Text(
    //     dateTime.timeAgo(),
    //     style: const TextStyle(
    //       fontSize: 10,
    //       color: AppColors.grey,
    //     ),
    //   );
    // }

    // var dateTimeFormattedString =
    //     DateFormat('yyyy-MM-dd hh:mm a').format(dateTime);

    return Text(
      dateTime.timeAgo(),
      style: const TextStyle(
        fontSize: 10,
        color: AppColors.grey,
      ),
    );
  }

  // bool today(DateTime currentDate, DateTime dateTime) {
  //   return currentDate.day == dateTime.day &&
  //       currentDate.month == dateTime.month &&
  //       currentDate.year == dateTime.year;
  // }

  void _navigateToOthersProfile() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => OthersProfilePage(userId: widget.data.senderId),
      ),
    );
  }


  
}
