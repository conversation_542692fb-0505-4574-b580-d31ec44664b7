import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/presentation/page/create_group_chat_page.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/pages/chat_page.dart';
import 'package:fitsomnia_app/src/features/profile/domain/entities/fit_buddy_entity.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/fit_buddies/fit_buddies_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MessageToPage extends StatefulWidget {
  const MessageToPage({
    Key? key,
    this.autoFocus = false,
  }) : super(key: key);

  final bool autoFocus;

  @override
  State<MessageToPage> createState() => _MessageToPageState();
}

class _MessageToPageState extends State<MessageToPage> {
  final TextEditingController searchController = TextEditingController();
  List<FitBuddyEntity> fitBuddiesEntity = [];
  List<FitBuddyEntity> searchedData = [];

  @override
  void initState() {
    super.initState();
    BlocProvider.of<FitBuddiesBloc>(context).add(
      GetFitBuddiesListEvent(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: _buildAppBar(),
      body: BlocConsumer<FitBuddiesBloc, BaseState>(
        listener: (context, state) {
          if (state is SuccessState) {
            int length = (state.data as List<FitBuddyEntity>).length;

            if (length > 0) {
              fitBuddiesEntity = state.data;
              searchedData = state.data;
            }
          }
        },
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSearchBar(),
                const SizedBox(height: 16),
                _buildCreateANewGroupCard(context),
                const SizedBox(height: 16),
                const Text(
                  "Fit Buddies",
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: AppColors.black54,
                  ),
                ),
                const SizedBox(height: 16),
                if (state is SuccessState) _buildPeopleList()
              ],
            ),
          );
        },
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        "Message To",
        style: TextStyle(
          color: AppColors.black,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevation: 0,
      iconTheme: const IconThemeData(color: AppColors.black),
      backgroundColor: AppColors.white,
    );
  }

  Widget _buildSearchBar() {
    return Container(
      height: Values.v48,
      padding: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        color: AppColors.grey6,
        borderRadius: BorderRadius.circular(Values.v30),
      ),
      child: TextFormField(
        controller: searchController,
        autocorrect: false,
        autofocus: widget.autoFocus,
        decoration: InputDecoration(
          hintText: "Search for people",
          border: InputBorder.none,
          prefixIcon: Icon(
            Icons.search,
            color: AppColors.apple,
          ),
        ),
        keyboardType: TextInputType.name,
        onChanged: (value) {
          searchedData = value == ''
              ? fitBuddiesEntity
              : fitBuddiesEntity
                  .where((element) =>
                      element.name!.startsWith(searchController.text))
                  .toList();

          if (mounted) {
            setState(() {
              '';
            });
          }
        },
      ),
    );
  }

  Widget _buildCreateANewGroupCard(BuildContext context) {
    return ListTile(
      onTap: () {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (BuildContext context) => const CreateGroup(),
          ),
        );
      },
      contentPadding: EdgeInsets.zero,
      leading: CircleAvatar(
        backgroundColor: AppColors.grey6,
        child: Icon(
          Icons.group,
          color: AppColors.apple,
        ),
      ),
      title: const Text("Create a new group"),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        size: 16,
      ),
    );
  }

  Widget _buildPeopleList() {
    return Expanded(
      child: ListView.builder(
        itemCount: searchedData.length,
        itemBuilder: (context, index) {
          return _buildUserCard(
            context,
            searchedData[index],
          );
        },
      ),
    );
  }

  Widget _buildUserCard(BuildContext context, FitBuddyEntity data) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Row(
              children: [
                _buildAvatar(data),
                const SizedBox(width: 16),
                _buildUserName(data.name!),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: () => _navigateToChatPage(
                context,
                data.name!,
                data.id!,
                data.image != null
                    ? data.image!.profile != null
                        ? data.image!.profile!
                        : ""
                    : ""),
            style: ElevatedButton.styleFrom(
              elevation: 0,
              backgroundColor: AppColors.apple,
            ),
            child: const Text("Message"),
          )
        ],
      ),
    );
  }

  Widget _buildAvatar(FitBuddyEntity data) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        ImageContainer.circularImage(
          image: data.image != null
              ? data.image!.profile != null
                  ? data.image!.profile ?? ''
                  : ''
              : '',
          radius: Values.v23,
        ),
      ],
    );
  }

  Widget _buildUserName(String name) {
    return Expanded(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Text(
          name,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
            color: AppColors.black,
          ),
        ),
      ),
    );
  }

  void _navigateToChatPage(
      BuildContext context, String title, String id, String? image) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(
          title: title,
          fitBuddyId: id,
          image: image,
        ),
      ),
    );
  }
}
