import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/domain/entities/user_review_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/domain/repositories/coach_program_review_repository.dart';

class CoachProgramReviewUseCase {
  final CoachProgramReviewRepository repository;

  CoachProgramReviewUseCase({required this.repository});

  Future<Either<ErrorModel, List<UserReviewEntity>>> getCoachProgramReviews(
      {required String programId}) async {
    return await repository.getCoachProgramReviews(programId: programId);
  }

  Future<Either<ErrorModel, UserReviewEntity>> getCoachProgramReviewById(
      {required String reviewId}) async {
    return await repository.getCoachProgramReviewById(reviewId: reviewId);
  }

  Future<Either<ErrorModel, UserReviewEntity>> raviewCoachProgram(
      {required String programId, required Map<String, dynamic> data}) async {
    return await repository.reviewCoachProgram(
        programId: programId, data: data);
  }

  Future<Either<ErrorModel, UserReviewEntity>> deleteCoachReview(
      {required String reviewId}) async {
    return await repository.deleteCoachProgramReview(reviewId: reviewId);
  }

  Future<Either<ErrorModel, UserReviewEntity>> updateCoachReview(
      {required String reviewId, required Map<String, dynamic> data}) async {
    return await repository.updateCoachProgramReview(
        reviewId: reviewId, data: data);
  }

  Future<Either<ErrorModel, List<UserReviewEntity>>> getCoachProfileReviews(
      {required String coachId}) async {
    return await repository.getCoachProfileReviews(coachId: coachId);
  }

  Future<Either<ErrorModel, UserReviewEntity>> getCoachProfileReviewById(
      {required String reviewId}) async {
    return await repository.getCoachProfileReviewById(reviewId: reviewId);
  }

  Future<Either<ErrorModel, UserReviewEntity>> reviewCoachProfile({
    required String coachId,
    required Map<String, dynamic> data,
  }) async {
    return await repository.reviewCoachProfile(coachId: coachId, data: data);
  }

  Future<Either<ErrorModel, UserReviewEntity>> deleteCoachProfileReview(
      {required String reviewId}) async {
    return await repository.deleteCoachProfileReview(reviewId: reviewId);
  }

  Future<Either<ErrorModel, UserReviewEntity>> updateCoachProfileReview({
    required String reviewId,
    required Map<String, dynamic> data,
  }) async {
    return await repository.updateCoachProfileReview(
        reviewId: reviewId, data: data);
  }
}
