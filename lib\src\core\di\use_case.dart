part of 'injection_container.dart';

Future<void> _initUseCases() async {
  sl.registerLazySingleton(
    () => RegistrationUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => LoginUseCase(
      authRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => LoginWithGoogleUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => LoginWithFacebookUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => LoginWithAppleUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => VerificationUseCase(
      authRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => ForgotPasswordUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => IdentityVerificationUseCase(
      authRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => ResetPasswordUseCase(
      authRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetAllNotificationsUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => MarkAllNotificationAsReadUseCase(
      repository: sl.call(),
    ),
  );

  /// Story
  sl.registerLazySingleton(
    () => GetStoriesUseCase(
      storyRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => CreateStoryUseCase(
      storyRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => MarkStoryAsViewedStoryUseCase(
      storyRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => DeleteStoryUseCase(
      storyRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => MyStoryUseCase(
      storyRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => StoryViewersUseCase(
      storyViewersRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetUserProfileUseCase(
      userProfileRepository: sl(),
    ),
  );

  sl.registerLazySingleton(
    () => UpdateUserProfileInfoUseCase(
      userProfileRepository: sl(),
    ),
  );

  sl.registerLazySingleton(
    () => UpdateUserProfilePictureUseCase(
      userProfileRepository: sl(),
    ),
  );

  sl.registerLazySingleton(
    () => GetNotesUseCase(
      plannerRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => CreateNoteUseCase(
      plannerRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetNoteDetailsUseCase(
      plannerRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => UpdateNoteUseCase(
      plannerRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => DeleteNoteUseCase(
      plannerRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => SuggestedUserListUseCase(
      spotNotRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => SendSpotRequestUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => UpdateRequestUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => SendLocationToServerUseCase(
      sharedRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => SpotListUseCase(
      notificationRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => MembershipStatusUseCase(
      clubRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => MyClubUseCase(
      clubRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => NearbyClubUseCase(
      sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => JoinClubUseCase(
      clubRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => LeaveClubUseCase(
      clubRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => NearbyClubsMembersUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => ClubMembersUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => CreatePostUseCase(
      createPostRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => EditPostUseCase(
      feedRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => FileUploadUseCase(
      fileUploadRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => ScanQRCodeUseCase(
      qrCodeRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetFeedsUseCase(
      feedRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetAllUsersUseCase(
      allUserRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => RecentChatListUseCase(
      chatListRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => CreateGroupUseCase(
      groupRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetGroupListUseCase(
      groupRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetGroupHistoryUseCase(
      groupRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => DeleteMessageUseCase(
      deleteMessageRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetGroupMembersUseCase(
      groupRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => LeaveGroupUseCase(
      groupRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetChatHistoryUseCase(
      oneToOneChatRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => DeleteOneToOneMessageUseCase(
      deleteMessageRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => AddMemberUseCase(
      groupRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => ActiveUserListUseCase(
      activeUserRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => AddCommentUseCase(
      commentRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetAllCommentsUseCase(
      commentRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => LikePostUseCase(
      feedRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => PickImageUseCase(
      imagePickingRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetImageUrlUseCase(
      imageUploadRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetTrainingExerciseListUseCase(
      trainingRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetExerciseCategoryUseCase(
      exerciseCategoryRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetProfileFeedPhotosUseCase(
      profileFeedRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => ChangePasswordUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => PreferenceUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => ChangeEmailUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => BlockUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetMonthlyChallenges(
      monthlyChallengeRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => AcceptChallengeUseCase(
      monthlyChallengeRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetChallengeHistoryUseCase(
      monthlyChallengeRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetPointsUseCase(
      pointsRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => UploadChallengeVideoUseCase(
      monthlyChallengeRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetLeaderboardUseCase(
      monthlyChallengeRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetPointsHistoryUseCase(
      pointsRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetMuscleGroupUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetBodyBuildingProgramUseCase(
      bodyBuildingProgramRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetBodyBuildingProgramDetailsUseCase(
      bodyBuildingProgramRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetProfileFeedVideosUseCase(
      profileFeedRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetFitBuddiesUseCase(
      profileFeedRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => BlockFitBuddyUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => GetTimelineUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => FetchDietPlansUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => FetchActivityLevelUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => CreateDietPlanUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => DietHistoryUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => IdealCaloriesUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => WaterConsumptionUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => RecentFoodsUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => MyFoodsUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => CreateFoodUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => FoodConsumptionUseCase(
      repository: sl.call(),
      foodRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => FitMarketDashboardUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => CartUseCase(
      cartRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ShippingAddressUseCase(
      shippingAddressRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => UnfriendFitBuddyUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => PeopleSearchUsecase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ClubSearchUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ProductSearchUsecase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ProductUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => GetOthersSpotMeUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => AddToWishlistUseCase(
      wishlistRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => RemoveFromWishlistUseCase(
      wishlistRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => GetWishlistUseCase(
      wishlistRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => BillingAddressUseCase(
      billingAddressRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => OrderHistoryUseCase(
      orderHistoryRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CreateOrderUseCase(
      orderRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => GetSpotMeProfileUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CreateSpotMeProfileUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => UpdateSpotMeProfileUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => GetSpotProfileUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => GetFitBuddyStatusUseCase(
      spotNotRequestRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => GetOrderDetailsUseCase(
      orderRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CreatePaymentUseCase(
      paymentRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CreatePaymentByPointsUseCase(
      paymentRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => OrderSummaryUseCase(
      orderSummaryRepository: sl.call(),
    ),
  );

  sl.registerLazySingleton(
    () => DeletePostUseCase(
      feedRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => NotRequestUseCase(
      spotNotRequestRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => GetPaymentMethodsUseCase(
      orderSummaryRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CancelSpotRequestUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => UpdateDietPlanUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CreateReviewUseCase(
      reviewRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => GetReviewUseCase(
      reviewRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => EditReviewUseCase(
      reviewRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => DeleteReviewUseCase(
      reviewRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => LiveMemberUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => SpotBackUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => GetPostDetailsUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => DeleteSpotProfileUseCase(
      repository: sl.call(),
    ),
  );

  /// Likers Info
  sl.registerFactory(
    () => LikersInfoUseCase(
      likersInfoRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => LogoutUseCase(
      repository: sl.call(),
    ),
  );

  ///Subscriptions
  sl.registerFactory(
    () => SubscriptionsUseCase(
      subscriptionRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => MySubscriptionPackageUseCase(
      subscriptionRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => SubscribePackageUseCase(
      subscriptionRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ValidateAddressUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => DeleteAccountUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => FitbotChatUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => TeamPollUseCase(
      teamPollRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => EventUseCase(
      eventRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CoachUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CoachProfileUseCase(
      coachRegistrationRepository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CoachProgramUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CoachPersonalUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => UserProgramSubscriptionUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CoachNewsfeedUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CoachProgramReviewUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => CoachWithdrawPaymentUsecase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => RewardPointUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => RewardLeaderboardUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => ReferralUseCase(
        repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => AppInfoUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => DailyTaskUseCase(
      repository: sl.call(),
    ),
  );

  sl.registerFactory(
    () => FoodScanUseCase(
      repository: sl.call(),
    ),
  );

}
