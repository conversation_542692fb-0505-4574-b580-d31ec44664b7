import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/services/local_storage/cache_service.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/entity/referral_data_entity.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/presentation/bloc/referral_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/presentation/widget/referral_card_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ReferralAvailablePage extends StatefulWidget {
  const ReferralAvailablePage({super.key});

  @override
  State<ReferralAvailablePage> createState() => _ReferralAvailablePageState();
}

class _ReferralAvailablePageState extends State<ReferralAvailablePage> {
  List<ReferralDataEntity> _referralList = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // _referralList = List.generate(3, (index) {
    //   return testReferralData;
    // });

    _isLoading = true;
    BlocProvider.of<ReferralBloc>(context).add(GetReferrals());
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ReferralBloc, ReferralState>(
        listener: (context, state) {
          if (state is GetReferralsSuccess) {
            setState(() {
              _referralList = state.referrals;
              _isLoading = false;
            });

            _showHowToWinPointsAlert();
          }

          if (state is GetReferralsFail) {
            setState(() {
              _isLoading = false;
            });
          }
        },
        child: Scaffold(
          appBar: _buildAppBar(),
          body: SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: Values.v20,
                  horizontal: Values.v20,
                ),
                child: (_isLoading)
                    ? const Center(
                        child: CircularProgressIndicator(
                        color: UIColors.primary,
                      ))
                    : (_referralList.isEmpty)
                        ? Text('No Data Available')
                        : _buildReferrelsSection(),
              ),
            ),
          ),
        ));
  }

  _buildReferrelsSection() {
    return ListView.separated(
      itemBuilder: (
        context,
        index,
      ) {
        return ReferralCardWidget(
          referralDataEntity: _referralList[index],
        );
      },
      separatorBuilder: (context, index) {
        return SizedBox(
          height: 10,
        );
      },
      itemCount: _referralList.length,
      shrinkWrap: true,
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        'Referrals',
        style: TextStyle(
          color: UIColors.primaryGreen950,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: true,
      elevation: 0,
    );
  }

  _showHowToEarnPointsAlert() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(Values.v10)),
          ),
          contentPadding: EdgeInsets.all(0),
          // titlePadding: EdgeInsets.all(1),
          // actionsPadding: EdgeInsets.all(1),
          // buttonPadding: EdgeInsets.all(1),
          // insetPadding: EdgeInsets.all(1),
          content: RewardEarningInfoWidget(),
        );
      },
      barrierDismissible: true,
    );
  }

  _showHowToWinPointsAlert() async {
    bool _isShowHowToWinAlert =
        await CacheService.instance.getHowToWinPointAlertShowStatus();
    if (!_isShowHowToWinAlert) {
      await CacheService.instance.setHowToWinPointAlertShowStatus(true);
      _showHowToEarnPointsAlert();
    }
  }
}

class RewardEarningInfoWidget extends StatelessWidget {
  const RewardEarningInfoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: ExactAssetImage(Assets.referralBackgroundImg),
          fit: BoxFit.cover,
          opacity: 0.15,
        ),
        color: UIColors.primaryGreen950,
        // gradient: RadialGradient(colors: [UIColors.primaryGreen900, Colors.transparent], radius: 1.0),
        borderRadius: BorderRadius.circular(Values.v10),
        // border: Border.all(color: UIColors.primaryGreen950)
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            // decoration: BoxDecoration(border: Border.all(color: Colors.red)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  // iconSize: Values.v18,
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  icon: Icon(
                    Icons.clear_sharp,
                    color: UIColors.white,
                  ),
                )
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(Values.v20),
            // height: 500,
            // width: 400,
            child: Image.asset(Assets.rewardPointsEarningProcess),
          ),
        ],
      ),
    );
  }
}
