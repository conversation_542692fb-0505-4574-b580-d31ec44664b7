import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/entities/user.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/repositories/auth_repository.dart';

class LoginWithGoogleUseCase {
  final AuthRepository repository;

  LoginWithGoogleUseCase({required this.repository});

  Future<Either<ErrorModel, User>> call() async {
    return await repository.google();
  }
}
