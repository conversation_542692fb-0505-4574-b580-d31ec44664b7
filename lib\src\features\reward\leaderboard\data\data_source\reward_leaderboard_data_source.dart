import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class RewardLeaderboardDataSource {
  Future<Response> getTopUser({
    required int? offset,
    required int? limit,
    required String? filterType,
  });

  Future<Response> getUserCurrentRank();
}

class RewardLeaderboardDataSourceImpl extends RewardLeaderboardDataSource {
  final RestClient restClient;

  RewardLeaderboardDataSourceImpl({required this.restClient});
  
  @override
  Future<Response> getTopUser({
    required int? offset,
    required int? limit,
    required String? filterType,
  }) async {
     final response =
        await restClient.get(APIType.PROTECTED, API.rewardPointLeaderboard, data: {
      if (limit != null) "limit": limit,
      if (offset != null) "offset": offset,
      if(filterType != null) 'filterType': filterType,
    });

    return response;
    
  }
  
  @override
  Future<Response> getUserCurrentRank() async {
    final response = await restClient.get(APIType.PROTECTED, API.getRewardPoints);

    return response;
  }
}
