import 'package:fitsomnia_app/src/features/coach/coach_program_review/domain/entities/user_review_entity.dart';

class UserReviewModel extends UserReviewEntity {
  UserReviewModel({
    required super.reviewId,
    required super.userId,
    required super.userName,
    required super.userImage,
    required super.coachId,
    required super.rating,
    required super.comment,
    required super.createdAt,
    required super.updatedAt,
    super.programId,
  });

  factory UserReviewModel.fromJson(Map<String, dynamic> json) {
    return UserReviewModel(
      reviewId: json['id'],
      userId: json['userId'],
      userName: json['userName'],
      userImage: json['userImage'] ?? '',
      coachId: json['coachId'] ?? '',
      rating: (json['rating'] == null)
          ? 0.0
          : ((json['rating'] is int)
              ? json['rating'].toDouble()
              : json['rating']),
      comment: json['comment'],
      createdAt: DateTime.tryParse(json['createdAt']) ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt']) ?? DateTime.now(),
      programId: json['programId'],
    );
  }

  UserReviewEntity toEntity() {
    return UserReviewEntity(
        reviewId: reviewId,
        userId: userId,
        userName: userName,
        userImage: userImage,
        coachId: coachId,
        rating: rating,
        comment: comment,
        createdAt: createdAt,
        updatedAt: updatedAt);
  }
}
