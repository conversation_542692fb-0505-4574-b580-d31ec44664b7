import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/extensions/extensions.dart';
import 'package:fitsomnia_app/src/core/services/firebase/firebase_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/spot_not/spot_not_cubit.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/presentation/page/spot_me_details_page.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/presentation/page/spot_profile_others_details_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ClubMemberCard extends StatelessWidget {
  const ClubMemberCard({
    Key? key,
    required this.id,
    this.image,
    required this.name,
    this.clubName,
    this.status,
    this.isHorizontal = false,
    this.callback,
    this.width,
    this.height,
  }) : super(key: key);

  final String id;
  final String? image;
  final String name;
  final String? clubName;
  final String? status;
  final bool isHorizontal;
  final Function(String status)? callback;
  final double? width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return isHorizontal
        ? _HorizontalCard(
            id: id,
            image: image,
            name: name,
            clubName: clubName,
            status: status,
            callback: callback,
          )
        : _VerticalCard(
            id: id,
            image: image,
            name: name,
            clubName: clubName,
            status: status,
            callback: callback,
            height: height,
            width: width,
          );
  }
}

class _VerticalCard extends StatefulWidget {
  _VerticalCard({
    Key? key,
    required this.id,
    required this.image,
    required this.name,
    this.clubName,
    this.status,
    this.callback,
    this.width,
    this.height,
  }) : super(key: key);

  final String id;
  final String? image;
  final String name;
  final String? clubName;
  String? status;
  final Function(String status)? callback;
  final double? width;
  final double? height;

  @override
  State<_VerticalCard> createState() => __VerticalCardState();
}

class __VerticalCardState extends State<_VerticalCard> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width ?? 150.w,
      height: widget.height ?? 188.h,
      margin: EdgeInsets.only(right: widget.height == null ? 10.w : 0.0),
      padding: EdgeInsets.zero,
      decoration: widget.height == null
          ? const BoxDecoration(
              color: Colors.white,
            )
          : BoxDecoration(
              border: Border.all(
                color: AppColors.alto,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(8.r),
              gradient: const LinearGradient(
                colors: [
                  Color(0xFFA0B4B7),
                  AppColors.white,
                ],
                stops: [
                  0.3,
                  0.3,
                ],
                begin: FractionalOffset.topCenter,
                end: FractionalOffset.bottomCenter,
                tileMode: TileMode.clamp,
              ),
            ),
      child: InkWell(
        onTap: () => _navigateToSpotMeScreen(),
        child: Stack(
          children: [
            widget.height == null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: widget.image != null
                        ? ImageContainer.rectangularImage(
                            image: widget.image,
                            height: widget.height ?? 188.h,
                            width: widget.width ?? 150.w,
                            fit: BoxFit.cover,
                            useSmall: true,
                            hideLoadingIndicator: true,
                            errorWidget: _imageErrorPlaceHolder(),
                          )
                        : Image.asset(
                            Assets.image1,
                            width: widget.width ?? 150.w,
                            height: widget.height ?? 188.h,
                            fit: BoxFit.cover,
                          ),
                  )
                : Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: 8.h),
                    child: widget.image != null
                        ? ImageContainer.circularImage(
                            image: widget.image ?? '',
                            radius: 45.r,
                            size: 40.r,
                            errorWidget: errorWidget(),
                          )
                        : Image.asset(
                            Assets.image1,
                            width: widget.width ?? 150.w,
                            height: widget.height ?? 188.h,
                            fit: BoxFit.cover,
                          ),
                  ),
            Container(
              alignment: Alignment.bottomCenter,
              margin: EdgeInsets.zero,
              padding: EdgeInsets.all(8.h),
              width: widget.width ?? 150.w,
              decoration: widget.height == null
                  ? BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      gradient: _buildLinearGradient(),
                    )
                  : BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    widget.name.toCapitalFirst(),
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    style: widget.height == null
                        ? AppTypography.semiBold14(
                            color: AppColors.white,
                          )
                        : AppTypography.semiBold18(
                            color: Colors.blueGrey,
                          ),
                  ),
                  Visibility(
                    visible: widget.clubName != null,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          Assets.clubIcon,
                          width: Values.v17.w,
                          height: Values.v17.h,
                        ),
                        SizedBox(width: Values.v4.w),
                        Flexible(
                          child: Text(
                            widget.clubName ?? '',
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            style: AppTypography.semiBold12(
                              color: AppColors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 5.h),
                  _CTA(
                    id: widget.id,
                    status: widget.status!,
                    callback: widget.callback!,
                    isVertical: true,
                    width: widget.width,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _imageErrorPlaceHolder() {
    return Container(
      color: AppColors.alto_08,
      width: widget.width ?? Values.v173.w,
      height: widget.height ?? Values.v190.h,
      child: Icon(
        Icons.person,
        color: AppColors.darkSoftBg,
        size: 100,
      ),
    );
  }

  Widget errorWidget() {
    return Container(
      color: AppColors.alto,
      child: Image.asset(
        Assets.spotMeNoImage,
        fit: BoxFit.contain,
      ),
    );
  }

  LinearGradient _buildLinearGradient() {
    return LinearGradient(
      begin: Alignment.bottomCenter,
      end: Alignment.topCenter,
      colors: [
        AppColors.black.withOpacity(.7),
        AppColors.black.withOpacity(0),
      ],
    );
  }

  void _navigateToSpotMeScreen() {
    FirebaseService().logFeatureUsage('club', 'club_spotme', '');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => SpotProfileOthersDetailsPage(
          userId: widget.id,
          isFromClubPage: true,
          callback: widget.callback!,
        ),
      ),
    );
  }
}

class _HorizontalCard extends StatefulWidget {
  _HorizontalCard({
    Key? key,
    required this.id,
    required this.image,
    required this.name,
    this.clubName,
    this.status,
    this.callback,
  }) : super(key: key);

  final String id;
  final String? image;
  final String name;
  final String? clubName;
  String? status;
  final Function(String status)? callback;

  @override
  State<_HorizontalCard> createState() => __HorizontalCardState();
}

class __HorizontalCardState extends State<_HorizontalCard> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.alto_08,
          ),
        ),
      ),
      child: InkWell(
        onTap: () => _navigateToSpotMeScreen(),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              flex: 0,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(100),
                child: ImageContainer.rectangularImage(
                  image: widget.image!,
                  height: 55.h,
                  width: 55.w,
                  fit: BoxFit.cover,
                  useSmall: true,
                  hideLoadingIndicator: true,
                  errorWidget: _imageErrorPlaceHolder(),
                ),
              ),
            ),
            Expanded(
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 10.w),
                    child: Text(
                      widget.name,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: AppTypography.regular16(
                        color: AppColors.black,
                      ),
                    ),
                  ),
                  _CTA(
                    id: widget.id,
                    status: widget.status!,
                    callback: widget.callback!,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _imageErrorPlaceHolder() {
    return Container(
      color: AppColors.alto_08,
      width: 55.w,
      height: 55.h,
      child: const Icon(
        Icons.person,
        color: AppColors.white,
        size: 40,
      ),
    );
  }

  void _navigateToSpotMeScreen() {
    FirebaseService().logFeatureUsage('club', 'club_spotme', '');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => SpotProfileOthersDetailsPage(
          userId: widget.id,
          isFromClubPage: true,
          callback: widget.callback!,
        ),
      ),
    );
  }
}

class _CTA extends StatelessWidget {
  const _CTA({
    Key? key,
    required this.id,
    required this.status,
    required this.callback,
    this.isVertical = false,
    this.width,
  }) : super(key: key);

  final String id;
  final String status;
  final Function(String)? callback;
  final bool isVertical;
  final double? width;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SpotNotCubit, SpotNotState>(
      listener: (context, state) {
        if (state.id == id) {
          if (state.status == SpotNotStatus.success &&
              state.spotRequestStatus == SpotRequestStatus.SENT) {
            callback!("REQUESTED");
          }
          if (state.status == SpotNotStatus.success &&
              state.spotRequestStatus == SpotRequestStatus.CANCELLED) {
            callback!("NOT_SPOTTED");
          }
          if (state.status == SpotNotStatus.success &&
              state.spotRequestStatus == SpotRequestStatus.ACCEPTED) {
            callback!("SPOTTED");
          }
        }
      },
      buildWhen: (previous, current) => current.id == id,
      builder: (context, state) {
        return SizedBox(
          height: isVertical ? 40.h : 35.h,
          child: Button.outlined(
            onPressed: () {
              if (status == TextConstants.NOT_SPOTTED) {
                context.read<SpotNotCubit>().sendRequest(id: id);
              } else if (status == TextConstants.REQUESTED) {
                context.read<SpotNotCubit>().cancelRequest(id: id);
              } else if (status == TextConstants.SPOT_BACK) {
                context.read<SpotNotCubit>().updateRequest(
                      requesterId: id,
                      isAccepted: true,
                    );
              }
            },
            isLoading: state.status == SpotNotStatus.loading,
            width: width ?? 130.w,
            scale: 1,
            background: status == TextConstants.NOT_SPOTTED
                ? AppColors.transparent
                : AppColors.primaryGreen,
            label: status == TextConstants.NOT_SPOTTED
                ? 'Spot'
                : status == TextConstants.REQUESTED
                    ? 'Spotted'
                    : status == TextConstants.SPOT_BACK
                        ? 'Spot Back'
                        : 'Fit Buddy',
            textStyle: AppTypography.regular14().copyWith(
              color: status == TextConstants.NOT_SPOTTED
                  ? isVertical
                      ? AppColors.primaryGreen
                      : AppColors.black
                  : AppColors.white,
              fontSize: width == null ? 14.sp : 16.sp,
            ),
          ),
        );
      },
    );
  }
}
