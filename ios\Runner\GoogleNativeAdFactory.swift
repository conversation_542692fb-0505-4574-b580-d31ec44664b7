//
//  GoogleNativeAdFactory.swift
//  Runner
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 10/24/24.
//

import Foundation
import google_mobile_ads

@objc class GoogleNativeAdFactory: NSObject, FLTNativeAdFactory {

    func createNativeAd(_ nativeAd: GADNativeAd, customOptions: [AnyHashable : Any]? = nil) -> GADNativeAdView? {
        let nativeAdView = Bundle.main.loadNibNamed("GoogleNativeAdView", owner: nil, options: nil)?.first as! GADNativeAdView
        
        (nativeAdView.headlineView as? UILabel)?.text = nativeAd.headline
        
        (nativeAdView.advertiserView as? UILabel)?.text = nativeAd.advertiser
        
        (nativeAdView.iconView as? UIImageView)?.image = nativeAd.icon?.image
        
        (nativeAdView.bodyView as? UILabel)?.text = nativeAd.body
        
        nativeAdView.mediaView?.mediaContent = nativeAd.mediaContent
        
//        (nativeAdView.starRatingView as? UIImageView)?.image = imageOfStars(from: nativeAd.starRating)
//        (nativeAdView.storeView as? UILabel)?.text = nativeAd.store
//        (nativeAdView.priceView as? UILabel)?.text = nativeAd.price
//        (nativeAdView.starRatingView as? UIImageView)?.isHidden = true
//        (nativeAdView.storeView as? UILabel)?.isHidden = true
//        (nativeAdView.priceView as? UILabel)?.isHidden = true
        
        
        (nativeAdView.callToActionView as? UIButton)?.setTitle(nativeAd.callToAction, for: .normal)
        
        nativeAdView.callToActionView?.isUserInteractionEnabled = false
        
        // Associate the native ad view with the native ad object. This is
        // required to make the ad clickable.
        nativeAdView.nativeAd = nativeAd
        
        
        return nativeAdView
    }
    
    private func imageOfStars(from starRating: NSDecimalNumber?) -> UIImage? {
        guard let rating = starRating?.doubleValue else {
          return nil
        }
        if rating >= 5 {
          return UIImage(named: "stars_5")
        } else if rating >= 4.5 {
          return UIImage(named: "stars_4_5")
        } else if rating >= 4 {
          return UIImage(named: "stars_4")
        } else if rating >= 3.5 {
          return UIImage(named: "stars_3_5")
        } else {
          return nil
        }
    }
}
