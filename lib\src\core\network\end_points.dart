class API {
  static const String test = 'https://api-stag.fitsomnia.com/api';
  static const String prod = 'https://fakestoreapi.com';
  static const String dev = 'https://api-dev.fitsomnia.com/api';
  static const String website = 'https://fitsomnia.com';

  //TODO: Later use static const String devSocket = 'https://api-dev.fitsomnia.com';
  static const String devSocket =
      'http://api-dev.fitsomnia.com:3000'; // 'http://3.6.68.73:3000';

  static const base = dev; //'http://192.168.0.106:3000/api';

  /// Authentication
  static const registration = "/user-auth/signup/v2/send-otp";
  static const verification = "/user-auth/signup/v2/verify-otp";
  static const String signIn = '/user-auth/sign-in';
  static const String signInWithGoogle = '/user-auth/google/sign-in';
  static const String signInWithFacebook = '/user-auth/facebook/sign-in';
  static const String signInWithApple = '/user-auth/apple/sign-in';
  static const String logout = '/user-auth/logout';

  /// Reset password / forgot password
  static const resetPassword = '/user-auth/forgot-password/v2';
  static const forgotPasswordSendOTP = '/user-auth/forgot-password/v2/send-otp';
  static const identityVerification =
      '/user-auth/forgot-password/v2/verify-otp';

  /// Search
  static const search = '/search';

  /// Story
  static const String createStory = '/story/share';
  static const String myStory = '/story/my-story';
  static const String fetchStories = '/story/fetch-stories';

  static String markStoryAsViewed(
          {required String storyId, required String userId}) =>
      '/story/view/stories/$storyId/users/$userId';
  static const String deleteStory = '/story/my-story/delete';
  static const String storyViewers = '/story';

  /// Planner Note
  static const getNotes = '/planner/notes';
  static const getNoteDetails = '/planner/note/';
  static const createNote = '/planner/note/create';
  static const updateNote = '/planner/note/update';
  static const deleteNote = '/planner/note/delete';

  /// SpotNot
  static const spotNotSuggestedUserList = '/spot/nearest-user-list';

  static String spotMeProfileInfo({required String userId}) =>
      '/user/info/$userId';
  static const String spotProfile = '/spot/profile';

  /// Profile
  static const user = '/user';

  static String profileFeedImages({required String userId}) =>
      '/post/profile-feed-images/$userId';

  static String profileFeedVideos({required String userId}) =>
      '/post/profile-feed-videos/$userId';

  static String blockFitBuddy({required String fitBuddyId}) =>
      '/spot/block-fitBuddy/$fitBuddyId';

  static String unfriendFitBuddy({required String fitBuddyId}) =>
      '/spot/unfriend/$fitBuddyId';
  static const String fitBuddiesList = '/fitBuddies-list';
  static const String timeline = '/post/timeline';

  /// Location
  static const String updateLocation = '/user/update-location';

  /// Spot Not Request Management
  static const spotRequest = '/spot/request';
  static const spotNotRequest = '/spot/not';
  static const spotBack = '/spot/accept-request';

  /// Fit Buddy Status
  static const fitBuddyStatus = '/spot/fitBuddy-status';

  /// Notifications
  static const String notifications = '/notifications';
  static const String spotList = '/spot/list';

  /// Club
  static const String clubs = '/clubs/';
  static const String myClubs = '/clubs/my-clubs';
  static const String nearbyClubs = '/clubs/nearby-clubs';

  static String joinClub(String clubId) => '/clubs/$clubId/join-club';

  static String leaveAndJoinClub(String clubId) =>
      '/clubs/$clubId/leave-and-join-club';

  static String leaveClub(String clubId) => '/clubs/$clubId/leave-club';

  static String peopleFromYourClub({required String id}) =>
      '/clubs/$id/members';

  static String peopleFromNearbyClubs({required String id}) =>
      '/clubs/$id/nearby-club-memberlist';
  static String liveMembersNearMe = '/spot/nearest-user-club-list';

  /// Media
  static const String uploadMedia = '/media/upload-presigned-url';
  static const String uploadSinglePublicMedia = '/media/public/upload/single';

  /// News Feed
  static const String newsFeed = '/post/newsfeed';
  static const String createPost = '/post/create';
  static const String deletePost = '/post/delete';
  static const String updatePost = '/post';

  //// Multireaction post
  static String likePost({
    required String ownerId,
    required String postId,
    required String reactionType,
    required bool isReacting,
  }) =>
      '/post/$postId/multireaction?ownerId=$ownerId&reactionType=$reactionType&isReacting=$isReacting';

  /// Comment
  static String addComment({required String ownerId, required String postId}) =>
      '/post/$postId/comment?ownerId=$ownerId';

  static String getAllComments({
    required String ownerId,
    required String postId,
    required int offset,
    required int limit,
  }) =>
      '/post/$postId/comments?ownerId=$ownerId&offset=$offset&limit=$limit';

  /// Chat
  static const String allUsersList = '/users';
  static const String recentConversation = '/chat/conversation-list';

  /// Group Chat
  static const String createGroupChat = '/group-chat/create-group';
  static const String getGroupList = '/group-chat/conversation-list';

  static String getGroupHistory({required String groupId}) =>
      '/group-chat/history/$groupId';

  static String deleteGroupChatMessage({required String messageId}) =>
      '/group-chat/$messageId';

  static String getGroupParticipants({required String groupId}) =>
      '/group-chat/$groupId/members';

  static String leaveGroupChat({required String groupId}) =>
      '/group-chat/leave/$groupId';

  static String getChatHistory({required String userId}) =>
      '/chat/history/$userId';

  /// fitbot
  static String chatbotHistory({required String userId}) =>
      '/chatbot/history/$userId';
  static const String fitbotQuery = '/chatbot/query';
  static const String chatbotMessageStream = '/chatbot/query/stream';

  static String deleteOneToOneMessage({required String messageId}) =>
      '/chat/$messageId';

  static String addNewMember({
    required String memberId,
    required String groupId,
  }) =>
      '/group-chat/add-member?memberId=$memberId&groupId=$groupId';
  static const String getOnlineBuddies = '/chat/online-buddies';

  /// Training
  static const String trainingExerciseList = '/training/list';
  static const String trainingCategoryList = '/training/category/list';
  static const String muscleGroupList = '/training/muscle-group/list';
  static const String bodyBuildingProgram =
      '/training/body-building-program/list';
  static const String intermediateTrainingList = '/training/intermediate/list';

  /// Diet
  static const String customFoods = '/diet/custom-foods';
  static const String allFoods = '/diet/global-foods';
  static const String activities = '/diet/activities';
  static const String dietPlan = '/diet/diet-plan/';
  static const String dietPlans = '/diet/diet-plans';
  static const String dietStatus = '/diet/diet-status';
  static const String idealCalories = '/diet/ideal-calories/';
  static const String dietHistory = '/diet/diet-history/';
  static const String dietHistoryFood = '/diet/diet-history/food';
  static const String waterConsumption = '/diet/water-consumption/';
  static const String dietHistoryFoodAi = '/diet/diet-history/ai/food';

  /// Settings
  /// Change Email
  static const String changeEmailRequestForOTP = '/user/change-email/send-otp';
  static const String changeEmailVerifyOTP = '/user/change-email/verify-otp';

  /// Change Password
  static const String changePassword = '/user/change-password';

  /// Block List
  static const String blockList = '/spot/block-list';

  static String blockUser(String id) => '/spot/block-fitBuddy/$id';

  static String unBlockUser(String id) => '/spot/unblock-fitBuddy/$id';

  /// Preference (Profile Visibility, Email and Push Notification)
  static const String preference = '/preference';

  /// About
  static const String about = '/about-us';
  static const String aboutUs = '/about';
  static const String privacyPolicy = '/privacy-policy';
  static const String termsOfUse = '/terms-of-service';

  /// Hall of Weight
  static const String monthlyChallenges = '/user/challenges/monthly';
  static const String acceptChallenge = '/user/challenges/accept';
  static const String challengeHistory = '/user/challenges/history';
  static const String currentPoints = '/points';
  static const String uploadChallengeVideo = '/user/challenges/upload-video';
  static const String leaderboard = '/user/challenges/leaderboard';
  static const String pointsHistory = '/user/challenges/points-history';

  /// Fit market
  static const String categories = '/categories';
  static const String products = '/user/products';
  static const String cart = '/cart'; //add-delete item to cart
  static const String updateCart = '/cart/item'; //add/subtract quantity
  static const String addAddress = '/user/add-address';
  static const String addToWishlist = '/wishlist';
  static const String getWishlist = '/wishlist';
  static const String removeFromWishlist = '/wishlist/items';
  static const String orderHistory =
      '/user/orders/list?sortField=orderedDate&sortType=desc';
  static const String orderSummary = '/user/orders/checkout-summary';
  static const String createOrder = '/user/orders/create-order';
  static const String orderDetails = '/user/orders';
  static const String reOrder = '/user/orders/reorder';
  static const String paymentMethods = '/user/orders/available-payment-methods';

  static String updateAddress(String addressId) =>
      '/user/update-address/$addressId';

  static String deleteAddress(String addressId) =>
      '/user/delete-address/$addressId';
  static String validateAddress = '/user/validate-address';

  /// Payment
  static const String makePayment = '/payment/stripe/create-payment';
  static const String makePaymentByPoints = '/payment/points/create-payment';

  /// Review
  static const String review = '/user/order/review';

  /// Post Details
  static const String postDetails = '/post/info';

  /// Subscriptions
  static const String subscriptionPackages = '/user/packages';
  static const String myPackage = '/user/packages/subscribe/my-package';
  static const String subscribe = '/user/packages/subscribe';

  /// Delete Account
  static const String deleteAccount = '/user/delete-account';

  /// poll
  static const String polls = '/user/polls';
  static const String pollById = '/user/poll'; // '/user/{pollId}'

  /// event
  static const String multipleEvents =
      '/events/multiple'; // get multiple events
  static const String eventWithId =
      '/events/single'; // '/events/single/{eventId}'
  static const String joinEvent = '/events/join'; // '/events/join/{eventId}'
  static const String leaveEvent = '/events/leave'; // '/events/leave/{eventId}'
  static const String eventUser = '/events/users'; // get multiple events

  /// coach
  static const String coachCategory = '/user/coach/categories';
  static const String coachSubCategory = '/user/coach/sub-categories';
  static const String coachCategoryMap = '/user/coach/category-map';
  static const String createCoachProfile = '/coach/profile/pending';
  static String updateCoachProfile(String coachId) => '/coach/$coachId/profile';

  // coach program
  static String coachProgramCreate(String coachId) => '/coach/$coachId/program';
  static String coachPrograms(String coachId) => '/coach/$coachId/programs';
  static String coachSingleProgram(String coachId) =>
      '/coach/$coachId/program'; // /api/coach/my/program/{programId}
  static String coachProgramDelete(String coachId) => '/coach/$coachId/program';
  static String coachProgramUpdate(String coachId) => '/coach/$coachId/program';
  static String coachProgramAllEnrollments(String coachId) =>
      '/coach/$coachId/subscriptions';
  static String coachProgramSingleEnrollment(String coachId) =>
      '/coach/$coachId/subscription'; // /api/coach/enrollment/{subscriptionId}
  static String subscriptionToProgram(String coachId, String programId) =>
      '/user/coach/$coachId/subscribe/$programId';
  static String subscriptionPaymentByBkash(String subscriptionId) =>
      '/user/coach/pay/$subscriptionId/bkash';

  // coach dashboard
  static String coachOwnProfile(String coachId) => '/coach/$coachId/profile';
  static String coachOwnPrograms(String coachId) => '/coach/$coachId/programs';
  static String coachProgramByPorgramId(String coachId) =>
      '/coach/$coachId/program';
  static String coachProgramEnrollers(String coachId) =>
      '/coach/$coachId/subscriptions';
  static String coachProgramSingleEnroller(String coachId) =>
      '/coach/$coachId/subscription';
  static String coachProgramSubscriptionCancel(
          String coachId, String subscriptionId) =>
      '/coach/$coachId/subscription/$subscriptionId/cancel';
  static String coachProgramEnrollerHistory(String coachId, String programId) =>
      '/coach/$coachId/program/$programId/subscribers';
  static String coachProgramPaymentHistory(String coachId, String programId) =>
      '/coach/$coachId/program/$programId/payment-history';
  // coach newsfeed
  static const String coachProfiles = '/user/coach/profiles';
  static const String coachProfile = '/user/coach/profile';

  //coach user dashboard
  static const String userSubscriptionHistory =
      '/user/coach/subscription-history';
  static const String userSubscriptions = '/user/coach/subscriptions';
  static const String userSubscriptionById = '/user/coach/subscription';
  static const String refundRequest = '/user/coach/refund';
  static String userSubscriptionCancel(String subscriptionId) =>
      '/user/coach/subscription/$subscriptionId/cancel';
  static String coachIncomeInfo(String coachId) => '/coach/$coachId/income';

  //coached offered programs
  static const String coachOfferedPrograms = '/user/coach/programs';
  static const String coachOfferedSinglePrograms = '/user/coach/program';
  static const String coachOfferedBestPrograms = '/user/coach/best/programs';

  //coach program subscribe
  static const String coachProgramSubscribe = '/user/coach/subscribe';

  //coach rating
  static const String coachProgramSingleRateing = '/user/coach/program/rating';
  static const String coachProgramMultipleRating =
      '/user/coach/program/ratings';

  static const String coachUserProfileStatus = '/user/coach/profile-status';

  // coach profile rating
  static String coachProfileReview(String id) =>
      '/user/coach/profile/rating/$id';
  static String coachProfileReviews(String id) =>
      '/user/coach/profile/ratings/$id';

  //search coach programs
  static String coachProgramFilter = '/user/coach/programs';

  // coach withdraw
  static String coachWithdrawMoney(String coachId) =>
      '/coach/$coachId/withdraw';
  static String coachUpdatePhoneNumber(String coachId) =>
      '/coach/$coachId/withdraw/mobile-number';

  // reward_referral
  static const String weeklyLoginHistory =
      '/user-activity-streak/weekly-streak';
  static const String userCurrentRank = '';
  static const String getRewardPoints = '/user/points';
  static const String referralCode = '/user/referral/referral-codes';
  static const String multipleReferrals = '/user/referral/campaigns';
  static const String referralSingel = '/user/referral/campaign';
  static const String useReferralCode = '/user/referral/join';
  static const String rewardPointHistory = '/user/points-history';
  static const String rewardPointLeaderboard = '/user/leaderboard';

  // daily task
  static const String dailyTask = '/tasks';

  // app version update
  static const String appVersionLatest = '/version/latest';

  // food scanner
  static const String foodScannerAnalyze = '/food-scanner/analyze';
}
