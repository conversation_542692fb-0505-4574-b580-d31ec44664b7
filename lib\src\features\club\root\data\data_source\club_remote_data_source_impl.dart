import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';
import 'package:fitsomnia_app/src/features/club/root/data/data_source/club_remote_data_source.dart';

class ClubRemoteDataSourceImpl implements ClubRemoteDataSource {
  const ClubRemoteDataSourceImpl({required this.restClient});

  final RestClient restClient;

  @override
  Future<Response> myClub() async {
    final response = await restClient.get(
      APIType.PROTECTED,
      API.myClubs,
    );

    return response;
  }

  @override
  Future<Response> nearbyClubsMembers(
    Map<String, dynamic> map,
    String id,
    int? offset,
  ) async {
    final response = await restClient.get(
      APIType.PROTECTED,
      API.peopleFromNearbyClubs(id: id),
      data: map
        ..addEntries(
          [
            if (offset != null) MapEntry("skip", offset),
            const MapEntry("limit", "10")
          ],
        ),
    );

    return response;
  }

  @override
  Future<Response> clubMembers(String id, int? offset) async {
    final response = await restClient.get(
      APIType.PROTECTED,
      API.peopleFromYourClub(id: id),
      data: {
        if (offset != null) "offset": offset,
        "limit": "10",
      },
    );

    return response;
  }

  @override
  Future<Response> findNearbyClubs(
      {double? long, double? lat, int? offset}) async {
    final response = await restClient.get(
      APIType.PROTECTED,
      API.nearbyClubs,
      data: {
        "longitude": long ?? 90.40008,
        "latitude": lat ?? 23.78154,
        "limit": "10",
        if (offset != null) "offset": offset,
      },
    );

    return response;
  }

  @override
  Future<Response> joinClub(String clubId) async {
    final response = await restClient.post(
      APIType.PROTECTED,
      API.joinClub(clubId),
      {},
    );

    return response;
  }

  @override
  Future<Response> leaveAndJoinClub(String clubId) async {
    final response = await restClient.patch(
      APIType.PROTECTED,
      API.leaveAndJoinClub(clubId),
      {},
    );

    return response;
  }

  @override
  Future<Response> leaveClub(String clubId) async {
    final response = await restClient.delete(
      APIType.PROTECTED,
      API.leaveClub(clubId),
    );

    return response;
  }

  @override
  Future<Response> liveMembersNearMe(Map<String, dynamic> query) async {
    final response = await restClient.get(
      APIType.PROTECTED,
      API.liveMembersNearMe,
      data: query,
    );

    return response;
  }
}
