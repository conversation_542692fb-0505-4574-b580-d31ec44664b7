import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppGradientButton extends StatelessWidget {
  const AppGradientButton({
    Key? key,
    required this.title,
    required this.onClick,
    this.width = double.infinity,
    this.height,
    this.startColor,
    this.endColor,
    this.borderRadius = Values.v3,
    this.prefixIcon,
    this.isDisable = false,
    this.style,
  }) : super(key: key);

  final double width;
  final double? height;
  final Color? startColor;
  final Color? endColor;
  final double borderRadius;
  final String title;
  final Widget? prefixIcon;
  final VoidCallback onClick;
  final bool isDisable;
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? Values.v50.h,
      width: width,
      decoration: BoxDecoration(
        gradient: isDisable ? null : LinearGradient(
          colors: [
            startColor ?? AppColors.primaryGreen,
            endColor ?? AppColors.primaryGreen,
          ],
        ),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: ElevatedButton(
        onPressed: isDisable ? null : onClick,
        style: ElevatedButton.styleFrom(
          backgroundColor: isDisable ? AppColors.greyLight: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
        ),
        child: (prefixIcon != null) ? Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            prefixIcon!,
            Text(
              title,
              style: style ?? AppTypography.regular14(
                color: AppColors.white,
              ),
            ),
          ],
        ): Text(
          title,
          style: style ?? AppTypography.regular14(
            color: AppColors.white,
          ),
        ),
      ),
    );
  }
}
