import 'dart:convert';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/domain/entity/chat_list_entity.dart';

ChatListEntity chatListEntityFromJson(String str) =>
    ChatListModel.fromJson(json.decode(str));

class ChatListModel extends ChatListEntity {
  const ChatListModel({
    required super.lastMessageInfo,
    required super.fitBuddyId,
    required super.name,
    required super.image,
  });

  factory ChatListModel.fromJson(Map<String, dynamic> json) => ChatListModel(
        lastMessageInfo: json["lastMessageInfo"] == null
            ? null
            : LastMessageInfo.fromJson(json["lastMessageInfo"]),
        fitBuddyId: json["fitBuddyId"],
        name: json["name"],
        image: json["image"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "lastMessageInfo":
            lastMessageInfo == null ? null : lastMessageInfo!.toJson(),
        "fitBuddyId": fitBuddyId,
        "name": name,
        "image": image,
      };

  ChatListEntity toEntity() => ChatListEntity(
        lastMessageInfo: lastMessageInfo,
        fitBuddyId: fitBuddyId,
        name: name,
        image: image,
      );
}
