part of 'group_members_bloc.dart';

class GroupMembersState extends Equatable {
  @override
  List<Object?> get props => [];
}

class GroupMembersInitial extends GroupMembersState {}

class GroupMembersLoading extends GroupMembersState {}

class GroupMembersSuccess extends GroupMembersState {
  final List<AllUsersEntityWithoutProfile> groupMembersList;

  GroupMembersSuccess({required this.groupMembersList});

  @override
  List<Object?> get props => groupMembersList;
}

class GroupMembersFailure extends GroupMembersState {
  final String errorMessage;

  GroupMembersFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}
