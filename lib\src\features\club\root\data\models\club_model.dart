import 'package:fitsomnia_app/src/features/club/root/domain/entities/club_entity.dart';

class ClubModel extends ClubEntity {
  ClubModel({
    required super.id,
    required super.name,
    required super.image,
    required super.description,
    required super.location,
  });

  factory ClubModel.fromJson(Map<String, dynamic> json) => ClubModel(
    id: json["id"],
    name: json["name"],
    image: ClubImage.fromJson(json["image"]),
    description: json["description"],
    location: ClubLocation.fromJson(json["location"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "image": image.toJson(),
    "description": description,
    "location": location.toJson(),
  };
}
