import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/extensions/extensions.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/pages/coach_program_preview_page.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CoachOfferedProgramViewWidget extends StatefulWidget {
  const CoachOfferedProgramViewWidget({
    super.key,
    required this.programEntity,
    required this.isBestProgram,
  });
  final CoachProgramEntity programEntity;
  final bool isBestProgram;

  @override
  State<CoachOfferedProgramViewWidget> createState() =>
      _CoachOfferedProgramViewWidgetState();
}

class _CoachOfferedProgramViewWidgetState
    extends State<CoachOfferedProgramViewWidget> {
  @override
  Widget build(BuildContext context) {
    return _buildEnrollmentInfoCard();
  }

  _buildEnrollmentInfoCard() {
    return GestureDetector(
      onTap: () {
        //TODO
        _moveToProgramPreviewPage();
      },
      child: Container(
        margin: const EdgeInsets.only(top: Values.v20),
        padding: const EdgeInsets.symmetric(
            vertical: Values.v20, horizontal: Values.v16),
        decoration: BoxDecoration(
            color: (widget.isBestProgram)
                ? UIColors.purple50
                : AppColors.greyscale10,
            border: Border.all(
                color: (widget.isBestProgram)
                    ? UIColors.purple500
                    : AppColors.greyscale100),
            borderRadius: BorderRadius.circular(Values.v10)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProgramTitle(title: widget.programEntity.title),
              ],
            ),
            _buildProgramDescriptionSection(desc: widget.programEntity.desc),
            const SizedBox(
              height: Values.v20,
            ),
            _buildProgramInfoSection(),
            const SizedBox(
              height: Values.v20,
            ),
            _buildProgramSubscribeSection(),
            const SizedBox(
              height: Values.v20,
            ),
            _buildOtherPaymentsSystemInfo(),
          ],
        ),
      ),
    );
  }

  _buildProgramTitle({required String title}) {
    return Expanded(
      child: Text(
        title,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
      ),
    );
  }

  _buildProgramDescriptionSection({required String? desc}) {
    if (desc == null) {
      return const SizedBox.shrink();
    }

    return Text(
      desc,
      style: AppTypography.poppinsMedium14(color: AppColors.greyscale400),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  // _buildProgramInfoSection(
  //     {required DateTime enrollmentDate, required int payment}) {
  //   return Wrap(
  //     children: [
  //       // Text('Total Enrollment: ${programEntity.totalSubscription}'),
  //       _buildProgramInfoText(
  //           'Subscribed On: ',
  //           '${DateFormat.yMMMMd().format(enrollmentDate)}',
  //           AppTypography.poppinsMedium12(color: AppColors.greyscale400),
  //           AppTypography.poppinsMedium14(color: UIColors.primaryGreen950)),
  //       Container(
  //         margin: EdgeInsets.symmetric(horizontal: 5),
  //         height: 20,
  //         width: 2,
  //         color: UIColors.primaryGreen400,
  //       ),
  //       // Text('Total Income: BDT ${350000}'),
  //       _buildProgramInfoText(
  //           '',
  //           'BDT ${payment}',
  //           AppTypography.poppinsMedium12(color: AppColors.greyscale400),
  //           AppTypography.poppinsMedium14(color: UIColors.primaryGreen950)),
  //     ],
  //   );
  // }

  _buildItemSeperator() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 5),
      height: 15,
      width: 2,
      color: UIColors.primaryGreen400,
    );
  }

  _buildProgramInfoSection() {
    List<Widget> supports = [
      _buildInfoText(
          text:
              '${widget.programEntity.durationCount} ${widget.programEntity.durationType.toCapitalFirst()}')
    ];

    for (String eachGuarantee in widget.programEntity.guarantees) {
      supports.add(_buildItemSeperator());
      supports.add(_buildInfoText(text: eachGuarantee));
    }

    return Wrap(
      children: supports,
    );
  }

  _buildInfoText({required String text}) {
    return Text(
      text,
      style: AppTypography.poppinsMedium12(color: UIColors.primaryGreen950),
    );
  }

  _buildProgramInfoText(String infoName, String? infoValue,
      TextStyle infoNameStyle, TextStyle infoValueStyle) {
    if (infoValue == null || infoValue == '') return SizedBox.shrink();

    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          '${(infoName.length != 0) ? '${infoName}:' : ''}',
          style: infoNameStyle,
        ),
        SizedBox(
          width: Values.v5,
        ),
        Flexible(
          child: Wrap(
            children: [
              Text(
                infoValue,
                style: infoValueStyle,
                overflow: TextOverflow.clip,
              ),
            ],
          ),
        )
      ],
    );
  }

  _buildProgramSubscribeSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(flex: 3, child: _buildProgramPriceSection()),
        if (!widget.programEntity.hasSubscription!)
          Flexible(flex: 3, child: _buildSubscribeButtonSection()),
      ],
    );
  }

  _buildProgramPriceSection() {
    return Container(
      width: double.maxFinite,
      color: AppColors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'BDT ${widget.programEntity.payments.first.discountedPrice}',
            style: AppTypography.poppinsBold24(
                color: (widget.isBestProgram)
                    ? UIColors.purple500
                    : UIColors.primary),
          ),
          if (widget.programEntity.payments.first.discountedPrice! <
              widget.programEntity.payments.first.actualPrice!)
            Text('BDT ${widget.programEntity.payments.first.actualPrice}',
                style: TextStyle(
                  color: AppColors.greyscale400,
                  fontSize: 16,
                  fontFamily: FontConstants.poppinsFontFamily,
                  fontWeight: FontWeight.w500,
                  decoration: TextDecoration.lineThrough,
                )),
          // if (widget.programEntity.payments.first.discountedPrice! <=
          //     widget.programEntity.payments.first.actualPrice!) SizedBox(height: Values.v16,),
        ],
      ),
    );
  }

  _buildSubscribeButtonSection() {
    return Button.filled(
      label: 'Subscribe',
      // background: UIColors.purple500,
      // borderColor: UIColors.purple500,
      height: Values.v36,
      onPressed: () {
        Log.debug('subscribe program button pressed');
        _moveToProgramPreviewPage();
      },
      background:
          (widget.isBestProgram) ? UIColors.purple500 : UIColors.primary,
      borderColor:
          (widget.isBestProgram) ? UIColors.purple500 : UIColors.primary,
      textStyle: AppTypography.poppinsRegular16(color: AppColors.white),
    );
  }

  void _moveToProgramPreviewPage() {
    Log.debug('coach program item pressed');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CoachProgramPreviewPage(
          isCreatingCoachProgram: false,
          isUpdateCoachProgram: false,
          isPreviewCoachProgram: true,
          programEntity: widget.programEntity,
        ),
      ),
    );
  }

  _buildOtherPaymentsSystemInfo() {
    if (widget.programEntity.payments.length < 2) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(Values.v5),
        decoration: BoxDecoration(
            color: UIColors.orange50,
            border: Border.all(color: UIColors.orange400),
            borderRadius: BorderRadius.circular(Values.v5)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [Text('Other payment term supported', style: AppTypography.poppinsRegular12(color: UIColors.orange500),)],
        ));
  }
}
