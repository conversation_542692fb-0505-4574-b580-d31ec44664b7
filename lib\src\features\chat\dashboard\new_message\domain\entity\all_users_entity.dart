import 'package:equatable/equatable.dart';

class AllUsersEntity extends Equatable {
  final String id;
  final String name;
  final AllUsersImage? image;

  const AllUsersEntity(
      {required this.id, required this.name, required this.image});

  @override
  List<Object?> get props => [id, name, image];
}

class AllUsersImage {
  AllUsersImage({
    this.profile,
  });

  final String? profile;

  factory AllUsersImage.fromJson(Map<String, dynamic> json) => AllUsersImage(
        profile: json["profile"],
      );

  Map<String, dynamic> toJson() => {
        "profile": profile,
      };
}

class AllUsersEntityWithoutProfile {
  final String id;
  final String name;
  final String? image;

  const AllUsersEntityWithoutProfile(
      {required this.id, required this.name, required this.image});
}
