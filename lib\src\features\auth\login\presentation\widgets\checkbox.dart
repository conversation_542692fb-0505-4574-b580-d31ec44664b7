part of '../pages/login_page.dart';

class _CheckboxBuilder extends StatelessWidget {
  const _CheckboxBuilder({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    LoginBloc loginBloc = context.read<LoginBloc>();

    return ValueListenableBuilder(
      valueListenable: loginBloc.isRememberMeChecked,
      builder: (context, value, _) {
        return Checkbox(
          onChanged: (val) {
            loginBloc.isRememberMeChecked.value = val!;
          },
          value: value,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          activeColor: AppColors.primaryGreen,
          // fillColor: WidgetStateProperty.all(AppColors.primaryGreen),
          checkColor: AppColors.white,
          side: WidgetStateBorderSide.resolveWith(
            (states) {
              return const BorderSide(
                color: AppColors.grey,
              );
            },
          ),
        );
      },
    );
  }
}
