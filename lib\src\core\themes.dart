import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:flutter/material.dart';

ThemeData appTheme() {
  return ThemeData(
    dividerColor: Colors.transparent,
    appBarTheme: const AppBarTheme(
      backgroundColor: UIColors.white,
      elevation: 0,
      centerTitle: false,
      iconTheme: IconThemeData(
        color: UIColors.black,
      ),
    ),
    primaryColor: Colors.green,
    textTheme: TextTheme(
      labelLarge: AppTypography.semiBold14(
        color: AppColors.white,
      ),
    ),
    progressIndicatorTheme: ProgressIndicatorThemeData(
      color: Colors.white,
      circularTrackColor: AppColors.white60,
    ),
    inputDecorationTheme: InputDecorationTheme(
      suffixIconColor: AppColors.primaryGreen,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primaryGreen,
      ),
    ),
    textSelectionTheme: TextSelectionThemeData(
      cursorColor: AppColors.black,
      selectionColor: AppColors.grey50,
      selectionHandleColor: AppColors.grey50,
    ),
    checkboxTheme: CheckboxThemeData(
      visualDensity: const VisualDensity(horizontal: -4),
      fillColor: WidgetStateColor.resolveWith(
        (states) => AppColors.white,
      ),
      checkColor: WidgetStateColor.resolveWith(
        (states) => AppColors.greyDark,
      ),
      side: WidgetStateBorderSide.resolveWith(
        (states) => const BorderSide(
          width: 1.0,
          color: AppColors.white,
        ),
      ),
    ), colorScheme: ColorScheme.fromSwatch(primarySwatch: Colors.green).copyWith(surface: UIColors.background),
  );
}

const MaterialColor primarySwatchWhite = MaterialColor(
  0xFFFFFFFF,
  <int, Color>{
    50: Color(0xFFFFFFFF),
    100: Color(0xFFFFFFFF),
    200: Color(0xFFFFFFFF),
    300: Color(0xFFFFFFFF),
    400: Color(0xFFFFFFFF),
    500: Color(0xFFFFFFFF),
    600: Color(0xFFFFFFFF),
    700: Color(0xFFFFFFFF),
    800: Color(0xFFFFFFFF),
    900: Color(0xFFFFFFFF),
  },
);
