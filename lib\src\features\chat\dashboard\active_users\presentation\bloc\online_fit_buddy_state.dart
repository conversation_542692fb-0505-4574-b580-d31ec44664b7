part of 'online_fit_buddy_bloc.dart';

class OnlineFitBuddyState extends Equatable {
  @override
  List<Object?> get props => [];
}

class OnlineFitBuddyInitial extends OnlineFitBuddyState {}

class OnlineFitBuddyLoading extends OnlineFitBuddyState {}

class OnlineFitBuddySuccess extends OnlineFitBuddyState {
  final List<ActiveUserEntity> activeUserEntity;

  OnlineFitBuddySuccess({required this.activeUserEntity});

  @override
  List<Object?> get props => activeUserEntity;
}

class OnlineFitBuddyFailure extends OnlineFitBuddyState {
  final String errorMessage;

  OnlineFitBuddyFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}
