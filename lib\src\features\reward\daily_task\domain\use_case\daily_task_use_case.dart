import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/domain/entities/daily_task.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/domain/repositories/daily_task_repository.dart';

class DailyTaskUseCase {
  final DailyTaskRepository repository;

  DailyTaskUseCase({required this.repository});

  Future<Either<ErrorModel, List<DailyTask>>> getDailyTasks(
      {required int? offset,
      required int? limit,
      required String? taskTypeFilter,
      required String? taskStatusFilter}) async {
    return await repository.getDailyTasks(
        offset: offset,
        limit: limit,
        taskTypeFilter: taskTypeFilter,
        taskStatusFilter: taskStatusFilter);
  }
}
