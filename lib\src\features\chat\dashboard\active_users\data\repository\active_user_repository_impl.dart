import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/data/data_source/active_user_data_source.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/data/model/active_user_model.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/domain/entity/active_user_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/domain/repository/active_user_repository.dart';

class ActiveUserRepositoryImpl implements ActiveUserRepository {
  ActiveUserRepositoryImpl({required this.activeUserDataSource});

  final ActiveUserDataSource activeUserDataSource;

  @override
  Future<Either<String, List<ActiveUserEntity>>> getActiveFitBuddies({
    int? limit,
    int? offset,
  }) async {
    try {
      final Response response = await activeUserDataSource.getActiveFitBuddies(
        limit: limit,
        offset: offset,
      );
      final data = response.data;

      List<ActiveUserEntity> activeUsers = data
          .map<ActiveUserEntity>((users) => ActiveUsersModel.fromJson(users))
          .toList();

      return Right(activeUsers);
    } catch (e) {
      Log.error(e.toString());

      return Left(e.toString());
    }
  }
}
