import 'dart:async';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/pages/coach_program_tab_view.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/pages/coach_tab_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CoachSearchPage extends StatefulWidget {
  const CoachSearchPage({super.key});

  @override
  State<CoachSearchPage> createState() => _CoachSearchPageState();
}

class _CoachSearchPageState extends State<CoachSearchPage>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  ValueNotifier<int> selectedTabIndex = ValueNotifier(0);

  @override
  void initState() {
    super.initState();

    _tabController = TabController(
      length: 2,
      initialIndex: selectedTabIndex.value,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SafeArea(
        child: Column(
          children: [
            // _buildPageHeader(),
            _buildSearchSection(),
            TabBar(
              controller: _tabController,
              indicatorColor: AppColors.primaryGreen,
              labelColor: AppColors.primaryGreen,
              unselectedLabelColor: AppColors.black,
              onTap: (int index) {
                selectedTabIndex.value = index;
              },
              tabs: const [
                Tab(text: "Coach"),
                Tab(text: "Program"),
              ],
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: const [
                  CoachTabView(),
                  CoachProgramTabView(),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        'Search',
        style: TextStyle(
          color: UIColors.primaryGreen950,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: true,
      elevation: 0,
    );
  }

  _buildSearchSection() {
    return _buildSearchBar();
  }

  TextEditingController _searchTextController = TextEditingController();
  Timer? _timer;

  _buildSearchBar() {
    return ValueListenableBuilder(
      valueListenable: selectedTabIndex,
      builder: (context, value, child) {
        String hintTextPlaceHolder = value == 0
            ? "Search Coach"
            :  "Search Coach Program";
        
        _searchTextController.text = '';
        

        return Padding(
          padding: const EdgeInsets.only(left: Values.v20, right: Values.v20),
          child: Container(
            // padding: EdgeInsets.all(Values.v20),
            decoration: BoxDecoration(
                border: Border.all(color: AppColors.greyscale50),
                borderRadius: BorderRadius.circular(Values.v10)),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  width: 10,
                ),
                Icon(Icons.search_rounded),
                // Text('Search coach', style: AppTypography.poppinsRegular16(color: AppColors.greyscale400),),
                Expanded(
                  child: TextFormField(
                    onChanged: (_) {
                      Log.debug('searching...');
                      _onChanged();
                    },
                    controller: _searchTextController,
                    decoration: InputDecoration(
                      // contentPadding:
                      //     const EdgeInsets.fromLTRB(12.0, 0.0, 12.0, 12.0),
                      border: InputBorder.none,
                      hintText: hintTextPlaceHolder,
                      hintStyle: AppTypography.poppinsRegular16(
                          color: AppColors.greyscale400),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }
    );
  }

  _buildPageHeader() {
    return Padding(
      padding: const EdgeInsets.only(top: Values.v20, left: Values.v20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCoachListTitle(),
          Text(
            'Get to know your coach and subscibe for training',
            style: AppTypography.poppinsMedium14(color: AppColors.greyscale400),
          ),
          const SizedBox(
            height: 24,
          ),
        ],
      ),
    );
  }

  Widget _buildCoachListTitle() {
    return RichText(
      text: TextSpan(
        style: AppTypography.poppinsSemiBold24(color: UIColors.primaryGreen950),
        children: [
          TextSpan(
            text: 'Visit our ',
            style: AppTypography.poppinsSemiBold24(color: UIColors.primary),
          ),
          const TextSpan(text: 'coaches ! '),
        ],
      ),
    );
  }

  final Duration _debounceDuration = const Duration(milliseconds: 500);
  void _onChanged() {
    Log.debug('${_searchTextController.text}');
    if (_timer?.isActive ?? false) {
      _timer?.cancel();
    }
    _timer = Timer(_debounceDuration, () {
      _executeSearch();
    });
  }

  void _executeSearch() {
    if (_searchTextController.text != '')
      if(selectedTabIndex.value == 0) {
        context.read<CoachNewsfeedBloc>().add(GetCoachProfilesEvent(
          filterType: CoachProfileFilterType.NAME,
          namePrefix: _searchTextController.text));
      } else {
        context.read<CoachNewsfeedBloc>().add(GetCoachProgramsEvent(
          filterType: CoachProfileFilterType.NAME,
          namePrefix: _searchTextController.text));
      }
      
  }

  _buildSearchResultSection() {
    return TabBarView(
      controller: _tabController,
      children: const [
        CoachTabView(),
        CoachProgramTabView(),
      ],
    );
  }
}
