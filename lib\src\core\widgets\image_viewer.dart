import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';

class ImageViewer extends StatelessWidget {
  const ImageViewer({Key? key, required this.image}) : super(key: key);

  final String image;

  @override
  Widget build(BuildContext context) {
    String cacheKeyString = image.split("?")[0];

    return Scaffold(
      backgroundColor: AppColors.alto,
      appBar: buildAppBar(context),
      body: Center(
        child: Hero(
          tag: cacheKeyString,
          child: PhotoView(
            backgroundDecoration: BoxDecoration(
              color: AppColors.alto,
            ),
            imageProvider: NetworkImage(
              image,
            ),
            minScale: PhotoViewComputedScale.contained,
          ),
        ),
      ),
    );
  }

  AppBar buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.alto,
      elevation: 0,
      automaticallyImplyLeading: false,
      actions: [
        IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: Icon(
            Icons.close,
            color: AppColors.darkChat,
          ),
        )
      ],
    );
  }
}

class ImageAssetViewer extends StatelessWidget {
  const ImageAssetViewer({Key? key, required this.image}) : super(key: key);

  final String image;

  @override
  Widget build(BuildContext context) {
    String cacheKeyString = image.split("?")[0];

    return Scaffold(
      backgroundColor: AppColors.alto,
      appBar: buildAppBar(context),
      body: Center(
        child: Hero(
          tag: cacheKeyString,
          child: PhotoView(
            backgroundDecoration: BoxDecoration(
              color: AppColors.alto,
            ),
            imageProvider: Image.asset(image).image,
            minScale: PhotoViewComputedScale.contained,
          ),
        ),
      ),
    );
  }

  AppBar buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.alto,
      elevation: 0,
      automaticallyImplyLeading: false,
      actions: [
        IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: Icon(
            Icons.close,
            color: AppColors.darkChat,
          ),
        )
      ],
    );
  }
}
