import 'package:fitsomnia_app/src/features/reward/referrals/domain/entity/referral_data_entity.dart';

class ReferralDataModel extends ReferralDataEntity {
  ReferralDataModel({
    required super.referralId,
    required super.referralTitle,
    required super.referralDesc,
    required super.totalPoint,
    required super.totalAmount,
    required super.referralCode,
    required super.referralRules,
  });

  factory ReferralDataModel.fromJson(Map<String, dynamic> json) {
    return ReferralDataModel(
        referralId: json['id'],
        referralTitle: json['name'] ?? '',
        referralDesc: json['description'] ?? '',
        totalPoint: json['maxRewardsTotal'] ?? 0,
        totalAmount: json['rewardAmount'] ?? 0,
        referralCode: 'test-code',
        referralRules: (json['rules'] == null)
            ? []
            : List<String>.from(json['rules']!.map((x) => x.toString())));
  }

  ReferralDataEntity toEntity() {
    return ReferralDataEntity(
        referralId: referralId,
        referralTitle: referralTitle,
        referralDesc: referralDesc,
        totalPoint: totalPoint,
        totalAmount: totalAmount,
        referralCode: referralCode,
        referralRules: referralRules);
  }
}
