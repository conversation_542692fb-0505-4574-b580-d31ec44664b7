import 'dart:io';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/extensions/extensions.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/image_picker/image_picker_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/image_upload/image_upload_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/bloc/coach_profile_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CoachProfileFileUploadWidget extends StatefulWidget {
  const CoachProfileFileUploadWidget(
      {Key? key,
      required this.featureName,
      required this.fieldName,
      required this.title,
      this.initialFiles})
      : super(key: key);
  final String featureName;
  final String fieldName;
  final String title;
  final List<CoachMediaFile>? initialFiles;

  @override
  State<CoachProfileFileUploadWidget> createState() =>
      _CoachProfileFileUploadWidgetState();
}

class _CoachProfileFileUploadWidgetState
    extends State<CoachProfileFileUploadWidget> {
  List<CoachMediaFile> files = [];
  String? fileType;
  String? filePath;

  @override
  void initState() {
    super.initState();
    Log.debug('upload widget init');
    files = widget.initialFiles ?? [];
    _uploadInitialFiles();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<ImagePickerBloc, ImagePickerState>(
          listener: (context, state) {
            if (state is ImagePickerSuccess) {
              if (state.featureName == widget.featureName &&
                  state.fieldName == widget.fieldName) {
                fileType = state.image.getMediaType(); // video, image, audio
                filePath = state.image.path;
                Log.debug('path: $filePath type: $fileType');

                _onImagePickerSuccessState(context, state);
              }

              // upload file to server
            }

            if (state is ImagePickerFailure) {
              if (state.featureName == widget.featureName &&
                  state.fieldName == widget.fieldName) {
                AppToast.showToast(
                    message: state.errorMessage, gravity: ToastGravity.BOTTOM);
              }
            }
          },
        ),
        BlocListener<ImageUploadBloc, ImageUploadState>(
          listener: (context, state) {
            if (state is ImageUploadSuccess) {
              if (state.featureName == widget.featureName &&
                  state.fieldName == widget.fieldName) {
                Log.debug(state.filePath);
                Log.debug(state.imageUrl);

                setState(() {
                  files.add(CoachMediaFile(
                      url: state.imageUrl, mediaType: fileType ?? 'image'));

                  if (widget.fieldName == 'indentification') {
                    BlocProvider.of<CoachProfileBloc>(context)
                        .add(UpdateCoachIdentificationDocLocally(files: files));
                  }
                  if (widget.fieldName == 'credential') {
                    BlocProvider.of<CoachProfileBloc>(context).add(
                        UpdateCoachCredentialDocLocally(credentials: files));
                  }

                  if (widget.fieldName == 'coach_profile') {
                    BlocProvider.of<CoachProfileBloc>(context).add(
                        UpdateCoachProfilePictureLocally(
                            profilePictures: files));
                  }
                });
              }
            }

            if (state is ImageUploadFailure) {
              if (state.featureName == widget.featureName &&
                  state.fieldName == widget.fieldName) {
                AppToast.showToast(
                    message: state.errorMessage, gravity: ToastGravity.BOTTOM);
              }
            }
          },
        ),
      ],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle(),
          SizedBox(
            height: 5,
          ),
          _buildUploadButton(),
          SizedBox(
            height: 5,
          ),
          _buildUploadedFilePreviewSection(),
        ],
      ),
    );
  }

  _buildTitle() {
    return RichText(
      text: TextSpan(
        text: widget.title,
        style: AppTypography.poppinsRegular16(color: UIColors.primaryGreen950),
        children: const <TextSpan>[
          TextSpan(
            text: '*',
            style: TextStyle(color: Colors.red),
          ),
        ],
      ),
    );
  }

  _buildUploadButton() {
    return InkWell(
      child: Container(
        height: 60,
        decoration: BoxDecoration(
            border: Border.all(color: AppColors.greyscale100),
            borderRadius: BorderRadius.circular(10)),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.all(4.0),
              child: Icon(Icons.file_upload_outlined),
            ),
            Text(
              'Upload here',
              style: AppTypography.poppinsRegular16(
                  color: UIColors.primaryGreen950),
            ),
          ],
        ),
      ),
      onTap: () {
        Log.debug('pick image from gallery');
        if (widget.fieldName == 'indentification') {
          if (BlocProvider.of<CoachProfileBloc>(context)
                  .identificationFiles
                  .length >=
              5) {
            AppToast.showToast(message: 'Maximum 5 Images can be uploaded');

            return;
          }
        }
        if (widget.fieldName == 'credential') {
          if (BlocProvider.of<CoachProfileBloc>(context)
                  .credentialFiles
                  .length >=
              5) {
            AppToast.showToast(message: 'Maximum 5 Images can be uploaded');

            return;
          }
        }
        if (widget.fieldName == 'coach_profile') {
          if (BlocProvider.of<CoachProfileBloc>(context)
                  .profilePictures
                  .length >=
              5) {
            AppToast.showToast(message: 'Maximum 5 Images can be uploaded');

            return;
          }
        }

        BlocProvider.of<ImagePickerBloc>(context).add(ImagePickerEvent(
          featureName: widget.featureName,
          fieldName: widget.fieldName,
        ));
      },
    );
  }

  _buildUploadedFilePreviewSection() {
    if (files.isEmpty) return const SizedBox.shrink();

    return Container(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: files.length,
        itemBuilder: (context, index) {
          return _buildFileView(path: files[index].url, index: index);
        },
      ),
    );
  }

  _buildFileView({required String? path, required int index}) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          // color: AppColors.alto,
        ),
        child: (path == null || path == '')
            ? const Icon(
                Icons.add,
                size: 60,
                color: AppColors.grey,
              )
            : Container(
                // width: 100,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Image.network(
                    //   path,
                    //   width: 60,
                    //   height: 60,
                    //   fit: BoxFit.cover,
                    //   loadingBuilder: (context, child, loadingProgress) {
                    //     if (loadingProgress == null) return child;

                    //     return const SizedBox(
                    //       width: 60,
                    //       height: 60,
                    //       child: Center(
                    //         child: CircularProgressIndicator.adaptive(
                    //           valueColor:
                    //               AlwaysStoppedAnimation(UIColors.primary),
                    //         ),
                    //       ),
                    //     );
                    //   },
                    // ),
                    ImageContainer.rectangularImage(
                      image: path,
                      width: Values.v60,
                      height: Values.v60,
                      cornerRadius: Values.v10,
                      useOriginal: false,
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _fileDeleteIcon(index: index),
                      ],
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  _fileDeleteIcon({required int index}) {
    return InkWell(
      onTap: () {
        Log.debug('delete file: $index');
        files.removeAt(index);
        if (widget.fieldName == 'indentification') {
          BlocProvider.of<CoachProfileBloc>(context)
              .add(UpdateCoachIdentificationDocLocally(files: files));
        }
        if (widget.fieldName == 'credential') {
          BlocProvider.of<CoachProfileBloc>(context)
              .add(UpdateCoachCredentialDocLocally(credentials: files));
        }

        if (widget.fieldName == 'coach_profile') {
          BlocProvider.of<CoachProfileBloc>(context)
              .add(UpdateCoachProfilePictureLocally(profilePictures: files));
        }

        setState(() {
          '';
        });
      },
      child: const Icon(
        Icons.close_rounded,
        size: 20,
      ),
    );
  }

  void _onImagePickerSuccessState(
    BuildContext context,
    ImagePickerSuccess state,
  ) {
    context.read<ImageUploadBloc>().add(
          ImageUploadEvent(
            file: File(state.image.path),
            filePath: state.image.path,
            featureName: widget.featureName,
            fieldName: widget.fieldName,
          ),
        );
  }

  _uploadInitialFiles() {
    if (widget.fieldName == 'indentification') {
      BlocProvider.of<CoachProfileBloc>(context).add(
          UpdateCoachIdentificationDocLocally(
              files: widget.initialFiles ?? []));
    }
    if (widget.fieldName == 'credential') {
      BlocProvider.of<CoachProfileBloc>(context).add(
          UpdateCoachCredentialDocLocally(
              credentials: widget.initialFiles ?? []));
    }

    if (widget.fieldName == 'coach_profile') {
      BlocProvider.of<CoachProfileBloc>(context).add(
          UpdateCoachProfilePictureLocally(
              profilePictures: widget.initialFiles ?? []));
    }
  }
}
