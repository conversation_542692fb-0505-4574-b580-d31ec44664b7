import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/entity/referral_code_entity.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/entity/referral_data_entity.dart';

abstract class ReferralRepository {
  Future<Either<ErrorModel, List<ReferralDataEntity>>> getReferrals();
  Future<Either<ErrorModel, ReferralDataEntity>> getReferralById({required String referralId});
  Future<Either<ErrorModel, ReferralCodeEntity>> getReferralCode(
      {required String userId, required String referralId});
  Future<Either<ErrorModel, ReferralDataEntity>> useReferralCode(
      {required String userId, required String referralCode});
}

