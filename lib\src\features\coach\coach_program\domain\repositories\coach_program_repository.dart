import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/program_subscription_fee_payment_info.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';

abstract class CoachProgramRepository {
  Future<Either<ErrorModel, CoachProgramEntity>> createCoachProgram({required String coachId, required CoachProgramEntity programEntity});
  Future<Either<ErrorModel, CoachProgramEntity>> updateCoachProgram({required String coachId, required String programId, required CoachProgramEntity programEntity});
  Future<Either<ErrorModel, CoachProgramEntity>> getCoachProgramById({required String coachId, required String programId});
  Future<Either<ErrorModel, CoachProgramEntity>> deleteCoachProgramById({required String coachId, required String programId});
  Future<Either<ErrorModel, CoachProgramEnrollmentEntity>> subscribeProgram({required String coachId, required String programId});
  Future<Either<ErrorModel, ProgramSubscriptionFeePaymentInfo >> paySubscriptionFee({required String subscriptionId, required String paymentTerm});
}