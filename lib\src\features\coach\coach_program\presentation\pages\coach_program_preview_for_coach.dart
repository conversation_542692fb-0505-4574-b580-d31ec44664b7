import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/extensions/extensions.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/page/coach_single_program_enrollment_history.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/page/coach_single_program_payment_history.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/pages/coach_program_checkout_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/page/coach_registration_introduction_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/widget/process_steps_timeline_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/model/coach_program_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/bloc/coach_program_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/pages/create_coach_program_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/widgets/program_preview_price_list_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/presentation/widgets/coach_program_reviews_widget.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/bloc/dashboard_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CoachProgramPreviewForCoach extends StatefulWidget {
  const CoachProgramPreviewForCoach({
    super.key,
    required this.programId,
    required this.coachId,
  });
  final String programId;
  final String coachId;

  @override
  State<CoachProgramPreviewForCoach> createState() =>
      _CoachProgramPreviewForCoachState();
}

class _CoachProgramPreviewForCoachState
    extends State<CoachProgramPreviewForCoach> {
  CoachProgramEntity? _programEntity;
  CoachEntity? _coachEntity;
  CoachProgramPaymentInfo? selectedPaymentType;

  @override
  void initState() {
    super.initState();

    BlocProvider.of<CoachNewsfeedBloc>(context)
        .add(GetSingleCoachProfileEvent(coachId: widget.coachId));

    BlocProvider.of<CoachBloc>(context)
        .add(GetCoachOfferedSingleProgramDetailForCoach(coachId: widget.coachId, programId: widget.programId));
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<CoachBloc, CoachState>(
          listener: (context, state) {
            if (state is GetCoachOfferedSingleProgramSuccess) {
              setState(() {
                _programEntity = state.programs;
              });
            }

            if (state is GetCoachOfferedSingleProgramFail) {
              Log.debug('get coach single program failed');
              AppToast.showToast(message: 'Program details not found');
            }
          },
        ),
        BlocListener<CoachNewsfeedBloc, CoachNewsfeedState>(
            listener: (context, state) {
          if (state is GetSingleCoachProfileSuccess) {
            Log.debug('get coach profile success');
            setState(() {
              _coachEntity = state.coachProfile;
            });
          }
          if (state is GetSingleCoachProfileFail) {
            Log.debug('get coach profile fail');
          }
        })
      ],
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Program Overview'),
          centerTitle: true,
          titleTextStyle:
              AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
        ),
        body: SafeArea(
          child: (_programEntity == null || _coachEntity == null)
              ? Center(
                  child: CircularProgressIndicator(
                    color: UIColors.primary,
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Stack(
                    children: [
                      _createProgramDetailsInfoSection(),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // _buildProgramPriceSection(),
                          _buildProgramAvailablePriceSection(),
                          _createProgramEditButtonSection(),
                        ],
                      )
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  _createProgramDetailsInfoSection() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildEnrollmentTotalInfoSection(),
          _buildProgramTitle(),
          _builCoachInfoSection(),
          _buildProgramInfoSection(),
          _buildProgramFeatureInfoSection(),
          _buildProgramProcessSection(),
          _buildProgramReviewSection(),
          const SizedBox(
            height: 300,
          ),
        ],
      ),
    );
  }

  _builCoachInfoSection() {
    return (_coachEntity == null)
        ? const SizedBox.shrink()
        : Padding(
            padding: const EdgeInsets.symmetric(
                vertical: Values.v20, horizontal: Values.v20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _buildCoachProfileImage(),
                SizedBox(
                  width: Values.v5,
                ),
                _buildCoachTotalRatingSection(),
              ],
            ),
          );
  }

  _buildCoachTotalRatingSection() {
    ///|| _coachEntity!.reviewCount! <= 0
    if (_coachEntity!.reviewCount == null) {
      return SizedBox.shrink();
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.star,
          color: UIColors.primaryGreen400,
        ),
        SizedBox(
          width: Values.v3,
        ),
        Text(
          '${_coachEntity!.currentRating ?? 0}',
          style:
              AppTypography.poppinsSemiBold16(color: UIColors.primaryGreen950),
        ),
        Text(
          '(${_coachEntity!.reviewCount} Reviews)',
          style: AppTypography.poppinsRegular14(color: AppColors.greyscale400),
        )
      ],
    );
  }

  _buildCoachProfileImage() {
    if (_coachEntity!.media == null || _coachEntity!.media!.isEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(Values.v100),
        child: SizedBox(
          height: Values.v32,
          width: Values.v32,
          child: Image.asset(
            Assets.spotMeNoImage,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    if (_coachEntity!.media!.first.url == 'string') {
      return ClipRRect(
        borderRadius: BorderRadius.circular(Values.v100),
        child: SizedBox(
          height: Values.v32,
          width: Values.v32,
          child: Image.asset(
            Assets.spotMeNoImage,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    return ImageContainer.circularImage(
      image: _coachEntity!.media!.first.url,
      radius: Values.v16,
      showBorder: false,
    );
  }

  _buildProgramTitle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Flexible(
          child: Text(
            '${_programEntity!.title}',
            maxLines: 2,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            style: AppTypography.poppinsBold24(color: UIColors.primaryGreen950),
          ),
        ),
      ],
    );
  }

  _buildProgramFeatureInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('What You\'ll Get'),
        Column(
          children: _programEntity!.features.map<Widget>((feature) {
            return _buildProgramFeature(
                feature.featureTitle, feature.featureDescription);
          }).toList(),
        ),
      ],
    );
  }

  _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(
        top: 1,
      ),
      child: Text(
        title,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        style: AppTypography.poppinsSemiBold20(color: UIColors.primary),
      ),
    );
  }

  _buildProgramFeature(String featureTitle, String featureDescription) {
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // const Icon(
          //   Icons.check_circle_outline_rounded,
          //   color: UIColors.primary,
          // ),

          Padding(
            padding: const EdgeInsets.all(4.0),
            child: Image.asset(
              Assets.coachProgramOfferIcon,
              height: 20,
              width: 20,
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Expanded(
              child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFeaturetHeader(featureTitle),
              _buildFeatureBody(featureDescription),
            ],
          )),
        ],
      ),
    );
  }

  _buildFeaturetHeader(String benefitName) {
    return Text(
      benefitName,
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
      style: AppTypography.poppinsSemiBold16(color: UIColors.primaryGreen950),
    );
  }

  _buildFeatureBody(String desc) {
    return Text(
      desc,
      style: AppTypography.poppinsSemiBold14(color: AppColors.greyscale400),
    );
  }

  _buildProgramProcessSection() {
    return ProcessStepsTimelineWidget(
      title: coachProgramProcessTitle,
      steps: coachProgramSteps,
    );
  }

  _buildProgramAvailablePriceSection() {
    return ProgramPreviewPriceListWidget(
      programEntity: _programEntity!,
      callback: (paymentType) {
        selectedPaymentType = paymentType; // student selected payment type
      },
    );
  }

  _createProgramEditButtonSection() {
    return Container(
      color: AppColors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Button.filled(
              borderColor: AppColors.white,
              label: 'Edit Program',
              onPressed: () {
                Log.debug('coach program edit');
                Navigator.of(context).push(MaterialPageRoute(
                    builder: (_) => CreateCoachProgramPage(
                          programId: widget.programId,
                          isCreateProgram: false,
                        )));
              },
              disable: (_programEntity!.totalSubscription! > 0),
            ),
          ),
        ],
      ),
    );
  }

  _buildProgramReviewSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInfoSectionHeader(Assets.spotnotProfileBioSvg, 'Ratings'),
        SizedBox(
          height: Values.v16.h,
        ),
        CoachProgramReviewsWidget(programId: widget.programId),
      ],
    );
  }

  _buildInfoSectionHeader(String icon, String title) {
    return Row(
      children: [
        Container(
          height: Values.v36.h,
          width: Values.v36.w,
          decoration: BoxDecoration(
            color: UIColors.primaryGreen100,
            borderRadius: BorderRadius.circular(Values.v36.r),
          ),
          child: Center(
            child: SvgPicture.asset(
              icon,
              height: Values.v18.h,
              width: Values.v18.w,
            ),
          ),
        ),
        SizedBox(
          width: Values.v10.w,
        ),
        Text(
          title,
          style:
              AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen600),
        ),
      ],
    );
  }

  _buildProgramInfoSection() {
    List<Widget> supports = [
      _buildInfoText(
          text:
              '${_programEntity!.durationCount} ${_programEntity!.durationType.toCapitalFirst()}')
    ];

    for (String eachGuarantee in _programEntity!.guarantees) {
      supports.add(_buildItemSeperator());
      supports.add(_buildInfoText(text: eachGuarantee));
    }

    return Padding(
      padding: EdgeInsets.only(
          top: Values.v10,
          bottom: Values.v40,
          left: Values.v20,
          right: Values.v20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: Wrap(
              alignment: WrapAlignment.center,
              runAlignment: WrapAlignment.center,
              crossAxisAlignment: WrapCrossAlignment.center,
              runSpacing: Values.v10,
              children: supports,
            ),
          ),
        ],
      ),
    );
  }

  _buildItemSeperator() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 5),
      height: 15,
      width: 2,
      color: UIColors.primaryGreen400,
    );
  }

  _buildInfoText({required String text}) {
    return Text(
      textAlign: TextAlign.center,
      text,
      style: AppTypography.poppinsMedium14(color: UIColors.primaryGreen950),
    );
  }

  void _navigateToCoachFeatureDashboard() {
    // move to coach featue page
    Navigator.of(context).popUntil((route) => route.isFirst);
    context
        .read<DashboardCubit>()
        .changePage(2); // coach featue position is 2 in nav bar
  }

  _buildEnrollmentTotalInfoSection() {
    return Padding(
      padding: EdgeInsets.only(bottom: Values.v20),
      child: Row(
        children: [
          Expanded(flex: 1, child: _buildTotalEnrollmenCard()),
          const SizedBox(
            width: Values.v10,
          ),
          Expanded(flex: 1, child: _buildTotalPaymentCard()),
        ],
      ),
    );
  }

  _buildTotalPaymentCard() {
    return Container(
      padding: EdgeInsets.all(5),
      decoration: BoxDecoration(
          color: UIColors.skyBlue50,
          border: Border.all(color: UIColors.skyBlue500),
          borderRadius: BorderRadius.circular(Values.v10)),
      child: Column(
        children: [
          // Padding(
          //   padding: const EdgeInsets.all(10),
          //   child: Image.asset(
          //     Assets.coachProgramPaymentImg,
          //     height: 40,
          //     width: 40,
          //   ),
          // ),
          Text('Total Income',
              style: AppTypography.poppinsMedium12(
                  color: UIColors.primaryGreen950)),
          Text(
            _programEntity!.totalIncome.toString(),
            style: AppTypography.poppinsSemiBold20(color: UIColors.skyBlue500),
          ),
          IconButton(
            onPressed: () {
              Log.debug('payment history');
              _navigateToProgramPaymentHistory();
            },
            icon: Container(
              // margin: EdgeInsets.all(Values.v5),
              padding: const EdgeInsets.all(Values.v5),
              decoration: BoxDecoration(
                  color: UIColors.purple500,
                  borderRadius: BorderRadius.circular(Values.v100)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Payment History',
                    style:
                        AppTypography.poppinsSemiBold14(color: AppColors.white),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  _buildTotalEnrollmenCard() {
    return Container(
      padding: EdgeInsets.all(5),
      decoration: BoxDecoration(
          color: UIColors.orange50,
          border: Border.all(color: UIColors.orange500),
          borderRadius: BorderRadius.circular(Values.v10)),
      child: Column(
        children: [
          // Padding(
          //   padding: const EdgeInsets.all(10),
          //   child: Image.asset(
          //     Assets.coachProgramEnrollmentImg,
          //     height: 40,
          //     width: 40,
          //   ),
          // ),
          Text('Total Enrollment',
              style: AppTypography.poppinsMedium12(
                  color: UIColors.primaryGreen950)),
          Text(
            _programEntity!.totalSubscription!.toString(),
            style: AppTypography.poppinsSemiBold20(color: UIColors.orange500),
          ),
          IconButton(
            onPressed: () {
              Log.debug('view enrollers');
              _navigateToProgramEnrollerHistory();
            },
            icon: Container(
              // margin: EdgeInsets.all(Values.v5),
              padding: const EdgeInsets.all(Values.v5),
              decoration: BoxDecoration(
                  color: UIColors.limeGreen500,
                  borderRadius: BorderRadius.circular(Values.v100)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'View Enrollers',
                    style:
                        AppTypography.poppinsSemiBold14(color: AppColors.white),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  void _navigateToProgramEnrollerHistory() {
    Future.delayed(const Duration(milliseconds: 300), () {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (_) => CoachSingleProgramEnrollmentHistory(
            coachId: widget.coachId,
            programId: widget.programId,
          ),
        ),
      );
    });
  }

  void _navigateToProgramPaymentHistory() {
    Future.delayed(const Duration(milliseconds: 300), () {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (_) => CoachSingleProgramPaymentHistory(
            coachId: widget.coachId,
            programId: widget.programId,
          ),
        ),
      );
    });
  }
}

String coachProgramProcessTitle = 'How It Works?';
List<CoachRegistrationStep> coachProgramSteps = [
  CoachRegistrationStep(stepDesc: 'You enroll in a package of your choice'),
  CoachRegistrationStep(
      stepDesc:
          'You fill in your additional key details like food preferences, preferred time to contact, any medical issues etc'),
  CoachRegistrationStep(
      stepDesc: 'The Coach calls you within 24 hours at your preferred time'),
  CoachRegistrationStep(
      stepDesc:
          'Coach understands your goals, sets expectations about how this will work'),
  CoachRegistrationStep(
      stepDesc:
          'Coach evaluates and prepares the best plan that works for you'),
  CoachRegistrationStep(
      stepDesc:
          'Coach assesses your weekly progress and makes course adjustments'),
  CoachRegistrationStep(stepDesc: 'You get results, yay!'),
];
