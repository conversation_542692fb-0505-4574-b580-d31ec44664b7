import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class ActiveUserDataSource {
  Future<Response> getActiveFitBuddies({
    int? offset,
    int? limit,
  });
}

class ActiveUserDataSourceImpl implements ActiveUserDataSource {
  const ActiveUserDataSourceImpl({required this.restClient});

  final RestClient restClient;

  @override
  Future<Response> getActiveFitBuddies({
    int? offset,
    int? limit,
  }) async {
    final response =
        await restClient.get(APIType.PROTECTED, API.getOnlineBuddies, data: {
      "offset": offset,
      "limit": limit,
    });

    return response;
  }
}
