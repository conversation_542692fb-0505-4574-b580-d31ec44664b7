import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/entities/food_info_entity.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/entities/food_scan_image_request.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/repositories/food_scan_repository.dart';
import 'package:fitsomnia_app/src/features/food_scanner/presentation/pages/food_scanner_page.dart';

class FoodScanUseCase {
  final FoodScanRepository repository;

  FoodScanUseCase({required this.repository});

  Future<Either<ErrorModel, FoodInfoEntity>> getFoodInfoFromImage({
    required FoodScanImageRequest request,
  }) async {
    return await repository.getFoodInfoFromSingleMedia(request: request);
  }

  Future<Either<ErrorModel, FoodInfoEntity>> getFoodInfoFromFeedback({
    required FoodScanFeedbackRequest request,
  }) async {
    return await repository.getFoodInfoFromFeedback(request: request);
  }
}
