import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_point_rank_entity.dart';

class RewardPointRankModel extends RewardPointRankEntity {
  RewardPointRankModel({
    required super.userId,
    required super.userName,
    required super.userImage,
    required super.totalPoints,
    required super.totalAmount,
    required super.totalParticipant,
    required super.userRank,
    required super.pointPrice,
    required super.pointRedeemThreshold,
  });

  factory RewardPointRankModel.fromJson(Map<String, dynamic> json) {
    return RewardPointRankModel(
      userId: json['userId'],
      userName: json['name'] ?? 'no-name',
      userImage: json['image'] ?? '',
      totalPoints: json['points'],
      totalAmount: json['amountToWithdraw'] ?? 0,
      totalParticipant: json['totalRanking'] ?? 0,
      userRank: json['userRanking'] ?? 0,
      pointPrice: (json['conversionRate'] == null) ? 0.0 : (json['conversionRate'] is double) ? json['conversionRate'] : double.parse(json['conversionRate'].toString()),
      pointRedeemThreshold: json['pointRedeemThreshold'] ?? 5000,
    );
  }

  RewardPointRankEntity toEntity() {
    return RewardPointRankEntity(
      userId: userId,
      userName: userName,
      userImage: userImage,
      totalPoints: totalPoints,
      totalAmount: totalAmount,
      totalParticipant: totalParticipant,
      userRank: userRank,
      pointPrice: pointPrice,
      pointRedeemThreshold: pointRedeemThreshold,
    );
  }
}
