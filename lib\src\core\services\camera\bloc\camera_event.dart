part of 'camera_bloc.dart';

abstract class CameraEvent extends Equatable {
  const CameraEvent();
}

class ChangeCameraEvent extends CameraEvent {
  final CameraController cameraController;
  const ChangeCameraEvent(this.cameraController);
  @override
  List<Object?> get props => [cameraController];
}

class InitCameraEvent extends CameraEvent {
  @override
  List<Object?> get props => [];
}

class TimerTickEvent extends CameraEvent {
  final int counterValue;
  const TimerTickEvent(this.counterValue);
  @override
  List<Object?> get props => [];
}
