import 'package:fitsomnia_app/src/core/di/injection_container.dart' as di;
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/services/location_service/location_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/use_cases/live_member_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'live_members_state.dart';

class LiveMembersCubit extends Cubit<LiveMembersState> {
  LiveMembersCubit({
    required this.useCase,
  }) : super(const LiveMembersState());

  final LiveMemberUseCase useCase;

  List byteImages = [];

  Future<void> getLiveMembers({
    double maxDistance = 10,
    int? offset,
    int? limit = 5,
  }) async {
    LocationService locationService = di.sl<LocationService>();

    if (locationService.userLocation == null) {
      await locationService.getLocation();
    }

    if (offset == null) emit(state.copyWith(data: []));
    if (state.hasReachedMax) return;

    emit(state.copyWith(
      status: LiveMembersStatus.loading,
      error: null,
    ));

    Map<String, dynamic> query = {
      'longitude': locationService.userLocation!.longitude,
      'latitude': locationService.userLocation!.latitude,
      'maxDistance': maxDistance,
      if (offset != null) 'offset': offset,
      if (limit != null) 'limit': limit,
    };

    try {
      final result = await useCase.liveMembers(query);
      result.fold(
        (l) => emit(
          state.copyWith(
            status: LiveMembersStatus.error,
            error: l.message,
          ),
        ),
        (r) {
          emit(
            r.isEmpty
                ? state.copyWith(
                    status: LiveMembersStatus.success,
                    hasReachedMax: true,
                  )
                : state.copyWith(
                    status: LiveMembersStatus.success,
                    data: List.of(state.data)..addAll(r),
                    hasReachedMax: false,
                  ),
          );
        },
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      emit(state.copyWith(
        status: LiveMembersStatus.error,
        error: TextConstants.somethingWentWrong,
      ));
    }
  }

  void resetHasReachedMax() {
    emit(state.copyWith(hasReachedMax: false));
  }
}
