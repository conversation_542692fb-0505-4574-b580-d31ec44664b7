class ProgramSubscriptionFeePaymentInfo {
  final String programId;
  final String subscriptionId;
  final String userId;
  final String paymentTerm;
  final String paymentStatus;
  final double paymentAmount;
  final DateTime paymentDate;
  final String paymentGateway;
  final String paymentUrl;

  ProgramSubscriptionFeePaymentInfo({
    required this.programId,
    required this.subscriptionId,
    required this.userId,
    required this.paymentTerm,
    required this.paymentStatus,
    required this.paymentAmount,
    required this.paymentDate,
    required this.paymentGateway,
    required this.paymentUrl,
  });
}
