import 'package:fitsomnia_app/src/core/global/globals.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/repositories/image_picking_repository.dart';
import 'package:image_picker/image_picker.dart';

class ImagePickingRepositoryImpl implements ImagePickingRepository {
  final ImagePicker _picker = ImagePicker();

  @override
  Future<XFile> pickImage({required bool isImageCaptureEvent}) async {
    XFile? image;

    image = isImageCaptureEvent
        ? await _picker.pickImage(source: ImageSource.camera)
        : await _picker.pickImage(source: ImageSource.gallery);

    if (image == null) {
      showGlobalLoader.value = false;
    }

    return image!;
  }
}
