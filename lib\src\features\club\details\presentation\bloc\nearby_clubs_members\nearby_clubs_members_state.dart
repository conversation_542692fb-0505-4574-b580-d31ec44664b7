part of 'nearby_clubs_members_cubit.dart';

enum NearbyClubsMembersStatus {
  initial,
  loading,
  success,
  error,
}

class NearbyClubsMembersState extends Equatable {
  const NearbyClubsMembersState({
    this.status = NearbyClubsMembersStatus.initial,
    this.data = const [],
    this.hasReachedMax = false,
    this.error,
  });

  final NearbyClubsMembersStatus status;
  final List<NearbyClubsMemberEntity> data;
  final bool hasReachedMax;
  final String? error;

  NearbyClubsMembersState copyWith({
    NearbyClubsMembersStatus? status,
    List<NearbyClubsMemberEntity>? data,
    bool? hasReachedMax,
    String? error,
  }) {
    return NearbyClubsMembersState(
      status: status ?? this.status,
      data: data ?? this.data,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      error: error ?? this.error,
    );
  }

  @override
  String toString() =>
      'NearbyClubsMembersSuccessState(stats: $status, data: $data, hasReachedMax: $hasReachedMax, error: $error)';

  @override
  List<Object?> get props => [status, data, hasReachedMax, error];
}
