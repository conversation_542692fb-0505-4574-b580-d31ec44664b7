import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/domain/entities/daily_task.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/domain/use_case/daily_task_use_case.dart';

part 'daily_task_event.dart';
part 'daily_task_state.dart';

class DailyTaskBloc extends Bloc<DailyTaskEvent, DailyTaskState> {
  DailyTaskBloc({required this.useCase}) : super(DailyTaskInitial()) {
    on<GetDailyTasks>(_onGetDailyTasks);
  }

  final DailyTaskUseCase useCase;

  Future<void> _onGetDailyTasks(
    GetDailyTasks event,
    Emitter<DailyTaskState> emit,
  ) async {
    try {
      emit(DailyTaskLoading());
      final response = await useCase.getDailyTasks(
          offset: event.offset,
          limit: event.limit,
          taskTypeFilter: event.taskTypeFilter,
          taskStatusFilter: event.taskStatusFilter);

      response.fold((l) {
        emit(GetDailyTasksFail(data: l));
      }, (r) {
        emit(GetDailyTasksSuccess(dailyTasks: r));
      });
    } catch (e) {
      Log.debug(e.toString());
      emit(GetDailyTasksFail(data: e));
    }
  }
}
