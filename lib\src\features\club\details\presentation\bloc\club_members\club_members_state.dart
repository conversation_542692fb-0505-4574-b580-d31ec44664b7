part of 'club_members_cubit.dart';

enum ClubMembersStatus {
  initial,
  loading,
  success,
  error,
}

class ClubMembersState extends Equatable {
  const ClubMembersState({
    this.status = ClubMembersStatus.initial,
    this.data = const [],
    this.hasReachedMax = false,
    this.error,
  });

  final ClubMembersStatus status;
  final List<ClubMemberEntity> data;
  final bool hasReachedMax;
  final String? error;

  ClubMembersState copyWith({
    ClubMembersStatus? status,
    List<ClubMemberEntity>? data,
    bool? hasReachedMax,
    String? error,
  }) {
    return ClubMembersState(
      status: status ?? this.status,
      data: data ?? this.data,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      error: error ?? this.error,
    );
  }

  @override
  String toString() =>
      'ClubMemberSuccessState(stats: $status, data: $data, hasReachedMax: $hasReachedMax, error: $error)';

  @override
  List<Object?> get props => [status, data, hasReachedMax, error];
}
