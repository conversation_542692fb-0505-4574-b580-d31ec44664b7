part of 'verification_bloc.dart';

class VerificationEvent extends Equatable {
  const VerificationEvent();

  @override
  List<Object?> get props => [];
}

class VerificationTimeCountDownEvent extends VerificationEvent {}

class VerificationWithOtpEvent extends VerificationEvent {
  const VerificationWithOtpEvent({
    required this.email,
    required this.otpCode,
  });

  final String email;
  final String otpCode;
}
