import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/presentation/bloc/group_list_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/pages/group_chat_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class GroupChatList extends StatefulWidget {
  const GroupChatList({Key? key}) : super(key: key);

  @override
  State<GroupChatList> createState() => _GroupChatListState();
}

class _GroupChatListState extends State<GroupChatList> {
  @override
  void initState() {
    super.initState();
    BlocProvider.of<GroupListBloc>(context).add(GetGroupListEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        title: const Text(
          "Groups",
          style: TextStyle(
            color: AppColors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: InkWell(
          onTap: () {
            BlocProvider.of<GroupListBloc>(context)
                .add(GetGroupListEvent(limit: 5));
            Navigator.of(context).pop();
          },
          child: const Icon(Icons.arrow_back_ios_new),
        ),
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.black),
        backgroundColor: AppColors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: BlocBuilder<GroupListBloc, GroupListState>(
          builder: (context, state) {
            return state is GroupListSuccess
                ? ListView.builder(
                    itemCount: state.groupList.length,
                    itemBuilder: (context, index) {
                      return GestureDetector(
                        onTap: () {
                          _navigateToGroupChatPage(context, state, index);
                        },
                        child: Container(
                          margin: const EdgeInsets.symmetric(vertical: 10),
                          child: Row(
                            children: [
                              ImageContainer.circularImage(
                                image: state.groupList[index].image ?? '',
                                radius: Values.v26,
                                isGroup: true,
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Container(
                                  width: double.infinity,
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  decoration: BoxDecoration(
                                    border: Border(
                                      bottom: BorderSide(
                                        color: AppColors.black10,
                                      ),
                                    ),
                                  ),
                                  child: Text(
                                    state.groupList[index].name,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 18,
                                      color: AppColors.black,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  )
                : const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  void _navigateToGroupChatPage(
      BuildContext context, GroupListSuccess state, int index) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => GroupChatPage(
          groupId: state.groupList[index].groupId,
          groupName: state.groupList[index].name,
          groupImage: state.groupList[index].image ?? "",
        ),
      ),
    );
  }
}
