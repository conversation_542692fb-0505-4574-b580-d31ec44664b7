part of 'login_bloc.dart';

abstract class LoginEvent extends Equatable {
  const LoginEvent();

  @override
  List<Object> get props => [];
}

class LoginInitialEvent extends LoginEvent {}

class LoginWithEmailAndPasswordEvent extends LoginEvent {
  const LoginWithEmailAndPasswordEvent();
}

class LoginWithGoogleEvent extends LoginEvent {}

class LoginWithFacebookEvent extends LoginEvent {}

class LoginWithAppleEvent extends LoginEvent {}

class CheckLoginMethodEvent extends LoginEvent {}
