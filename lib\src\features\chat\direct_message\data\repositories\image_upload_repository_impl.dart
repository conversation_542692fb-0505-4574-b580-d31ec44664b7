import 'dart:io';

import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/repositories/image_upload_repository.dart';
import 'package:fitsomnia_app/src/features/file_upload/data/data_sources/file_upload_remote_datasource.dart';
import 'package:fitsomnia_app/src/features/file_upload/data/models/upload_pre_signed_url_model.dart';
import 'package:fitsomnia_app/src/features/file_upload/domain/entities/upload_pre_signed_url_entity.dart';
import 'package:mime/mime.dart';

class ImageUploadRepositoryImpl implements ImageUploadRepository {
  final FileUploadRemoteDatasource fileUploadDatasource;

  ImageUploadRepositoryImpl(this.fileUploadDatasource);

  @override
  Future<UploadPreSignedUrlEntity?> getImageUrl({
    required String filePath,
    required File file,
    String? featureName,
  }) async {
    /// For Uploading Image (User / Profile / Review)
    if (featureName != null &&
        (featureName == "user" ||
            featureName == "profile" ||
            featureName == "review" ||
            featureName == 'coach' ||
            featureName == 'event')) {
      try {
        final mimeType = lookupMimeType(file.path);

        String mediaType = mimeType!.split("/")[0];
        String fileExtension = mimeType.split("/")[1];

        final response = await fileUploadDatasource.uploadSinglePublicMedia(
          featureName: featureName,
          file: file,
          mediaType: mediaType,
          fileExtension: fileExtension,
        );
        UploadPreSignedUrlEntity entity =
            UploadPreSignedUrlEntity(s3UploadedURLKey: response.data['data']);

        return entity;
      } catch (e) {
        Log.error("Error Uploading Image: ${e.toString()}");

        return null;
      }
    }

    /// For Uploading Other Medias
    final response1 = await fileUploadDatasource.uploadPreSignedUrl(
      requestBody: {
        "featureName": featureName ?? "chat",
        "filenames": [filePath],
      },
    );

    List<UploadPreSignedUrlEntity> models = response1.data['data']
        .map<UploadPreSignedUrlModel>(
            (element) => UploadPreSignedUrlModel.fromJson(element))
        .toList();

    final response2 = await fileUploadDatasource.fileUpload(
      preAssignedUrl: models.first.presignedUrl!,
      file: file,
    );

    return (response2.statusCode == IntValue.v200 || response2.statusCode == IntValue.v201) ? models.first : null;
  }
}
