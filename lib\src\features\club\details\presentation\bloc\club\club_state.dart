part of 'club_bloc.dart';

abstract class ClubState extends Equatable {
  const ClubState();
}

class ClubInitialState extends ClubState {
  @override
  List<Object?> get props => [];
}

class ClubLoadingState extends ClubState {
  const ClubLoadingState({
    required this.event,
    required this.userId,
  });

  final ClubEventType event;
  final String? userId;

  @override
  List<Object?> get props => [event, userId];
}

class ClubNotFoundState extends ClubState {
  @override
  List<Object?> get props => [];
}

class ClubErrorState extends ClubState {
  const ClubErrorState({
    required this.message,
    required this.event,
    required this.userId,
  });

  final String message;
  final ClubEventType event;
  final String? userId;

  @override
  List<Object?> get props => [
        message,
        event,
        userId,
      ];
}

class ClubLoadedState extends ClubState {
  const ClubLoadedState({
    required this.club,
  });

  final ClubEntity club;

  @override
  List<Object?> get props => [club];
}

class LeaveClubSuccessState extends ClubState {
  const LeaveClubSuccessState();

  @override
  List<Object?> get props => [];
}
