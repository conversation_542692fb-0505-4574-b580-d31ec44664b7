import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/program_subscription_fee_payment_info.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/repositories/coach_program_repository.dart';

class CoachProgramUseCase {
  final CoachProgramRepository repository;

  CoachProgramUseCase({required this.repository});

  Future<Either<ErrorModel, CoachProgramEntity>> createCoachProgram({
    required String coachId,
    required CoachProgramEntity programEntity,
  }) async {
    return await repository.createCoachProgram(
        coachId: coachId, programEntity: programEntity);
  }

  Future<Either<ErrorModel, CoachProgramEntity>> updateCoachProgram({
    required String coachId,
    required String programId,
    required CoachProgramEntity programEntity,
  }) async {
    return await repository.updateCoachProgram(
        coachId: coachId, programId: programId, programEntity: programEntity);
  }

  Future<Either<ErrorModel, CoachProgramEntity>> getCoachProgramById({
    required String coachId,
    required String programId,
  }) async {
    return await repository.getCoachProgramById(
        coachId: coachId, programId: programId);
  }

  Future<Either<ErrorModel, CoachProgramEntity>> deleteCoachProgramById({
    required String coachId,
    required String programId,
  }) async {
    return await repository.deleteCoachProgramById(
        coachId: coachId, programId: programId);
  }

  Future<Either<ErrorModel, CoachProgramEnrollmentEntity>> subscribeProgram({
    required String coachId,
    required String programId,
  }) async {
    return await repository.subscribeProgram(
        coachId: coachId, programId: programId);
  }

  Future<Either<ErrorModel, ProgramSubscriptionFeePaymentInfo>>
      paySubscriptionFee({
    required String subscriptionId,
    required String paymentTerm,
  }) async {
    return await repository.paySubscriptionFee(
        subscriptionId: subscriptionId, paymentTerm: paymentTerm);
  }
}
