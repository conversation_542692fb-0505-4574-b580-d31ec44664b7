import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/entities/club_members_entity.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/use_cases/club_members_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'club_members_state.dart';

class ClubMembersCubit extends Cubit<ClubMembersState> {
  ClubMembersCubit({
    required this.useCase,
  }) : super(const ClubMembersState());

  late ClubMembersUseCase useCase;

  String? clubId;

  Future<void> getClubMembers({
    String? clubId,
    bool refresh = false,
  }) async {
    if (state.status == ClubMembersStatus.loading && !refresh) return;
    if (state.hasReachedMax && !refresh) return;
    if (refresh) emit(state.copyWith(data: []));

    emit(state.copyWith(
      status: ClubMembersStatus.loading,
      error: null,
    ));

    try {
      if (clubId != null) this.clubId = clubId;

      final result = await useCase.clubMembers(
        this.clubId!,
        state.data.length,
      );

      result.fold(
        (l) => emit(
          state.copyWith(
            status: ClubMembersStatus.error,
            error: l.error!.message,
          ),
        ),
        (r) {
          emit(
            r.isEmpty
                ? state.copyWith(
                    status: ClubMembersStatus.success,
                    hasReachedMax: true,
                  )
                : state.copyWith(
                    status: ClubMembersStatus.success,
                    data: List.of(state.data)..addAll(r),
                    hasReachedMax: false,
                  ),
          );
        },
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      emit(state.copyWith(
        status: ClubMembersStatus.error,
        error: TextConstants.defaultErrorMessage,
      ));
    }
  }

  void updateRelationship({
    required int index,
    required String relationship,
  }) {
    emit(
      state.copyWith(
        data: List.of(state.data)
          ..removeAt(index)
          ..insert(
            index,
            state.data[index].copyWith(relationStatus: relationship),
          ),
      ),
    );
  }

  void resetState() {
    emit(const ClubMembersState());
  }
}
