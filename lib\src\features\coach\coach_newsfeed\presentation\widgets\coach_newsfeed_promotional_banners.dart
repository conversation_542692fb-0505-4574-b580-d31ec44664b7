import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_banner_view_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_profile_list_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/root/data/model/coach_program_category_model.dart';
import 'package:fitsomnia_app/src/features/coach/root/domain/entities/coach_program_category_entity.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CoachNewsfeedPromotionalBanner extends StatefulWidget {
  const CoachNewsfeedPromotionalBanner({super.key});

  @override
  State<CoachNewsfeedPromotionalBanner> createState() =>
      _CoachNewsfeedPromotionalBannerState();
}

class _CoachNewsfeedPromotionalBannerState
    extends State<CoachNewsfeedPromotionalBanner> {
  List<CoachProgramCategoryEntity> _promotCoachCategories = [];
  List<Widget> _pages = [];
  int _selectedIndex = 0;
  PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _promotCoachCategories = getBannerInfoList();
    // _pages = _promotCoachCategories.map((info) {
    //   return CoachBannerViewWidget(subcategoryEnrity: info);
    // }).toList();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildBannerSlider(),
        SizedBox(height: 5,),
        _buildSliderIndicator(),
      ],
    );
  }

  _buildBannerSlider() {
    return Container(
      height: Values.v200.h,
      child: PageView.builder(
        controller: _pageController,
        itemCount: _promotCoachCategories.length,
        itemBuilder: (context, index) {
          return CoachBannerViewWidget(
              categoryEntity: _promotCoachCategories[index]);
        },
        onPageChanged: (value) {
          Log.debug('selected page: ${value}');

          setState(() {
            _selectedIndex = value;
          });
        },
      ),
    );
  }

  _buildSliderIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: _promotCoachCategories
          .asMap()
          .map<int, Widget>((index, value) {
            return MapEntry(
              index,
              Container(
                margin: EdgeInsets.symmetric(horizontal: 5),
                height: 10,
                width: 10,
                decoration: BoxDecoration(
                  color: (index == _selectedIndex)
                      ? UIColors.primaryGreen500
                      : UIColors.primaryGreen100,
                  borderRadius: BorderRadius.circular(5),
                ),
              ),
            );
          })
          .values
          .toList(),
    );
  }

  List<CoachProgramCategoryEntity> getBannerInfoList() {
    List<CoachProgramCategoryEntity> lists = [];
    CoachProgramCategoryEntity fitnessBanner = CoachProgramCategoryEntity(
      id: 'ca071cb0-59f7-4ff4-bfcd-13f27515b8d1',
      title: 'Fitness',
      subTitle: 'PERSONAL',
      subTitle2: 'COACH',
      desc: 'Get To Your Best Version !',
      buttonText: 'View Coaches',
      mediaList: [
        CoachCategoryMediaFile(
            urlPath:
                Assets.coachBannerPersonalCoachImg,
            mediaType: 'image')
      ],
      subcategories: [],
      totalCoach: 100,
    );

    CoachProgramCategoryEntity cricketBanner = CoachProgramCategoryEntity(
      id: '91438d52-573a-46dd-bbde-f615071f6f62',
      title: 'Cricket',
      subTitle: 'CRICKET',
      subTitle2: 'MASTER',
      desc: 'For Your Cricket Improvement',
      buttonText: 'View Coaches',
      mediaList: [
        CoachCategoryMediaFile(
            urlPath:
                Assets.coachBannerCricketMasterImg,
            mediaType: 'image')
      ],
      subcategories: [],
      totalCoach: 100,
    );

    CoachProgramCategoryEntity nutritionsBanner = CoachProgramCategoryEntity(
      id: 'ea9eae39-69fd-403f-822c-bc21a9c66b4e',
      title: 'Nutrition',
      subTitle: 'NUTRITIONS',
      subTitle2: 'EXPERT',
      desc: 'Get The Diet You Need Now !',
      buttonText: 'View Coaches',
      mediaList: [
        CoachCategoryMediaFile(
            urlPath:
                Assets.coachBannerNutritionExpertImg,
            mediaType: 'image')
      ],
      subcategories: [],
      totalCoach: 100,
    );

    CoachProgramCategoryEntity muscleBuildBanner = CoachProgramCategoryEntity(
      id: 'ca071cb0-59f7-4ff4-bfcd-13f27515b8d1',
      title: 'Fitness',
      subTitle: 'BUILD YOUR',
      subTitle2: 'MUSCLES',
      desc: 'Experts Who Help You Build Muscle !',
      buttonText: 'View Coaches',
      mediaList: [
        CoachCategoryMediaFile(
            urlPath:
                Assets.coachBannerBuildMuscleImg,
            mediaType: 'image')
      ],
      subcategories: [],
      totalCoach: 100,
    );

    lists = [fitnessBanner, cricketBanner, nutritionsBanner, muscleBuildBanner];

    return lists;
  }
}
