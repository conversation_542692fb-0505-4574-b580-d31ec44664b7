import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/presentation/bloc/all_users_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/presentation/model/user_view_model.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/add_member_bloc/add_member_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/group_members/group_members_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';

class AddGroupChatMemberPage extends StatefulWidget {
  const AddGroupChatMemberPage({
    Key? key,
    required this.groupId,
  }) : super(key: key);

  final String groupId;

  @override
  State<AddGroupChatMemberPage> createState() => _AddGroupChatMemberPageState();
}

class _AddGroupChatMemberPageState extends State<AddGroupChatMemberPage> {
  final TextEditingController searchController = TextEditingController();

  List<UserViewModel> users = [];
  List<String> selectedUserIds = [];

  @override
  void initState() {
    super.initState();
    BlocProvider.of<AllUsersBloc>(context)
        .add(const GetAllUsersEvent(name: ""));
    BlocProvider.of<AddMemberBloc>(context).emit(AddMemberInitial());
  }

  @override
  void dispose() {
    BlocProvider.of<AddMemberBloc>(context).emit(AddMemberInitial());
    users.clear();
    selectedUserIds.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AddMemberBloc, AddMemberState>(
      listenWhen: (previous, current) => previous != current,
      listener: (context, state) {
        if (state is AddMemberSuccess) {
          AppToast.showToast(
            message: "New Member Added!",
            backgroundColor: AppColors.primaryGreen,
            textColor: AppColors.white,
            gravity: ToastGravity.BOTTOM,
          );

          BlocProvider.of<GroupMembersBloc>(context)
              .emit(GroupMembersInitial());

          Navigator.of(context).pop();
        } else {
          AppToast.showToast(
            message: "Try again later..",
            backgroundColor: AppColors.red,
            textColor: AppColors.white,
            gravity: ToastGravity.BOTTOM,
          );
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.white,
        appBar: _buildAppBar(context),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSearchBar(),
            const SizedBox(height: 16),
            _buildPeopleList()
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text(
        "Add Member",
        style: TextStyle(
          color: AppColors.black,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevation: 0,
      iconTheme: const IconThemeData(color: AppColors.black),
      backgroundColor: AppColors.white,
      actions: [
        TextButton(
          onPressed: () => _navigateToGroupChatPage(context),
          child: Text(
            "Done",
            style: TextStyle(
              fontSize: Values.v14,
              color: AppColors.apple,
            ),
          ),
        )
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      height: Values.v48,
      padding: const EdgeInsets.symmetric(horizontal: 10),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.grey6,
        borderRadius: BorderRadius.circular(Values.v30),
      ),
      child: TextFormField(
        controller: searchController,
        onChanged: (value) {
          BlocProvider.of<AllUsersBloc>(context).add(
            GetAllUsersEvent(name: searchController.text),
          );
        },
        decoration: InputDecoration(
          hintText: "Search",
          border: InputBorder.none,
          prefixIcon: GestureDetector(
            onTap: () => BlocProvider.of<AllUsersBloc>(context).add(
              GetAllUsersEvent(name: searchController.text),
            ),
            child: Icon(
              Icons.search,
              color: AppColors.apple,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPeopleList() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BlocConsumer<AllUsersBloc, BaseState>(
              listener: (context, state) {
                if (state is SuccessState) {
                  users.clear();

                  state.data.forEach((element) {
                    users.add(UserViewModel(
                      id: element.id,
                      name: element.name,
                      image:
                          element.image == null ? "" : element.image!.profile,
                    ));
                  });
                }
              },
              builder: (context, state) {
                if (state is SuccessState) {
                  return state.data.isNotEmpty
                      ? dataList(state)
                      : errorOrNoDataView(
                          text: "",
                        );
                } else if (state is ErrorState) {
                  return errorOrNoDataView(text: state.data);
                } else if (state is LoadingState) {
                  return errorOrNoDataView(
                    text: "Please wait...",
                    isLoadingWidget: true,
                  );
                }

                return errorOrNoDataView(
                  text: "Something went wrong. Please try again later...",
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget dataList(SuccessState state) {
    return Expanded(
      child: ListView.builder(
        itemCount: state.data.length,
        itemBuilder: (context, index) {
          return _buildUserCard(context, users[index], index);
        },
      ),
    );
  }

  Widget errorOrNoDataView(
      {required String text, bool isLoadingWidget = false}) {
    return SizedBox.expand(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          isLoadingWidget
              ? const CircularProgressIndicator(
                  color: Colors.green,
                )
              : Text(text),
        ],
      ),
    );
  }

  Widget _buildUserCard(
      BuildContext context, UserViewModel userViewModel, int index) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: InkWell(
              onTap: () {
                if (selectedUserIds.isEmpty) {
                  setState(() {
                    users[index].isSelected = !users[index].isSelected;
                  });

                  if (users[index].isSelected) {
                    selectedUserIds.add(users[index].id);
                  }
                } else {
                  AppToast.showToast(
                    message: "You can add only one person at a time!",
                    backgroundColor: AppColors.red,
                    textColor: AppColors.white,
                    gravity: ToastGravity.BOTTOM,
                  );
                }
              },
              child: Row(
                children: [
                  _buildAvatar(index),
                  const SizedBox(width: 16),
                  _buildUserName(index),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar(int index) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        ImageContainer.circularImage(
          image: users[index].image ?? "",
          radius: Values.v26,
        ),
      ],
    );
  }

  Widget _buildUserName(int index) {
    return Expanded(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: AppColors.black10,
            ),
          ),
        ),
        child: Row(
          children: [
            Text(
              users[index].name,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 18,
                color: AppColors.black,
              ),
            ),
            const Spacer(),
            users[index].isSelected
                ? Icon(
                    Icons.check_circle_rounded,
                    color: AppColors.primaryGreen,
                  )
                : const Icon(
                    Icons.crop_square,
                    color: AppColors.black54,
                  )
          ],
        ),
      ),
    );
  }

  void _navigateToGroupChatPage(BuildContext context) {
    if (selectedUserIds.length == 1) {
      BlocProvider.of<AddMemberBloc>(context).add(
        AddMemberEvent(
            memberId: selectedUserIds.first, groupId: widget.groupId),
      );
    } else {
      AppToast.showToast(
        message: "You can add one person at a time!",
        backgroundColor: AppColors.red,
        textColor: AppColors.white,
        gravity: ToastGravity.BOTTOM,
      );
    }
  }
}
