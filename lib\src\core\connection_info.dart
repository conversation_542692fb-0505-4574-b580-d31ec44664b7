import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

abstract class ConnectionInfo {
  Future<bool> get isConnected;
}

class ConnectionInfoImpl implements ConnectionInfo {
  final InternetConnection connectionChecker;

  ConnectionInfoImpl(this.connectionChecker);

  @override
  Future<bool> get isConnected => connectionChecker.hasInternetAccess;
}


class InternetConnectionService extends ConnectionInfo {
  static final InternetConnection _connection = InternetConnection();

  Future<bool>  _checkConnection() async {
    try {
        return await _connection.hasInternetAccess;
    } catch (e) {
      return false;
    }
  }
  
  @override
  Future<bool> get isConnected => _checkConnection();
}


