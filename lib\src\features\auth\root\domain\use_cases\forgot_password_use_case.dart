import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/repositories/auth_repository.dart';

class ForgotPasswordUseCase {
  ForgotPasswordUseCase({required this.repository});

  final AuthRepository repository;

  Future<Either<ErrorModel, bool>> sendOTP(
      Map<String, dynamic> requestBody) async {
    return await repository.sendOTP(requestBody);
  }
}
