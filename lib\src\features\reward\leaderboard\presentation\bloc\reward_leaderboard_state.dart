part of 'reward_leaderboard_bloc.dart';

sealed class RewardLeaderboardState extends Equatable {
  const RewardLeaderboardState();

  @override
  List<Object> get props => [];
}

final class RewardLeaderboardInitial extends Re<PERSON><PERSON>eaderboardState {}

final class RewardLeaderboardLoading extends Re<PERSON><PERSON>eaderboardState {}

class GetRewardLeaderboardTopUserListSuccess extends RewardLeaderboardState {
  final List<RewardLeaderboardDataEntity> leaderboardEntity;

  GetRewardLeaderboardTopUserListSuccess({required this.leaderboardEntity});

  @override
  List<Object> get props => [leaderboardEntity];
}

class GetRewardLeaderboardTopUserListFail<T> extends RewardLeaderboardState {
  final T? data;
  const GetRewardLeaderboardTopUserListFail({required this.data});
}

class GetUserCurrentRankSuccess extends RewardLeaderboardState {
  final RewardPointRankEntity rankEntity;

  GetUserCurrentRankSuccess({required this.rankEntity});

  @override
  List<Object> get props => [rankEntity];
}

class GetUserCurrentRankFail<T> extends RewardLeaderboardState {
  final T? data;
  const GetUserCurrentRankFail({required this.data});
}
