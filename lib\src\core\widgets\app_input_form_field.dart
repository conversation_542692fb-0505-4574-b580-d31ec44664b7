// import 'package:fitsomnia_app/src/core/colors.dart';
// import 'package:fitsomnia_app/src/core/typography/style.dart';
// import 'package:fitsomnia_app/src/core/values.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:input_form_field/input_form_field.dart';
//
// class AppInputFormField extends StatelessWidget {
//   const AppInputFormField({
//     Key? key,
//     this.height,
//     required this.controller,
//     required this.labelText,
//     this.hintText,
//     this.validatorMessage,
//     this.suffixText,
//     this.borderColor,
//     this.enableDefaultValidation,
//   }) : super(key: key);
//
//   final double? height;
//   final TextEditingController controller;
//   final String? validatorMessage;
//   final String? suffixText;
//   final String? hintText;
//   final String labelText;
//   final Color? borderColor;
//   final bool? enableDefaultValidation;
//
//   @override
//   Widget build(BuildContext context) {
//     return InputFormField(
//       bottomMargin: 5,
//       textEditingController: controller,
//       height: height ?? Values.v50.h,
//       borderRadius: BorderRadius.circular(Values.v3.r),
//       borderColor: borderColor ?? AppColors.nobel,
//       errorPadding: EdgeInsets.only(bottom: 5.h),
//       borderType: BorderType.outlined,
//       hintText: hintText,
//       hintTextStyle: AppTypography.regular14(
//         color: AppColors.dark,
//       ),
//       label: Padding(
//         padding: EdgeInsets.only(bottom: Values.v5.h),
//         child: Text(
//           labelText,
//           style: AppTypography.regular16(
//             color: AppColors.black,
//           ),
//         ),
//       ),
//       contentPadding: EdgeInsets.all(Values.v9.w),
//
//       suffix: suffixText != null
//           ? SizedBox(
//               width: Values.v15.w,
//               child: Center(
//                 child: Text(
//                   suffixText!,
//                   style: AppTypography.regular14(
//                     color: AppColors.silver,
//                   ),
//                   textAlign: TextAlign.center,
//                 ),
//               ),
//             )
//           : const SizedBox.shrink(),
//       enableDefaultValidation: enableDefaultValidation ?? false,
//       // validator: (value) => value!.isEmpty ? validatorMessage : null
//       // validator: validatorMessage != null
//       //     ? (value) => value!.isEmpty ? validatorMessage : null
//       //     : null,
//     );
//   }
// }
