import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:video_player/video_player.dart';

class PrimaryVideoPlayer extends StatefulWidget {
  final String videoUrl;

  const PrimaryVideoPlayer({required this.videoUrl});

  @override
  PrimaryVideoPlayerState createState() => PrimaryVideoPlayerState();
}

class PrimaryVideoPlayerState extends State<PrimaryVideoPlayer> {
  late VideoPlayerController _controller;
  bool showVideoBar = false;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));

    _controller.addListener(() {
      setState(() {
        '';
      });
    });
    _controller.setLooping(false);
    _controller.initialize().then((_) => setState(() {
          showVideoBar = true;
        }));
    _controller.play();
  }

  @override
  void dispose() {
    _controller.dispose();
    Log.debug("Video Player Dispose Called");
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.black,
        leading: IconButton(
          icon: const Icon(
            Icons.close,
            color: AppColors.white,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        elevation: 0,
      ),
      backgroundColor: AppColors.black,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: AspectRatio(
                  aspectRatio: _controller.value.aspectRatio,
                  child: Stack(
                    alignment: Alignment.bottomCenter,
                    children: <Widget>[
                      showVideoBar
                          ? VideoPlayer(_controller)
                          : Center(
                              child: CircularProgressIndicator(
                                color: AppColors.primaryGreen,
                              ),
                            ),
                      showVideoBar
                          ? _ControlsOverlay(controller: _controller)
                          : const SizedBox.shrink(),
                      showVideoBar
                          ? VideoProgressIndicator(
                              _controller,
                              allowScrubbing: true,
                            )
                          : const SizedBox.shrink(),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(
              height: 16.h,
            ),
          ],
        ),
      ),
    );
  }
}

class _ControlsOverlay extends StatelessWidget {
  const _ControlsOverlay({required this.controller});

  final VideoPlayerController controller;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 50),
          reverseDuration: const Duration(milliseconds: 200),
          child: controller.value.isPlaying
              ? const SizedBox.shrink()
              : Container(
                  color: Colors.black26,
                  child: const Center(
                      child: Icon(
                    Icons.play_circle,
                    color: AppColors.white,
                    size: 60,
                  )),
                ),
        ),
        GestureDetector(
          onTap: () {
            controller.value.isPlaying ? controller.pause() : controller.play();
          },
        ),
        Align(
          alignment: Alignment.topRight,
          child: GestureDetector(
            onTap: () {
              controller.value.volume == 0.0
                  ? controller.setVolume(1.0)
                  : controller.setVolume(0.0);
            },
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 50),
              reverseDuration: const Duration(milliseconds: 200),
              child: controller.value.volume == 0.0
                  ? Container(
                      padding: EdgeInsets.only(right: 8.w, top: 8.h),
                      child: Icon(
                        Icons.volume_off_rounded,
                        color: AppColors.primaryGreen,
                        size: 30.0,
                        semanticLabel: 'Mute',
                      ),
                    )
                  : Container(
                      padding: EdgeInsets.only(right: 8.w, top: 8.h),
                      child: Icon(
                        Icons.volume_up_rounded,
                        color: AppColors.primaryGreen,
                        size: 30.0,
                        semanticLabel: 'Volume Up',
                      ),
                    ),
            ),
          ),
        )
      ],
    );
  }
}
