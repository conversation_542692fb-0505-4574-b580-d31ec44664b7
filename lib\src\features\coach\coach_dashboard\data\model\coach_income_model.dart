import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_income_entity.dart';

class CoachIncomeModel extends CoachIncomeEntity {
  CoachIncomeModel({
    required super.currentAccountBalance,
    required super.withdrawableAmount,
    required super.totalSubscriptions,
    required super.lastWeekSubscriptionCount,
    required super.userImageLinks,
    required super.lastIncomeDate,
    required super.isWithdrawRequestPending,
    required super.withdrawThreshold,
    super.coachId,
    super.phoneNumber,
  });

  factory CoachIncomeModel.fromJson(Map<String, dynamic> json) {
    return CoachIncomeModel(
      coachId: json['coachId'],
      currentAccountBalance: (json['currentAccountBalance'] is double)
          ? json['currentAccountBalance']
          : json['currentAccountBalance'].toDouble(),
      withdrawableAmount: (json['withdrawableAmount'] is double)
          ? json['withdrawableAmount']
          : json['withdrawableAmount'].toDouble(),
      totalSubscriptions: json['totalSubscriptions'] ?? 0,
      lastWeekSubscriptionCount: json['lastWeekSubscriptionCount'] ?? 0,
      userImageLinks: (json['userImageLinks'] == null)
          ? []
          : List<String>.from(json['userImageLinks']!.map((x) => x.toString())),
      lastIncomeDate: DateTime.tryParse(json['lastIncomeDate']),
      phoneNumber: json['lastUsedBkashNumber'],
      isWithdrawRequestPending: json['lastWithdrawStatus'] ?? false,
      withdrawThreshold: (json['withdrawThreshold'] == null)
          ? 1000
          : (json['withdrawThreshold'] is double)
              ? json['withdrawThreshold']
              : json['withdrawThreshold'].toDouble(),
    );
  }
}
