import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/validators/input_validators.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/bloc/coach_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';

class ProgramSubscriptionRemoveByCoachWidget extends StatefulWidget {
  const ProgramSubscriptionRemoveByCoachWidget(
      {super.key, required this.subscriptionId});
  final String subscriptionId;

  @override
  State<ProgramSubscriptionRemoveByCoachWidget> createState() =>
      _ProgramSubscriptionRemoveByCoachWidgetState();
}

class _ProgramSubscriptionRemoveByCoachWidgetState
    extends State<ProgramSubscriptionRemoveByCoachWidget> {
  TextEditingController removeSubscriptionReasonController =
      TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late String coachId;

  @override
  void initState() {
    super.initState();
    coachId = context.read<CoachBloc>().coachId ?? '';
  }

  @override
  void dispose() {
    removeSubscriptionReasonController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: AppColors.white, borderRadius: BorderRadius.circular(10)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              IconButton(
                icon: Icon(Icons.close_sharp),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
          _buildContentSection(),
        ],
      ),
    );
  }

  _buildContentSection() {
    return Form(
      key: _formKey,
      child: Padding(
        padding: EdgeInsets.only(
            left: Values.v20, right: Values.v20, bottom: Values.v20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Remove Subscription',
              style: AppTypography.poppinsSemiBold24(
                  color: UIColors.primaryGreen950),
            ),
            Text(
              'Let us know why you want to remove the enroller',
              style: AppTypography.poppinsMedium14(color: AppColors.greyscale400),
            ),
            SizedBox(
              height: 20,
            ),
            _buildReasonSection(),
            SizedBox(
              height: 30,
            ),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  _buildReasonSection() {
    return _buildEditTextInfo(
      name: 'Write reason',
      text: '',
      controller: removeSubscriptionReasonController,
      validator: InputValidators.name,
      maxLine: 5,
    );
  }

  _buildEditTextInfo(
      {required String name,
      required String? text,
      required TextEditingController controller,
      required String? Function(String?)? validator,
      int maxLine = 1}) {
    if (text != null) controller.text = text;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "$name",
          style:
              AppTypography.poppinsRegular16(color: UIColors.primaryGreen950),
        ),
        SizedBox(
          height: Values.v5,
        ),
        TextFormField(
          cursorColor: AppColors.black,
          controller: controller,
          maxLines: maxLine,
          minLines: maxLine,
          autofocus: false,
          autocorrect: false,
          decoration: _buildInputDecoration(),
          validator: validator,
          autovalidateMode: AutovalidateMode.onUserInteraction,
        )
      ],
    );
  }

  InputDecoration _buildInputDecoration() {
    var inputBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(Values.v10),
      borderSide: BorderSide(
          color: UIColors.primaryGreen950.withOpacity(0.2), width: Values.v2),
    );

    return InputDecoration(
      enabledBorder: inputBorder,
      focusedBorder: inputBorder,
      // hintText: 'Write a short bio?',
      hintStyle: AppTypography.regular18(
        color: AppColors.silver,
      ),
      contentPadding: EdgeInsets.all(Values.v16),
      // filled: true,
      // fillColor: AppColors.alto.withOpacity(0.2),
    );
  }

  _buildSubmitButton() {
    return Button.filled(
      label: 'Submit',
      onPressed: () {
        Log.debug('remove subscription reason');
        Log.debug('remove reason: ${removeSubscriptionReasonController.text}');

        final cancelReason = removeSubscriptionReasonController.text;
        if(!_formKey.currentState!.validate()) {
          AppToast.showToast(message: 'Reason is empty', gravity: ToastGravity.BOTTOM, backgroundColor: AppColors.warning);
        }

        BlocProvider.of<CoachDashboardBloc>(context)
            .add(ProgramSubscriptionCancelByCoachEvent(
              coachId: coachId,
          subscriptionId: widget.subscriptionId,
          cancelReason: cancelReason,
        ));
        
        Navigator.of(context).pop();
      },
    );
  }
}
