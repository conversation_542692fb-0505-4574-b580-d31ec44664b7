part of 'recent_conversation_bloc.dart';

class RecentConversationEvent extends Equatable {
  const RecentConversationEvent();

  @override
  List<Object?> get props => [];
}

class GetRecentConversationListEvent extends RecentConversationEvent {
  final int? limit;
  final int? offset;

  const GetRecentConversationListEvent({this.limit, this.offset});
}

class UpdateRecentConversationListEvent extends RecentConversationEvent {
  final List<ChatListEntity> chatListEntities;

  const UpdateRecentConversationListEvent({required this.chatListEntities});

  @override
  List<Object?> get props => chatListEntities;
}

class DeleteAndInsertAtFirstEvent extends RecentConversationEvent {
  final dynamic data;
  final UserProfile userProfile;

  const DeleteAndInsertAtFirstEvent(this.data, this.userProfile);
}
