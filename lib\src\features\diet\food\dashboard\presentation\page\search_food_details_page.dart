import 'dart:io';

import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/extensions/extensions.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/bloc/dashboard_cubit.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/bloc/food_consumption/food_consumption_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/food/root/data/model/food_response_model.dart';
import 'package:fitsomnia_app/src/features/diet/track_diet/presentation/bloc/diet_history/diet_history_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';

class SearchFoodDetailsPage extends StatefulWidget {
  const SearchFoodDetailsPage({super.key, required this.foodInfo});
  final FoodModel foodInfo;

  @override
  State<SearchFoodDetailsPage> createState() => _SearchFoodDetailsPageState();
}

class _SearchFoodDetailsPageState extends State<SearchFoodDetailsPage> {
  late ValueNotifier<int> servingSizeNotifier;

  @override
  void initState() {
    super.initState();
    servingSizeNotifier = ValueNotifier(1);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
          child: Stack(
        fit: StackFit.expand,
        children: [
          // Background Image
          _buildFoodImage(imagePath: widget.foodInfo.foodImage),
          // Gradient overlay for better text visibility
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.7),
                  Colors.transparent,
                ],
              ),
            ),
          ),

          // Bottom Sheet with Food Details
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: _buildFoodDetails(widget.foodInfo),
            ),
          ),
          // Back Button

          Align(
            alignment: Alignment.topLeft,
            // top: MediaQuery.of(context).padding.top + 10,
            // left: 0,
            // child: IconButton(
            //   icon: Icon(Icons.arrow_back, color: Colors.white),
            //   onPressed: () => Navigator.pop(context),
            // ),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
              child: Row(
                children: [
                  IconButton(
                    icon: Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                  SizedBox(width: Values.v100),
                  Text(
                    'Search Foods',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          blurRadius: 4,
                          color: Colors.black.withOpacity(0.3),
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      )),
    );
  }

  _buildFoodImage({required String? imagePath}) {
    bool isNetworkImage =
        (imagePath == null) ? false : Uri.parse(imagePath).hasAbsolutePath;
    Log.debug('isNetworkImage: $isNetworkImage, ${imagePath}');

    if (imagePath == null || !isNetworkImage) {
      return Image.asset(
        imagePath ?? Assets.noFoodImage,
        fit: BoxFit.cover,
      );
    } else {
      // return Image.network(
      //   imagePath,
      //   fit: BoxFit.fill,
      // );

      return ImageContainer.rectangularImage(
        cornerRadius: Values.v10,
        image: imagePath,
        width: Values.defaultWidth,
        height: Values.defaultHeight,
        fit: BoxFit.fill,
        useOriginal: true,
        hideLoadingIndicator: true,
        errorWidget: Container(
          height: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(Values.v10),
            image: DecorationImage(
              image: const Image(
                image: AssetImage(
                  Assets.noFoodImage,
                ),
              ).image,
              fit: BoxFit.fill,
            ),
          ),
        ),
      );
    }
  }

  // ignore: long-method
  Widget _buildFoodDetails(FoodModel foodInfo) {
    return BlocConsumer<FoodConsumptionCubit, BaseState>(
      listener: (context, state) {
        Log.debug('FoodConsumptionCubit state listen: ${state.toString()}');
        if (state is ErrorState) {
          AppToast.showToast(
            message: state.data,
            gravity: ToastGravity.BOTTOM,
          );
        }
        if (state is SuccessState) {
          Log.debug('add search food in diet history success');
          AppToast.showToast(
            message: "Food added successfully",
            gravity: ToastGravity.BOTTOM,
            toastLength: Toast.LENGTH_LONG,
            backgroundColor: AppColors.primaryGreen,
          );

          // Navigator.popUntil(
          //     context, (route) => route.settings.name == Routes.dietDashboard);
          _navigateToDietFeatureDashboard(context);
          context.read<DietHistoryBloc>().add(
                const RefreshDietHistoryEvent(),
              );
        }
      },
      builder: (context, state) {
        return Stack(
          alignment: Alignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  _buildMealTypeText(),
                  SizedBox(
                    height: Values.v16,
                  ),
                  Text(
                    foodInfo.name,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(
                    height: Values.v5,
                  ),

                  ServingSizeWidget(
                      servingSizeValueNotifier: servingSizeNotifier),

                  Container(
                    padding: const EdgeInsets.all(Values.v10),
                    // decoration: BoxDecoration(
                    //   // color: Colors.grey[200],
                    //   borderRadius: BorderRadius.circular(Values.v8),
                    //   border: Border.all(color: AppColors.greyscale100, width: 1),
                    // ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text('Amount Per',
                            style: AppTypography.poppinsRegular14(
                                color: AppColors.greyscale400)),
                        Text(' 100 Gram',
                            style: AppTypography.poppinsSemiBold14(
                                color: UIColors.primaryGreen950)),
                      ],
                    ),
                  ),

                  const SizedBox(height: 5),

                  Wrap(
                    spacing: Values.v5,
                    runSpacing: 5,
                    children: [
                      _buildNutritionInfo(
                        'Calories',
                        foodInfo.calories.toString(),
                        Assets.mealCalorie,
                        'kcal',
                      ),
                      _buildNutritionInfo(
                        'Carbs',
                        foodInfo.carb.toString(),
                        Assets.mealCarbs,
                        'g',
                      ),
                      _buildNutritionInfo(
                        'Fat',
                        foodInfo.fat.toString(),
                        Assets.mealFats,
                        'g',
                      ),
                      _buildNutritionInfo(
                        'Protein',
                        foodInfo.protein.toString(),
                        Assets.mealProtein,
                        'g',
                      ),
                    ],
                  ),

                  Divider(
                    color: AppColors.greyscale100,
                    thickness: 1,
                    height: Values.v40,
                  ),
                  // if (foodInfo.ingredients != null) ..._buildIngredients(foodInfo),
                  // const SizedBox(height: Values.v16),
                  _addToMealButton(foodInfo),
                  const SizedBox(height: Values.v16),
                ],
              ),
            ),
            if (state is LoadingState)
              Center(
                child: CircularProgressIndicator(
                  color: UIColors.primary,
                ),
              ),
          ],
        );
      },
    );
  }

  _buildIngredients(FoodModel foodInfo) {
    return [
      Text(
        'Breakdown',
        style: AppTypography.poppinsRegular14(
          color: AppColors.greyscale400,
        ),
      ),
      SizedBox(height: Values.v8),
      _buildBreakdownItems([]),
    ];
  }

  Widget _buildNutritionInfo(
      String label, String value, String mealSvgImg, String unit) {
    return Container(
      padding: const EdgeInsets.all(Values.v8),
      // height: Values.v100,
      width: Values.v90,
      decoration: BoxDecoration(
        // color: Colors.grey[200],
        borderRadius: BorderRadius.circular(Values.v8),
        border: Border.all(color: AppColors.greyscale100, width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(Values.v5),
            child: SvgPicture.asset(
              mealSvgImg,
              width: 24,
              height: 24,
            ),
          ),
          Text(
            '$value $unit',
            style: AppTypography.poppinsSemiBold14(
                color: UIColors.primaryGreen950),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
          const SizedBox(height: Values.v4),
          Text(
            label,
            style:
                AppTypography.poppinsRegular12(color: AppColors.greyscale400),
          ),
        ],
      ),
    );
  }

  Widget _addToMealButton(FoodModel foodInfo) {
    return ElevatedButton(
      onPressed: () {
        Log.debug('add search food info to database');
        _addFoodToDietHistory();
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: UIColors.primaryGreen600,
        padding: const EdgeInsets.symmetric(
            horizontal: Values.v24, vertical: Values.v12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Add To ${_mealTypeName().toCapitalFirst()}',
            style: AppTypography.poppinsMedium16(color: UIColors.white),
          )
        ],
      ),
    );
  }

  _buildBreakdownItems(List<String> list) {
    return Wrap(
      spacing: Values.v8,
      runSpacing: Values.v12,
      children: list.map((item) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: Values.v4),
          child: Container(
            padding: const EdgeInsets.all(Values.v8),
            decoration: BoxDecoration(
              // color: Colors.grey[200],
              borderRadius: BorderRadius.circular(Values.v8),
              border: Border.all(color: AppColors.greyscale100, width: 1),
            ),
            child: Text(
              item,
              style: AppTypography.poppinsRegular14(
                color: UIColors.primaryGreen950,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  void _addFoodToDietHistory() {
    Log.debug('add food to diet history');
    final selectedIndex = context.read<FoodConsumptionCubit>().selectedTabIndex;
    context.read<FoodConsumptionCubit>().addFoodToDietHistory(
          foodId: widget.foodInfo.id,
          servingSize: servingSizeNotifier.value,
          type: selectedIndex.value,
          date: context.read<DietHistoryBloc>().currentDate,
          source: widget.foodInfo.source,
        );
  }

  _buildMealTypeText() {
    String mealName = _mealTypeName();

    return Text(
      '${mealName.toCapitalFirst()}',
      style: AppTypography.poppinsRegular14(color: AppColors.greyscale400),
    );
  }

  String _mealTypeName() {
    int mealTypeIndex =
        context.read<FoodConsumptionCubit>().selectedTabIndex.value;

    switch (mealTypeIndex) {
      case 0:
        return 'Breakfast';
      case 1:
        return 'Lunch';
      case 2:
        return 'Dinner';
      default:
        return 'Snacks';
    }
  }

  void _navigateToDietFeatureDashboard(BuildContext context) {
    // move to diet feature page
    // Navigator.of(context).popUntil((route) => route.isFirst);
    // navigatorKey?.currentState?.pushAndRemoveUntil(
    //   MaterialPageRoute(
    //       builder: (_) => DashboardPage(
    //             selectedIndex: 3,
    //           )),
    //   (route) => false,
    // );

    navigatorKey?.currentState?.popUntil(
      (route) =>
          route is MaterialPageRoute && route.settings.name == Routes.dashboard,
    );
    context
        .read<DashboardCubit>()
        .changePage(3); // diet feature position is 3 in nav bar
  }
}

class ServingSizeWidget extends StatefulWidget {
  const ServingSizeWidget({super.key, required this.servingSizeValueNotifier});

  final ValueNotifier<int> servingSizeValueNotifier;

  @override
  State<ServingSizeWidget> createState() => _ServingSizeWidgetState();
}

class _ServingSizeWidgetState extends State<ServingSizeWidget> {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: () {
            if (widget.servingSizeValueNotifier.value > 1)
              widget.servingSizeValueNotifier.value--;
          },
          icon: SvgPicture.asset(
            Assets.decreaseIcon,
            height: Values.v40,
            width: Values.v40,
          ),
        ),
        _buildCounterText(),
        IconButton(
          onPressed: () {
            widget.servingSizeValueNotifier.value++;
          },
          icon: SvgPicture.asset(
            Assets.increaseIcon,
            height: Values.v40,
            width: Values.v40,
          ),
        ),
      ],
    );
  }

  _buildCounterText() {
    return ValueListenableBuilder(
        valueListenable: widget.servingSizeValueNotifier,
        builder: (context, count, _) {
          return Padding(
            padding: const EdgeInsets.all(Values.v10),
            child: Text(
              '$count',
              style: AppTypography.poppinsBold24(),
            ),
          );
        });
  }
}
