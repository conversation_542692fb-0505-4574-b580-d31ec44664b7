import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/presentation/model/user_view_model.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/domain/use_case/get_all_users_use_case.dart';

part 'all_users_event.dart';
part 'all_users_state.dart';

class AllUsersBloc extends Bloc<AllUsersEvent, BaseState> {
  AllUsersBloc({required this.getAllUsersUseCase}) : super(InitialState()) {
    on<GetAllUsersEvent>(_onGetAllUsersEvent);
    on<SearchUsersEvent>(_onSearchUsersEvent);
  }

  late GetAllUsersUseCase getAllUsersUseCase;

  List<UserViewModel> allUsers = [];
  List<UserViewModel> displayedUsers = [];

  Future<void> _onGetAllUsersEvent(
    GetAllUsersEvent event,
    Emitter<BaseState> emit,
  ) async {
    emit(const LoadingState());

    try {
      final response = await getAllUsersUseCase.call(name: event.name);

      response.fold(
        (l) => emit(
          ErrorState(
            data: l.toString(),
          ),
        ),
        (r) {
          allUsers.clear();
          displayedUsers.clear();

          for (var element in r) {
            allUsers.add(
              UserViewModel(
                id: element.id,
                name: element.name,
                image: element.image == null ? "" : element.image!.profile,
              ),
            );
          }

          displayedUsers = allUsers;

          emit(SuccessState(data: r));
        },
      );
    } catch (_) {
      emit(
        const ErrorState(
          data: TextConstants.failedToLoadData,
        ),
      );
    }
  }

  Future<void> _onSearchUsersEvent(
    SearchUsersEvent event,
    Emitter<BaseState> emit,
  ) async {
    emit(const LoadingState());

    try {
      List<UserViewModel> filteredUsers = allUsers.where((user) {
        final query = event.name.toLowerCase();
        final userName = user.name.toLowerCase();

        return userName.contains(query);
      }).toList();

      displayedUsers = filteredUsers;

      emit(const SuccessState());
    } catch (_) {
      emit(
        const ErrorState(
          data: TextConstants.failedToLoadData,
        ),
      );
    }
  }
}
