import 'package:fitsomnia_app/src/core/extensions/date_time.dart';
import 'package:intl/intl.dart';

class UserLoginActivityEntity {
  final String userId;
  final int totalLoginStreak;
  final List<DailyLoginActivity> loginHistory;

  UserLoginActivityEntity({
    required this.userId,
    required this.totalLoginStreak,
    required this.loginHistory,
  });
}

class DailyLoginActivity {
  final DateTime date;
  final bool loginStatus;

  DailyLoginActivity({
    required this.date,
    required this.loginStatus,
  });

  UserDailyLoginActivityStatus get loginStatusType {
    if (loginStatus) return UserDailyLoginActivityStatus.LOGIN_SUCCEDD;

    
    if ( date.isSameDate(DateTime.now()) || date.isAfterDate(DateTime.now())) {
      return UserDailyLoginActivityStatus.UPCOMMING;
    }

    return UserDailyLoginActivityStatus.LOGIN_FAIL;
  }

  String get weekDayName {
    return DateFormat.E().format(date);
  }

  int get weekDayCount {
    return date.weekday;
  }
}

enum UserDailyLoginActivityStatus {
  LOGIN_SUCCEDD,
  LOGIN_FAIL,
  UPCOMMING,
}

var day1 = DailyLoginActivity(date: DateTime.now().add(Duration(days: -3)), loginStatus: false);
var day2 = DailyLoginActivity(date: DateTime.now().add(Duration(days: -2)), loginStatus: true);
var day3 = DailyLoginActivity(date: DateTime.now().add(Duration(days: -1)), loginStatus: true);
var day4 = DailyLoginActivity(date: DateTime.now(), loginStatus: true);
var day5 = DailyLoginActivity(date: DateTime.now().add(Duration(days: 1)), loginStatus: false);
var day6 = DailyLoginActivity(date: DateTime.now().add(Duration(days: 2)), loginStatus: false);
var day7 = DailyLoginActivity(date: DateTime.now().add(Duration(days: 3)), loginStatus: false);
var testUserLoginActivity = UserLoginActivityEntity(userId: 'user-id', totalLoginStreak: 0, loginHistory: [day1, day2, day3, day4, day5, day6, day7]);
