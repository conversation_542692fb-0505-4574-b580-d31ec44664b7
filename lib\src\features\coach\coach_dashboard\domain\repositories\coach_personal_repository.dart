import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_income_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';

abstract class CoachPersonalRepository {
  Future<Either<ErrorModel, CoachEntity>> getCoachOwnProfile(
      {required String coachId});
  Future<Either<ErrorModel, List<CoachProgramEntity>>> getCoachPrograms(
      {required String coachId});
  Future<Either<ErrorModel, CoachProgramEntity>> getCoachProgram(
      {required String coachId, required String programId});
  Future<Either<ErrorModel, List<CoachProgramEnrollmentEntity>>>
      getProgramEnrollers({required String coachId});
  Future<Either<ErrorModel, CoachProgramEnrollmentEntity>> getProgramEnroller(
      {required String coachId, required String subscriptionId});
  Future<Either<ErrorModel, CoachProgramEnrollmentEntity>>
      cancelProgramSubscriptionByCoach({
    required String coachId,
    required String subscriptionId,
    required String cancelReason,
  });

  Future<Either<ErrorModel, CoachIncomeEntity>> getCoachIncomeInfo(
      {required String coachId});

  Future<Either<ErrorModel, List<CoachProgramEnrollmentEntity>>>
      getProgramEnrollerHistory({
    required String coachId,
    required String programId,
  });

  Future<Either<ErrorModel, List<CoachProgramEnrollmentEntity>>>
      getProgramPaymentHistory({
    required String coachId,
    required String programId,
  });
}
