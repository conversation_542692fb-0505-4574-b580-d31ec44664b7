import 'package:camera/camera.dart';
import 'package:fitsomnia_app/src/core/services/camera/bloc/camera_bloc.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CameraHeaderWidget extends StatefulWidget {
  const CameraHeaderWidget({Key? key}) : super(key: key);

  @override
  State<CameraHeaderWidget> createState() => _CameraHeaderWidgetState();
}

class _CameraHeaderWidgetState extends State<CameraHeaderWidget> {
  bool flash = false;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: Values.v0.h,
      child: Container(
        color: Colors.black,
        padding: EdgeInsets.only(top: Values.v5.h, bottom: Values.v5.h),
        width: MediaQuery.of(context).size.width,
        child: Bloc<PERSON>uilder<CameraBloc, CameraState>(
          builder: (context, state) {
            return state.cameraController == null
                ? const SizedBox.shrink()
                : Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildCloseIcon(state.cameraController!),
                      /// TODO: Disabling Flash as it doesn't work
                      // _buildFlashIcon(state.cameraController!),
                    ],
                  );
          },
        ),
      ),
    );
  }

  Widget _buildFlashIcon(CameraController cameraController) {
    return IconButton(
        icon: Icon(
          flash ? Icons.flash_on : Icons.flash_off,
          color: Colors.white,
          size: Values.v28.r,
        ),
        onPressed: () {
          setState(() {
            flash = !flash;
          });
          flash
              ? cameraController.setFlashMode(FlashMode.torch)
              : cameraController.setFlashMode(FlashMode.off);
        });
  }

  Widget _buildCloseIcon(CameraController cameraController) {
    return IconButton(
      icon: const Icon(
        Icons.close,
        color: Colors.white,
      ),
      onPressed: () => Navigator.pop(context),
    );
  }
}
