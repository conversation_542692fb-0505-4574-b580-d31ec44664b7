import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/local_storage/cache_service.dart';
import 'package:fitsomnia_app/src/core/services/socket/scoket_service.dart';
import 'package:fitsomnia_app/src/features/auth/login/presentation/pages/login_page.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/root/presentation/page/chat_dashboard_page.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/pages/dashboard/others_profile_page.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/pages/post_details/timeline_post_details_page.dart';
import 'package:flutter/material.dart';

/// Handle Navigation when tapped on the Push Notification while App is closed
/// or in the background
Future<void> handleMessage(Map<String, dynamic> data) async {
  // Safely get title with null check
  String title = (data['title'] ?? '').toString().toLowerCase();
  String? documentId = data['documentId']?.toString();
  String? type = data['type']?.toString().toLowerCase();
  String? action = data['action']?.toString().toLowerCase();

  bool isSpotModule = title.contains('spot');
  bool isSingleMessage = title.contains('message');
  bool isGroupMessage = title.contains('group');
  bool isReminderNotification = title.contains('reminder') ||
                               type == 'daily_reminder' ||
                               action == 'navigate_diet';

  String? token = await CacheService.instance.retrieveBearerToken();

  if (token == null) {
    navigatorKey?.currentState?.pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => const LoginPage(),
      ),
      (route) => false,
    );
  } else if (isReminderNotification) {
    // Navigate to Diet Dashboard for reminder notifications
    navigatorKey?.currentState?.pushNamed(
      Routes.dietDashboard,
      arguments: [false, false], // showStepperOnTop: false, isFromBottom: false
    );
  } else if (isSpotModule && documentId != null) {
    navigatorKey?.currentState?.push(
      MaterialPageRoute(
        builder: (context) => OthersProfilePage(
          userId: documentId,
        ),
      ),
    );
  } else if (isSingleMessage || isGroupMessage) {
    await SocketService.initialize();
    SocketService.instance.connect();

    navigatorKey?.currentState?.push(
      MaterialPageRoute(
        builder: (context) => const ChatDashboardPage(),
      ),
    );
  } else if (documentId != null) {
    navigatorKey?.currentState?.push(
      MaterialPageRoute(
        builder: (context) => TimelinePostDetails(
          postId: documentId,
        ),
      ),
    );
  }
  // If documentId is null and it's not a chat notification, do nothing
}
