import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/local_storage/cache_service.dart';
import 'package:fitsomnia_app/src/core/services/socket/scoket_service.dart';
import 'package:fitsomnia_app/src/features/auth/login/presentation/pages/login_page.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/root/presentation/page/chat_dashboard_page.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/pages/dashboard/others_profile_page.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/pages/post_details/timeline_post_details_page.dart';
import 'package:flutter/material.dart';

/// Feature types for reminder notifications
enum ReminderFeatureType {
  rewards('rewards'),
  referrals('referrals'),
  dailyTasks('daily-tasks'),
  challenges('challenges'),
  diet('diet'),
  leaderboard('leaderboard'),
  other('other');

  const ReminderFeatureType(this.value);
  final String value;

  static ReminderFeatureType fromString(String? feature) {
    if (feature == null || feature.isEmpty) return ReminderFeatureType.other;

    for (ReminderFeatureType type in ReminderFeatureType.values) {
      if (type.value.toLowerCase() == feature.toLowerCase()) {
        return type;
      }
    }
    return ReminderFeatureType.other;
  }
}

/// Navigation mapping for reminder features
class ReminderNavigationHelper {
  static String? getRouteForFeature(ReminderFeatureType featureType) {
    switch (featureType) {
      case ReminderFeatureType.rewards:
        return Routes.rewardDashboardPage;
      case ReminderFeatureType.referrals:
        return Routes.referralListPage;
      case ReminderFeatureType.dailyTasks:
        return Routes.dailyTaskPage;
      case ReminderFeatureType.challenges:
        return Routes.allMonthlyChallenges;
      case ReminderFeatureType.diet:
        return Routes.dietDashboard;
      case ReminderFeatureType.leaderboard:
        return Routes.leaderboard;
      case ReminderFeatureType.other:
        return null; // No specific navigation for 'other'
    }
  }

  static dynamic getArgumentsForFeature(ReminderFeatureType featureType) {
    switch (featureType) {
      case ReminderFeatureType.diet:
        return [false, false]; // showStepperOnTop: false, isFromBottom: false
      default:
        return null;
    }
  }
}

/// Handle Navigation when tapped on the Push Notification while App is closed
/// or in the background
Future<void> handleMessage(Map<String, dynamic> data) async {
  // Safely get title with null check
  String title = (data['title'] ?? '').toString().toLowerCase();
  String? documentId = data['documentId']?.toString();
  String? type = data['type']?.toString().toLowerCase();
  String? action = data['action']?.toString().toLowerCase();
  String? feature = data['feature']?.toString(); // Get feature field from API

  bool isSpotModule = title.contains('spot');
  bool isSingleMessage = title.contains('message');
  bool isGroupMessage = title.contains('group');
  bool isReminderNotification = title.contains('reminder') ||
      type == 'daily_reminder' ||
      action == 'navigate_diet';

  String? token = await CacheService.instance.retrieveBearerToken();

  if (token == null) {
    navigatorKey?.currentState?.pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => const LoginPage(),
      ),
      (route) => false,
    );
  } else if (isReminderNotification) {
    // Dynamic navigation based on feature field from reminder API
    ReminderFeatureType featureType = ReminderFeatureType.fromString(feature);
    String? route = ReminderNavigationHelper.getRouteForFeature(featureType);
    dynamic arguments = ReminderNavigationHelper.getArgumentsForFeature(featureType);

    if (route != null) {
      navigatorKey?.currentState?.pushNamed(
        route,
        arguments: arguments,
      );
    }
    // If route is null (for 'other' feature type), do nothing
  } else if (isSpotModule && documentId != null) {
    navigatorKey?.currentState?.push(
      MaterialPageRoute(
        builder: (context) => OthersProfilePage(
          userId: documentId,
        ),
      ),
    );
  } else if (isSingleMessage || isGroupMessage) {
    await SocketService.initialize();
    SocketService.instance.connect();

    navigatorKey?.currentState?.push(
      MaterialPageRoute(
        builder: (context) => const ChatDashboardPage(),
      ),
    );
  } else if (documentId != null) {
    navigatorKey?.currentState?.push(
      MaterialPageRoute(
        builder: (context) => TimelinePostDetails(
          postId: documentId,
        ),
      ),
    );
  }
  // If documentId is null and it's not a chat notification, do nothing
}
