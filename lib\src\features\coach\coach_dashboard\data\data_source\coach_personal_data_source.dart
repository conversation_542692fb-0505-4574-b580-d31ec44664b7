import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class CoachPersonalDataSource {
  Future<Response> getCoachOwnProfile({required String coachId});
  Future<Response> getCoachPrograms({required String coachId});
  Future<Response> getCoachProgram(
      {required String coachId, required String programId});
  Future<Response> getEnrollers({required String coachId});
  Future<Response> getEnrollerById({
    required String coachId,
    required String subscriptionId,
  });
  Future<Response> cancelProgramSubscriptionByCoach({
    required String coachId,
    required String subscriptionId,
    required Map<String, dynamic> data,
  });

  Future<Response> getCoachIncomeAmount({
    required String coachId,
  });

  Future<Response> getProgramEnrollerHistory({
    required String coachId,
    required String programId,
  });

  Future<Response> getProgramPaymentHistory({
    required String coachId,
    required String programId,
  });
}

class CoachPersonalDataSourceImpl extends CoachPersonalDataSource {
  final RestClient restClient;

  CoachPersonalDataSourceImpl({required this.restClient});

  @override
  Future<Response> getCoachOwnProfile({required String coachId}) async {
    final response =
        await restClient.get(APIType.PROTECTED, API.coachOwnProfile(coachId));

    return response;
  }

  @override
  Future<Response> getCoachProgram({
    required String coachId,
    required String programId,
  }) async {
    final response = await restClient.get(APIType.PROTECTED,
        '${API.coachProgramByPorgramId(coachId)}/$programId');

    return response;
  }

  @override
  Future<Response> getCoachPrograms({required String coachId}) async {
    final response =
        await restClient.get(APIType.PROTECTED, API.coachOwnPrograms(coachId));

    return response;
  }

  @override
  Future<Response> getEnrollerById({
    required String coachId,
    required String subscriptionId,
  }) async {
    final response = await restClient.get(APIType.PROTECTED,
        '${API.coachProgramSingleEnroller(coachId)}/$subscriptionId');

    return response;
  }

  @override
  Future<Response> getEnrollers({required String coachId}) async {
    final response = await restClient.get(
        APIType.PROTECTED, API.coachProgramEnrollers(coachId));

    return response;
  }

  @override
  Future<Response> cancelProgramSubscriptionByCoach({
    required String coachId,
    required String subscriptionId,
    required Map<String, dynamic> data,
  }) async {
    final response = restClient.post(APIType.PROTECTED,
        API.coachProgramSubscriptionCancel(coachId, subscriptionId), data);

    return response;
  }
  
  @override
  Future<Response> getCoachIncomeAmount({required String coachId}) {
    final response = restClient.get(APIType.PROTECTED, API.coachIncomeInfo(coachId));

    return response;
  }
  
  @override
  Future<Response> getProgramEnrollerHistory({required String coachId, required String programId,}) async {
    final response = await restClient.get(
        APIType.PROTECTED, API.coachProgramEnrollerHistory(coachId, programId));

    return response;
  }
  
  @override
  Future<Response> getProgramPaymentHistory({required String coachId, required String programId,}) async {
    final response = await restClient.get(
        APIType.PROTECTED, API.coachProgramPaymentHistory(coachId, programId));

    return response;
  }
}
