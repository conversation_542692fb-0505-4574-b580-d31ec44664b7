class TextConstants {
  /// Global
  static const String appName = 'Fitsomnia';
  static const cameraNotFound = 'Camera Not Found!';
  static const loadingMessage = 'Please wait...';
  static const somethingWentWrong = 'Something went wrong!';
  static const invalidEmailOrPass = 'Invalid email or password!';
  static const yes = 'Yes';
  static const no = 'No';
  static const close = 'Close';
  static const deleteNote = 'Delete Note';
  static const areYouSureToDeleteThisNote = 'Are you sure to delete this note?';

  ///global error message
  static const String connectionError =
      'Connection error, please check your connection';
  static const String pleaseTryAgain = 'Please try again';
  static const String requestSendSuccessfully = 'Request send successfully';

  static const String couldNotConnectToServer = 'Couldn\'t connect to server';
  static const String defaultErrorMessage =
      'Something went wrong. Please, try again later.';
  static const String alreadySendSpotRequest = 'Already send spot request';

  /// Suffix text
  static const String lb = 'lb';
  static const String gm = 'gm';
  static const String cal = 'cal';
  static const String kcal = 'kcal';

  /// OnBoarding Page
  static const String socialInteraction = 'Social Interaction';
  static const String socialInteractionDescription =
      'Fitsomnia is designed to be your one stop fitness app. Join Fitsomnia to be part of the largest fitness community. We offer everything you need to lead a healthy lifestyle.';
  static const String spotter = 'Need a Spotter?';
  static const String spotterDescription =
      'You don’t need to worry about finding a Spotter to lift more weights anymore. Come join Fitsomnia and find the right spotter for you!';
  static const String shopping = 'Shopping';
  static const String shoppingDescription =
      'Looking to buy your perfect gym clothing? Try our FitMarket on the Fitsomnia app';
  static const String dietDescription =
      'Tired of counting your calories? Come check out our Calorie tracker to track all your calories.';
  static const String workout = 'Workout';
  static const String workoutDescription =
      'Looking for the right exercises to make progress in the gym? Try our training programs!';

  static const String continueText = 'Continue';
  static const String skip = 'Skip';

  /// Login Page
  static const String login = 'Log In';

  /// Welcome Page
  static const welcomePageTitle = 'Welcome To Fitsomnia';
  static const welcomePageDescription =
      '''Your trusted fitness partner. Get all things in one place.''';
  static const String signUp = ' Sign Up';

  /// Sign Up Page
  static const String signUpButtonText = 'Sign Up';
  static const String firstName = 'First name';
  static const String lastName = 'Last name';
  static const String orSignUpWith = 'Or Sign Up with';
  static const String alreadyHaveAnAccount = 'Already have an account ? ';
  static const String passwordDoNotMatch = "Passwords don't match";

  /// Profile Screen
  static const String userName = 'User Name';
  static const String phone = 'Phone';
  static const String email = 'Email';
  static const String emailOrPhone = 'Email Address';
  static const String company = 'Company';
  static const String country = 'Country';
  static const String state = 'State';
  static const String postCode = 'PostCode';
  static const String tag = 'Tag';
  static const String addressLine1 = 'Address Line 1';
  static const String addressLine2 = 'Address Line 2';
  static const String editProfile = 'Edit Profile';
  static const String fitBuddies = 'Fit Buddies';
  static const String following = 'Following';
  static const String maxBench = 'Max Bench';
  static const String maxSquat = 'Max Squat';
  static const String timeline = 'Timeline';
  static const String tagged = 'Tagged';
  static const String hallOfWeight = 'Hall Of Weights';

  static const String changeProfilePhoto = 'Change Profile Photo';
  static const String updateProfile = 'Update Profile';
  static const String fullName = 'Full Name';
  static const String title = 'Title';
  static const String organization = 'Organization';
  static const String dob = 'Date of Birth';
  static const String gender = 'Gender';
  static const String name = 'Name';

  /// Spot Me
  static const String spotNotFilter = 'Spot Not Filter';
  static const String setDistance = 'Set Distance';
  static const String filter = 'Filter';
  static const String ghostMode = 'Ghost mode';
  static const String not = 'Not';
  static const String spot = 'Spot';
  static const String spotBack = 'Spot Back';
  static const String bench = 'Bench';
  static const String benchHint = 'Enter bench value';
  static const String squat = 'Squat';
  static const String squatHint = 'Enter squat value';
  static const String previous = 'Previous';
  static const String next = 'Next';
  static const String done = 'Done';
  static const String showFilterOptions = 'Show filter options';
  static const String mySettings = 'My Setting';
  static const String filterOptions = 'Filter Options';

  //Profile menu
  static const String profile = 'Profile';

  //Home
  static const String home = "Home";

  /// Near by user profile
  static const String backToHome = 'Back To Home';
  static const String spotted = 'Spotted';

  /// Login Screen
  static const String password = 'Password';
  static const String rememberMe = 'Remember Me';
  static const String forgotPassword = 'Forgot password ?';
  static const String otherLogins = 'Or Continue with';
  static const String doNotHaveAccount = 'Don’t have an account ? ';
  static const String signUP = ' Sign Up';
  static const String createAccount = 'Create Account';

  /// Verification Page
  static const String verification = 'Verification';
  static const String verificationDetails =
      '''You have been sent a one-time verification code to your phone. Please enter the code before the time limit expires in order to proceed.''';
  static const String enterYourOTPCode = 'Enter your OTP code';

  /// Forgot Password
  static const String forgotPasswordCaption = 'Forgot password';
  static const String forgotPasswordDescription =
      '''You have been sent a one-time verification code to your email address or phone number. Please enter the code before the time limit expires in order to proceed.''';
  static const String enterYourEmail = 'Enter Your Email';
  static const String sendOtp = 'Send OTP';
  static const String enterEmailOrPhone = 'Enter Email or Phone';

  /// View Post Page
  static const String postCaption =
      '''Lorem Ipsum is simply dummy text of the printing and industry Lorem Ipsum has been the industry's standard...see more''';
  static const String reply = 'Reply';
  static const String verifyOtpFailed = 'Please enter the correct OTP';

  /// Change Your Password Page
  static const String changeYourPassword = 'Change your password';
  static const String newPassword = 'New password';
  static const String confirmPassword = 'Confirm password';
  static const String changePassword = 'Change Password';
  static const String resetPassword = 'Reset Password';

  /// Notifications Page
  static const String notification = 'Notification';
  static const String spotList = 'Spot list';
  static const String others = 'Others';
  static const String all = 'All';
  static const String markAllAsRead = 'Mark all as read';
  static const String noNotificationFound = 'No Notification Found!';
  static const String notificationTitle =
      'Robert Johnson just started to follow you';

  /// Chatting list
  static const String chatSearchBarHint = 'Search  people, groups, chats';
  static const String online = 'Online';
  static const String activePeople = 'Active People';
  static const String total124 = ' (124 total)';
  static const String groups = 'Groups';
  static const String total24 = ' (24 total)';
  static const String seeAll = 'See all';

  /// Chatting list - active list
  static const String johnRick = 'John Rick';
  static const String adamBrick = 'Adam Brick';
  static const String vanitaSmith = 'Vanita Smith';
  static const String kendraSmith = 'Kendra Smith';
  static const String laraSmith = 'Lara Smith';
  static const String britney = 'Britney';

  /// Chatting List - Group Name
  static const String anytimeFitness = 'Anytime Fitness';
  static const String noPain = 'No Pain';
  static const String fitnessWorker = 'Fitness Worker';
  static const String gymFitness = 'Gym Fitness';
  static const String fitnessCenter = 'Fitness Center';
  static const String fitness = 'Fitness';

  /// Chat List -- Chat
  static const String janiceHperaza = 'Janice H. Peraza';
  static const String debraJfowler = 'Debra J. Fowler';
  static const String jamesWnewby = 'James W. Newby';
  static const String pennyWgarrett = 'Penny W. Garrett';

  /// Create Diet
  static const String yourAge = 'Your Age*';
  static const String yourWeight = 'Your Weight*';
  static const String cm = 'cm';
  static const String ft = 'ft';
  static const String inc = 'in';
  static const String yourHeight = 'Your Height*';
  static const String kg = 'kg';
  static const String targetWeight = 'Target Weight*';
  static const String loosePerWeek = 'Loose Per Week*';
  static const String fat = 'Fat';
  static const String activity = 'Activity';
  static const String male = 'Male';
  static const String female = 'Female';

  //Create diet popup message
  

  /// Create Story Page
  static const String holdForVideo = "Hold for record Video";
  static const String tapForVideo = "Tap to record Video";
  static const String typeHere = 'Type here...';
  static const String failedToCreateStory = 'Failed to create story';
  static const String storyCreatedSuccessfully = 'Story created successfully';

  /// Diet
  static const String dietDashboard = 'Diet Dashboard';
  static const String noDietPlanFound = 'No Diet Plan Found';
  static const String noDietPlanFoundMessage =
      'It looks like you didn\'t create any diet plan yet. Do you want to create one?';
  static const String createdSuccessfully = 'Created successfully';
  static const String trackDiet = 'Diet';
  static const String foodDashboard = 'Food Dashboard';
  static const String recentlyAdded = 'Recently Added';
  static const String foodList = 'Foods';
  static const String consumedFoodList = 'Consumed Foods';
  static const String breakfast = 'Breakfast';
  static const String lunch = 'Lunch';
  static const String dinner = 'Dinner';
  static const String snacks = 'Snacks';
  static const String search = 'Search';
  static const String addFood = 'Add Food';
  static const String createFood = 'Create Food';
  static const String addPersonalisedFood = 'Add Personalised Food';
  static const String addPersonalisedFoodDescription =
      'Take control of your meal planning by adding your own personalized foods to your diet app.';
  static const String foodName = 'Name';
  static const String calories = 'Calories';
  static const String carb = 'Carb';
  static const String protein = 'Protein';

  /// Settings
  static const String settings = 'Settings';
  static const String personalInformation = 'Personal Information';
  static const String subscription = 'Subscription';
  static const String security = 'Security';
  static const String support = 'Support';

  static const String changeEmail = 'Change Email';
  static const String newEmailAndVerify =
      "Enter your new email address and verify it with a confirmation code.";
  static const String enterOTP =
      "Please enter the confirmation code sent to your email address";
  static const String enterEmail = 'Enter Email';
  static const String enterCode = 'Enter Code';
  static const String sendOTP = 'Send OTP';

  static const String unlinkSocialMediaAccount = 'Unlink Social Media Accounts';
  static const String socialMediaAccount = 'Social Media Account';
  static const String googleAccount = 'Google';
  static const String facebookAccount = 'Facebook';
  static const String appleAccount = 'Apple';
  static const String deleteAccount = 'Delete Account';

  static const String privacy = 'Privacy';
  static const String blockList = 'Block List';
  static const String unBlock = 'Unblock';
  static const String unBlockConfirmation = 'Are you sure you want to unblock?';
  static const String cancel = 'Cancel';
  static const String unBlockedSuccessfully = 'Unblocked successfully';
  static const String privateAccount = 'Private Account';
  static const String photosAndVideosAreMorePrivate =
      'Your photos and videos are more private';

  static const String notifications = 'Notifications';
  static const String emailNotification = 'Email Notifications';
  static const String pushNotification = 'Push Notifications';
  static const String showNotification = 'Show Notifications';
  static const String youWillReceivePushNotificationOnYourDevice =
      'You will receive push notifications on your device';

  static const String about = 'About';
  static const String privacyPolicy = 'Privacy Policy';
  static const String termsOfUse = 'Terms of Use';
  static const String aboutUs = 'About us';
  static const String failedToLoadChallenges = 'Failed to Load Challenges';
  static const String failedToLoadData = 'Failed to load data';
  static const String failedToCreateData = 'Failed to create data';
  static const String failedToDeleteData = 'Failed to delete data';
  static const String unexpectedError =
      'Something unexpected happened. Please try again later..';
  static const String failedToLoadImages = 'Failed to load images';
  static const String failedToLoadVideos = 'Failed to load videos';

  /// MISC
  static const String diet = 'Diet';
  static const String training = 'Training';
  static const String planner = 'Planner';
  static const String orderHistory = 'Order History';
  static const String logout = 'Logout';
  static const String termsAndConditions = 'Terms and Conditions';
  static const String dummyPlaceholderText =
      '''Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old. Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source. Lorem Ipsum comes from sections 1.10.32 and 1.10.33 of "de Finibus Bonorum et Malorum" (The Extremes of Good and Evil) by Cicero, written in 45 BC. This book is a treatise on the theory of ethics, very popular during the Renaissance. The first line of Lorem Ipsum, "Lorem ipsum dolor sit amet..", comes from a line in section 1.10.32.

The standard chunk of Lorem Ipsum used since the 1500s is reproduced below for those interested. Sections 1.10.32 and 1.10.33 from "de Finibus Bonorum et Malorum" by Cicero are also reproduced in their exact original form, accompanied by English versions from the 1914 translation by H. Rackham.''';

  /// Spot Not Filter Page
  static const String showMe = 'Show Me';
  static const String age = 'Age';
  static const String workOutType = 'Workout type';
  static const String bodyType = 'Body Type';
  static const String gym = 'Gym';
  static const String interest = 'Interests';

  /// Planner page
  static const String noEventFound = 'No Event Found';
  static const String content = 'Content';
  static const String add = 'Add';
  static const String noteDetails = 'Note details';

  /// Club
  static const String club = 'Gym';
  static const String myClub = 'My Gym';
  static const String peopleFromNearbyClubs = 'People From Nearby Gym';
  static const String liveMembersNearYou = 'Live Members Near You';
  static const String peopleFromYourClub = 'People From Your Gym';
  static const String SPOTTED = 'SPOTTED';
  static const String NOT_SPOTTED = 'NOT_SPOTTED';
  static const String REQUESTED = 'REQUESTED';
  static const String SPOT_BACK = 'SPOT_BACK';
  static const String liveLocationPermissionFeedback =
      'Please enable location service or give permission to see live members near you.';
  static const String nearbyClubsLocationPermissionFeedback =
      'Please enable location service or give permission to see people from nearby Gym';
  static const String noClubFoundNearYourLocation = "No gym found near your location";
  static const String clubsNearYou = "Gym near you";
  static const String searchClubYouWantToJoin = "Search the gym you want to join";
  static const String searchClub = "Search Gym";
  static const String searchHere = 'Search here...';

  /// Story
  static const String selectAtLeastOneMedia = "Long press to select a media";

  /// News feed
  static const String post = 'Post';
  static const String newPost = 'New Post';
  static const String createPost = 'Create post';
  static const String pleaseSelect = 'Please Select';
  static const String shareYourFeelings = 'Share your feelings';
  static const String noDataAvailable = 'No data available';
  static const String cannotPlayVideo = 'Cannot play this video';

  /// Comment
  static const String comment = 'Comment';
  static const String writeAComment = 'Write a comment...';
  static const String commentFailed = 'Failed to comment in the post';
  static const String commentAddedSuccessfully = 'Comment added successfully';
  static const String noCommentsFound = 'No comment found';

  /// post reaction
  static const String noReactionFound = 'No reaction found';

  /// QR Scanner and Generator
  static const String scanQRCode = 'Scan QR Code';
  static const String generateQRCode = 'Generate QR Code';
  static const String fitsomniaQR = '+FITSOMNIA';
  static const String fitCodeMessage =
      'People can scan your QR Code to Spot you!';

  /// Chat Module
  static const String chat = 'Chat';

  /// Filter By Values for Hall of Weight
  static const String filterByInProgress = 'In progress';
  static const String filterByPending = 'Pending';
  static const String filterByExpired = 'Expired';
  static const String filterByCompleted = 'Completed';
  static const String filterByDeclined = 'Declined';

  /// Hall of Weight
  static const String myPoints = 'My Points';
  static const String pointsInformation =
      'Points will be converted in cash when you buy from fitmarket. Click on points to see your history and redeem points.';
  static const String pointsABBR = 'Pts';
  static const String monthlyChallenge = 'Monthly Challenges';
  static const String failedToUploadVideo = 'Failed to Upload Video';
  static const String failedToLoadLeaderboard = 'Failed to Load Leaderboard';
  static const String failedToLoadPointsHistory =
      'Failed to Load Points History';

  /// Training
  static const String selectYourExpertise = 'Select Your Expertise';
  static const String selectYourExercise = 'Select Your Exercise';
  static const String pushExercises = 'Push Exercises';
  static const String selectYourBodybuildingProgram =
      'Select Your Bodybuilding Program';
  static const String chooseMuscleGroupToTrainYourself =
      'Choose Muscle Group To  Train Yourself';
  static const String trainingsThatFitYou = 'Trainings That Fit You';
  static const String expandedExercise = 'Expanded Exercise';
  static const String backToExercise = 'Back To Exercise';
  static const String goToNextScreen = 'Go To Next Screen';
  static const String error = 'Error';
  static const String exerciseCategory = 'Exercise Category';

  /// Fit Market
  static const String viewAll = 'View All';
  static const String trendingProducts = 'Trending Products';
  static const String bestSelling = 'Best Selling';
  static const String dealOfTheDay = 'Deal Of The Day';
  static const String topRated = 'Top Rated';
  static const String youMayLike = 'You May Like';
  static const String exploreAll = 'Explore All';
  static const String productDetails = 'Product Details';
  static const String productDescriptions =
      'Men Fitness T shirt For All Kind Of Workout Easy to fit for body';
  static const String size = 'Size :';
  static const String sizeGuide = 'Size Guide';
  static const String description = 'Description';
  static const String productDescription =
      'The standard chunk of Lorem Ipsum used since the 1500s is reproduced below for those interested. Sections 1.10.32 and 1.10.33 from "de Finibus Bonorum et Malorum" by Cicero are also reproduced in their exact original form, accompanied by English versions from the 1914 translation by H. Rackham.';
  static const String questionsAndAnswer = 'Questions & Answers';
  static const String ratingsAndReviews = 'Ratings & Reviews';
  static const String youMayAlsoLike = 'You may also like';
  static const String addToCart = 'Add To Cart';
  static const String buyNow = 'Buy Now';
  static const String productReview = 'Good Quality Product. Everything is ok';
  static const String cart = 'Cart';
  static const String checkout = 'Checkout';
  static const String selectShippingAddress = 'Select shipping address';
  static const String selectBillingAddress = 'Select billing address';
  static const String edit = 'Edit';
  static const String addNewAddress = 'Add new address';
  static const String addShippingAddress = 'Add Shipping Address';
  static const String addBillingAddress = 'Add Billing Address';
  static const String updateShippingAddress = 'Update Shipping Address';
  static const String updateBillingAddress = 'Update Billing Address';
  static const String save = 'Save';
  static const String makeItMyDefaultAddress = 'Make it my default address';
  static const String confirm = 'Confirm';
  static const String totalPrice = 'Total Price';
  static const String tax = 'TAX';
  static const String points = 'Points';
  static const String discount = 'Discount';
  static const String payment = 'Payment';
  static const String selectPaymentMethod = 'Select Payment Method';
  static const String shortOnCash = 'Short On Cash';
  static const String payByDigitalCash = 'Pay By Digital Cash';
  static const String payLater = 'Pay Later';
  static const String update = 'Update';
  static const String delete = 'Delete';
  static const String loading = 'Loading';
  static const String shopNow = 'Shop now';
  static const String addedToWishlist = 'Added to Wishlist';
  static const String removeFromWishlist = 'Removed from Wishlist';
  static const String wishlist = 'Wishlist';
  static const String orderDetails = 'Order Details';

  /// media
  static const String media = 'Media';

  /// Primary App Bar Action Items
  static const String actionItemsNotification = 'notification';
  static const String actionItemsMessage = 'message';
  static const String actionItemsWishlist = 'wishlist';
  static const String actionItemsCart = 'cart';
  static const String actionItemsQRScanner = 'qr';

  /// Address Type
  static const String addressTypeShipping = 'shipping';
  static const String addressTypeBilling = 'billing';

  /// Spot Me
  static const String profileNotFound = 'PROFILE_NOT_FOUND';

  /// Post Details
  static const String postDetails = 'Post Details';

  ///SubscriptionErrorScreen
  static const String subscriptionErrorTitle = 'Free Daily Limit Exceeded';
  static const String subscriptionErrorDetails =
      'You have reached the maximum usage for spot not and gym feature for today under the free plan. To continue using these features without interruption, please consider subscribing to a premium plan.';

  /// Club Disclaimers
  static const String clubDisclaimer1 =
      'This fitness app features a "Gym" section that includes a database of gym names, allowing users to virtually join and switch between different gyms. It is important to note that the inclusion of these gym names within the app does not indicate endorsement, affiliation, or authorization from the respective gym owners or authorized sources unless explicitly stated.';
  static const String clubDisclaimer2 =
      'The gym names listed in this app are sourced from publicly available directories and other publicly accessible information. While efforts have been made to ensure the accuracy and timeliness of the information, we cannot guarantee its completeness or reliability.';
  static const String clubDisclaimer3 =
      'Please be aware that the virtual joining and switching of gyms within this app does not grant users actual membership or access to these gyms in real life. The "Gym" feature is intended for entertainment and interactive purposes only and should not be construed as an official representation of any gym or membership status.';
  static const String clubDisclaimer4 =
      'Additionally, this app includes a sub-feature within the Gym feature that allows users to view each other\'s live location and personal information. It is important to respect the privacy of all users. The sharing of personal information and live location within the app is solely at the discretion and responsibility of the users involved. We strongly encourage users to exercise caution and discretion when sharing personal information or live location data.';
  static const String clubDisclaimer5 =
      'By using this app and participating in the "Gym" feature or utilizing the sub-feature for sharing personal information and live location, you acknowledge and agree that the app developer, its affiliates, and partners shall not be held responsible or liable for any inaccuracies, omissions, legal implications, privacy concerns, or discrepancies that may arise from the use of gym names or personal information within this app.';
  static const String clubDisclaimer6 =
      'We advise users to carefully consider the implications of sharing personal information and live location data, and to consult with legal professionals regarding privacy and data protection laws, intellectual property rights, and any other legal considerations before using the app\'s features';

  /// fitbot
  static const String fitbotName = 'Ujjibok';

  /// coach
  static const String coach = 'Coach';
  static const String coachApplicationUnderReviewText = 'Your coach application is under review. Need 3 to 5 working days for processing.';

}
