import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/exception/network_exception.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/data/data_source/reward_leaderboard_data_source.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/data/model/reward_point_rank_model.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_point_rank_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/data/data_source/reward_point_data_source.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/data/model/reward_point_history_model.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/data/model/reward_point_model.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/data/model/user_login_activity_model.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/reward_point_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/reward_point_history_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/user_login_activity_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/repositories/reward_point_repository.dart';

class RewardPointRepositoryImpl extends RewardPointRepository{
  final RewardPointDataSource dataSource;

  RewardPointRepositoryImpl({required this.dataSource});

  @override
  Future<Either<ErrorModel, RewardPointRankEntity>> getCurrentPoints() async{
    try {
      final Response response = await dataSource.getCurrentPoints();
      final data = response.data["data"];

      RewardPointRankEntity entity = RewardPointRankModel.fromJson(data).toEntity();

      return Right(entity);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }
  

  @override
  Future<Either<ErrorModel, UserLoginActivityEntity>> getWeeklyLoginHistory({required String userId}) async {
     try {
      // return Right(testUserLoginActivity);
      final Response response = await dataSource.getWeeklyLoginHistory(userId: userId);
      final data = response.data["data"]; 
      
      UserLoginActivityEntity entity = UserLoginActivityModel.fromJson(data).toEntity();

      return Right(entity);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, List<RewardPointHistoryEntity>>> getRewardPointHistory({required int? offset, required int? limit, required String? pointHistoryFilter}) async {
    try {
      // return Right(testUserLoginActivity);
      final Response response = await dataSource.getRewardPointHistory(offset: offset, limit: limit, pointHistoryFilter: pointHistoryFilter);
      final data = response.data["data"]; 
      
      List<RewardPointHistoryEntity> history = List<RewardPointHistoryEntity>.from(data!.map((x) => RewardPointHistoryModel.fromJson(x).toEntity()));

      return Right(history);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }
  
}