part of 'coach_program_review_bloc.dart';

sealed class CoachProgramReviewEvent extends Equatable {
  const CoachProgramReviewEvent();

  @override
  List<Object> get props => [];
}

// coach program review
class ReviewCoachProgramEvent extends CoachProgramReviewEvent {
  final String programId;
  final UserReviewRequestEntity reviewRequest;

  const ReviewCoachProgramEvent(this.programId, {required this.reviewRequest});

  @override
  List<Object> get props => [programId, reviewRequest];
}

class GetCoachProgramReviews extends CoachProgramReviewEvent {
  final String programId;
  final int? offset;
  final int? limit;

  const GetCoachProgramReviews({
    required this.programId,
    this.offset,
    this.limit,
  });
  @override
  List<Object> get props => [programId];
}

class GetCoachProgramSingleReview extends CoachProgramReviewEvent {
  final String reviewId;

  const GetCoachProgramSingleReview({required this.reviewId});
  @override
  List<Object> get props => [reviewId];
}

class DeleteCoachProgramReview extends CoachProgramReviewEvent {
  final String reviewId;

  const DeleteCoachProgramReview({required this.reviewId});
  @override
  List<Object> get props => [reviewId];
}

class UpdateCoachProgramReview extends CoachProgramReviewEvent {
  final String reviewId;
  final UserReviewRequestEntity reviewRequest;

  const UpdateCoachProgramReview(this.reviewId, {required this.reviewRequest});

  @override
  List<Object> get props => [reviewRequest];
}

// coach profile review
class ReviewCoachProfileEvent extends CoachProgramReviewEvent {
  final String coachId;
  final UserReviewRequestEntity reviewRequest;

  const ReviewCoachProfileEvent(this.coachId, {required this.reviewRequest});

  @override
  List<Object> get props => [coachId, reviewRequest];
}

class GetCoachProfileReviews extends CoachProgramReviewEvent {
  final String coachId;
  final int? offset;
  final int? limit;

  const GetCoachProfileReviews({
    required this.coachId,
    this.offset,
    this.limit,
  });
  @override
  List<Object> get props => [coachId];
}

class GetCoachProfileSingleReview extends CoachProgramReviewEvent {
  final String reviewId;

  const GetCoachProfileSingleReview({required this.reviewId});
  @override
  List<Object> get props => [reviewId];
}

class DeleteCoachProfileReview extends CoachProgramReviewEvent {
  final String reviewId;

  const DeleteCoachProfileReview({required this.reviewId});
  @override
  List<Object> get props => [reviewId];
}

class UpdateCoachProfileReview extends CoachProgramReviewEvent {
  final String reviewId;
  final UserReviewRequestEntity reviewRequest;

  const UpdateCoachProfileReview(this.reviewId, {required this.reviewRequest});

  @override
  List<Object> get props => [reviewRequest];
}