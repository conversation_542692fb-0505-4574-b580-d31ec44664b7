import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SocialIconContainer extends StatefulWidget {
  const SocialIconContainer({
    required this.onTap,
    required this.icon,
    this.width = Values.v38,
    this.height = Values.v38,
    this.padding = Values.v10,
    this.isLoading = false,
    Key? key,
  }) : super(key: key);

  final VoidCallback onTap;
  final String icon;
  final double width;
  final double height;
  final double padding;
  final bool isLoading;

  @override
  State<SocialIconContainer> createState() => _SocialIconContainerState();
}

class _SocialIconContainerState extends State<SocialIconContainer> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: SizedBox(
        width: 48.r,
        height: 48.r,
        child: Center(
          child: SvgPicture.asset(
            widget.icon,
            width: widget.width.w,
            height: widget.height.h,
          ),
        ),
      ),
    );
  }
}
