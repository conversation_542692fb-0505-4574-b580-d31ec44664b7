import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/fit_market/cart/domain/entities/cart_entity.dart';

class CoachProgramModel extends CoachProgramEntity {
  CoachProgramModel({
    required super.programId,
    required super.coachId,
    required super.title,
    required super.desc,
    required super.features,
    required super.guarantees,
    required super.totalSubscription,
    required super.totalIncome,
    required super.isBestProgram,
    required super.categoryId,
    required super.subCategoryId,
    super.oneTimePrice,
    super.dailyPrice,
    super.weeklyPrice,
    super.monthlyPrice,
    super.hasSubscription,
    required super.durationType,
    required super.durationCount,
  });

  factory CoachProgramModel.fromJson(Map<String, dynamic> json) {
    return CoachProgramModel(
      programId: json['id'],
      coachId: json['coachId'],
      title: json['title'] ?? '',
      desc: json['description'] ?? '',
      categoryId: json['categoryId'] ?? '',
      subCategoryId: json['subCategoryId'] ?? '',
      oneTimePrice: (json['oneTimePrice'] == null)
          ? null
          : CoachProgramPaymentInfo.fromJson(json['oneTimePrice']),
      dailyPrice: (json['dailyPrice'] == null)
          ? null
          : CoachProgramPaymentInfo.fromJson(json['dailyPrice']),
      weeklyPrice: (json['weeklyPrice'] == null)
          ? null
          : CoachProgramPaymentInfo.fromJson(json['weeklyPrice']),
      monthlyPrice: (json['monthlyPrice'] == null)
          ? null
          : CoachProgramPaymentInfo.fromJson(json['monthlyPrice']),
      features: (json['features'] == null)
          ? []
          : List<CoachProgramFeature>.from(
              json['features']!.map((x) => CoachProgramFeature.fromJson(x))),
      guarantees: (json['guarantees'] == null)
          ? []
          : List<String>.from(json['guarantees']!.map((x) => x.toString())),
      totalSubscription: json['totalSubscription'] ?? 0,
      totalIncome: json['totalEarnings'] ?? 0,
      isBestProgram: json['isBestProgram'] ?? false,
      hasSubscription: json['hasSubscription'] ?? false,
      durationType: json['durationTerm'] ?? 'months',
      durationCount: json['durationCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (coachId != null) 'coachId': coachId,
      if (programId != null) 'programId': programId,
      'title': title,
      'description': desc,
      'categoryId': categoryId,
      'subCategoryId': subCategoryId,
      if (oneTimePrice != null) 'oneTimePrice': oneTimePrice!.toJson(),
      // if (dailyPrice != null) 'dailyPrice': dailyPrice!.toJson(),
      // if (weeklyPrice != null) 'weeklyPrice': weeklyPrice!.toJson(),
      if (monthlyPrice != null) 'monthlyPrice': monthlyPrice!.toJson(),
      'features': List<dynamic>.from(features.map<dynamic>((x) => x.toJson())),
      'guarantees': List<dynamic>.from(guarantees.map((x) => x.toString())),
      'durationTerm': durationType,
      'durationCount': durationCount,
    };
  }
  // List<CoachProgramFeature>.from(features.map((x)=>x.toJson())),
  //"photos": List<AdvertisePhoto>.from(photos.map((x) => x.toJson())),
  // features.map<dynamic>((x)=>x.toJson()).toList()

  factory CoachProgramModel.fromEntity(CoachProgramEntity entity) {
    return CoachProgramModel(
      programId: entity.programId,
      coachId: entity.coachId,
      title: entity.title,
      desc: entity.desc,
      categoryId: entity.categoryId,
      subCategoryId: entity.subCategoryId,
      oneTimePrice: entity.oneTimePrice,
      dailyPrice: entity.dailyPrice,
      weeklyPrice: entity.weeklyPrice,
      monthlyPrice: entity.monthlyPrice,
      features: entity.features,
      guarantees: entity.guarantees,
      totalSubscription: entity.totalSubscription,
      totalIncome: entity.totalIncome,
      isBestProgram: entity.isBestProgram,
      hasSubscription: entity.hasSubscription,
      durationType: entity.durationType,
      durationCount: entity.durationCount,
    );
  }
}

class CoachProgramPaymentInfo {
  String? paymentTerm;
  double? actualPrice = 0;
  double? discountedPrice = 0;

  CoachProgramPaymentInfo({
    this.actualPrice,
    this.discountedPrice,
    this.paymentTerm,
  });

  // in app discountedPrice is larger
  // in backend actual price is larger
  factory CoachProgramPaymentInfo.fromJson(Map<String, dynamic> json) {
    return CoachProgramPaymentInfo(
      discountedPrice: (json['discountedPrice'] is double)
          ? json['discountedPrice']
          : json['discountedPrice'].toDouble(),
      actualPrice: (json['actualPrice'] is double)
          ? json['actualPrice']
          : json['actualPrice'].toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'discountedPrice': discountedPrice,
      'actualPrice': actualPrice ?? discountedPrice,
    };
  }
}
