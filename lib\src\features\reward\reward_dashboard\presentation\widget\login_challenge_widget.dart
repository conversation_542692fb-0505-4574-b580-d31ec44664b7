import 'dart:math';

import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/firebase/firebase_service.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_leaderboard_data_entity.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/presentation/bloc/reward_leaderboard_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/user_login_activity_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/presentation/bloc/reward_point_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/presentation/widget/user_login_status_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_image_stack/flutter_image_stack.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LoginChallengeWidget extends StatefulWidget {
  const LoginChallengeWidget({super.key, required this.userId});
  final String userId;

  @override
  State<LoginChallengeWidget> createState() => _LoginChallengeWidgetState();
}

class _LoginChallengeWidgetState extends State<LoginChallengeWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _buildDailyChallengeCard();
  }

  _buildDailyChallengeCard() {
    return Container(
      padding: EdgeInsets.all(Values.v20),
      decoration: BoxDecoration(
        gradient: _backgroundGradientGreenColor(),
        borderRadius: BorderRadius.circular(Values.v12),
      ),
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeaderTextSection(),
          SizedBox(
            height: Values.v20,
          ),
          Align(child: _buildDailyLoginDataSection(),),
          // SizedBox(
          //   height: Values.v10,
          // ),
          _buildBottomInfoSection(),
        ],
      ),
    );
  }

  _backgroundGradientGreenColor() {
    return LinearGradient(colors: [UIColors.greenDeep, UIColors.greenLight]);
  }

  _buildHeaderTextSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildFireImage(),
        SizedBox(
          width: Values.v10,
        ),
        _buildHeaderText()
      ],
    );
  }

  _buildDailyLoginDataSection() {
    return UserLoginStatusWidget(
      userId: widget.userId,
    );
  }

  _buildBottomInfoSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Flexible(child: LeaderboardUserImageStackWidget()),
        Flexible(child: _buildViewLeaderboardButtonSection()),
      ],
    );
  }

  _buildViewLeaderboardButtonSection() {
    return Button.filled(
      textStyle: AppTypography.poppinsMedium16(color: UIColors.white),
      borderColor: UIColors.white,
      background: UIColors.primaryGreen700,
      label: 'Leaderboard',
      onPressed: () {
        Log.debug('reward leaderboard');
        FirebaseService()
                  .logFeatureUsage('rewards', 'rewards_leaderboard', '');
        if (mounted)
          Navigator.of(context).pushNamed(Routes.rewardLeaderboardPage);
      },
      // width: Values.v200,
    );
  }

  _buildFireImage() {
    return Image.asset(
      Assets.fireIconImg,
      width: Values.v32,
      height: Values.v32,
    );
  }

  _buildHeaderText() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Daily Streak',
            style: AppTypography.poppinsSemiBold20(color: UIColors.white)),
        Text(
          'Everyday in a week login challenge',
          style: AppTypography.poppinsMedium14(color: UIColors.white),
        ),
      ],
    );
  }
}

class LeaderboardUserImageStackWidget extends StatefulWidget {
  const LeaderboardUserImageStackWidget({super.key});

  @override
  State<LeaderboardUserImageStackWidget> createState() =>
      _LeaderboardUserImageStackWidgetState();
}

class _LeaderboardUserImageStackWidgetState
    extends State<LeaderboardUserImageStackWidget> {
  late List<RewardLeaderboardDataEntity> _topUserLists;

  bool _isLoading = false;
  @override
  void initState() {
    // _createTestData();

    _topUserLists = [];
    _isLoading = true;
    BlocProvider.of<RewardLeaderboardBloc>(context).add(
        GetRewardLeaderboardTopUserList(
            limit: 10, offset: 0, filterType: null));

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RewardLeaderboardBloc, RewardLeaderboardState>(
      listener: (context, state) {
        if (state is GetRewardLeaderboardTopUserListSuccess) {
          setState(() {
            _topUserLists = state.leaderboardEntity;
            _isLoading = false;
          });
        }

        if (state is GetRewardLeaderboardTopUserListFail) {
          setState(() {
            _topUserLists = [];
            _isLoading = false;
          });
        }
      },
      child: (_isLoading || _topUserLists.isEmpty)
          ? SizedBox.shrink()
          : _buildLeaderboardUserInfoSection(),
    );
  }

  _buildLeaderboardUserInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildLeadearboardUserImageStack(),
        SizedBox(
          height: Values.v5,
        ),
        _buildLeaderboardUserText(),
      ],
    );
  }

  String imageTemp =
      "https://dev-public-cdn.fitsomnia.com/original/user/2025/1/6/f1af7004-1dcc-4d4c-8a18-b4d652a58759/858174_17361931530061000005669.webp";
  List<String> images = [];

  _buildLeadearboardUserImageStack() {
    //// for testing
    // images = List.generate(10, (index) {
    //   return imageTemp;
    // });

    images = _topUserLists.map((entity) => entity.userImage).toList();

    final userImages = images.map((imagePath) {
      if (imagePath == null || imagePath == '') {
        return ExactAssetImage(Assets.spotMeNoImage) as ImageProvider;
      }

      return NetworkImage(imagePath) as ImageProvider;
    }).toList();

    if (userImages.isEmpty)
      return SizedBox(
        height: Values.v40,
      );

    int totalVoterCount = userImages.length;
    int imageCount = 6;

    return GestureDetector(
      onTap: () {
        //TODO:
        Log.debug('press on user list');
      },
      child: FlutterImageStack.providers(
        providers: userImages,
        itemRadius: Values.v32.r,
        totalCount: totalVoterCount,
        itemCount: min(imageCount, totalVoterCount),
        itemBorderWidth: Values.v2,
        itemBorderColor: AppColors.white,
        backgroundColor: UIColors.primaryGreen600,
        showTotalCount: false, //(totalVoterCount > imageCount),
        extraCountTextStyle:
            AppTypography.poppinsSemiBold16(color: UIColors.white),
      ),
    );
  }

  _buildLeaderboardUserText() {
    return Text(
      'Top User in leaderboard!',
      style: AppTypography.poppinsMedium14(color: UIColors.white),
      textAlign: TextAlign.center,
    );
  }
}
