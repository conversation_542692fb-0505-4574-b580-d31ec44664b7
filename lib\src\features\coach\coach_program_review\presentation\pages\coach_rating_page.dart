
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/presentation/widgets/coach_profile_reviews_widget.dart';
import 'package:flutter/material.dart';

class CoachRatingPage extends StatefulWidget {
  const CoachRatingPage({super.key, required this.coachId});
  final String coachId;

  @override
  State<CoachRatingPage> createState() => _CoachRatingPageState();
}

class _CoachRatingPageState extends State<CoachRatingPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Reviews'),
      ),
      body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(Values.v20),
            child: SingleChildScrollView(child: CoachProfileReviewsWidget(coachId: widget.coachId, isShowAllRating: true,)),
          ),
      ),
    );
  }
}
