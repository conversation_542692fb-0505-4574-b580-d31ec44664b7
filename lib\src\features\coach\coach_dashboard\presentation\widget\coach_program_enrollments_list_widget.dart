import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/bloc/coach_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';

class CoachProgramEnrollmentsListWidget extends StatefulWidget {
  const CoachProgramEnrollmentsListWidget({super.key, required this.coachId});
  final String coachId;

  @override
  State<CoachProgramEnrollmentsListWidget> createState() =>
      _CoachProgramEnrollmentsListWidgetState();
}

class _CoachProgramEnrollmentsListWidgetState
    extends State<CoachProgramEnrollmentsListWidget> {
  List<CoachProgramEnrollmentEntity> _enrolledPrograms = [];
  List<CoachProgramEnrollmentEntity> testPrograms = List.generate(5, (index) {
    return testProgramEnrollmentEntity;
  });

  @override
  void initState() {
    //TODO disable for testing
    // _enrolledPrograms = testPrograms;

    BlocProvider.of<CoachDashboardBloc>(context)
        .add(GetCoachAllProgramEnrollmentHistoryEvent(coachId: widget.coachId));

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachDashboardBloc, CoachDashboardState>(
      listener: (context, state) {
        if (state is GetCoachProgramEnrollersSuccess) {
          Log.debug('get user enrolled program success');
          setState(() {
            _enrolledPrograms = state.userSubscriptionHistoryEntity;
          });
        }

        if (state is GetCoachProgramEnrollersFail) {
          Log.debug('get user enrolled program fail');
        }
      },
      child: Container(
        margin: EdgeInsets.only(top: Values.v40),
        child: Column(
          children: [
            _buildProgramSectionHeader(
              Assets.coachProgramEnrolledIcon,
              'Enrollments',
              showCount: true,
              iconColor: UIColors.primaryGreen100,
              textColor: UIColors.primary,
            ),
            _buildStudentProgramsEnrollmentList(),
          ],
        ),
      ),
    );
  }

  _buildProgramSectionHeader(
    String icon,
    String title, {
    required bool showCount,
    Color? iconColor,
    Color? textColor,
  }) {
    return Row(
      children: [
        Container(
          margin: const EdgeInsets.only(right: Values.v4),
          decoration: BoxDecoration(
            color: (iconColor != null) ? iconColor : UIColors.primaryGreen100,
            borderRadius: BorderRadius.circular(Values.v36),
          ),
          child: Center(
            child: SvgPicture.asset(
              icon,
              height: Values.v36,
              width: Values.v36,
            ),
          ),
        ),
        Text(
          '$title ${(showCount && _enrolledPrograms.isNotEmpty) ? '(${_enrolledPrograms.length})' : ''}',
          style: AppTypography.poppinsSemiBold20(
              color:
                  (textColor != null) ? textColor : UIColors.primaryGreen950),
        ),
      ],
    );
  }

  _buildStudentProgramsEnrollmentList() {
    return (_enrolledPrograms.isEmpty)
        ? SizedBox(
            height: 200,
            child: Center(
              child: Text(
                'No Enrollments',
                style: AppTypography.poppinsMedium16(
                    color: AppColors.greyscale400),
              ),
            ),
          )
        : Column(
            children: _enrolledPrograms.map<Widget>((programEnrollmentEntity) {
              return CoachProgrammSingleEnrollmentInfoWidget(
                  programEnrollmentEntity: programEnrollmentEntity);
            }).toList(),
          );
  }
}

class CoachProgrammSingleEnrollmentInfoWidget extends StatefulWidget {
  const CoachProgrammSingleEnrollmentInfoWidget(
      {super.key, required this.programEnrollmentEntity});

  final CoachProgramEnrollmentEntity programEnrollmentEntity;

  @override
  State<CoachProgrammSingleEnrollmentInfoWidget> createState() =>
      _CoachProgrammSingleEnrollmentInfoWidgetState();
}

class _CoachProgrammSingleEnrollmentInfoWidgetState
    extends State<CoachProgrammSingleEnrollmentInfoWidget> {
  @override
  Widget build(BuildContext context) {
    return _buildEnrollmentInfoCard();
  }

  _buildEnrollmentInfoCard() {
    return GestureDetector(
      onTap: () {
        Log.debug('enrollment item pressed');
        Navigator.of(context).pushNamed(Routes.coachProgramEnrolledDetailsPage,
            arguments: widget.programEnrollmentEntity.subscriptionId);
      },
      child: Container(
        margin: EdgeInsets.only(top: Values.v20),
        padding:
            EdgeInsets.symmetric(vertical: Values.v20, horizontal: Values.v16),
        decoration: BoxDecoration(
            color: AppColors.greyscale10,
            border: Border.all(color: AppColors.greyscale100),
            borderRadius: BorderRadius.circular(Values.v10)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProgramTitle(
                    title: widget.programEnrollmentEntity.programTitle),
              ],
            ),
            const SizedBox(
              height: Values.v20,
            ),
            _buildStudentEnrollmentInfoSection(
                enrollmentDate: DateTime.now(), // TODO need to replace
                payment: 3000),
            Container(
              margin: EdgeInsets.symmetric(vertical: 10),
              height: 2,
              width: double.infinity,
              color: AppColors.greyscale100,
            ),
            _buildEnrolledUserInfo(),
          ],
        ),
      ),
    );
  }

  _buildProgramTitle({required String title}) {
    return Expanded(
      child: Text(
        title,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
      ),
    );
  }

  _buildStudentEnrollmentInfoSection(
      {required DateTime enrollmentDate, required int payment}) {
    return Wrap(
      children: [
        // Text('Total Enrollment: ${programEntity.totalSubscription}'),
        _buildProgramInfoText(
            'Subscribed On: ',
            '${DateFormat.yMMMMd().format(enrollmentDate)}',
            AppTypography.poppinsMedium12(color: AppColors.greyscale400),
            AppTypography.poppinsMedium14(color: UIColors.primaryGreen950)),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 5),
          height: 20,
          width: 2,
          color: UIColors.primaryGreen400,
        ),
        // Text('Total Income: BDT ${350000}'),
        _buildProgramInfoText(
            '',
            'BDT ${payment}',
            AppTypography.poppinsMedium12(color: AppColors.greyscale400),
            AppTypography.poppinsMedium14(color: UIColors.primaryGreen950)),
      ],
    );
  }

  _buildProgramInfoText(String infoName, String? infoValue,
      TextStyle infoNameStyle, TextStyle infoValueStyle) {
    if (infoValue == null || infoValue == '') return SizedBox.shrink();

    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          '${(infoName.length != 0) ? '${infoName}:' : ''}',
          style: infoNameStyle,
        ),
        SizedBox(
          width: Values.v5,
        ),
        Flexible(
          child: Wrap(
            children: [
              Text(
                infoValue,
                style: infoValueStyle,
                overflow: TextOverflow.clip,
              ),
            ],
          ),
        )
      ],
    );
  }

  _buildEnrolledUserInfo() {
    String userImageUrl = Assets.spotMeNoImage;
    if (widget.programEnrollmentEntity.coachImages.isNotEmpty) {
      if (Uri.parse(widget.programEnrollmentEntity.coachImages.first.url)
          .hasAbsolutePath) {
        userImageUrl = widget.programEnrollmentEntity.coachImages.first.url;
      }
    }

    return Container(
      child: Row(
        children: [
          _buildStudentProfileImage(userImageUrl),
          const SizedBox(
            width: 5,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.programEnrollmentEntity.userName ?? 'no-name',
                style: AppTypography.poppinsSemiBold14(
                    color: UIColors.primaryGreen950),
              ),
            ],
          )
        ],
      ),
    );
  }

  _buildStudentProfileImage(String? imagePath) {
    if (imagePath == null ||
        imagePath == Assets.spotMeNoImage ||
        imagePath == '') {
      return ClipRRect(
        borderRadius: BorderRadius.circular(Values.v100),
        child: SizedBox(
          height: Values.v24,
          width: Values.v24,
          child: Image.asset(
            Assets.spotMeNoImage,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    return ImageContainer.circularImage(
      image: imagePath,
      radius: Values.v12,
    );
  }
}
