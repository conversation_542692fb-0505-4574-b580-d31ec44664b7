import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/services/local_storage/cache_service.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/registration_use_case.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'sign_up_event.dart';
part 'sign_up_state.dart';

class SignUpBloc extends Bloc<SignUpEvent, BaseState> {
  SignUpBloc({
    required this.useCase,
  }) : super(InitialState()) {
    on<SignUpWithEmailAndPasswordEvent>(_onSignUpWithEmailAndPasswordEvent);
    on<SignUpWithGoogleEvent>(_onSignUpWithGoogleEvent);
    on<SignUpWithFacebookEvent>(_onSignUpWithFacebookEvent);
    on<SignUpWithAppleEvent>(_onSignUpWithAppleEvent);
  }

  late RegistrationUseCase useCase;

  Future<void> _onSignUpWithEmailAndPasswordEvent(
    SignUpWithEmailAndPasswordEvent event,
    Emitter<BaseState> emit,
  ) async {
    emit(const LoadingState());

    try {
      String? fcmToken = await CacheService.instance.retrieveFcmToken();

      Map<String, dynamic> requestBody = {
        "email": emailField.text,
        "password": passwordField.text,
        if (fcmToken != null) 'fcmToken': fcmToken,
      };

      final response = await useCase.registerWithEmailAndPassword(requestBody);

      return response.fold(
        (l) => emit(ErrorState(data: l.message)),
        (r) => emit(SuccessState(data: r)),
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      emit(const ErrorState());
    }
  }

  Future<void> _onSignUpWithGoogleEvent(
    SignUpWithGoogleEvent event,
    Emitter<BaseState> emit,
  ) async {
    try {
      emit(const LoadingState());

      final response = await useCase.registerWithGoogle();

      await response.fold(
        (l) async => emit(ErrorState(data: l.message)),
        (r) async {
            await CacheService.instance.storeBearerToken(r.token);
            emit(SocialMediaSignUpSuccessState(token: r.token));
        },
        
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      emit(ErrorState(data: ErrorModel()));
    }
  }

  Future<void> _onSignUpWithFacebookEvent(
    SignUpWithFacebookEvent event,
    Emitter<BaseState> emit,
  ) async {
    try {
      emit(const LoadingState());

      final response = await useCase.registerWithFacebook();

      await response.fold(
        (l) async => emit(ErrorState(data: l.message)),
        (r) async {
            await CacheService.instance.storeBearerToken(r.token);
            emit(SocialMediaSignUpSuccessState(token: r.token));
        },
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      emit(ErrorState(data: ErrorModel()));
    }
  }

  Future<void> _onSignUpWithAppleEvent(
    SignUpWithAppleEvent event,
    Emitter<BaseState> emit,
  ) async {
    try {
      emit(const LoadingState());

      final response = await useCase.registerWithApple();

      await response.fold(
        (l) async => emit(ErrorState(data: l.message)),
        (r) async {
            await CacheService.instance.storeBearerToken(r.token);
            emit(SocialMediaSignUpSuccessState(token: r.token));
        },
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      emit(ErrorState(data: ErrorModel()));
    }
  }

  void clearControllers() {
    firstNameField.clear();
    lastNameField.clear();
    emailField.clear();
    phoneField.clear();
    passwordField.clear();
    confirmPassword.clear();
  }

  /// Text fields controllers
  final TextEditingController firstNameField = TextEditingController();
  final TextEditingController lastNameField = TextEditingController();
  final TextEditingController emailField = TextEditingController();
  final TextEditingController phoneField = TextEditingController();
  final TextEditingController passwordField = TextEditingController();
  final TextEditingController confirmPassword = TextEditingController();
}
