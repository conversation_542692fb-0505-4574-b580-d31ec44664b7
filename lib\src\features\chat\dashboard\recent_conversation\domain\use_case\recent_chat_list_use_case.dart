import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/domain/entity/chat_list_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/domain/repository/chat_list_repository.dart';

class RecentChatListUseCase {
  const RecentChatListUseCase({required this.chatListRepository});

  final ChatListRepository chatListRepository;

  Future<Either<String, List<ChatListEntity>>> call(
      {required int? limit, required int? offset}) async {
    return await chatListRepository.getRecentChat(
      limit: limit,
      offset: offset,
    );
  }
}
