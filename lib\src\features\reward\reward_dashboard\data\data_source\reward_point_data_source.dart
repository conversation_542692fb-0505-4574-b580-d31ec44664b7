import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class RewardPointDataSource {
  Future<Response> getCurrentPoints();
  Future<Response> getWeeklyLoginHistory({required String userId});
  Future<Response> getRewardPointHistory({required int? offset, required int? limit, required String? pointHistoryFilter});
}

class RewardPointDataSourceImpl extends RewardPointDataSource {
  final RestClient restClient;

  RewardPointDataSourceImpl({required this.restClient});

  @override
  Future<Response> getCurrentPoints() async {
    final response = await restClient.get(APIType.PROTECTED, API.getRewardPoints);

    return response;
  }
  
  @override
  Future<Response> getWeeklyLoginHistory({required String userId}) async {
    final String path = '${API.weeklyLoginHistory}?userId=$userId';
    final response = await restClient.get(APIType.PROTECTED, path);

    return response;
  }
  
  @override
  Future<Response> getRewardPointHistory({required int? offset, required int? limit, required String? pointHistoryFilter}) async {
    String path = '${API.rewardPointHistory}';
    if(limit != null) {
      path += '?offset=$offset&limit=$limit';
    }
    if(pointHistoryFilter != null) {
      path += '&filter=$pointHistoryFilter';
    }

    final response = await restClient.get(APIType.PROTECTED, path);

    return response;
  }

}