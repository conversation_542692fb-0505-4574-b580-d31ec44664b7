import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/entities/user.dart';

abstract class AuthRepository {
  Future<Either<ErrorModel, String>> registration(
    Map<String, dynamic> requestBody,
  );

  Future<Either<ErrorModel, String>> verification(Map<String, dynamic> map);

  Future<Either<ErrorModel, User>> login(
    Map<String, dynamic> data,
  );

  Future<Either<ErrorModel, User>> google();

  Future<Either<ErrorModel, User>> facebook();

  Future<Either<ErrorModel, User>> apple();

  /// Send OTP to Verify Forgot Password Request
  Future<Either<ErrorModel, bool>> sendOTP(
    Map<String, dynamic> requestBody,
  );

  Future<Either<ErrorModel, bool>> identityVerification(
    Map<String, dynamic> requestBody,
  );

  Future<Either<ErrorModel, bool>> resetPassword(Map<String, dynamic> map);
}
