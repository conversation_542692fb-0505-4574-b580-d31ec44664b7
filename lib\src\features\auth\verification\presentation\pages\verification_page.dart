import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/validators/input_validators.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/auth/login/presentation/pages/login_page.dart';
import 'package:fitsomnia_app/src/features/auth/root/presentations/widgets/authentication_wrapper.dart';
import 'package:fitsomnia_app/src/features/auth/sign_up/presentation/bloc/sign_up_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/verification/presentation/bloc/verification_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/verification/presentation/widgets/count_down_timer.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:input_form_field/input_form_field.dart';

class VerificationPage extends StatelessWidget {
  VerificationPage({
    Key? key,
    required this.email,
  }) : super(key: key);

  final String email;

  final _formKey = GlobalKey<FormState>();
  final TextEditingController otpController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return ScrollableWrapper(
      appBar: AppBar(
        backgroundColor: UIColors.background,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppColors.black,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildVerificationTitle(),
            SizedBox(height: Values.v8.h),
            _buildVerificationDetails(),
            SizedBox(height: Values.v8.h),
            _buildTimeCounter(context),
            SizedBox(height: Values.v30.h),
            _buildOtpField(),
            SizedBox(height: Values.v215.h),
            _buildContinueButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildVerificationTitle() {
    return Align(
      alignment: Alignment.center,
      child: Text(
        TextConstants.verification,
        style: AppTypography.regular16(
          color: AppColors.emperor,
        ).copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildVerificationDetails() {
    return Align(
      alignment: Alignment.center,
      child: Text(
        TextConstants.verificationDetails,
        style: AppTypography.regular12(
          color: AppColors.emperor,
        ).copyWith(
          fontWeight: FontWeight.w400,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTimeCounter(BuildContext context) {
    return const Align(
      alignment: Alignment.center,
      child: CountdownTimer(),
    );
  }

  Widget _buildOtpField() {
    return InputFormField(
      textEditingController: otpController,
      labelText: TextConstants.enterYourOTPCode,
      keyboardType: TextInputType.number,
      validator: InputValidators.otp,
    );
  }

  Widget _buildContinueButton(BuildContext context) {
    return BlocConsumer<VerificationBloc, VerificationState>(
      listener: (context, state) {
        if (state is VerificationSuccessState) {
          _onVerificationSuccessState(context);
        } else if (state is VerificationErrorState) {
          AppToast.showToast(
            message: state.message,
            gravity: ToastGravity.BOTTOM,
            backgroundColor: AppColors.error,
          );
        }
      },
      builder: (context, state) {
        return Button.filled(
          onPressed: () {
            if (!_formKey.currentState!.validate()) return;

            BlocProvider.of<VerificationBloc>(context).add(
              VerificationWithOtpEvent(
                email: email,
                otpCode: otpController.text,
              ),
            );
          },
          isLoading: state is VerificationLoadingState,
          label: TextConstants.continueText,
        );
      },
    );
  }

  void _onVerificationSuccessState(BuildContext context) {
    context.read<SharedBloc>().add(LoadEssentialDataEvent());
    context.read<SignUpBloc>().clearControllers();
    _navigateToLoginPage(context);
  }

  void _navigateToLoginPage(BuildContext context) {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const LoginPage()),
      (Route<dynamic> route) => false,
    );
  }
}
