import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/domain/entity/active_user_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/domain/repository/active_user_repository.dart';

class ActiveUserListUseCase {
  const ActiveUserListUseCase({required this.activeUserRepository});

  final ActiveUserRepository activeUserRepository;

  Future<Either<String, List<ActiveUserEntity>>> call({
    int? limit,
    int? offset,
  }) async {
    return await activeUserRepository.getActiveFitBuddies(limit: limit, offset: offset);
  }
}
