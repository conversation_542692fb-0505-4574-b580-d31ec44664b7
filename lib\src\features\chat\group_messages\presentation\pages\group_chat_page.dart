import 'dart:async';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/global/globals.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/services/socket/scoket_service.dart';
import 'package:fitsomnia_app/src/core/services/socket/socket_enum.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/presentation/bloc/group_list_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/image_picker/image_picker_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/image_upload/image_upload_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/widget/send_attachment.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/entity/group_chat_entity.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/group_chat/group_chat_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/leave_group/leave_group_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/widget/group_message_bubble.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/domain/entities/user_profile_entity.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'group_chat_information_page.dart';

class GroupChatPage extends StatefulWidget {
  const GroupChatPage({
    super.key,
    required this.groupId,
    required this.groupName,
    required this.groupImage,
  });

  @override
  State<GroupChatPage> createState() => _GroupChatPageState();

  final String groupId;
  final String groupName;
  final String? groupImage;
}

class _GroupChatPageState extends State<GroupChatPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isExpanded = false;

  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  final ScrollController _scrollController = ScrollController();
  bool _firstAutoscrollExecuted = false;
  bool _shouldAutoscroll = false;

  late UserProfile _userProfile;

  bool _messageSendingLoader = false;

  StreamController<GroupChatEntity> streamController =
      StreamController<GroupChatEntity>.broadcast();

  final int _offset = 0;
  final int _limit = 5;
  bool _loading = false;

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        _scrollController.animateTo(
          _firstAutoscrollExecuted
              ? _scrollController.position.maxScrollExtent
              : _scrollController.position.maxScrollExtent * 2,
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOut,
        );
      },
    );
  }

  void _stayAtSameScrollPosition() {
    if (!context.read<GroupChatBloc>().hasReachedMaximum) {
      _scrollController.animateTo(
        _scrollController.offset + 20, // target offset
        duration: const Duration(seconds: 1), // duration of animation
        curve: Curves.easeIn, // curve of animation
      );
    }
  }

  void _scrollListener() {
    _firstAutoscrollExecuted = true;

    _shouldAutoscroll = _scrollController.hasClients &&
            _scrollController.position.pixels ==
                _scrollController.position.maxScrollExtent
        ? true
        : false;

    Log.info(
        "maxScrollExtent :: ${_scrollController.position.maxScrollExtent}");

    if (_scrollController.offset <=
            _scrollController.position.minScrollExtent &&
        !_loading) {
      _loading = true;
      _getMoreData();
    }
  }

  @override
  void initState() {
    super.initState();
    chatScreenActive.value = true;
    _scrollController.addListener(_scrollListener);
    _userProfile = BlocProvider.of<SharedBloc>(context).userProfile!;
    BlocProvider.of<GroupChatBloc>(context).add(
      GroupChatHistoryEvent(
        groupId: widget.groupId,
        limit: 20,
        offset: _offset,
      ),
    );
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250),
    );
  }

  @override
  void didChangeDependencies() {
    _initiateSocketListener();
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    Log.debug("Group Chat Page: Socket Disposed");
    chatScreenActive.value = false;
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpand() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardVisibilityBuilder(
      builder: (context, isKeyboardVisible) {
        if (isKeyboardVisible) {
          if(_scrollController.hasClients) _scrollToBottom();
        }

        return BlocListener<SharedBloc, SharedState>(
          listener: (context, state) {
            if (state is EssentialDataLoaded) {
              _userProfile = state.userProfile;
            }
          },
          child: BlocConsumer<ImagePickerBloc, ImagePickerState>(
            listener: (context, imagePickerState) {
              if (imagePickerState is ImagePickerSuccess) {
                _navigateToSendAttachmentPage(
                  withImageString: imagePickerState.image.path,
                );
              }
            },
            buildWhen: (previous, current) => previous != current,
            builder: (context, imagePickerState) {
              return BlocConsumer<ImageUploadBloc, ImageUploadState>(
                listener: (context, imageUploadState) {
                  if (imageUploadState is ImageUploadSuccess) {
                    addImageToChat(imageUploadState.imageUrl);
                  }
                },
                buildWhen: (previous, current) => previous != current,
                builder: (context, state) {
                  return Scaffold(
                    appBar: _buildAppBar(),
                    backgroundColor: AppColors.white,
                    body: SafeArea(
                      maintainBottomViewPadding: true,
                      child: Column(
                        children: [
                          Expanded(child: _buildConversationThread()),
                          _messageSendingNotifier(),
                          _buildInputField(context)
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }

  Widget _messageSendingNotifier() {
    return Visibility(
      visible: _messageSendingLoader,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "Sending",
            style: AppTypography.regular12(color: AppColors.greyDark),
          ),
          const SizedBox(width: 4.0),
          SizedBox(
            height: 8.0,
            width: 8.0,
            child: CircularProgressIndicator(
              color: AppColors.greyDark,
              strokeWidth: 1,
            ),
          ),
        ],
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      leading: InkWell(
        onTap: () {
          BlocProvider.of<GroupListBloc>(context)
              .add(GetGroupListEvent(limit: 5));
          Navigator.of(context).pop();
        },
        child: const Icon(Icons.arrow_back),
      ),
      title: GestureDetector(
        onTap: _navigateToGroupChatInformationPage,
        child: Row(
          children: [
            _buildAvatar(),
            const SizedBox(width: 16),
            Expanded(child: _buildName()),
          ],
        ),
      ),
      actions: [
        _buildPopUpMenu(context),
      ],
      elevation: 0,
      iconTheme: const IconThemeData(color: AppColors.black),
      backgroundColor: AppColors.white,
      titleSpacing: 0,
    );
  }

  Stack _buildAvatar() {
    
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        ImageContainer.circularImage(
          image: widget.groupImage ?? '',
          radius: Values.v22,
          isGroup: true,
        ),
      ],
    );
  }

  Text _buildName() {
    return Text(
      widget.groupName,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: const TextStyle(
        fontWeight: FontWeight.w600,
        fontSize: 16,
        color: AppColors.black,
      ),
    );
  }

  Widget _buildPopUpMenu(BuildContext context) {
    return PopupMenuButton(
      color: AppColors.white,
      position: PopupMenuPosition.under,
      child: Container(
        margin: const EdgeInsets.only(right: 16),
        decoration: const BoxDecoration(
            // color: AppColors.apple.withOpacity(Values.v0_25),
            // shape: BoxShape.circle,
            ),
        child: const Padding(
          padding: EdgeInsets.all(4.0),
          child: Icon(
            Icons.more_vert,
            // color: AppColors.apple,
            size: Values.v20,
          ),
        ),
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          onTap: () {
            _onLeaveGroupTapped();
          },
          child: const Text("Leave Group"),
        ),
      ],
    );
  }

  Future<void> _onLeaveGroupTapped() async {
    Future.delayed(
      const Duration(seconds: 0),
      () async => await showDialog<void>(
          context: context,
          builder: (context) {
            return BlocListener<LeaveGroupBloc, LeaveGroupState>(
              listener: (context, state) {
                if (state is LeaveGroupSuccess) {
                  _showLeftGroupToast();
                }
              },
              child: CupertinoAlertDialog(
                title: Column(
                  children: const [
                    Text("Alert"),
                  ],
                ),
                content:
                    const Text("Are you sure you want to leave this group?"),
                actions: [
                  CupertinoDialogAction(
                    child: const Text("Yes"),
                    onPressed: () {
                      BlocProvider.of<LeaveGroupBloc>(context).add(
                        LeaveGroupEvent(groupId: widget.groupId),
                      );
                    },
                  ),
                  CupertinoDialogAction(
                    child: const Text("No"),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            );
          }),
    );
  }

  Widget _buildConversationThread() {
    return BlocConsumer<GroupChatBloc, GroupChatState>(
      listener: (context, state) {
        if (state is GroupChatInitial) {
          BlocProvider.of<GroupChatBloc>(context).add(
            GroupChatHistoryEvent(groupId: widget.groupId),
          );
        } else if (state is GroupChatSuccess) {
          BlocProvider.of<GroupChatBloc>(context).add(
            UpdateGroupChatHistoryEvent(
              groupChatEntities: state.groupChatEntities,
            ),
          );
        }
      },
      buildWhen: (previous, current) => previous != current,
      builder: (context, groupChatState) {
        if (groupChatState is GroupChatSuccess) {
          _loading = false;
          clearStreamAndAddNewValues();

          return StreamBuilder<dynamic>(
              stream: streamController.stream,
              builder: (context, snapshot) {
                _checkScrollingCondition();

                if (snapshot.hasData) {
                  return _conversationThread();
                } else if (snapshot.hasError) {
                  return const Center(
                    child: Text('Error'),
                  );
                } else {
                  return _conversationThread();
                }
              });
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _conversationThread() {
    var groupChatEntities = context.read<GroupChatBloc>().groupConversationList;
    // print('group chat ${groupChatEntities}');

    return Container(
      // color: AppColors.apple.withOpacity(Values.v0_05),
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: GestureDetector(
        onTap: () {
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: ListView.builder(
          controller: _scrollController,
          itemCount: groupChatEntities.length,
          itemBuilder: (context, index) {
            final String message = groupChatEntities[index].content;
            // final image = groupChatEntities[index].senderImage;
            // print(image);
            // print('message ${image}');
            final bool isSentByMe =
                groupChatEntities[index].senderId == _userProfile.id;

            return groupChatEntities[index].groupId == widget.groupId
                ? GroupMessageBubble(
                    text: message,
                    isSentByMe: isSentByMe,
                    data: groupChatEntities[index],
                  )
                : const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  Widget _buildInputField(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h),
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      child: Row(
        children: [
          IconButton(
            icon: _isExpanded ? _buildLeftArrowIcon() : _buildRightArrowIcon(),
            onPressed: _toggleExpand,
          ),

          animatedIcons(),
          inputTextField(context),
          _buildSendIcon(),

          // Send button
        ],
      ),
    );
  }

  AnimatedBuilder animatedIcons() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Visibility(
          visible: _animationController.value > 0.0,
          maintainSize: false,
          child: Row(
            children: [
              Transform.translate(
                offset: Offset(-40.0 * (1.0 - _animationController.value), 0.0),
                child: Opacity(
                  opacity: _animationController.value,
                  child: _buildAttachmentIcon(),
                ),
              ),
              Transform.translate(
                offset: Offset(-60.0 * (1.0 - _animationController.value), 0.0),
                child: Opacity(
                  opacity: _animationController.value,
                  child: SizedBox(width: 4.w),
                ),
              ),
              Transform.translate(
                offset: Offset(-80.0 * (1.0 - _animationController.value), 0.0),
                child: Opacity(
                  opacity: _animationController.value,
                  child: _buildCameraIcon(),
                ),
              ),
              Transform.translate(
                offset:
                    Offset(-100.0 * (1.0 - _animationController.value), 0.0),
                child: Opacity(
                  opacity: _animationController.value,
                  child: SizedBox(width: 4.w),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget inputTextField(BuildContext context) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.grey6,
          borderRadius: BorderRadius.circular(Values.v30),
        ),
        padding: EdgeInsets.only(left: 16.w),
        child: TextFormField(
          controller: _controller,
          focusNode: _focusNode,
          autofocus: true,
          autocorrect: false,
          scrollPadding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewPadding.bottom,
          ),
          maxLines: 4,
          minLines: 1,
          decoration: InputDecoration(
            hintText: 'Type a message',
            border: InputBorder.none,
            // suffixIcon: _buildSendIcon(),
          ),
        ),
      ),
    );
  }

  Widget _buildRightArrowIcon() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50.r),
        color: AppColors.apple,
      ),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Icon(
          Icons.keyboard_arrow_right,
          color: AppColors.white,
          size: Values.v32,
        ),
      ),
    );
  }

  Widget _buildLeftArrowIcon() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50.r),
        color: AppColors.apple,
      ),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Icon(
          Icons.keyboard_arrow_left,
          color: AppColors.white,
          size: Values.v32,
        ),
      ),
    );
  }

  Widget _buildCameraIcon() {
    return GestureDetector(
      onTap: () {
        BlocProvider.of<ImagePickerBloc>(context).add(
          const ImagePickerEvent(isImageCapture: true),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50.r),
          color: AppColors.primaryGreen900,
        ),
        margin: EdgeInsets.only(right: 4.w),
        child: Padding(
          padding: EdgeInsets.all(8.w),
          child: Icon(
            Icons.camera_alt_sharp,
            color: AppColors.white,
            size: Values.v22,
          ),
        ),
      ),
    );
  }

  Widget _buildAttachmentIcon() {
    return GestureDetector(
      onTap: () {
        BlocProvider.of<ImagePickerBloc>(context).add(const ImagePickerEvent());
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50.r),
          color: AppColors.primaryGreen900,
        ),
        margin: EdgeInsets.only(right: 4.w),
        child: Padding(
          padding: EdgeInsets.all(8.w),
          child: Icon(
            Icons.image,
            color: AppColors.white,
            size: Values.v22,
          ),
        ),
      ),
    );
  }

  Widget _buildSendIcon() {
    return GestureDetector(
      onTap: onSendMessageIconTap,
      child: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.primaryGreen900,
          shape: BoxShape.circle,
        ),
        // child: const Padding(
        //   padding: EdgeInsets.all(8.0),
        //   child: Icon(
        //     Icons.send_rounded,
        //     color: AppColors.white,
        //     size: 20,
        //   ),
        // ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50.r),
            color: AppColors.primaryGreen900,
          ),
          margin: EdgeInsets.only(right: 4.w),
          child: Padding(
            padding:
                EdgeInsets.only(left: 10.w, top: 8.w, bottom: 8.w, right: 6.w),
            child: const Icon(
              Icons.send_rounded,
              color: AppColors.white,
              size: Values.v22,
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _getMoreData() async {
    await Future.delayed(const Duration(seconds: 1), () {
      BlocProvider.of<GroupChatBloc>(context).add(
        GetMoreGroupChatHistoryEvent(
          groupId: widget.groupId,
          limit: _limit,
          offset: context.read<GroupChatBloc>().groupConversationList.length,
        ),
      );
      _stayAtSameScrollPosition();
    });
  }

  void onSendMessageIconTap() {
    if (_controller.text.trim() != "") {
      if (mounted) {
        addMessageToConversationList();

        setState(() {
          _messageSendingLoader = true;
        });
      }
    }
  }

  void addMessageToConversationList() {
    SocketService.instance
        .emit(ClientToServer.GROUP_CLIENT_TO_SERVER_SEND_MESSAGE.name, {
      "receiverId": widget.groupId,
      "content": _controller.text.trim(),
      "type": "text",
      "senderName": _userProfile.name,
      "senderImage":
          _userProfile.image != null ? _userProfile.image!.profile ?? "" : "",
      "receiverName": widget.groupName,
      "receiverImage": widget.groupImage ?? "",
    });

    _controller.clear();
  }

  void addImageToChat(String imageUrl) {
    SocketService.instance
        .emit(ClientToServer.GROUP_CLIENT_TO_SERVER_SEND_MESSAGE.name, {
      "receiverId": widget.groupId,
      "content": imageUrl,
      "type": "image",
      "senderName": _userProfile.name,
      "senderImage":
          _userProfile.image != null ? _userProfile.image!.profile ?? "" : "",
      "receiverName": widget.groupName,
      "receiverImage": widget.groupImage ?? "",
    });

    _controller.clear();

    if (mounted) {
      setState(() {
        _shouldAutoscroll = true;
      });
    }
  }

  void _initiateSocketListener() {
    SocketService.instance
        .on(ServerToClient.GROUP_SERVER_TO_CLIENT_RECEIVE_MESSAGE.name, (data) {
      if (mounted) {
        BlocProvider.of<GroupChatBloc>(context).add(
          AddGroupChatHistoryEvent(
            groupChatEntity: GroupChatEntity(
              id: data['id'],
              senderId: data['senderId'],
              groupId: data['receiverId'],
              type: data['type'],
              content: data['content'],
              createdAt: DateTime.now().toLocal(),
              senderName: data['senderName'],
              senderImage: data['senderImage'],
            ),
          ),
        );

        clearStreamAndAddNewValues();

        setState(() {
          _shouldAutoscroll = true;
        });

        if (data['senderId'] == _userProfile.id) {
          setState(() {
            _messageSendingLoader = false;
          });
        }
      }
    });
  }

  void clearStreamAndAddNewValues() {
    context.read<GroupChatBloc>().groupConversationList.forEach((element) {
      streamController.sink.add(element);
    });
  }

  void _checkScrollingCondition() {
    if (_scrollController.hasClients && _shouldAutoscroll) {
      _scrollToBottom();
      _shouldAutoscroll = false;
    }

    if (!_firstAutoscrollExecuted) {
      Log.info("First Scroll Executed");
      if(_scrollController.hasClients) _scrollToBottom();
    }
  }

  void _showLeftGroupToast() {
    BlocProvider.of<GroupListBloc>(context).add(GetGroupListEvent(limit: 5));
    Navigator.of(context).pop();
    AppToast.showToast(message: "You left the Group!");
    Navigator.of(context).pop();
  }

  void _navigateToGroupChatInformationPage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GroupChatInformationPage(
          groupImage: widget.groupImage,
          groupName: widget.groupName,
          groupId: widget.groupId,
        ),
      ),
    );
  }

  void _navigateToSendAttachmentPage({required String withImageString}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SendAttachment(imagePath: withImageString),
      ),
    );
  }
}
