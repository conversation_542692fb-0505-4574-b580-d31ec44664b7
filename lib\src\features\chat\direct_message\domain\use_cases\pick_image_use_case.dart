import 'package:camera/camera.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/repositories/image_picking_repository.dart';

class PickImageUseCase {
  const PickImageUseCase({required this.imagePickingRepository});

  final ImagePickingRepository imagePickingRepository;

  Future<XFile> call({required bool isImageCaptureEvent}) async {
    return await imagePickingRepository.pickImage(
      isImageCaptureEvent: isImageCaptureEvent,
    );
  }
}
