part of 'image_upload_bloc.dart';

class ImageUploadState extends Equatable {
  @override
  List<Object?> get props => [];
}

class ImageUploadInitial extends ImageUploadState {}

class ImageUploadLoading extends ImageUploadState {}

class ImageUploadSuccess extends <PERSON>UploadState {
  final String imageUrl;
  final String filePath;
  final String? featureName;
  final String? fieldName;

  ImageUploadSuccess({
    required this.imageUrl,
    required this.filePath,
    this.featureName,
    this.fieldName,
  });

  @override
  List<Object?> get props => [
        imageUrl,
        filePath,
        featureName,
        fieldName,
      ];
}

class ImageUploadFailure extends ImageUploadState {
  final String errorMessage;
  final String? featureName;
  final String? fieldName;

  ImageUploadFailure(
    this.errorMessage, {
    this.featureName,
    this.fieldName,
  });

  @override
  List<Object?> get props => [
        errorMessage,
        featureName,
        fieldName,
      ];
}
