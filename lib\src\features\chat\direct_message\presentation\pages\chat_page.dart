import 'dart:async';

import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/global/globals.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/services/socket/scoket_service.dart';
import 'package:fitsomnia_app/src/core/services/socket/socket_enum.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/entity/chat_history_entity.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/chat_history/chat_history_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/image_picker/image_picker_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/image_upload/image_upload_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/widget/message_bubble.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/widget/send_attachment.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/domain/entities/user_profile_entity.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/block_fit_buddy/block_fit_buddy_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/pages/dashboard/others_profile_page.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/domain/entities/fit_buddy_status_entity.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/presentation/bloc/spot_bloc/spot_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ChatPage extends StatefulWidget {
  const ChatPage({
    super.key,
    required this.title,
    required this.fitBuddyId,
    this.image,
  });

  @override
  State<ChatPage> createState() => _ChatPageState();

  final String title;
  final String fitBuddyId;
  final String? image;
}

class _ChatPageState extends State<ChatPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isExpanded = false;

  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  final ScrollController _scrollController = ScrollController();
  bool _firstAutoscrollExecuted = false;
  bool _shouldAutoscroll = false;

  late UserProfile userProfile;
  bool _messageSendingLoader = false;

  StreamController<ChatHistoryEntity> streamController =
      StreamController<ChatHistoryEntity>.broadcast();

  final int _offset = 0;
  final int _limit = 5;
  bool _loading = false;

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        _scrollController.animateTo(
          _firstAutoscrollExecuted
              ? _scrollController.position.maxScrollExtent
              : _scrollController.position.maxScrollExtent * 2,
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOut,
        );
      },
    );
  }

  void _stayAtSameScrollPosition() {
    if (!context.read<ChatHistoryBloc>().hasReachedMaximum) {
      _scrollController.animateTo(
        _scrollController.offset + 20, // target offset
        duration: const Duration(seconds: 1), // duration of animation
        curve: Curves.easeIn, // curve of animation
      );
    }
  }

  void _scrollListener() {
    _firstAutoscrollExecuted = true;

    _shouldAutoscroll = _scrollController.hasClients &&
            _scrollController.position.pixels ==
                _scrollController.position.maxScrollExtent
        ? true
        : false;

    Log.info(
        "maxScrollExtent :: ${_scrollController.position.maxScrollExtent}");

    if (_scrollController.offset <=
            _scrollController.position.minScrollExtent &&
        !_loading) {
      _loading = true;
      _getMoreData();
    }
  }

  @override
  void initState() {
    super.initState();
    // WidgetsBinding.instance.addObserver(this);
    chatScreenActive.value = true;
    _scrollController.addListener(_scrollListener);
    userProfile = BlocProvider.of<SharedBloc>(context).userProfile!;
    BlocProvider.of<ChatHistoryBloc>(context).add(
      GetChatHistoryEvent(
        userId: widget.fitBuddyId,
        limit: 20,
        offset: _offset,
      ),
    );
    BlocProvider.of<SpotBloc>(context).add(
      GetFitBuddyStatusEvent(fitBuddyId: widget.fitBuddyId),
    );
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250),
    );
    // _initiateSocketListener();
  }

  @override
  void didChangeDependencies() {
    _initiateSocketListener();
    super.didChangeDependencies();
  }

  // @override
  // void didChangeAppLifecycleState(AppLifecycleState state) {
  //   if (state.name == 'resumed') {
  //     Log.debug('State Resumed');
  //     if (mounted) setState(() => '');
  //     _initiateSocketListener();
  //   } else if (state.name == 'Paused') {
  //     SocketService.instance.disconnect();
  //   }
  //
  //   super.didChangeAppLifecycleState(state);
  // }

  @override
  void dispose() {
    Log.debug("Chat Page: Socket Disposed");
    chatScreenActive.value = false;
    _scrollController.removeListener(_scrollListener);
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpand() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SharedBloc, SharedState>(
      listenWhen: (previous, current) => previous != current,
      listener: (context, state) {
        if (state is EssentialDataLoaded) {
          userProfile = state.userProfile;
        }
      },
      child: KeyboardVisibilityBuilder(
        builder: (context, isKeyboardVisible) {
          if (isKeyboardVisible) {
            if(_scrollController.hasClients) _scrollToBottom();
          }

          return BlocConsumer<ImagePickerBloc, ImagePickerState>(
            listener: (context, imagePickerState) {
              if (imagePickerState is ImagePickerSuccess) {
                _navigateToShowAttachmentPage(
                  withImageString: imagePickerState.image.path,
                );
              }
            },
            buildWhen: (previous, current) => previous != current,
            builder: (context, imagePickerState) {
              return BlocConsumer<ImageUploadBloc, ImageUploadState>(
                listener: (context, imageUploadState) {
                  if (imageUploadState is ImageUploadSuccess) {
                    addImageToChat(
                      imageUploadState.imageUrl,
                      imageUploadState.filePath,
                    );
                  }
                },
                buildWhen: (previous, current) => previous != current,
                builder: (context, imageUploadState) {
                  return Scaffold(
                    appBar: _buildAppBar(),
                    backgroundColor: AppColors.white,
                    body: SafeArea(
                      maintainBottomViewPadding: true,
                      child: Stack(
                        children: [
                          Column(
                            children: [
                              Expanded(child: _buildConversationThread()),
                              _messageSendingNotifier(),
                              BlocBuilder<SpotBloc, BaseState>(
                                buildWhen: (previous, current) =>
                                    previous != current,
                                builder: (context, state) {
                                  if (state is FitBuddyStatusSuccessState) {
                                    String? status =
                                        (state.data as FitBuddyStatusEntity)
                                            .status;
                                    if (status != null) {
                                      if (status == "BLOCKED") {
                                        return _blockedMessage();
                                      }
                                    }
                                  }

                                  return _buildInputField(context);
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  Widget _blockedMessage() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            decoration: BoxDecoration(
              color: AppColors.alto,
            ),
            child: Text(
              "You can't reply to this conversation.",
              style: AppTypography.regular16(),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  Widget _messageSendingNotifier() {
    return Visibility(
      visible: _messageSendingLoader,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "Sending",
            style: AppTypography.regular12(color: AppColors.greyDark),
          ),
          const SizedBox(width: 4.0),
          SizedBox(
            height: 8.0,
            width: 8.0,
            child: CircularProgressIndicator(
              color: AppColors.greyDark,
              strokeWidth: 1,
            ),
          ),
        ],
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      leading: InkWell(
        onTap: () {
          Navigator.of(context).pop();
        },
        child: const Icon(Icons.arrow_back),
      ),
      title: GestureDetector(
        onTap: _navigateToOthersTimeline,
        child: Row(
          children: [
            _buildAvatar(),
            const SizedBox(width: 16),
            Expanded(child: _buildName()),
            // Column(
            //   crossAxisAlignment: CrossAxisAlignment.start,
            //   mainAxisSize: MainAxisSize.min,
            //   children: [
            //     Flexible(child: _buildName()),
            //     // _buildActivityStatus(),
            //   ],
            // ),
          ],
        ),
      ),
      actions: [
        BlocBuilder<SpotBloc, BaseState>(
          buildWhen: (previous, current) => previous != current,
          builder: (context, state) {
            if (state is FitBuddyStatusSuccessState) {
              String? status = (state.data as FitBuddyStatusEntity).status;
              if (status != null) {
                if (status == "BLOCKED") {
                  return const SizedBox();
                }
              }
            }

            return _buildPopUpMenu(context);
          },
        ),
      ],
      elevation: 0,
      iconTheme: const IconThemeData(color: AppColors.black),
      backgroundColor: AppColors.white,
      titleSpacing: 0,
    );
  }

  Widget _buildAvatar() {
    print('image: ${widget.image}');

    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        ImageContainer.circularImage(
          image: widget.image ?? '',
          radius: Values.v20,
        ),
      ],
    );
  }

  Widget _buildName() {
    return Text(
          widget.title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
            color: AppColors.black,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        );
  }

  Widget _buildActivityStatus() {
    return Text(
      "Active Now",
      style: TextStyle(
        fontWeight: FontWeight.w600,
        fontSize: Values.v10,
        color: AppColors.apple,
      ),
    );
  }

  Widget _buildPopUpMenu(BuildContext context) {
    return PopupMenuButton(
      color: AppColors.white,
      position: PopupMenuPosition.under,
      padding: EdgeInsets.zero,
      child: Container(
        margin: const EdgeInsets.only(right: 16),
        decoration: const BoxDecoration(
            // color: AppColors.apple.withOpacity(Values.v0_25),
            // shape: BoxShape.circle,
            ),
        child: const Padding(
          padding: EdgeInsets.all(4.0),
          child: Icon(
            Icons.more_vert,
            // color: AppColors.apple,
            size: Values.v20,
          ),
        ),
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          onTap: () {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _onViewProfileTapped();
            });
          },
          child: const Text("View Profile"),
        ),
        PopupMenuItem(
          onTap: () {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _onBlockTapped();
            });
          },
          child: const Text("Block"),
        ),
      ],
    );
  }

  void _onViewProfileTapped() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => OthersProfilePage(
          userId: widget.fitBuddyId,
        ),
      ),
    );
  }

  void _onBlockTapped() {
    showDialog(
      context: context,
      builder: (context) {
        return CupertinoAlertDialog(
          title: Column(
            children: const [
              Text("Alert"),
            ],
          ),
          content: const Text("Are you sure you want to block this user?"),
          actions: [
            CupertinoDialogAction(
              child: const Text("Yes"),
              onPressed: () {
                BlocProvider.of<BlockFitBuddyBloc>(context).add(
                  BlockFitBuddyEvent(fitBuddyId: widget.fitBuddyId),
                );

                Navigator.of(context)
                  ..pop()
                  ..pop();
              },
            ),
            CupertinoDialogAction(
              child: const Text("No"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildConversationThread() {
    return BlocConsumer<ChatHistoryBloc, ChatHistoryState>(
      listener: (context, state) {
        if (state is ChatHistorySuccess) {
          BlocProvider.of<ChatHistoryBloc>(context).add(
            UpdateChatHistoryEvent(
              chatHistoryEntities: state.chatHistoryEntity,
            ),
          );
        }
      },
      buildWhen: (previous, current) => previous != current,
      builder: (context, state) {
        if (state is ChatHistorySuccess) {
          _loading = false;
          clearStreamAndAddNewValues();

          return StreamBuilder<ChatHistoryEntity>(
              stream: streamController.stream,
              builder: (context, snapshot) {
                _checkScrollingCondition();

                if (snapshot.hasData) {
                  return _conversationThread();
                } else if (snapshot.hasError) {
                  return const Center(
                    child: Text('Error'),
                  );
                } else {
                  return _conversationThread();
                }
              });
        } else if (state is ChatHistoryFailure) {
          return Center(
            child: Text(state.errorMessage),
          );
        } else if (state is ChatHistoryLoading) {
          return const Center(
            child: CircularProgressIndicator(
              color: AppColors.green,
            ),
          );
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _conversationThread() {
    var chatHistoryEntities = context.read<ChatHistoryBloc>().conversationList;

    return Container(
      // color: AppColors.apple.withOpacity(Values.v0_05),
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: GestureDetector(
        onTap: () {
          if (_focusNode.hasFocus) {
            _focusNode.unfocus();
          }
        },
        child: ListView.builder(
          controller: _scrollController,
          itemCount: chatHistoryEntities.length,
          itemBuilder: (context, index) {
            String message = chatHistoryEntities[index].content;
            bool isSentByMe =
                chatHistoryEntities[index].senderId == userProfile.id;

            return MessageBubble(
                text: message,
                isSentByMe: isSentByMe,
                data: chatHistoryEntities[index],
                image: widget.image);
          },
        ),
      ),
    );
  }

  Widget _buildInputField(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Row(
        children: [
          IconButton(
            icon: _isExpanded ? _buildLeftArrowIcon() : _buildRightArrowIcon(),
            onPressed: _toggleExpand,
          ),
          animatedIcons(),
          inputTextField(context),
          _buildSendIcon()
        ],
      ),
    );
  }

  AnimatedBuilder animatedIcons() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Visibility(
          visible: _animationController.value > 0.0,
          maintainSize: false,
          child: Row(
            children: [
              Transform.translate(
                offset: Offset(-40.0 * (1.0 - _animationController.value), 0.0),
                child: Opacity(
                  opacity: _animationController.value,
                  child: _buildAttachmentIcon(),
                ),
              ),
              Transform.translate(
                offset: Offset(-60.0 * (1.0 - _animationController.value), 0.0),
                child: Opacity(
                  opacity: _animationController.value,
                  child: SizedBox(width: 4.w),
                ),
              ),
              Transform.translate(
                offset: Offset(-80.0 * (1.0 - _animationController.value), 0.0),
                child: Opacity(
                  opacity: _animationController.value,
                  child: _buildCameraIcon(),
                ),
              ),
              Transform.translate(
                offset:
                    Offset(-100.0 * (1.0 - _animationController.value), 0.0),
                child: Opacity(
                  opacity: _animationController.value,
                  child: SizedBox(width: 4.w),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget inputTextField(BuildContext context) {
    return Expanded(
      flex: 1,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.grey6,
          borderRadius: BorderRadius.circular(Values.v30),
        ),
        padding: EdgeInsets.only(left: 16.w),
        child: TextFormField(
          controller: _controller,
          focusNode: _focusNode,
          autofocus: true,
          autocorrect: false,
          scrollPadding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewPadding.bottom,
          ),
          maxLines: 4,
          minLines: 1,
          decoration: InputDecoration(
            hintText: 'Type a message',
            border: InputBorder.none,
            // suffixIcon: _buildSendIcon(),
          ),
        ),
      ),
    );
  }

  Widget _buildAttachmentIcon() {
    return GestureDetector(
      onTap: () {
        BlocProvider.of<ImagePickerBloc>(context).add(const ImagePickerEvent());
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50.r),
          color: AppColors.primaryGreen900,
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Icon(
            Icons.image,
            color: AppColors.white,
            size: Values.v22,
          ),
        ),
      ),
    );
  }

  Widget _buildRightArrowIcon() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50.r),
        color: AppColors.apple,
      ),
      child: const Padding(
        padding: EdgeInsets.all(4.0),
        child: Icon(
          Icons.keyboard_arrow_right,
          color: AppColors.white,
          size: Values.v30,
        ),
      ),
    );
  }

  Widget _buildLeftArrowIcon() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50.r),
        color: AppColors.apple,
      ),
      child: const Padding(
        padding: EdgeInsets.all(4.0),
        child: Icon(
          Icons.keyboard_arrow_left,
          color: AppColors.white,
          size: Values.v30,
        ),
      ),
    );
  }

  Widget _buildCameraIcon() {
    return GestureDetector(
      onTap: () {
        BlocProvider.of<ImagePickerBloc>(context).add(
          const ImagePickerEvent(isImageCapture: true),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50.r),
          color: AppColors.primaryGreen900,
        ),
        margin: EdgeInsets.only(right: 4.w),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Icon(
            Icons.camera_alt_sharp,
            color: AppColors.white,
            size: Values.v22,
          ),
        ),
      ),
    );
  }

  Widget _buildSendIcon() {
    return GestureDetector(
      onTap: onSendMessageIconTap,
      child: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.primaryGreen900,
          shape: BoxShape.circle,
        ),
        // child: Padding(
        //   // padding: EdgeInsets.all(12),
        //   padding:
        //       EdgeInsets.only(left: 10.w, top: 10.w, bottom: 10.w, right: 6.w),
        //   child: Center(
        //     child: Transform.rotate(

        //       angle: 0,
        //       alignment: Alignment.center,

        //       // 90 degrees in radians (π/2)
        //       child: Icon(
        //         Icons.send_rounded,
        //         color: AppColors.white,
        //         size: 20,
        //       ),
        //     ),
        //   ),

        // ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50.r),
            color: AppColors.primaryGreen900,
          ),
          margin: EdgeInsets.only(right: 4.w),
          child: Padding(
            padding:
                EdgeInsets.only(left: 10.w, top: 8.w, bottom: 8.w, right: 6.w),
            child: const Icon(
              Icons.send_rounded,
              color: AppColors.white,
              size: Values.v22,
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _getMoreData() async {
    await Future.delayed(const Duration(seconds: 1), () {
      BlocProvider.of<ChatHistoryBloc>(context).add(
        GetMoreChatHistoryEvent(
          userId: widget.fitBuddyId,
          limit: _limit,
          offset: context.read<ChatHistoryBloc>().conversationList.length,
        ),
      );
      _stayAtSameScrollPosition();
    });
  }

  void onSendMessageIconTap() {
    if (_controller.text.trim() != "") {
      if (mounted) {
        addMessageToConversationList();

        setState(() {
          _messageSendingLoader = true;
        });
      }
    }
  }

  Future<void> addMessageToConversationList() async {
    SocketService.instance
        .emit(ClientToServer.DIRECT_CLIENT_TO_SERVER_SEND_MESSAGE.name, {
      "receiverId": widget.fitBuddyId,
      "content": _controller.text.trim(),
      "type": "text",
      "senderName": userProfile.name,
      "senderImage": userProfile.getUsersImage() ?? "",
      "receiverName": widget.title,
      "receiverImage": widget.image ?? "",
    });

    _controller.clear();
  }

  Future<void> addImageToChat(String imageUrl, String filePath) async {
    SocketService.instance
        .emit(ClientToServer.DIRECT_CLIENT_TO_SERVER_SEND_MESSAGE.name, {
      "receiverId": widget.fitBuddyId,
      "content": imageUrl,
      "type": "image",
      "senderName": userProfile.name,
      "senderImage":
          userProfile.image != null ? userProfile.image!.profile ?? "" : "",
      "receiverName": widget.title,
      "receiverImage": widget.image ?? "",
    });

    _controller.clear();

    if (mounted) {
      setState(() {
        _shouldAutoscroll = true;
      });
    }
  }

  Future<void> _initiateSocketListener() async {
    Log.debug("Socket Listener Initiated");

    SocketService.instance.on(
        ServerToClient.DIRECT_SERVER_TO_CLIENT_RECEIVE_MESSAGE.name, (data) {
      Log.debug(
          "Chat Page: DIRECT_SERVER_TO_CLIENT_RECEIVE_MESSAGE ${data['senderId']} ${data['receiverId']}");
      if (mounted) {
        if (data['senderId'] == widget.fitBuddyId ||
            data['receiverId'] == widget.fitBuddyId) {
          BlocProvider.of<ChatHistoryBloc>(context).add(
            AddChatHistoryEvent(
              chatHistoryEntity: ChatHistoryEntity(
                senderId: data['senderId'],
                receiverId: userProfile.id,
                type: data['type'],
                content: data['content'],
                isLastSeen: true,
                id: data['id'],
                createdAt: DateTime.now(),
              ),
            ),
          );

          clearStreamAndAddNewValues();

          setState(() {
            _shouldAutoscroll = true;
            _messageSendingLoader = false;
          });
        }
      } else {
        Log.debug('Chat Page Socket: Not Mounted');
      }
    });
  }

  void clearStreamAndAddNewValues() {
    context.read<ChatHistoryBloc>().conversationList.forEach((element) {
      streamController.sink.add(element);
    });
  }

  void _checkScrollingCondition() {
    if (_scrollController.hasClients && _shouldAutoscroll) {
      _scrollToBottom();
      _shouldAutoscroll = false;
    }

    if (!_firstAutoscrollExecuted) {
      Log.info("First Scroll Executed");
      if(_scrollController.hasClients) _scrollToBottom();
    }
  }

  void _navigateToOthersTimeline() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => OthersProfilePage(
          userId: widget.fitBuddyId,
        ),
      ),
    );
  }

  void _navigateToShowAttachmentPage({required String withImageString}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SendAttachment(imagePath: withImageString),
      ),
    );
  }
}
