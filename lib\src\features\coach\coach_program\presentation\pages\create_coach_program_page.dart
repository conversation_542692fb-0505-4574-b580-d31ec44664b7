import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/core/widgets/list_single_selection_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/model/coach_program_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/bloc/coach_program_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/pages/coach_program_preview_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/widgets/coach_program_price_info_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/widgets/create_coach_program_basic_info_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/widgets/create_coach_program_feature_info_list_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';

final GlobalKey<FormState> programBasicInfoFormValidationKey =
    GlobalKey<FormState>();
final GlobalKey<FormState> programFeaturesFormValidationKey =
    GlobalKey<FormState>();
final GlobalKey<FormState> programPriceFormValidationKey =
    GlobalKey<FormState>();

class CreateCoachProgramPage extends StatefulWidget {
  const CreateCoachProgramPage(
      {super.key, this.programId, this.isCreateProgram = true});
  final bool isCreateProgram;
  final String? programId; // to edit program

  @override
  State<CreateCoachProgramPage> createState() => _CreateCoachProgramPageState();
}

class _CreateCoachProgramPageState extends State<CreateCoachProgramPage> {
  late PageController pageController;
  late List<Widget> pages;
  late int _currentPage;

  final GlobalKey<CreateCoachProgramBasicInfoWidgetState> programBasicInfoKey =
      GlobalKey<CreateCoachProgramBasicInfoWidgetState>();

  final GlobalKey<CreateCoachProgramFeatureInfoListWidgetState>
      programFeatureInfoKey =
      GlobalKey<CreateCoachProgramFeatureInfoListWidgetState>();

  final GlobalKey<CoachProgramPriceInfoWidgetState>
      programPriceInfoKey =
      GlobalKey<CoachProgramPriceInfoWidgetState>();

  List<GlobalKey<FormState>> pageValidationKey = [
    programBasicInfoFormValidationKey,
    programPriceFormValidationKey,
    programFeaturesFormValidationKey
  ];

  CoachProgramEntity? _coachProgramEntity;
  bool _isLoading = false;
  String? coachId;

  @override
  void initState() {
    super.initState();

    _currentPage = 0;
    pageController = PageController(initialPage: _currentPage);
    pages = [
      const SizedBox.shrink(),
      const SizedBox.shrink(),
      const SizedBox.shrink(),
    ];

    coachId = context.read<CoachBloc>().coachId;

    if (widget.programId != null) {
      if (coachId != null) {
        //update program
        _isLoading = true;
        BlocProvider.of<CoachProgramBloc>(context).add(
            GetCoachOwnProgramByIdEvent(
                coachId: coachId!, programId: widget.programId!));
      }
    }
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachProgramBloc, CoachProgramState>(
      listener: (context, state) {
        if (state is GetCoachOwnProgramByIdSuccess) {
          Log.debug('get coach program success');
          setState(() {
            _coachProgramEntity = state.programEntity;
            _isLoading = false;
          });
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
              (widget.programId == null) ? 'Add a Program' : 'Edit Program'),
          centerTitle: true,
          titleTextStyle:
              AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen900),
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: _isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: UIColors.primary,
                    ),
                  )
                : Column(
                    children: [
                      _buildPageIndicatorSection(),
                      Expanded(child: _buildPageViewSection()),
                      _buildButtonSection()
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  _buildPageIndicatorSection() {
    List<Widget> pageIndicators = [];
    int numOfPage = pages.length;
    for (int p = 0; p < numOfPage; p++) {
      pageIndicators.add(_buildPageIndicator(p) as Widget);
    }
    // List<Widget> pageIndicators = pages
    //     .asMap()
    //     .map((index, _) {
    //       return MapEntry(index, _buildPageIndicator(index) as Widget);
    //     })
    //     .values
    //     .toList();

    return Row(
      children: pageIndicators,
    );
  }

  _buildPageIndicator(int index) {
    if (index <= _currentPage) {
      return Expanded(
          child: Container(
        height: 5,
        color: AppColors.primaryGreen,
      ));
    } else {
      return Expanded(
          child: Container(
        height: 2,
        color: AppColors.primaryGreen400,
      ));
    }
  }

  _buildPageViewSection() {
    //TODO
    return _buildCreateCoachProgramPageView();
    // return SingleChildScrollView(child: Placeholder(fallbackHeight: 1000,),);
  }

  _buildCreateCoachProgramPageView() {
    pages = [
      _buildProgramBasicInfo(),
      _builProgramPriceInfo(),
      _buildProgramFeatureInfo(),
    ];

    return PageView(
      controller: pageController,
      physics: NeverScrollableScrollPhysics(),
      onPageChanged: (value) {
        setState(() {
          _currentPage = value;
        });
      },
      children: pages,
    );
  }

  _buildProgramBasicInfo() {
    return CreateCoachProgramBasicInfoWidget(
      key: programBasicInfoKey,
      programEntity: _coachProgramEntity,
    );
  }

  List<CoachProgramFeature> coachProgramFeatures = [];

  _buildProgramFeatureInfo() {
    if (_coachProgramEntity != null) {
      coachProgramFeatures = _coachProgramEntity!.features;
    }

    return CreateCoachProgramFeatureInfoListWidget(
      key: programFeatureInfoKey,
      features: coachProgramFeatures,
      callback: null,
      // callback: (List<CoachProgramFeature> programFeatures) {
      //   coachProgramFeatures = programFeatures;
      // },
    );
  }

  _buildButtonSection() {
    return Row(
      children: [
        Flexible(child: _builBackButton()),
        SizedBox(
          width: 10,
        ),
        Flexible(child: _buildContinueButton()),
      ],
    );
  }

  _builBackButton() {
    return Button.outlined(
        label: 'Back',
        onPressed: () {
          Log.debug('back button pressed');
          int pageIndex = pageController.page!.toInt();
          if (pageIndex > 0) {
            _moveToPage(index: pageIndex - 1);
          } else {
            Navigator.of(context).pop();
          }
        });
  }

  _buildContinueButton() {
    return Button.filled(
        label: 'Continue',
        onPressed: () {
          Log.debug('continue button pressed');
          int pageIndex = pageController.page!.toInt();

          if (!pageValidationKey[pageIndex].currentState!.validate()) {
            AppToast.showToast(
                message: 'Missing information', gravity: ToastGravity.BOTTOM);

            return;
          }

          if (pageIndex == 0) {
            if (_isProgramBasicInfoValid() == false) {
              return;
            }
          }

          if(pageIndex == 1) {
            if(programPriceInfoKey.currentState!.selectedOptions.isEmpty) {
              _showToastMessage('Program price information missing');

              return;
            }
          }

          if (pageIndex + 1 < pages.length) {
            _moveToPage(index: pageIndex + 1);
          } else {
            Log.debug('submit data to server');
            String programTitle =
                programBasicInfoKey.currentState!.programTitleController.text;
            String programDesc =
                programBasicInfoKey.currentState!.programDescController.text;

            // int durationInDays = int.tryParse(programBasicInfoKey
            //         .currentState!.programDurationNotifier.value.name) ??
            //     0;
            List<String> guarantees = programBasicInfoKey
                .currentState!.selectedGuarantees
                .map((option) {
              return option.name;
            }).toList();
            
            String category = programBasicInfoKey
                .currentState!.programCategoryNotifier.value.name;
            String subCategories = programBasicInfoKey
                .currentState!.programSubCategorieNotifier.value.name;

            List<CoachProgramFeature> features =
                programFeatureInfoKey.currentState!.programFeatures;
            coachProgramFeatures = features;

            String programDurationType = programBasicInfoKey.currentState!.programDurationNotifier.value.name;
            int programDurationCount = int.tryParse(programBasicInfoKey.currentState!.durationCountController.text) ?? 0;


            final programEntity = CoachProgramEntity(
              programId: (_coachProgramEntity != null)
                  ? _coachProgramEntity!.programId
                  : null,
              coachId: (_coachProgramEntity != null)
                  ? _coachProgramEntity!.coachId
                  : null,
              title: programTitle,
              desc: programDesc,
              
              features: features,
              guarantees: guarantees,
              totalSubscription: null,
              totalIncome: 0,
              isBestProgram: false,
              categoryId: category,
              subCategoryId: subCategories,
              oneTimePrice: programPriceInfoKey.currentState!.onetimePriceNotifier.value,
              // dailyPrice: programPriceInfoKey.currentState!.dailyPriceNotifier.value,
              // weeklyPrice: programPriceInfoKey.currentState!.weeklyPriceNotifier.value,
              monthlyPrice: programPriceInfoKey.currentState!.monthlyPriceNotifier.value,
              durationType: programDurationType, // TODO
              durationCount: programDurationCount, // TODO
            );

            
            Log.debug('program payments: ${programEntity.payments.length}');

             _moveToCoachProgramPreviewPage(programEntity);
          }
        });
  }

  _moveToPage({required int index}) {
    pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 400),
      curve: Curves.linear,
    );
  }

  _moveToCoachProgramPreviewPage(CoachProgramEntity programEntity) {

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CoachProgramPreviewPage(
          isCreatingCoachProgram: widget.isCreateProgram,
          isUpdateCoachProgram: !(widget.isCreateProgram),
          isPreviewCoachProgram: false,
          programEntity: programEntity,
        ),
      ),
    );
  }

  bool _isProgramBasicInfoValid() {
    if (programBasicInfoKey.currentState != null) {
      String category =
          programBasicInfoKey.currentState!.programCategoryNotifier.value.name;
      if (category == '') {
        _showToastMessage('Select a coach category');

        return false;
      }
      String subCategories = programBasicInfoKey
          .currentState!.programSubCategorieNotifier.value.name;

      if (subCategories == '') {
        _showToastMessage('Select a coach sub-category');

        return false;
      }

      String programDurations =
          programBasicInfoKey.currentState!.programDurationNotifier.value.name;

      if (programDurations == '') {
        _showToastMessage('Select a program duration');

        return false;
      }

      List<ChoiceOption> guarantees =
          programBasicInfoKey.currentState!.selectedGuarantees;
      if (guarantees.isEmpty) {
        _showToastMessage('No guarantees is selected');

        return false;
      }

      // ChoiceOption? paymentChoice =
      //     programBasicInfoKey.currentState!.selectedPaymentTerm.value;
      // if (paymentChoice == null || paymentChoice.name == '') {
      //   _showToastMessage('Select price validity');

      //   return false;
      // }

      return true;
    } else {
      return false;
    }
  }

  _programFeatureInfoValidation() {
    //TODO
  }

  _showToastMessage(String message) {
    AppToast.showToast(
      message: message,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: AppColors.warning,
    );
  }

  _builProgramPriceInfo() {
    return CoachProgramPriceInfoWidget(key: programPriceInfoKey, programEntity: _coachProgramEntity);
  }
}
