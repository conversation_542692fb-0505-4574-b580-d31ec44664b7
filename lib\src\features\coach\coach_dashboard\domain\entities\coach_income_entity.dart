class CoachIncomeEntity {
  String? coachId;
  final double currentAccountBalance;
  final double withdrawableAmount;
  final int totalSubscriptions;
  final int lastWeekSubscriptionCount;
  final List<String> userImageLinks;
  final bool isWithdrawRequestPending;
  DateTime? lastIncomeDate;
  String? phoneNumber;
  double withdrawThreshold;
  

  CoachIncomeEntity({
    this.coachId,
    required this.currentAccountBalance,
    required this.withdrawableAmount,
    required this.totalSubscriptions,
    required this.lastWeekSubscriptionCount,
    required this.userImageLinks,
    required this.isWithdrawRequestPending,
    required this.withdrawThreshold,
    this.lastIncomeDate,
    this.phoneNumber,
  });
}
