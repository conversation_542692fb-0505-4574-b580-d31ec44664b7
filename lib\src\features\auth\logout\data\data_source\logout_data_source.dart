import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';

abstract class LogoutDataSource {
  Future<Response> logout();
}

class LogoutDataSourceImpl implements LogoutDataSource {
  const LogoutDataSourceImpl({required this.restClient});

  final RestClient restClient;

  @override
  Future<Response> logout() async {
    final Response response = await restClient.get(
      APIType.PROTECTED,
      API.logout,
    );

    Log.info(
        'Received logout response from server with statusCode: ${response.statusCode}');
    if (response.statusCode == 200) {
      if (await GoogleSignIn().isSignedIn()) {
        Log.info('GoogleSignIn: true');
        await GoogleSignIn().signOut();
      }

      if (await FacebookAuth.instance.accessToken != null) {
        Log.info('Facebook SignIn: true');
        await FacebookAuth.instance.logOut();
      }
    }

    return response;
  }
}
