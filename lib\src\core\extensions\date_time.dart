extension DateTimeExt on DateTime {
  bool isSameDate(DateTime other) {
    return day == other.day && month == other.month && year == other.year;
  }

  bool isAfterDate(DateTime other) {
    if(year > other.year) {
      return true;
    }
    else if(year == other.year) {
      if(month > other.month) {
        return true;
      }
      else if(month == other.month) {
        if(day > other.day) {
          return true;
        }
      }

    }
    
    return false;
  }
}
