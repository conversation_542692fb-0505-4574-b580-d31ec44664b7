part of 'coach_newsfeed_bloc.dart';

sealed class <PERSON><PERSON>ewsfeedEvent extends Equatable {
  const CoachNewsfeedEvent();

  @override
  List<Object> get props => [];
}

class GetCoachProfilesEvent extends CoachNewsfeedEvent {
  final CoachProfileFilterType filterType;
  final String? namePrefix;
  final String? categoryId;
  final String? subcategoryId;
  final int? offset;
  final int? limit;

  GetCoachProfilesEvent({
    required this.filterType,
    this.namePrefix,
    this.categoryId,
    this.subcategoryId,
    this.offset,
    this.limit,
  });

  @override
  List<Object> get props => [filterType];
}


class GetBeastCoachProfilesEvent extends CoachNewsfeedEvent {
  final int? offset;
  final int? limit;

  GetBeastCoachProfilesEvent({
    this.offset,
    this.limit,
  });

  @override
  List<Object> get props => [];
}

class GetSingleCoachProfileEvent extends CoachNewsfeedEvent {
  final String coachId;

  const GetSingleCoachProfileEvent({required this.coachId});

  @override
  List<Object> get props => [coachId];
}

class GetCoachProgramsEvent extends CoachNewsfeedEvent {
  final CoachProfileFilterType filterType;
  final String? namePrefix;
  final int? offset;
  final int? limit;

  GetCoachProgramsEvent({
    required this.filterType,
    this.namePrefix,
    this.offset,
    this.limit,
  });

  @override
  List<Object> get props => [filterType];
}
