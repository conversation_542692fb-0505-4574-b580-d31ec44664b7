part of 'media_bloc.dart';

abstract class MediaState extends Equatable {
  @override
  List<Object> get props => [];
}

class PostLoadingState extends MediaState {}

class PostLoadedState extends MediaState {}

class FileLoadingState extends MediaState {}

class FileLoadedState extends MediaState {
  final List<AssetEntity> mediaFiles;
  FileLoadedState(this.mediaFiles);
}

class SelectedImagePreviewState extends MediaState {
  final Uint8List previewedFile;
  final List<AssetEntity> mediaFiles;
  SelectedImagePreviewState(this.previewedFile, this.mediaFiles);
  @override
  List<Object> get props => [previewedFile, mediaFiles];
}

class ImageSelectedState extends MediaState {
  final MediaModel mediaModel;
  ImageSelectedState(this.mediaModel);
  @override
  List<Object> get props => [mediaModel];
}
