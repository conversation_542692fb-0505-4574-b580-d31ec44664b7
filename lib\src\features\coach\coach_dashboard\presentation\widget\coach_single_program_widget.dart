import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/pages/coach_program_preview_for_coach.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/pages/create_coach_program_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class CoachSingleProgramWidget extends StatefulWidget {
  const CoachSingleProgramWidget({Key? key, required this.programEntity})
      : super(key: key);
  final CoachProgramEntity programEntity;

  @override
  State<CoachSingleProgramWidget> createState() =>
      _CoachSingleProgramWidgetState();
}

class _CoachSingleProgramWidgetState extends State<CoachSingleProgramWidget> {
  @override
  Widget build(BuildContext context) {
    return _buildProgramInfoCard();
  }

  _buildProgramInfoCard() {
    return GestureDetector(
      onTap: () {
        Log.debug('press program');
        Navigator.of(context).push(MaterialPageRoute(
            builder: (_) => CoachProgramPreviewForCoach(
                  coachId: widget.programEntity.coachId!,
                  programId: widget.programEntity.programId!,
                )));
      },
      child: Container(
        margin: EdgeInsets.only(top: Values.v20),
        padding:
            EdgeInsets.symmetric(vertical: Values.v20, horizontal: Values.v16),
        decoration: BoxDecoration(
            color: AppColors.greyscale10,
            border: Border.all(color: AppColors.greyscale100),
            borderRadius: BorderRadius.circular(Values.v10)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProgramTitle(title: widget.programEntity.title),
                _buildProgramEditIcon(),
              ],
            ),
            const SizedBox(
              height: Values.v20,
            ),
            _buildCoachIncomeFromProgramSection(
                enrollment: widget.programEntity.totalSubscription!,
                incomeAmount: widget.programEntity.totalIncome),
          ],
        ),
      ),
    );
  }

  _buildProgramTitle({required String title}) {
    return Expanded(
      child: Text(
        title,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
      ),
    );
  }

  _buildProgramEditIcon() {
    if (widget.programEntity.totalSubscription != null &&
        widget.programEntity.totalSubscription != 0) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: () {
        Log.debug('press edit program');
        Navigator.of(context).push(MaterialPageRoute(
            builder: (_) => CreateCoachProgramPage(
                  programId: widget.programEntity.programId,
                  isCreateProgram: false,
                )));
      },
      child: Container(
        margin: const EdgeInsets.only(right: Values.v4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Values.v36),
        ),
        child: Center(
          child: SvgPicture.asset(
            Assets.coachProgramEditIcon,
            height: Values.v20,
            width: Values.v20,
          ),
        ),
      ),
    );
  }

  _buildCoachIncomeFromProgramSection(
      {required int enrollment, required int incomeAmount}) {
    TextStyle valueTextStyle = TextStyle(
      fontSize: FontSize.s14,
      fontFamily: FontConstants.poppinsFontFamily,
      fontWeight: FontWeightManager.medium,
      color: UIColors.primaryGreen950,
      height: 0.8,
    );

    return Wrap(
      spacing: Values.v10,
      children: [
        // Text('Total Enrollment: ${programEntity.totalSubscription}'),
        _buildProgramInfoText(
          'Total Enrollment',
          '${enrollment}',
          AppTypography.poppinsMedium12(color: AppColors.greyscale400),
          valueTextStyle,
        ),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 3),
          height: 20,
          width: 2,
          color: UIColors.primaryGreen400,
        ),
        // Text('Total Income: BDT ${350000}'),
        _buildProgramInfoText(
            'Total Income',
            'BDT ${incomeAmount}',
            AppTypography.poppinsMedium12(color: AppColors.greyscale400),
            valueTextStyle),
      ],
    );
  }

  _buildProgramInfoText(String infoName, String? infoValue,
      TextStyle infoNameStyle, TextStyle infoValueStyle) {
    if (infoValue == null || infoValue == '') return SizedBox.shrink();

    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          "$infoName:",
          style: infoNameStyle,
        ),
        SizedBox(
          width: Values.v5,
        ),
        Flexible(
          child: Wrap(
            children: [
              Text(
                infoValue,
                style: infoValueStyle,
                overflow: TextOverflow.clip,
              ),
            ],
          ),
        )
      ],
    );
  }
}
