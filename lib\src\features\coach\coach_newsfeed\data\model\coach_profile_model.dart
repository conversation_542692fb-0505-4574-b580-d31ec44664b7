import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_profile_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';

class CoachProfileModel extends CoachProfileEntity {
  CoachProfileModel({
    required super.coachId,
    required super.userId,
    required super.userName,
    required super.legalName,
    required super.expertise,
    required super.experienceInYears,
    required super.subscriptionCount,
    required super.currentRating,
    required super.profilePictures,
  });

  factory CoachProfileModel.fromJson(Map<String, dynamic> json) {
    return CoachProfileModel(
      coachId: json['id'],
      userId: json['userId'] ,
      userName: json['userName'] ?? '',
      legalName: json['legalName'] ?? '',
      expertise: json['expertise'],
      experienceInYears: json['experienceInYear'],
      subscriptionCount: json['subscriptionCount'],
      currentRating: json['currentRating'].toDouble(),
      profilePictures: (json['media'] == null)
          ? []
          : List<CoachMediaFile>.from(
              json['media'].map((x) => CoachMediaFile.fromJson(x))),
    );
  }
}

// {
//   "data": [
//     {
//       "id": "string",
//       "userId": "string",
//       "userName": "string",
//       "legalName": "string",
//       "expertise": "string",
//       "experienceInYear": 0,
//       "subscriptionCount": 0,
//       "currentRating": 0
//     }
//   ]
// }
