import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/presentation/bloc/reward_point_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/presentation/widget/login_challenge_widget.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/presentation/widget/reward_option_widget.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/presentation/widget/total_points_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RewardDashboardPage extends StatefulWidget {
  const RewardDashboardPage({super.key});

  @override
  State<RewardDashboardPage> createState() => _RewardDashboardPageState();
}

class _RewardDashboardPageState extends State<RewardDashboardPage> {
  late final String userId;

  @override
  void initState() {
    super.initState();
    userId = context.read<SharedBloc>().userProfile!.id;
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RewardPointBloc, RewardPointState>(
      listener: (context, state) {
        // TODO: implement listener
        if (state is GetRewardPointSuccess) {
          // _showHowToEarnPointsAlert();
          Log.debug('received point from server');
        }
      },
      child: Scaffold(
        appBar: _buildAppBar(),
        body: SafeArea(
            child: Padding(
          padding: const EdgeInsets.all(Values.v10),
          child: SingleChildScrollView(child: _buildRewardDashboard()),
        )),
      ),
    );
  }

  _buildRewardDashboard() {
    return Column(
      children: [
        _buildTotalPointSection(),
        SizedBox(
          height: Values.v10,
        ),
        _buidLoginChallengeSection(),
        SizedBox(
          height: Values.v20,
        ),
        _buildRewardOptionSection(),
      ],
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        'Rewards',
        style: TextStyle(
          color: UIColors.primaryGreen950,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: true,
      elevation: 0,
    );
  }

  _buildTotalPointSection() {
    return TotalPointsWidget(
      userId: userId,
    );
  }

  _buidLoginChallengeSection() {
    return LoginChallengeWidget(
      userId: userId,
    );
  }

  _buildRewardOptionSection() {
    return RewardOptionWidget();
  }
}
