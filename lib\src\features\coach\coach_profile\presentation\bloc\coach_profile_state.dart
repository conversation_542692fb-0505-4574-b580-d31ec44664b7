part of 'coach_profile_bloc.dart';

sealed class Coach<PERSON><PERSON><PERSON>leState extends Equatable {
  const CoachProfileState();
  
  @override
  List<Object> get props => [];
}

final class CoachRegistrationInitial extends Coach<PERSON><PERSON><PERSON>leState {
  @override
  List<Object> get props => [];
}

class CoachRegistrationLoading extends CoachProfileState { }

class CoachRegistrationSuccess extends CoachProfileState {
  final CoachEntity registrationEntity;

  const CoachRegistrationSuccess({required this.registrationEntity});

   @override
  List<Object> get props => [registrationEntity];
}

class CoachRegistrationFail<T> extends CoachProfileState {
  final T? data;
  const CoachRegistrationFail({required this.data});

  @override
  List<Object> get props => [];
}

class UpdateCoachProfileSuccess extends CoachProfileState {
  final CoachEntity coachEntity;

  const UpdateCoachProfileSuccess({required this.coachEntity});

   @override
  List<Object> get props => [coachEntity];
}

class UpdateCoachProfileFail<T> extends CoachProfileState {
  final T? data;
  const UpdateCoachProfileFail({required this.data});

  @override
  List<Object> get props => [];

}
