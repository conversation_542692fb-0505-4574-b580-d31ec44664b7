import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_leaderboard_data_entity.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_point_rank_entity.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/repository/reward_leaderboard_repository.dart';

class RewardLeaderboardUseCase {
  final RewardLeaderboardRepository repository;

  RewardLeaderboardUseCase({required this.repository});
  
  Future<Either<ErrorModel, List<RewardLeaderboardDataEntity>>> getTopUser({
    required int? offset,
    required int? limit,
    required String? filterType,
  }) async {
    return await repository.getTopUser(offset: offset, limit: limit, filterType: filterType);
  }

  Future<Either<ErrorModel, RewardPointRankEntity>>getUserCurrentRank() async {
    return  await repository.getUserCurrentRank();
  }
}