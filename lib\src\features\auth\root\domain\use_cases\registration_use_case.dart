import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/entities/user.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/repositories/auth_repository.dart';

class RegistrationUseCase {
  const RegistrationUseCase({required this.repository});

  final AuthRepository repository;

  Future<Either<ErrorModel, String>> registerWithEmailAndPassword(
    Map<String, dynamic> requestBody,
  ) async {
    return repository.registration(requestBody);
  }

  Future<Either<ErrorModel, User>> registerWithGoogle() async {
    return repository.google();
  }

  Future<Either<ErrorModel, User>> registerWithFacebook() async {
    return repository.facebook();
  }

  Future<Either<ErrorModel, User>> registerWithApple() async {
    return repository.apple();
  }
}
