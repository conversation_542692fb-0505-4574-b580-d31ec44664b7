part of 'food_scan_bloc.dart';

sealed class FoodScanState extends Equatable {
  const FoodScanState();
  
  @override
  List<Object> get props => [];
}

final class FoodScanInitial extends FoodScanState {}

final class FoodScanLoading extends FoodScanState {}

final class FoodScanSuccess extends FoodScanState {
  
  final FoodInfoEntity foodInfo;

  const FoodScanSuccess({required this.foodInfo});

  @override
  List<Object> get props => [foodInfo];
}

class FoodScanFailure<T> extends FoodScanState {
  final T? data;

  const FoodScanFailure({this.data});

  @override
  List<Object> get props => [data ?? 'Unknown error'];
}

final class FoodScanFeedbackSuccess extends FoodScanState {
  final FoodInfoEntity foodInfo;

  const FoodScanFeedbackSuccess({required this.foodInfo});

  @override
  List<Object> get props => [foodInfo];
}

final class FoodScanFeedbackFailure<T> extends FoodScanState {
  final T? data;

  const FoodScanFeedbackFailure({this.data});

  @override
  List<Object> get props => [data ?? 'Unknown error'];
}
