import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/presentation/model/chat_model.dart';

class ChatListEntity extends Equatable {
  const ChatListEntity({
    required this.lastMessageInfo,
    required this.fitBuddyId,
    required this.name,
    required this.image,
  });

  final LastMessageInfo? lastMessageInfo;
  final String? fitBuddyId;
  final String? name;
  final String? image;

  ChatListViewModel toViewModel() {
    return ChatListViewModel(
      id: fitBuddyId!,
      name: name!,
      image: image,
      lastMessage: lastMessageInfo!.content!,
      isLastSeen: lastMessageInfo!.isLastSeen!,
      senderId: lastMessageInfo!.senderId!,
      type: lastMessageInfo!.type!,
      createdAt:lastMessageInfo!.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        lastMessageInfo,
        fitBuddyId,
        name,
        image,
      ];
}

class LastMessageInfo {
  LastMessageInfo({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.type,
    required this.content,
    required this.isLastSeen,
    required this.createdAt,
  });

  final String? id;
  final String? senderId;
  final String? receiverId;
  final String? type;
  final String? content;
  final bool? isLastSeen;
  final DateTime? createdAt;

  factory LastMessageInfo.fromJson(Map<String, dynamic> json) =>
      LastMessageInfo(
        id: json["id"],
        senderId: json["senderId"],
        receiverId: json["receiverId"],
        type: json["type"],
        content: json["content"],
        isLastSeen: json['isLastSeen'] ?? false,
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]).toLocal(),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "senderId": senderId,
        "receiverId": receiverId,
        "type": type,
        "content": content,
        "createdAt": createdAt == null ? null : createdAt!.toIso8601String(),
      };
}
