<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="jst-Vb-cui" customClass="GADNativeAdView">
            <rect key="frame" x="0.0" y="0.0" width="382" height="432"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="Headline" textAlignment="justified" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="C1a-JM-qYK">
                    <rect key="frame" x="16" y="102" width="346" height="57"/>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <color key="textColor" systemColor="darkTextColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="Body that is really really long and can take up to two lines or sometimes even more." textAlignment="justified" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SrO-EW-N9h">
                    <rect key="frame" x="16" y="325" width="346" height="28.666666666666686"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="28.670000000000002" id="uhw-c7-K3B"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <color key="textColor" systemColor="darkTextColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person.crop.circle" catalog="system" translatesAutoresizingMaskIntoConstraints="NO" id="1fk-kU-oWx">
                    <rect key="frame" x="16" y="60.666666666666671" width="40" height="38.666666666666671"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="HLv-bf-EGq"/>
                        <constraint firstAttribute="width" constant="40" id="LNd-PB-odz"/>
                    </constraints>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Advertiser" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="drl-dr-y0X">
                    <rect key="frame" x="63.999999999999993" y="59" width="72.333333333333314" height="17"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="14"/>
                    <color key="textColor" systemColor="darkTextColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ad" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Agc-xT-6Rv">
                    <rect key="frame" x="64" y="81" width="15" height="13.333333333333329"/>
                    <color key="backgroundColor" red="1" green="0.80000001190000003" blue="0.40000000600000002" alpha="1" colorSpace="calibratedRGB"/>
                    <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="11"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="wEU-pw-G8h">
                    <rect key="frame" x="89" y="361" width="200" height="27"/>
                    <color key="backgroundColor" systemColor="systemGreenColor"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="200" id="pN8-Lw-P71"/>
                        <constraint firstAttribute="height" constant="27" id="pRD-ke-nt5"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <state key="normal" title="Install">
                        <color key="titleColor" systemColor="labelColor"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                </button>
                <view contentMode="scaleAspectFit" verticalCompressionResistancePriority="751" translatesAutoresizingMaskIntoConstraints="NO" id="dPB-iV-i7k" customClass="GADMediaView">
                    <rect key="frame" x="61" y="167" width="256" height="150"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="150" id="F0n-0y-Y3L"/>
                        <constraint firstAttribute="width" constant="256" id="NcP-9o-JyN"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="mt9-Au-6mk"/>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="1fk-kU-oWx" firstAttribute="leading" secondItem="mt9-Au-6mk" secondAttribute="leading" constant="16" id="HSa-F8-IgV"/>
                <constraint firstItem="C1a-JM-qYK" firstAttribute="top" secondItem="1fk-kU-oWx" secondAttribute="bottom" constant="2" id="QAk-o9-I29"/>
                <constraint firstItem="1fk-kU-oWx" firstAttribute="top" secondItem="mt9-Au-6mk" secondAttribute="top" constant="1" id="TBs-fv-fHd"/>
                <constraint firstItem="C1a-JM-qYK" firstAttribute="leading" secondItem="1fk-kU-oWx" secondAttribute="leading" id="Tgi-j9-8l8"/>
                <constraint firstItem="dPB-iV-i7k" firstAttribute="top" secondItem="C1a-JM-qYK" secondAttribute="bottom" constant="8" symbolic="YES" id="Zgi-rn-yf9"/>
                <constraint firstItem="wEU-pw-G8h" firstAttribute="top" secondItem="SrO-EW-N9h" secondAttribute="bottom" constant="7.3300000000000001" id="adX-by-uHJ"/>
                <constraint firstItem="Agc-xT-6Rv" firstAttribute="leading" secondItem="drl-dr-y0X" secondAttribute="leading" id="avb-qU-JlO"/>
                <constraint firstItem="Agc-xT-6Rv" firstAttribute="top" secondItem="drl-dr-y0X" secondAttribute="bottom" constant="5" id="cSK-9a-8R8"/>
                <constraint firstItem="mt9-Au-6mk" firstAttribute="trailing" secondItem="C1a-JM-qYK" secondAttribute="trailing" constant="20" id="dso-CK-YaQ"/>
                <constraint firstItem="wEU-pw-G8h" firstAttribute="centerX" secondItem="C1a-JM-qYK" secondAttribute="centerX" id="eMS-cz-PGJ"/>
                <constraint firstItem="SrO-EW-N9h" firstAttribute="top" secondItem="dPB-iV-i7k" secondAttribute="bottom" constant="8" symbolic="YES" id="h2Z-Km-EVt"/>
                <constraint firstItem="drl-dr-y0X" firstAttribute="top" secondItem="mt9-Au-6mk" secondAttribute="top" id="leP-ke-gb8"/>
                <constraint firstItem="dPB-iV-i7k" firstAttribute="centerX" secondItem="C1a-JM-qYK" secondAttribute="centerX" id="prx-k2-mCu"/>
                <constraint firstItem="drl-dr-y0X" firstAttribute="leading" secondItem="1fk-kU-oWx" secondAttribute="trailing" constant="8" id="q2X-MC-WHP"/>
                <constraint firstItem="SrO-EW-N9h" firstAttribute="trailing" secondItem="C1a-JM-qYK" secondAttribute="trailing" id="vjQ-4W-A8r"/>
                <constraint firstItem="mt9-Au-6mk" firstAttribute="bottom" secondItem="wEU-pw-G8h" secondAttribute="bottom" constant="10" id="wam-zo-jmp"/>
                <constraint firstItem="SrO-EW-N9h" firstAttribute="leading" secondItem="C1a-JM-qYK" secondAttribute="leading" id="wyR-v5-Bka"/>
                <constraint firstItem="SrO-EW-N9h" firstAttribute="trailing" secondItem="C1a-JM-qYK" secondAttribute="trailing" id="x0U-Lz-WNh"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="advertiserView" destination="drl-dr-y0X" id="HsM-vK-fec"/>
                <outlet property="bodyView" destination="SrO-EW-N9h" id="fhl-QW-Bwx"/>
                <outlet property="callToActionView" destination="wEU-pw-G8h" id="ebh-zZ-ST5"/>
                <outlet property="headlineView" destination="C1a-JM-qYK" id="H8e-67-5mE"/>
                <outlet property="iconView" destination="1fk-kU-oWx" id="zB5-AG-01Y"/>
                <outlet property="mediaView" destination="dPB-iV-i7k" id="jYC-i8-jBm"/>
            </connections>
            <point key="canvasLocation" x="42.748091603053432" y="-88.028169014084511"/>
        </view>
    </objects>
    <resources>
        <image name="person.crop.circle" catalog="system" width="128" height="123"/>
        <systemColor name="darkTextColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemGreenColor">
            <color red="0.20392156859999999" green="0.78039215689999997" blue="0.34901960780000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
