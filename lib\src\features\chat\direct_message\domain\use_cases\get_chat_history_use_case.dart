import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/entity/chat_history_entity.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/repositories/one_to_one_chat_repository.dart';

class GetChatHistoryUseCase {
  const GetChatHistoryUseCase({required this.oneToOneChatRepository});

  final OneToOneChatRepository oneToOneChatRepository;

  Future<Either<String, List<ChatHistoryEntity>>> call(
      {int? limit, int? offset, required String userId}) async {
    return await oneToOneChatRepository.getChatHistory(
      userId: userId,
      limit: limit,
      offset: offset,
    );
  }
}