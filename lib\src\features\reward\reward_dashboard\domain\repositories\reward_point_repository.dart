import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_point_rank_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/reward_point_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/reward_point_history_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/user_login_activity_entity.dart';

abstract class RewardPointRepository {
  Future<Either<ErrorModel, RewardPointRankEntity>> getCurrentPoints();
  Future<Either<ErrorModel, UserLoginActivityEntity>> getWeeklyLoginHistory({required String userId});
  Future<Either<ErrorModel, List<RewardPointHistoryEntity>>>getRewardPointHistory({required int? offset, required int? limit, required String? pointHistoryFilter});
}