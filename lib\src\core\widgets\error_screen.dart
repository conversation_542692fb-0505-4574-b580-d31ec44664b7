import 'dart:async';

import 'package:app_settings/app_settings.dart';
import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:fitsomnia_app/src/core/global/globals.dart';

class ErrorScreen extends StatefulWidget {
  const ErrorScreen({
    Key? key,
    this.title = 'Uh-oh! Something went wrong.',
    this.noInternet = false,
  }) : super(key: key);

  final String title;
  final bool noInternet;

  @override
  State<ErrorScreen> createState() => _ErrorScreenState();
}

class _ErrorScreenState extends State<ErrorScreen> {
  Timer? _timer;

  @override
  void initState() {
    if (widget.noInternet) {
      _checkInternetConnection();
      noInternetScreenActive.value = true;
      errorScreenActive.value = false;
    } else {
      noInternetScreenActive.value = false;
      errorScreenActive.value = true;
    }
    super.initState();
  }

  @override
  void dispose() {
    noInternetScreenActive.value = false;
    errorScreenActive.value = false;
    _timer?.cancel();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 32.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(),
              const Icon(
                Icons.error_outline_rounded,
                size: 120,
                color: AppColors.red,
              ),
              SizedBox(height: 16.h),
              Text(
                widget.title,
                style: AppTypography.bold50().copyWith(
                  fontSize: 30.sp,
                  color: AppColors.black.withOpacity(0.7),
                ),
              ),
              SizedBox(height: 32.h),
              Text(
                widget.noInternet
                    ? "No Internet connection. Reconnect with internet to continue."
                    : "We tripped on a dumbbell. We're picking ourselves up to serve you better!",
                style: AppTypography.semiBold20(
                  color: AppColors.black.withOpacity(0.7),
                ),
              ),
              const Spacer(),
              Row(
                children: [
                  widget.noInternet
                      ? const SizedBox()
                      : Expanded(
                          child: Button(
                            label: 'Try Again',
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                          ),
                        ),
                  SizedBox(width: widget.noInternet ? 0 : 16.w),
                  Expanded(
                    child: Button.outlined(
                      label: widget.noInternet ? 'Open Settings' : 'Report',
                      onPressed: widget.noInternet
                          ? () {
                              AppSettings.openAppSettings();
                            }
                          : () {
                              String email = '<EMAIL>';
                              String subject = 'Fitsomnia Support';

                              openMailApp(email: email, subject: subject);
                            },
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
            ],
          ),
        ),
      ),
    );
  }

  void openMailApp({
    required String email,
    required String subject,
  }) async {
    final Uri emailLaunchUri = Uri(
      scheme: 'mailto',
      path: email,
      query: encodeQueryParameters(<String, String>{
        'subject': subject,
      }),
    );

    launchUrl(emailLaunchUri);
  }

  String? encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((MapEntry<String, String> e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }

  void _checkInternetConnection() {
    _timer = Timer.periodic(const Duration(seconds: 5), (timer) async {
      bool result = await InternetConnection().hasInternetAccess;
      if (result && mounted) {
        _timer?.cancel();
        if(navigatorKey?.currentState?.canPop() ?? false) {
          navigatorKey?.currentState?.pop();
        }
        
      }
    });
  }
}
