import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/domain/entity/all_users_entity.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/group_members/group_members_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/pages/dashboard/others_profile_page.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/domain/entities/fit_buddy_status_entity.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/presentation/bloc/spot_bloc/spot_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';

class GroupChatMembersPage extends StatefulWidget {
  const GroupChatMembersPage({Key? key, required this.groupId})
      : super(key: key);

  final String groupId;

  @override
  State<GroupChatMembersPage> createState() => _GroupChatMembersPageState();
}

class _GroupChatMembersPageState extends State<GroupChatMembersPage> {
  @override
  void initState() {
    super.initState();
    BlocProvider.of<GroupMembersBloc>(context)
        .add(GroupMembersEvent(groupId: widget.groupId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: _buildAppBar(),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [_buildPeopleList()],
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        "Group Members",
        style: TextStyle(
          color: AppColors.black,
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        IconButton(
            onPressed: () {
              Navigator.pushNamed(context, Routes.addGroupChatMember,
                  arguments: widget.groupId);
            },
            icon: const Icon(Icons.add)),
      ],
      elevation: 0,
      iconTheme: const IconThemeData(color: AppColors.black),
      backgroundColor: AppColors.white,
    );
  }

  Widget _buildPeopleList() {
    return BlocConsumer<GroupMembersBloc, GroupMembersState>(
      listener: (context, state) {
        if (state is GroupMembersInitial) {
          BlocProvider.of<GroupMembersBloc>(context).add(
            GroupMembersEvent(groupId: widget.groupId),
          );
        }
      },
      buildWhen: (previous, current) => previous != current,
      builder: (context, state) {
        if (state is GroupMembersLoading) {
          return const CircularProgressIndicator(
            color: Colors.green,
          );
        } else if (state is GroupMembersSuccess) {
          if (state.groupMembersList.isNotEmpty) {
            return Expanded(
              child: ListView.builder(
                itemCount: state.groupMembersList.length,
                itemBuilder: (context, index) {
                  return _buildUserCard(context, state.groupMembersList[index]);
                },
              ),
            );
          }

          return const Expanded(
            child: Center(
              child: Text("No Members Found!"),
            ),
          );
        } else if (state is GroupMembersFailure) {
          return const Expanded(
            child: Center(
              child: Text("Something went wrong. Please try again..."),
            ),
          );
        }

        return const CircularProgressIndicator(
          color: Colors.green,
        );
      },
    );
  }

  Widget _buildUserCard(
    BuildContext context,
    AllUsersEntityWithoutProfile data,
  ) {
    return InkWell(
      onTap: () {
        BlocProvider.of<SpotBloc>(context).add(
          GetFitBuddyStatusEvent(fitBuddyId: data.id),
        );
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => OthersProfilePage(
              userId: data.id,
            ),
          ),
        );
      },
      child: BlocListener<SpotBloc, BaseState>(
        listener: (context, state) {
          if (state is FitBuddyStatusSuccessState) {
            String? status = (state.data as FitBuddyStatusEntity).status;
            if (status != null) {
              if (status == "BLOCKED") {
                AppToast.showToast(
                  message: "Sorry, this profile is not available",
                  backgroundColor: AppColors.error,
                  gravity: ToastGravity.BOTTOM,
                );
              }
            }
          }
        },
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    _buildAvatar(data),
                    const SizedBox(width: 16),
                    _buildUserName(data.name),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(AllUsersEntityWithoutProfile data) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        ImageContainer.circularImage(
          image: data.image ?? '',
          radius: Values.v23,
        ),
      ],
    );
  }

  Widget _buildUserName(String name) {
    return Expanded(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Text(
          name,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
            color: AppColors.black,
          ),
        ),
      ),
    );
  }
}
