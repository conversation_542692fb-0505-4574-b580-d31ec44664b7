import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:flutter/material.dart';
import 'package:group_radio_button/group_radio_button.dart';

class AppGroupRadioButton extends StatefulWidget {
  const AppGroupRadioButton({
    Key? key,
    required this.groupValue,
    required this.onChanged,
  }) : super(key: key);

  final List<String> groupValue;
  final Function(String?)? onChanged;

  @override
  State<AppGroupRadioButton> createState() => _AppGroupRadioButtonState();
}

class _AppGroupRadioButtonState extends State<AppGroupRadioButton> {
  String? groupValue;

  @override
  Widget build(BuildContext context) {
    return RadioTheme(
      data: const RadioThemeData(
        visualDensity: VisualDensity(horizontal: -4),
      ),
      child: RadioGroup<String>.builder(
        direction: Axis.horizontal,
        activeColor: AppColors.black,
        groupValue: groupValue ?? widget.groupValue.first,
        horizontalAlignment: MainAxisAlignment.start,
        onChanged: (value) {
          setState(() {
            groupValue = value!;
          });
          widget.onChanged!(value);
        },
        items: widget.groupValue,
        textStyle: AppTypography.regular12(
          color: AppColors.black,
        ).copyWith(fontWeight: FontWeight.w500),
        itemBuilder: (item) => RadioButtonBuilder(
          item,
        ),
      ),
    );
  }
}