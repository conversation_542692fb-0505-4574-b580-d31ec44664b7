import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_profile_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_profile_list_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_profile_search_view_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

class CoachTabView extends StatefulWidget {
  const CoachTabView({
    super.key,
    this.subcategoryId,
    this.categoryId,
    this.coachProfileFilterType,
  });
  final CoachProfileFilterType? coachProfileFilterType;
  final String? subcategoryId;
  final String? categoryId;

  @override
  State<CoachTabView> createState() => _CoachTabViewState();
}

class _CoachTabViewState extends State<CoachTabView> {
  @override
  void initState() {
    super.initState();
    // if (widget.categoryId != null) {
    //    Log.debug('category id: ${widget.categoryId}');
    //   BlocProvider.of<CoachNewsfeedBloc>(context).add(GetCoachProfilesEvent(
    //       filterType:
    //           widget.coachProfileFilterType ?? CoachProfileFilterType.BEST, categoryId: widget.categoryId));

    // } else if(widget.subcategoryId != null) {
    //   Log.debug('subcategory id: ${widget.subcategoryId}');
    //   BlocProvider.of<CoachNewsfeedBloc>(context).add(GetCoachProfilesEvent(
    //       filterType:
    //           widget.coachProfileFilterType ?? CoachProfileFilterType.BEST, subcategoryId: widget.subcategoryId));
    // } else {
    //   BlocProvider.of<CoachNewsfeedBloc>(context).add(GetCoachProfilesEvent(
    //       filterType:
    //           widget.coachProfileFilterType ?? CoachProfileFilterType.BEST));
    // }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: _buildAppBar(),
      body: SafeArea(
        child: Column(
          children: [
            // _buildPageHeader(),
            // _buildSearchSection(),
            Expanded(
              child: _buildCoachListSection(),
            )
          ],
        ),
      ),
    );
  }

  // AppBar _buildAppBar() {
  //   return AppBar(
  //     title: const Text(
  //       'Coach List',
  //       style: TextStyle(
  //         color: UIColors.primaryGreen950,
  //         fontWeight: FontWeight.bold,
  //       ),
  //       maxLines: 1,
  //       overflow: TextOverflow.ellipsis,
  //     ),
  //     centerTitle: true,
  //     elevation: 0,
  //   );
  // }

  // _buildPageHeader() {
  //   return Padding(
  //     padding: const EdgeInsets.only(top: Values.v20, left: Values.v20),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         _buildCoachListTitle(),
  //         Text(
  //           'Get to know your coach and subscibe for training',
  //           style: AppTypography.poppinsMedium14(color: AppColors.greyscale400),
  //         ),
  //         const SizedBox(
  //           height: 24,
  //         ),
  //       ],
  //     ),
  //   );
  // }

  _buildCoachListSection() {
    // return CoachProfileListWidget();
    return CoachProfileSearchList(
      key: UniqueKey(),
    );
  }

  Widget _buildCoachListTitle() {
    return RichText(
      text: TextSpan(
        style: AppTypography.poppinsSemiBold24(color: UIColors.primaryGreen950),
        children: [
          TextSpan(
            text: 'Visit our ',
            style: AppTypography.poppinsSemiBold24(color: UIColors.primary),
          ),
          const TextSpan(text: 'coaches ! '),
        ],
      ),
    );
  }
}

class CoachProfileSearchList extends StatefulWidget {
  const CoachProfileSearchList({Key? key}) : super(key: key);

  @override
  State<CoachProfileSearchList> createState() => _CoachProfileSearchListState();
}

class _CoachProfileSearchListState extends State<CoachProfileSearchList> {
  List<CoachProfileEntity> coachProfiles = [];

  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    isLoading = false;
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachNewsfeedBloc, CoachNewsfeedState>(
      listener: (context, state) {
        if (state is CoachProfilesLoading) {
          Log.debug('coach profiles is loading');
        }

        if (state is GetCoachProfilesSuccess) {
          Log.debug('get best coach profile success');
          setState(() {
            coachProfiles = state.coachProfiles;
            isLoading = false;
          });
        }
        if (state is GetCoachProfilesFail) {
          Log.debug('get best coach profile fail');
          setState(() {
            isLoading = false;
          });
        }
      },
      child: Container(
        margin: EdgeInsets.only(
          top: Values.v20,
        ),
        child: _buildCoachProfileList(),
      ),
    );
  }

  _buildCoachProfileList() {
    // coachProfiles = List.generate(20, (index) {
    //   return testBestCoachProfileEntity;
    // });

    return (isLoading)
        ? const SizedBox(
            height: 200,
            child: Center(
              child: CircularProgressIndicator(
                color: UIColors.primary,
              ),
            ),
          )
        : (coachProfiles.isEmpty)
            ? Center(child: Text('No Coach Found'))
            : ListView.separated(
                shrinkWrap: true,
                itemCount: coachProfiles.length,
                padding: EdgeInsets.all(Values.v5),
                itemBuilder: (BuildContext context, int index) {
                  return CoachProfileCard(
                      coachProfileEntity: coachProfiles[index]);
                },
                separatorBuilder: (context, index) {
                  return Divider(
                    height: Values.v20,
                    indent: Values.v20,
                    endIndent: Values.v20,
                    color: AppColors.greyscale100,
                  );
                },
              );
  }
}

class CoachProfileCard extends StatelessWidget {
  const CoachProfileCard({super.key, required this.coachProfileEntity});

  final CoachProfileEntity coachProfileEntity;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        _navigateToCoachProfile(context);
      },
      child: Container(
        margin: EdgeInsets.only(
          left: Values.v20,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildProfileImage(),
            Expanded(child: _buildProfileInformation(),),
          ],
        ),
      ),
    );
  }

  _buildProfileImage() {
    // return Container(
    //   child: Image.network(
    //     widget.profileImage,
    //     fit: BoxFit.fitHeight,
    //     // width: double.infinity,
    //     // height: double.infinity,
    //   ),
    // );

    if (coachProfileEntity.profilePictures.isEmpty) {
      return Image.asset(
        Assets.spotMeNoImage,
        height: Values.v80,
        width: Values.v80,
      );
    }

    return ImageContainer.rectangularImage(
      cornerRadius: Values.v10,
      image: coachProfileEntity.profilePictures.first.url,
      width: Values.v80,
      height: Values.v80,
      fit: BoxFit.fill,
      useOriginal: false,
      useSmall: true,
      hideLoadingIndicator: true,
      errorWidget: Container(
        height: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Values.v10),
          image: DecorationImage(
            image: const Image(
              image: AssetImage(
                Assets.spotMeNoImage,
              ),
            ).image,
            fit: BoxFit.fill,
          ),
        ),
      ),
    );
  }

  _buildProfileInformation() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          // mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              coachProfileEntity.legalName,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: AppTypography.poppinsSemiBold16(
                  color: UIColors.primaryGreen950),
            ),
            _buildCoachRatingSubscriberSection(),
            Text(
                '${coachProfileEntity.expertise}',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style:
                    AppTypography.poppinsRegular12(color: AppColors.greyscale400),
              ),
          ],
        ),
    );
  }

  _buildCoachRatingSubscriberSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        _buildCoachRatingInfo(),
        // Spacer(),
        // _buildCoachSubscriberInfo(),
      ],
    );
  }

  _buildCoachRatingInfo() {
    return Container(
      // padding:
      //     EdgeInsets.symmetric(horizontal: Values.v8, vertical: Values.v4),
      // decoration: BoxDecoration(
      //   color: AppColors.black.withOpacity(0.5),
      //   borderRadius: BorderRadius.circular(Values.v15),
      // ),
      child: Row(
        children: [
          SvgPicture.asset(
            Assets.coachRateIcon,
          ),
          SizedBox(
            width: 3,
          ),
          Text(
            '${coachProfileEntity.currentRating} ',
            style: AppTypography.poppinsSemiBold16(
                color: UIColors.primaryGreen950),
          ),
        ],
      ),
    );
  }

  void _navigateToCoachProfile(BuildContext context) {
    Log.debug('coach profile tap');
    Navigator.of(context).pushNamed(Routes.coachProfileDetailsPage,
        arguments: [null, coachProfileEntity.coachId, true]);
  }
}
