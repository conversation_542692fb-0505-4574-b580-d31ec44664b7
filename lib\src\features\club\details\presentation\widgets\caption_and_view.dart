import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CaptionAndViewAll extends StatelessWidget {
  const CaptionAndViewAll({
    Key? key,
    required this.caption,
    required this.onPressed,
  }) : super(key: key);

  final String caption;
  final Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          caption,
          style: AppTypography.semiBold16().copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        if (onPressed != null)
          TextButton(
            style: ElevatedButton.styleFrom(
              minimumSize: Size(
                Values.v85.w,
                Values.v35.h,
              ),
              padding: EdgeInsets.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              alignment: Alignment.centerRight,
            ),
            onPressed: onPressed,
            child: Text(
              TextConstants.viewAll,
              style: AppTypography.regular14(
                color: AppColors.primaryGreen,
              ),
            ),
          )
      ],
    );
  }
}
