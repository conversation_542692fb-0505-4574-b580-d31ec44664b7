import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/data/data_sources/delete_message_data_source.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/repositories/delete_message_repository.dart';

class DeleteMessageRepositoryImpl implements DeleteMessageRepository {
  final DeleteMessageDataSource deleteMessageDataSource;

  DeleteMessageRepositoryImpl({required this.deleteMessageDataSource});

  @override
  Future<Either<String, String>> deleteMessage({required String messageId}) async {
    try {
      final Response response =
          await deleteMessageDataSource.deleteMessage(messageId: messageId);
      final data = response.data['data'];

      return Right(data['message']);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, String>> deleteOneToOneMessage({required String messageId}) async {
    try {
      final Response response =
          await deleteMessageDataSource.deleteOneToOneMessage(messageId: messageId);
      final data = response.data['data'];

      return Right(data['message']);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left(e.toString());
    }
  }
}
