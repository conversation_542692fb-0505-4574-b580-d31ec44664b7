part of 'group_list_bloc.dart';

class GroupListState extends Equatable {
  @override
  List<Object?> get props => [];
}

class GroupListInitial extends GroupListState {}

class GroupListLoading extends GroupListState {}

class GroupListSuccess extends GroupListState {
  final List<GroupListEntity> groupList;

  GroupListSuccess({required this.groupList});

  @override
  List<Object?> get props => groupList;
}

class GroupListFailure extends GroupListState {
  final String errorMessage;

  GroupListFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}
