import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/exception/network_exception.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/data/data_source/coach_newsfeed_data_source.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/data/model/coach_profile_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/data/model/coach_program_search_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_profile_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_program_search_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/repositories/coach_newsfeed_repository.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';

class CoachNewsfeedRepositoryImpl extends CoachNewsfeedRepository {
  final CoachNewsfeedDataSource dataSource;

  CoachNewsfeedRepositoryImpl({required this.dataSource});

  @override
  Future<Either<ErrorModel, CoachEntity>> getCoachProfileById(
      {required String coachId}) async {
    try {
      final response = await dataSource.getCoachProfileById(coachId: coachId);
      final data = response.data['data'];
      CoachEntity coachProfile = CoachModel.fromJson(data);

      return Right(coachProfile);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, List<CoachProfileEntity>>> getCoachProfiles({
    required CoachProfileFilterType filterType,
    String? namePrefix,
    String? categoryId,
    String? subcategoryId,
    int? offset,
    int? limit,
  }) async {
    try {
      final response = await dataSource.getCoachProfiles(
          filterType: filterType,
          namePrefix: namePrefix,
          categoryId: categoryId,
          subcategoryId: subcategoryId,
          offset: offset,
          limit: limit);
      final data = response.data['data'];
      List<CoachProfileEntity> coachProfiles = List<CoachProfileModel>.from(
          data.map((x) => CoachProfileModel.fromJson(x)));

      return Right(coachProfiles);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, List<CoachProgramSearchEntity>>> getPrograms({
    required CoachProfileFilterType filterType,
    String? namePrefix,
    int? offset,
    int? limit,
  }) async {
    try {
      final response = await dataSource.getPrograms(
          filterType: filterType,
          namePrefix: namePrefix,
          offset: offset,
          limit: limit);

      final data = response.data['data'];
      List<CoachProgramSearchEntity> programs =
          List<CoachProgramSearchEntity>.from(
              data.map((x) => CoachProgramSearchModel.fromJson(x)));

      return Right(programs);
    } catch (e, stackTrace) {
      Log.debug(e.toString());
      Log.debug(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }
}
