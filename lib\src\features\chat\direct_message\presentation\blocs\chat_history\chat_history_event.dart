part of 'chat_history_bloc.dart';

class ChatHistoryEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class GetChatHistoryEvent extends ChatHistoryEvent {
  final String userId;
  final int? limit;
  final int? offset;

  GetChatHistoryEvent({required this.userId, this.limit, this.offset});
}

class UpdateChatHistoryEvent extends ChatHistoryEvent {
  final List<ChatHistoryEntity> chatHistoryEntities;

  UpdateChatHistoryEvent({required this.chatHistoryEntities});

  @override
  List<Object?> get props => chatHistoryEntities;
}

class AddChatHistoryEvent extends ChatHistoryEvent {
  final ChatHistoryEntity chatHistoryEntity;

  AddChatHistoryEvent({required this.chatHistoryEntity});

  @override
  List<Object?> get props => [chatHistoryEntity];
}

class RemoveFromChatHistoryListEvent extends ChatHistoryEvent {
  final String messageId;

  RemoveFromChatHistoryListEvent({required this.messageId});

  @override
  List<Object?> get props => [messageId];
}

class Get<PERSON>ore<PERSON>hatHistoryEvent extends ChatH<PERSON><PERSON><PERSON>vent {
  final String userId;
  final int? limit;
  final int? offset;

  GetMoreChatHistoryEvent({required this.userId, this.limit, this.offset});
}
