import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/bloc/coach_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/widget/coach_program_enrollments_list_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';

class CoachSingleProgramPaymentHistory extends StatefulWidget {
  const CoachSingleProgramPaymentHistory({
    super.key,
    required this.coachId,
    required this.programId,
  });
  final String coachId;
  final String programId;

  @override
  State<CoachSingleProgramPaymentHistory> createState() =>
      _CoachSingleProgramPaymentHistoryState();
}

class _CoachSingleProgramPaymentHistoryState
    extends State<CoachSingleProgramPaymentHistory> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(), //Text('Enrollment History'),

      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.symmetric(
                vertical: Values.v20, horizontal: Values.v20),
            child: Column(
              children: [
                // _buildProgramTotalEnrollmentSection(),
                _buildProgramEnrollmentListSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _buildProgramEnrollmentListSection() {
    // return CoachProgramEnrollmentsListWidget(
    //   coachEntiry: widget.coachEntity,
    // );
    return CoachSingleProgramPaymentListWidget(
      coachId: widget.coachId,
      programId: widget.programId,
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        'Payment History',
        style: TextStyle(
          color: UIColors.primaryGreen950,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: true,
      elevation: 0,
    );
  }
}

class CoachSingleProgramPaymentListWidget extends StatefulWidget {
  const CoachSingleProgramPaymentListWidget({
    super.key,
    required this.coachId,
    required this.programId,
  });
  final String coachId;
  final String programId;

  @override
  State<CoachSingleProgramPaymentListWidget> createState() =>
      _CoachSingleProgramPaymentListWidgetState();
}

class _CoachSingleProgramPaymentListWidgetState
    extends State<CoachSingleProgramPaymentListWidget> {
  List<CoachProgramEnrollmentEntity> _enrolledPrograms = [];
  List<CoachProgramEnrollmentEntity> testPrograms = List.generate(5, (index) {
    return testProgramEnrollmentEntity;
  });

  @override
  void initState() {
    //TODO for testing
    // _enrolledPrograms = testPrograms;

    BlocProvider.of<CoachDashboardBloc>(context).add(
        GetCoachProgramPaymentHistoryEvent(
            programId: widget.programId, coachId: widget.coachId));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachDashboardBloc, CoachDashboardState>(
      listener: (context, state) {
        if (state is GetCoachProgramPaymentHistorySuccess) {
          Log.debug('get user enrolled program success');
          setState(() {
            _enrolledPrograms = state.userSubscriptionHistoryEntity;
          });
        }

        if (state is GetCoachProgramPaymentHistoryFail) {
          Log.debug('get user enrolled program fail');
        }
      },
      child: (_enrolledPrograms.isEmpty)
          ? const Center(
              child: Text('No Payment History'),
            )
          : Container(
              margin: EdgeInsets.only(top: Values.v20),
              child: Column(
                children: [
                  _buildStudentProgramsEnrollmentList(),
                ],
              ),
            ),
    );
  }

  _buildProgramSectionHeader(
    String icon,
    String title, {
    required bool showCount,
    Color? iconColor,
    Color? textColor,
  }) {
    return Row(
      children: [
        Container(
          margin: const EdgeInsets.only(right: Values.v4),
          decoration: BoxDecoration(
            color: (iconColor != null) ? iconColor : UIColors.primaryGreen100,
            borderRadius: BorderRadius.circular(Values.v36),
          ),
          child: Center(
            child: SvgPicture.asset(
              icon,
              height: Values.v36,
              width: Values.v36,
            ),
          ),
        ),
        Text(
          '$title ${(showCount && _enrolledPrograms.isNotEmpty) ? '(${_enrolledPrograms.length})' : ''}',
          style: AppTypography.poppinsSemiBold20(
              color:
                  (textColor != null) ? textColor : UIColors.primaryGreen950),
        ),
      ],
    );
  }

  _buildStudentProgramsEnrollmentList() {
    return ListView.separated(
      shrinkWrap: true,
      itemBuilder: (context, index) {
        return CoachProgrammSinglePaymentInfoWidget(
            programEnrollmentEntity: _enrolledPrograms[index]);
      },
      separatorBuilder: (context, index) {
        return Divider(color: AppColors.greyscale100);
      },
      itemCount: _enrolledPrograms.length,
    );
  }
}

class CoachProgrammSinglePaymentInfoWidget extends StatefulWidget {
  const CoachProgrammSinglePaymentInfoWidget(
      {super.key, required this.programEnrollmentEntity});

  final CoachProgramEnrollmentEntity programEnrollmentEntity;

  @override
  State<CoachProgrammSinglePaymentInfoWidget> createState() =>
      _CoachProgrammSinglePaymentInfoWidgetState();
}

class _CoachProgrammSinglePaymentInfoWidgetState
    extends State<CoachProgrammSinglePaymentInfoWidget> {
  @override
  Widget build(BuildContext context) {
    return _buildEnrollmentInfoCard();
  }

  _buildEnrollmentInfoCard() {
    return GestureDetector(
      onTap: () {
        Log.debug('enrollment item pressed');
        // Navigator.of(context).pushNamed(Routes.coachProgramEnrolledDetailsPage,
        //     arguments: widget.programEnrollmentEntity.subscriptionId);
      },
      child: Container(
        // margin: EdgeInsets.only(top: Values.v20),
        padding: EdgeInsets.symmetric(
          vertical: Values.v10,
        ),
        // decoration: BoxDecoration(
        //     color: AppColors.greyscale10,
        //     border: Border.all(color: AppColors.greyscale100),
        //     borderRadius: BorderRadius.circular(Values.v10)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // _buildEnrolledUserInfo(),
            _buildPaymentUserInfoTitle(),
            // Divider(color: AppColors.greyscale100,),
            SizedBox(
              height: Values.v10,
            ),
            _buildStudentEnrollmentInfoSection(
                enrollmentDate: DateTime.now(), // TODO need to replace
                payment: widget.programEnrollmentEntity.userTotalPaid ?? 0),
          ],
        ),
      ),
    );
  }

  _buildProgramTitle({required String title}) {
    return Expanded(
      child: Text(
        title,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
      ),
    );
  }

  _buildStudentEnrollmentInfoSection(
      {required DateTime enrollmentDate, required int payment}) {
    return Row(
      children: [
        // Text('Total Enrollment: ${programEntity.totalSubscription}'),

        // Text('Total Income: BDT ${350000}'),
        _buildProgramInfoText(
          '',
          'BDT ${payment}',
          AppTypography.poppinsMedium12(color: AppColors.greyscale400),
          AppTypography.poppinsSemiBold16(color: AppColors.greyscale400),
        ),
        Spacer(),
        _buildProgramInfoText(
          'Paid On: ',
          '${DateFormat.yMMMMd().format(enrollmentDate)}',
          AppTypography.poppinsMedium12(color: AppColors.greyscale400),
          AppTypography.poppinsMedium14(color: UIColors.primaryGreen950),
        ),
      ],
    );
  }

  _buildProgramInfoText(String infoName, String? infoValue,
      TextStyle infoNameStyle, TextStyle infoValueStyle) {
    if (infoValue == null || infoValue == '') return SizedBox.shrink();

    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          '${(infoName.length != 0) ? '${infoName}:' : ''}',
          style: infoNameStyle,
        ),
        SizedBox(
          width: Values.v5,
        ),
        Flexible(
          child: Wrap(
            children: [
              Text(
                infoValue,
                style: infoValueStyle,
                overflow: TextOverflow.clip,
              ),
            ],
          ),
        )
      ],
    );
  }

  _buildPaymentUserInfoTitle() {
    return Row(
      children: [_buildEnrolledUserInfo()],
    );
  }

  _buildEnrolledUserInfo() {
    String userImageUrl = Assets.spotMeNoImage;
    if (widget.programEnrollmentEntity.coachImages.isNotEmpty) {
      if (Uri.parse(widget.programEnrollmentEntity.coachImages.first.url)
          .hasAbsolutePath) {
        userImageUrl = widget.programEnrollmentEntity.coachImages.first.url;
      }
    }

    return Container(
      child: Row(
        children: [
          _buildStudentProfileImage(userImageUrl),
          const SizedBox(
            width: 5,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.programEnrollmentEntity.userName ?? 'no-name',
                style: AppTypography.poppinsSemiBold14(
                    color: UIColors.primaryGreen950),
              ),
            ],
          )
        ],
      ),
    );
  }

  _buildStudentProfileImage(String? imagePath) {
    if (imagePath == null ||
        imagePath == Assets.spotMeNoImage ||
        imagePath == '') {
      // return ImageContainer.circularImage(
      //   image: Assets.spotMeNoImage,
      //   radius: Values.v12,
      //   showBorder: false,
      // );

      return ClipRRect(
        borderRadius: BorderRadius.circular(Values.v24),
        child: SizedBox(
          height: Values.v24,
          width: Values.v24,
          child: Image.asset(
            Assets.spotMeNoImage,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    return ImageContainer.circularImage(
      image: imagePath,
      radius: Values.v12,
      showBorder: false,
    );
  }
}
