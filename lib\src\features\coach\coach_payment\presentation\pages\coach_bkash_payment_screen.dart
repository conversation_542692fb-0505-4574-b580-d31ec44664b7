import 'dart:io';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class CoachBkashPaymentScreen extends StatefulWidget {
  final String? url;

  const CoachBkashPaymentScreen({super.key, required this.url});

  @override
  State<CoachBkashPaymentScreen> createState() =>
      _CoachBkashPaymentScreenState();
}

class _CoachBkashPaymentScreenState extends State<CoachBkashPaymentScreen>
    with WidgetsBindingObserver {
  late final WebViewController _controller;
  late String serverURL;

  bool _showWebView = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    serverURL = API.base;

    ///Test
    // serverURL = 'https://api-dev.fitsomnia.com/api';

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(AppColors.greyscale200)
      ..enableZoom(false)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            print("WebView Progress: $progress");
          },
          onPageStarted: (String url) {
            print("WebView Page Started: $url");
          },
          onPageFinished: (String url) {
            print("WebView Page Finished: $url");
            if (url.startsWith(serverURL)) {
              Map<String, String> pathParameters = parseUri(url: url);

              if (pathParameters['status'] == 'success') {
                Navigator.pop(context, 'completed');
              } else {
                Navigator.pop(context, 'failed');
              }
            }
          },
          onWebResourceError: (WebResourceError error) {
            print("WebView Page Resource Error: $error");
          },
          onNavigationRequest: (NavigationRequest request) {
            Log.debug('WebView navigation request: ${request.url}');
            // if (request.url.startsWith(serverURL)) {

            //   Map<String, String> pathParameters = parseUri(url: request.url);

            //   if(pathParameters['status'] == 'success') {
            //     Navigator.pop(context, 'completed');
            //   } else {
            //     Navigator.pop(context, 'failed');
            //   }

            //   // return NavigationDecision.prevent;
            // }

            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url!));
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    Log.debug('AppLifecycleState: ' + state.name);

    if (state == AppLifecycleState.resumed) {
      // _controller.reload();
      // _controller.runJavaScript('document.activeElement.blur();'); // remove focus first
      // _controller.runJavaScript('document.activeElement.focus();'); // re-focus

      // Rebuild WebView to reattach platform view
      setState(() {
        _showWebView = !_showWebView;
      });

      // Re-insert WebView after a short delay
      // Future.delayed(const Duration(milliseconds: 10), () {
      //   if (mounted) {
      //     setState(() {
      //       _showWebView = true;
      //     });
      //   }
      // });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.greyscale200,
      body: SafeArea(
        child: WebViewWidget(
          key: UniqueKey(),
          controller: _controller,
        ),
      ),
    );
  }

  Map<String, String> parseUri({required String url}) {
    var uri = Uri.parse(url);
    Map<String, String> pathParameters = {};
    uri.queryParameters.forEach((key, value) {
      pathParameters[key] = value;
      Log.debug('$key : $value');
    });

    return pathParameters;
  }
}
