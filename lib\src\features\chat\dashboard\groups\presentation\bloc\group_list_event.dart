part of 'group_list_bloc.dart';

class GroupListEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class GetGroupListEvent extends GroupListEvent {
  final int? limit;

  GetGroupListEvent({this.limit});
}

class UpdateGroupListEvent extends GroupListEvent {
  final List<GroupListEntity> groupListEntities;

  UpdateGroupListEvent({required this.groupListEntities});

  @override
  List<Object?> get props => groupListEntities;
}

class AddGroupToListEvent extends GroupListEvent {
  final GroupListEntity groupListEntity;

  AddGroupToListEvent({required this.groupListEntity});

  @override
  List<Object?> get props => [groupListEntity];
}

class RemoveGroupFromListEvent extends GroupListEvent {
  final GroupListEntity groupListEntity;

  RemoveGroupFromListEvent({required this.groupListEntity});

  @override
  List<Object?> get props => [groupListEntity];
}
