import UIKit
import google_mobile_ads
import Flutter
import GoogleMaps

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GMSServices.provideAPIKey("AIzaSyBkm-VMVzvS_T4CCiCpBl7fweL8iC9XTek");
    GeneratedPluginRegistrant.register(with: self)
    
    FLTGoogleMobileAdsPlugin.registerNativeAdFactory(self, factoryId: "googleNativeAdFactory", nativeAdFactory: GoogleNativeAdFactory())
      
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
