import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/model/coach_program_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:flutter/material.dart';

class ProgramPreviewPriceListWidget extends StatefulWidget {
  const ProgramPreviewPriceListWidget({
    super.key,
    required this.programEntity,
    this.callback,
  });
  final CoachProgramEntity programEntity;
  final Function(CoachProgramPaymentInfo)? callback;

  @override
  State<ProgramPreviewPriceListWidget> createState() =>
      _ProgramPreviewPriceListWidgetState();
}

class _ProgramPreviewPriceListWidgetState
    extends State<ProgramPreviewPriceListWidget> {
  List<CoachProgramPaymentInfo> programPayments = [];
  late CoachProgramPaymentInfo selectedPaymentType;

  @override
  void initState() {
    super.initState();
    programPayments = widget.programEntity.payments;
    selectedPaymentType = programPayments.first;
    if(widget.callback != null) {
      widget.callback!(selectedPaymentType);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (programPayments.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.symmetric(vertical: Values.v20),
      height: Values.v180,
      color: AppColors.white,
      child: Row(
        children: programPayments.map<Widget>((paymentInfo) {
          return _buildProgramPriceItem(paymentInfo);
        }).toList(),
      ),
    );
  }

  _buildProgramPriceItem(CoachProgramPaymentInfo? paymentInfo) {
    if (paymentInfo == null) return const SizedBox.shrink();

    String paymentTerm =
        getPaymentTermEnumFromKey(paymentInfo.paymentTerm!).name();

    bool isSelected = (selectedPaymentType.paymentTerm == paymentInfo!.paymentTerm);

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            selectedPaymentType = paymentInfo;
            if(widget.callback != null) {
              widget.callback!(selectedPaymentType);
            }
          });
        },
        child: Container(
          margin: EdgeInsets.only(left: Values.v10),
          padding: EdgeInsets.all(Values.v10),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(Values.v12),
              border: Border.all(
                  width: Values.v3,
                  color: (isSelected)
                      ? AppColors.primaryGreen
                      : AppColors.greyscale400)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                'BDT ${paymentInfo.discountedPrice}',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: (isSelected)
                      ? AppColors.primaryGreen
                      : UIColors.primaryGreen950,
                  fontSize: 14,
                  fontFamily: FontConstants.poppinsFontFamily,
                  fontWeight: FontWeight.w700,
                ),
              ),
              if(paymentInfo.discountedPrice! < paymentInfo.actualPrice!) Text(
                'BDT ${paymentInfo.actualPrice}',
                style: TextStyle(
                  color: AppColors.greyscale400,
                  fontSize: 10,
                  fontFamily: FontConstants.poppinsFontFamily,
                  fontWeight: FontWeight.w500,
                  decoration: TextDecoration.lineThrough,
                ),
              ),
              if(paymentInfo.discountedPrice! <= paymentInfo.actualPrice!) SizedBox(height: Values.v16,),
              Divider(
                  color: AppColors.greyscale100,
                  height: Values.v20,
                  indent: Values.v10),
              Text(
                '$paymentTerm',
                style: TextStyle(
                  color: (isSelected)
                      ? AppColors.primaryGreen
                      : UIColors.primaryGreen950,
                  fontSize: 12,
                  fontFamily: FontConstants.poppinsFontFamily,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                'Payment',
                style: TextStyle(
                  color: (isSelected)
                      ? AppColors.primaryGreen
                      : UIColors.primaryGreen950,
                  fontSize: 12,
                  fontFamily: FontConstants.poppinsFontFamily,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
