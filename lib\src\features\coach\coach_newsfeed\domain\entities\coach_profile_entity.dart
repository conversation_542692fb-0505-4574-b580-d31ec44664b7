import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';

class CoachProfileEntity {
  final String coachId;
  final String userId;
  final String userName;
  final String legalName;
  final String expertise;
  List<CoachMediaFile> profilePictures;
  final String experienceInYears;
  final int subscriptionCount;
  final double currentRating;

  CoachProfileEntity({
    required this.coachId,
    required this.userId,
    required this.userName,
    required this.legalName,
    required this.expertise,
    required this.experienceInYears,
    required this.subscriptionCount,
    required this.currentRating,
    required this.profilePictures,

  });
}

// {
//   "data": [
//     {
//       "id": "string",
//       "userId": "string",
//       "userName": "string",
//       "legalName": "string",
//       "expertise": "string",
//       "experienceInYear": 0,
//       "subscriptionCount": 0,
//       "currentRating": 0
//     }
//   ]
// }


CoachProfileEntity testBestCoachProfileEntity = CoachProfileEntity(
    coachId: 'coachId',
    userId: 'userId',
    userName: '<PERSON><PERSON><PERSON><PERSON>',
    legalName: '<PERSON><PERSON><PERSON><PERSON>',
    expertise: 'expertise',
    experienceInYears: '3',
    subscriptionCount: 100,
    currentRating: 4,
    profilePictures: [
      CoachMediaFile(
        url:
            'https://dev-public-cdn.fitsomnia.com/original/coach/2025/1/28/6293a7cb-9788-4cc1-9cf3-12eb56ff687e/938925_1738071637314B9A405711.jpg',
        mediaType: 'image',
      )
    ]);