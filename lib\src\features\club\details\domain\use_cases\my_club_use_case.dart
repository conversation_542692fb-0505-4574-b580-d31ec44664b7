import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/entities/club_entity.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/repository/club_repository.dart';

class MyClubUseCase {
  const MyClubUseCase({required this.clubRepository});

  final ClubRepository clubRepository;

  Future<Either<ErrorResponseModel, ClubEntity>> call() async {
    return clubRepository.myClub();
  }
}
