import 'package:dio/dio.dart';
import 'package:dio/src/response.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/data_source/coach_profile_data_source.dart';

class CoachProfileDataSourceImpl extends CoachProfileDataSource{
  final RestClient restClient;

  CoachProfileDataSourceImpl({required this.restClient});
  
  @override
  Future<Response> createCoachProfile({required Map<String, dynamic> coachProfile}) async {
    final response = await restClient.post(APIType.PROTECTED, API.createCoachProfile, coachProfile);
    
    return response;

    // return Response(requestOptions: RequestOptions());
  }
  
  @override
  Future<Response> updateCoachProfile({required String coachId, required Map<String, dynamic> coachProfile}) async {
    final response = await restClient.patch(APIType.PROTECTED, API.updateCoachProfile(coachId), coachProfile);
    
    return response;
  }

}