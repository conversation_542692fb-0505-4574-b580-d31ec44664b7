import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppTypography {
  static TextStyle _getTextStyle(
    double fontSize,
    String fontFamily,
    FontWeight fontWeight,
    Color color,
  ) {
    return TextStyle(
      fontSize: fontSize,
      fontFamily: fontFamily,
      fontWeight: fontWeight,
      color: color,
    );
  }

  static TextStyle regular8({Color? color}) {
    return _getTextStyle(
      FontSize.s8,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle medium16({Color? color}) {
    return _getTextStyle(
      FontSize.s16,
      FontConstants.urbanistFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle bold32({Color? color}) {
    return _getTextStyle(
      FontSize.s32,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsRegular12({Color? color}) {
    return _getTextStyle(
      FontSize.s12,
      FontConstants.poppinsFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsRegular14({Color? color}) {
    return _getTextStyle(
      FontSize.s14,
      FontConstants.poppinsFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsRegular20({Color? color}) {
    return _getTextStyle(
      FontSize.s20,
      FontConstants.poppinsFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsRegular24({Color? color}) {
    return _getTextStyle(
      FontSize.s24,
      FontConstants.poppinsFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsRegular32({Color? color}) {
    return _getTextStyle(
      FontSize.s32,
      FontConstants.poppinsFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsLight14({Color? color}) {
    return _getTextStyle(
      FontSize.s14,
      FontConstants.poppinsFontFamily,
      FontWeightManager.light,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsLight16({Color? color}) {
    return _getTextStyle(
      FontSize.s16.sp,
      FontConstants.poppinsFontFamily,
      FontWeightManager.light,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsLight20({Color? color}) {
    return _getTextStyle(
      FontSize.s20,
      FontConstants.poppinsFontFamily,
      FontWeightManager.light,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsSemiBold18({Color? color}) {
    return _getTextStyle(
      FontSize.s18,
      FontConstants.poppinsFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsSemiBold14({Color? color}) {
    return _getTextStyle(
      FontSize.s14,
      FontConstants.poppinsFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsSemiBold16({Color? color}) {
    return _getTextStyle(
      FontSize.s16,
      FontConstants.poppinsFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsSemiBold20({Color? color}) {
    return _getTextStyle(
      FontSize.s20,
      FontConstants.poppinsFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsMedium12({Color? color}) {
    return _getTextStyle(
      FontSize.s12.sp,
      FontConstants.poppinsFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsMedium16({Color? color}) {
    return _getTextStyle(
      FontSize.s16,
      FontConstants.poppinsFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsMedium20({Color? color}) {
    return _getTextStyle(
      FontSize.s20,
      FontConstants.poppinsFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsMedium24({Color? color}) {
    return _getTextStyle(
      FontSize.s24,
      FontConstants.poppinsFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsRegular16({Color? color}) {
    return _getTextStyle(
      FontSize.s16,
      FontConstants.poppinsFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsRegular18({Color? color}) {
    return _getTextStyle(
      FontSize.s18,
      FontConstants.poppinsFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsSemiBold24({Color? color}) {
    return _getTextStyle(
      FontSize.s24,
      FontConstants.poppinsFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsSemiBold32({Color? color}) {
    return _getTextStyle(
      FontSize.s32,
      FontConstants.poppinsFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsSemiBold40({Color? color}) {
    return _getTextStyle(
      FontSize.s40,
      FontConstants.poppinsFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle sfRegular16({Color? color}) {
    return _getTextStyle(
      FontSize.s16,
      FontConstants.sfProDisplayFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle medium30({Color? color}) {
    return _getTextStyle(
      FontSize.s30,
      FontConstants.urbanistFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle medium24({Color? color}) {
    return _getTextStyle(
      FontSize.s24,
      FontConstants.urbanistFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle medium72({Color? color}) {
    return _getTextStyle(
      FontSize.s72,
      FontConstants.urbanistFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle medium42({Color? color}) {
    return _getTextStyle(
      FontSize.s42,
      FontConstants.urbanistFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsMedium14({Color? color}) {
    return _getTextStyle(
      FontSize.s14,
      FontConstants.poppinsFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle regular10({Color? color}) {
    return _getTextStyle(
      FontSize.s10,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle light12({Color? color}) {
    return _getTextStyle(
      FontSize.s12.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.light,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle regular12({Color? color}) {
    return _getTextStyle(
      FontSize.s12.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle semiBold12({Color? color}) {
    return _getTextStyle(
      FontSize.s12.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle bold12({Color? color}) {
    return _getTextStyle(
      FontSize.s12.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle light14({Color? color}) {
    return _getTextStyle(
      FontSize.s14.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.light,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle light18({Color? color}) {
    return _getTextStyle(
      FontSize.s18.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.light,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle regular14({Color? color}) {
    return _getTextStyle(
      FontSize.s14.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle medium14({Color? color}) {
    return _getTextStyle(
      FontSize.s14.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle semiBold14({Color? color}) {
    return _getTextStyle(
      FontSize.s14.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle bold14({Color? color}) {
    return _getTextStyle(
      FontSize.s14.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle regular16({Color? color}) {
    return _getTextStyle(
      FontSize.s16.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle semiBold16({Color? color}) {
    return _getTextStyle(
      FontSize.s16.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle bold16({Color? color}) {
    return _getTextStyle(
      FontSize.s16.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle regular18({Color? color}) {
    return _getTextStyle(
      FontSize.s18.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle semiBold18({Color? color}) {
    return _getTextStyle(
      FontSize.s18.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle semiBold20({Color? color}) {
    return _getTextStyle(
      FontSize.s20.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle semiBold22({Color? color}) {
    return _getTextStyle(
      FontSize.s22.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle bold18({Color? color}) {
    return _getTextStyle(
      FontSize.s18.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle bold20({Color? color}) {
    return _getTextStyle(
      FontSize.s20.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle bold22({Color? color}) {
    return _getTextStyle(
      FontSize.s22.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsBold14({Color? color}) {
    return _getTextStyle(
      FontSize.s14,
      FontConstants.poppinsFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsBold20({Color? color}) {
    return _getTextStyle(
      FontSize.s20,
      FontConstants.poppinsFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

   static TextStyle poppinsBold24({Color? color}) {
    return _getTextStyle(
      FontSize.s24,
      FontConstants.poppinsFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsBold32({Color? color}) {
    return _getTextStyle(
      FontSize.s32,
      FontConstants.poppinsFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsBold40({Color? color}) {
    return _getTextStyle(
      FontSize.s40,
      FontConstants.poppinsFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle regular24({Color? color}) {
    return _getTextStyle(
      FontSize.s24.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle regular20({Color? color}) {
    return _getTextStyle(
      FontSize.s20.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle semiBold24({Color? color}) {
    return _getTextStyle(
      FontSize.s24.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle bold24({Color? color}) {
    return _getTextStyle(
      FontSize.s24.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle bold50({Color? color}) {
    return _getTextStyle(
      FontSize.s50.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle bold36({Color? color}) {
    return _getTextStyle(
      FontSize.s36.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle bold40({Color? color}) {
    return _getTextStyle(
      FontSize.s40.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle calligraphicBold48({Color? color}) {
    return _getTextStyle(
      FontSize.s48.sp,
      FontConstants.caveatFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.fitBlack,
    );
  }

  /// notosensbengali font
  
  static TextStyle notoSensBengaliRegular12({Color? color}) {
    return _getTextStyle(
      FontSize.s12.sp,
      FontConstants.notoSansBengaliFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }
  static TextStyle notoSensBengaliSemiBold20({Color? color}) {
    return _getTextStyle(
      FontSize.s20.sp,
      FontConstants.notoSansBengaliFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle notoSensBengaliSemiBold16({Color? color}) {
    return _getTextStyle(
      FontSize.s16.sp,
      FontConstants.notoSansBengaliFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle notoSensBengaliSemiBold14({Color? color}) {
    return _getTextStyle(
      FontSize.s14.sp,
      FontConstants.notoSansBengaliFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.fitBlack,
    );
  }

  //-----------------------------

}


class AppTypographyGreen {
  static TextStyle _getTextStyle(
    double fontSize,
    String fontFamily,
    FontWeight fontWeight,
    Color color,
  ) {
    return TextStyle(
      fontSize: fontSize,
      fontFamily: fontFamily,
      fontWeight: fontWeight,
      color: color,
    );
  }

  static TextStyle regular8({Color? color}) {
    return _getTextStyle(
      FontSize.s8,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle medium16({Color? color}) {
    return _getTextStyle(
      FontSize.s16,
      FontConstants.urbanistFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle bold32({Color? color}) {
    return _getTextStyle(
      FontSize.s32,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsRegular14({Color? color}) {
    return _getTextStyle(
      FontSize.s14,
      FontConstants.poppinsFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsRegular20({Color? color}) {
    return _getTextStyle(
      FontSize.s20,
      FontConstants.poppinsFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsRegular24({Color? color}) {
    return _getTextStyle(
      FontSize.s24,
      FontConstants.poppinsFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsRegular32({Color? color}) {
    return _getTextStyle(
      FontSize.s32,
      FontConstants.poppinsFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsLight14({Color? color}) {
    return _getTextStyle(
      FontSize.s14,
      FontConstants.poppinsFontFamily,
      FontWeightManager.light,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsLight16({Color? color}) {
    return _getTextStyle(
      FontSize.s16,
      FontConstants.poppinsFontFamily,
      FontWeightManager.light,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsLight20({Color? color}) {
    return _getTextStyle(
      FontSize.s20,
      FontConstants.poppinsFontFamily,
      FontWeightManager.light,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsSemiBold18({Color? color}) {
    return _getTextStyle(
      FontSize.s18,
      FontConstants.poppinsFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsSemiBold14({Color? color}) {
    return _getTextStyle(
      FontSize.s14,
      FontConstants.poppinsFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsSemiBold16({Color? color}) {
    return _getTextStyle(
      FontSize.s16,
      FontConstants.poppinsFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsSemiBold20({Color? color}) {
    return _getTextStyle(
      FontSize.s20,
      FontConstants.poppinsFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsMedium16({Color? color}) {
    return _getTextStyle(
      FontSize.s16,
      FontConstants.poppinsFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsMedium20({Color? color}) {
    return _getTextStyle(
      FontSize.s20,
      FontConstants.poppinsFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsRegular16({Color? color}) {
    return _getTextStyle(
      FontSize.s16,
      FontConstants.poppinsFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsRegular18({Color? color}) {
    return _getTextStyle(
      FontSize.s18,
      FontConstants.poppinsFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.fitBlack,
    );
  }

  static TextStyle poppinsSemiBold24({Color? color}) {
    return _getTextStyle(
      FontSize.s24,
      FontConstants.poppinsFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle sfRegular16({Color? color}) {
    return _getTextStyle(
      FontSize.s16,
      FontConstants.sfProDisplayFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle medium30({Color? color}) {
    return _getTextStyle(
      FontSize.s30,
      FontConstants.urbanistFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle medium24({Color? color}) {
    return _getTextStyle(
      FontSize.s24,
      FontConstants.urbanistFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle medium72({Color? color}) {
    return _getTextStyle(
      FontSize.s72,
      FontConstants.urbanistFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle medium42({Color? color}) {
    return _getTextStyle(
      FontSize.s42,
      FontConstants.urbanistFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle poppinsMedium14({Color? color}) {
    return _getTextStyle(
      FontSize.s14,
      FontConstants.poppinsFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle regular10({Color? color}) {
    return _getTextStyle(
      FontSize.s10,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle light12({Color? color}) {
    return _getTextStyle(
      FontSize.s12.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.light,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle regular12({Color? color}) {
    return _getTextStyle(
      FontSize.s12.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle semiBold12({Color? color}) {
    return _getTextStyle(
      FontSize.s12.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle bold12({Color? color}) {
    return _getTextStyle(
      FontSize.s12.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle light14({Color? color}) {
    return _getTextStyle(
      FontSize.s14.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.light,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle light18({Color? color}) {
    return _getTextStyle(
      FontSize.s18.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.light,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle regular14({Color? color}) {
    return _getTextStyle(
      FontSize.s14.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle medium14({Color? color}) {
    return _getTextStyle(
      FontSize.s14.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.medium,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle semiBold14({Color? color}) {
    return _getTextStyle(
      FontSize.s14.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle bold14({Color? color}) {
    return _getTextStyle(
      FontSize.s14.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle regular16({Color? color}) {
    return _getTextStyle(
      FontSize.s16.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle semiBold16({Color? color}) {
    return _getTextStyle(
      FontSize.s16.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle bold16({Color? color}) {
    return _getTextStyle(
      FontSize.s16.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle regular18({Color? color}) {
    return _getTextStyle(
      FontSize.s18.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle semiBold18({Color? color}) {
    return _getTextStyle(
      FontSize.s18.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle semiBold20({Color? color}) {
    return _getTextStyle(
      FontSize.s20.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle semiBold22({Color? color}) {
    return _getTextStyle(
      FontSize.s22.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle bold18({Color? color}) {
    return _getTextStyle(
      FontSize.s18.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle bold20({Color? color}) {
    return _getTextStyle(
      FontSize.s20.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle bold22({Color? color}) {
    return _getTextStyle(
      FontSize.s22.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle regular24({Color? color}) {
    return _getTextStyle(
      FontSize.s24.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle regular20({Color? color}) {
    return _getTextStyle(
      FontSize.s20.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.regular,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle semiBold24({Color? color}) {
    return _getTextStyle(
      FontSize.s24.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.semiBold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle bold24({Color? color}) {
    return _getTextStyle(
      FontSize.s24.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle bold50({Color? color}) {
    return _getTextStyle(
      FontSize.s50.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle bold36({Color? color}) {
    return _getTextStyle(
      FontSize.s36.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle bold40({Color? color}) {
    return _getTextStyle(
      FontSize.s40.sp,
      FontConstants.urbanistFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.primaryGreen900,
    );
  }

  static TextStyle calligraphicBold48({Color? color}) {
    return _getTextStyle(
      FontSize.s48.sp,
      FontConstants.caveatFontFamily,
      FontWeightManager.bold,
      color ?? UIColors.primaryGreen900,
    );
  }
}
