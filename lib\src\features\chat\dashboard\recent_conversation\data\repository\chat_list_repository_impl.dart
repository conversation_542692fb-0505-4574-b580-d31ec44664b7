import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/data/data_source/recent_conversation_data_source.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/data/model/chat_list_model.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/domain/entity/chat_list_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/domain/repository/chat_list_repository.dart';

class ChatListRepositoryImpl implements ChatListRepository {
  const ChatListRepositoryImpl({required this.recentConversationDataSource});

  final RecentConversationDataSource recentConversationDataSource;

  @override
  Future<Either<String, List<ChatListEntity>>> getRecentChat({
    required int? limit,
    required int? offset,
  }) async {
    try {
      final Response response =
          await recentConversationDataSource.getRecentConversation(
        limit: limit,
        offset: offset,
      );
      final data = response.data['data'];

      List<ChatListEntity> models = data
          .map<ChatListEntity>(
              (chats) => ChatListModel.fromJson(chats).toEntity())
          .toList();

      models.removeWhere((element) => element.lastMessageInfo == null);

      return Right(models);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left(e.toString());
    }
  }
}
