import 'package:fitsomnia_app/src/features/reward/referrals/presentation/widget/referral_card_widget.dart';
import 'package:flutter/material.dart';

class ReferralDetailsPage extends StatefulWidget {
  const ReferralDetailsPage({
    super.key,
    required this.referralId,
  });
  final String referralId;

  @override
  State<ReferralDetailsPage> createState() => _ReferralDetailsPageState();
}

class _ReferralDetailsPageState extends State<ReferralDetailsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
          child: SingleChildScrollView(
        child: Text('Referral details'),
      )),
    );
  }

}
