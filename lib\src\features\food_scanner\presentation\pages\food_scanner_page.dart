import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/entities/food_info_entity.dart';
import 'package:fitsomnia_app/src/features/food_scanner/presentation/bloc/bloc/food_scan_bloc.dart';
import 'package:flutter/material.dart';

import 'dart:io';

import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/camera_v2/ui/camera_v2_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_picker/image_picker.dart';
import 'package:screenshot/screenshot.dart';
import 'package:path_provider/path_provider.dart' as path_provider;

class FoodScannerPage extends StatefulWidget {
  const FoodScannerPage({super.key});

  @override
  State<FoodScannerPage> createState() => _FoodScannerPageState();
}

class _FoodScannerPageState extends State<FoodScannerPage> {
  String? _imagePath;
  String? _compressedImagePath;
  FoodInfoEntity? _foodInfo;

  final ImagePicker _picker = ImagePicker();
  File? _image;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Food Scanner'),
      ),
      body: BlocListener<FoodScanBloc, FoodScanState>(
        listener: (context, state) {
          if(state is FoodScanSuccess) {
            setState(() {
              _foodInfo = (state as FoodScanSuccess).foodInfo;
            });
          }

          if(state is FoodScanFeedbackSuccess) {
            setState(() {
              _foodInfo = (state as FoodScanFeedbackSuccess).foodInfo;
            });
          }
        },
        child: Column(
          children: [
            _buildImagePreview(),
            _buildFoodInfoPreview(),
            _buildButtons(),
          ],
        ),
      ),
    );
  }

  _buildImagePreview() {
    if (_imagePath == null) {
      return Expanded(
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey, width: 3.0),
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Center(
            child: Text('No image selected'),
          ),
        ),
      );
    }

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Row(
          children: [
            Expanded(
              child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(
                      color: Colors.grey,
                    ),
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Image.file(
                    File(_imagePath!),
                    fit: BoxFit.cover,
                  )),
              // child: ImageContainer.rectangularImage(image: _imagePath, width: double.infinity, height: double.infinity)),
            ),
          ],
        ),
      ),
    );
  }

  _buildFoodInfoPreview() {
    if (_foodInfo == null) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: Align(
            alignment: Alignment.center,
            child: Container(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text('Name: ${_foodInfo!.name}',
                            style: TextStyle(fontSize: 16)),
                      ],
                    ),
                    Wrap(
                      children: [
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Text('Calories: ${_foodInfo!.calories}',
                                style: TextStyle(fontSize: 16)),
                          ),
                        ),
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Text('Protein: ${_foodInfo!.protein}',
                                style: TextStyle(fontSize: 16)),
                          ),
                        ),
                        Card(
                            child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Text('Carbs: ${_foodInfo!.carbs}',
                              style: TextStyle(fontSize: 16)),
                        )),
                        Card(
                            child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Text('Fats: ${_foodInfo!.fats}',
                              style: TextStyle(fontSize: 16)),
                        )),
                      ],
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  _buildButtons() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          IconButton(
            icon: const Icon(
              Icons.photo_library,
              // color: Colors.white,
              size: 32,
            ),
            onPressed: _pickFromGallery,
          ),
          IconButton(
            icon: const Icon(
              Icons.camera_alt,
              // color: Colors.white,
              size: 32,
            ),
            onPressed: _takePicture,
          ),
          // if(_image != null)
          //   IconButton(
          //     icon: Text(
          //       'Compress',
          //       style: TextStyle(color: Colors.white, fontSize: 16),
          //     ),

          //     onPressed: () {
          //       _compressImage(_image!);
          //     },
          //   ),
        ],
      ),
    );
  }

  Future<void> _takePicture() async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        },
      );
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        preferredCameraDevice: CameraDevice.rear,
        imageQuality: 80, // Adjust quality (0-100)
        maxWidth: 1920, // Add max dimensions to prevent huge images
        maxHeight: 1080,
      );

      // Hide loading indicator
      Navigator.of(context).pop();

      if (photo != null) {
        Log.debug('Image captured: ${photo.path}');
        _compressedImagePath = await _compressImage(File(photo.path));

        BlocProvider.of<FoodScanBloc>(context)
            .add(SendFoodScanImageEvent(imagePath: _compressedImagePath!));

        // setState(() {
        //   _image = File(photo.path);
        //   _imagePath = photo.path;
        //   _foodInfo = FoodInfo(
        //     name: 'Apple',
        //     calories: '52',
        //     protein: '0.3g',
        //     carbs: '14g',
        //     fats: '0.2g',
        //   );
        // });

        setState(() {
          _image = File(photo.path);
          _imagePath = photo.path;
        });
      }
    } catch (e) {
      Navigator.of(context).pop();
      debugPrint('Error taking picture: $e');

      // Show error to user
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to capture image. Please try again.'),
        ),
      );
    }
  }

  Future<void> _pickFromGallery() async {
    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (photo != null) {
        _compressedImagePath = await _compressImage(File(photo.path));
        Log.debug('Image selected from gallery: ${photo.path}');
        BlocProvider.of<FoodScanBloc>(context)
            .add(SendFoodScanImageEvent(imagePath: _compressedImagePath!));

        // setState(() {
        //   _image = File(photo.path);
        //   _imagePath = photo.path;
        //   _foodInfo = FoodInfo(
        //     name: 'Apple',
        //     calories: '52',
        //     protein: '0.3g',
        //     carbs: '14g',
        //     fats: '0.2g',
        //   );
        // });

        setState(() {
          _image = File(photo.path);
          _imagePath = photo.path;
        });
      }
    } catch (e) {
      debugPrint('Error picking image: $e');
    }
  }

  Future<String?> _compressImage(File file) async {
    try {
      final dir = await path_provider.getTemporaryDirectory();
      final targetPath =
          "${dir.absolute.path}/compressed_${DateTime.now().millisecondsSinceEpoch}.jpg";

      final result = await FlutterImageCompress.compressWithList(
        await file.readAsBytes(),
        minWidth: 1024,
        minHeight: 1024,
        quality: 50,
        rotate: 0,
      );

      // Write the compressed data to a file
      final compressedFile = File(targetPath);
      await compressedFile.writeAsBytes(result);

      setState(() {
        _image = compressedFile;
        // _compressedImagePath = compressedFile.path;
      });

      Log.debug('Compressed image saved to: ${compressedFile.path}');
      Log.debug('Original size: ${file.lengthSync() / (1024 * 1024)} MB');
      Log.debug(
          'Compressed size: ${compressedFile.lengthSync() / (1024 * 1024)} MB');

      return compressedFile.path;
    } catch (e) {
      Log.error('Error compressing image: $e');
    }

    return null;
  }
}

class FoodInfo {
  final String name;
  final String calories;
  final String protein;
  final String carbs;
  final String fats;

  FoodInfo(
      {required this.name,
      required this.calories,
      required this.protein,
      required this.carbs,
      required this.fats});
}
