import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/entities/club_entity.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/repository/club_repository.dart';

class NearbyClubUseCase {
  const NearbyClubUseCase(this.clubRepository);

  final ClubRepository clubRepository;

  Future<Either<ErrorResponseModel, List<ClubEntity>>> call({
    double? lat,
    double? long,
    int? offset,
  }) async {
    return clubRepository.findNearbyClubs(long: long, lat: lat, offset: offset);
  }
}
