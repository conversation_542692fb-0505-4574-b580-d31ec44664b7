import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_profile_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/page/coach_profile_details_page.dart';
import 'package:fitsomnia_app/src/features/coach/root/domain/entities/coach_program_category_entity.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class BestCoachViewWidget extends StatefulWidget {
  const BestCoachViewWidget({super.key, required this.coachProfileEntity});
  final CoachProfileEntity coachProfileEntity;

  @override
  State<BestCoachViewWidget> createState() => _BestCoachViewWidgetState();
}

class _BestCoachViewWidgetState extends State<BestCoachViewWidget> {
  late CoachProfileEntity
      coachEntity; // = testCoachProfileEntity; //TODO test data

  @override
  void initState() {
    super.initState();
    coachEntity = widget.coachProfileEntity;

    // BlocProvider.of<CoachNewsfeedBloc>(context).add(GetSingleCoachProfileEvent(coachId: widget.coachProfileEntity.coachId));
  }

  // listener: (context, state) {
  //       if (state is GetSingleCoachProfileSuccess) {
  //         Log.debug('get coach profile success');

  //         setState(() {
  //           _coachEntity = state.coachProfile;
  //         });
  //       }

  //       if (state is GetSingleCoachProfileFail) {
  //         Log.debug('get coach profile fail');
  //       }
  //     },

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Log.debug('coach profile select');
        Navigator.of(context).pushNamed(Routes.coachProfileDetailsPage,
            arguments: [null, coachEntity.coachId, false]);
      },
      child: Container(
        height: Values.v200.h,
        width: Values.v163.w,
        color: AppColors.transparent,
        child: Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(Values.v10),
              color: AppColors.white
              // color: UIColors.black.withOpacity(0.30),
              ),
          padding: EdgeInsets.only(left: 5, right: 5),
          child: _buildCoachPreviewWidget(),
        ),
      ),
    );
  }

  _buildCoachPreviewWidget() {
    return Container(
      color: AppColors.transparent,
      child: Stack(
        alignment: Alignment.center,
        children: [
          _buildProfileImage(),
          _buildProfileInformation(),
        ],
      ),
    );
  }

  _buildProfileImage() {
    // return Container(
    //   child: Image.network(
    //     widget.profileImage,
    //     fit: BoxFit.fitHeight,
    //     // width: double.infinity,
    //     // height: double.infinity,
    //   ),
    // );

    if (coachEntity.profilePictures.isEmpty) {
      return Image.asset(
        Assets.spotMeNoImage,
        height: double.infinity,
        width: double.infinity,
      );
    }

    return ImageContainer.rectangularImage(
      cornerRadius: Values.v10,
      image: coachEntity.profilePictures.first.url,
      width: double.infinity,
      height: double.infinity,
      fit: BoxFit.fill,
      useOriginal: false,
      useSmall: true,
      hideLoadingIndicator: true,
      errorWidget: Container(
        height: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Values.v10),
          image: DecorationImage(
            image: const Image(
              image: AssetImage(
                Assets.spotMeNoImage,
              ),
            ).image,
            fit: BoxFit.fill,
          ),
        ),
      ),
    );
  }

  _buildProfileInformation() {
    return Container(
      height: double.infinity,
      width: double.infinity,
      // padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(Values.v18),
        gradient: _buildLinearGradientWhiteToBlack(),
        // color: UIColors.black.withOpacity(0.30),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCoachSubscriberSection(),
          Spacer(),
          _buildCoachNameInfoSection(),
        ],
      ),
    );
  }

  _buildCoachNameInfoSection() {
    return Padding(
      padding: EdgeInsets.only(left: Values.v12.w, bottom: Values.v16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            coachEntity.legalName,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: AppTypography.poppinsSemiBold16(color: AppColors.white),
          ),
          Text(
            '${widget.coachProfileEntity.expertise}',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: AppTypography.poppinsRegular12(color: AppColors.white),
          )
        ],
      ),
    );
  }

  LinearGradient _buildLinearGradientWhiteToBlack() {
    return LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        AppColors.transparent,
        UIColors.black.withOpacity(0.3),
      ],
    );
  }

  _buildCoachSubscriberSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          margin: EdgeInsets.symmetric(vertical: Values.v8.h),
          padding: EdgeInsets.symmetric(horizontal: Values.v8.w,vertical: Values.v4),
          decoration: BoxDecoration(
            color: AppColors.black.withOpacity(0.5),
            borderRadius: BorderRadius.circular(Values.v15),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon(
              //   Icons.star,
              //   color: UIColors.purple500,
              //   size: Values.v16,
              // ),
              SvgPicture.asset(Assets.coachSubscribeIcon,),
              SizedBox(width: 3,),
              Text(
                '${widget.coachProfileEntity.subscriptionCount} Subscriber',
                // '${30} Subscriber',
                style: AppTypography.poppinsRegular12(color: AppColors.white),
              )
            ],
          ),
        )
      ],
    );
  }
}
