import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/entities/live_member_model.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/repository/club_repository.dart';

class LiveMemberUseCase {
  LiveMemberUseCase({required this.repository});

  final ClubRepository repository;

  Future<Either<ErrorModel, List<LiveMemberModel>>> liveMembers(
    Map<String, dynamic> query,
  ) async {
    return repository.liveMembersNearMe(query);
  }
}
