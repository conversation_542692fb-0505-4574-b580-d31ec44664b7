import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/entities/live_member_model.dart';

enum LiveMembersStatus {
  initial,
  loading,
  success,
  error,
}

class LiveMembersState extends Equatable {
  const LiveMembersState({
    this.status = LiveMembersStatus.initial,
    this.data = const [],
    this.hasReachedMax = false,
    this.error,
  });

  final LiveMembersStatus status;
  final List<LiveMemberModel> data;
  final bool hasReachedMax;
  final String? error;

  LiveMembersState copyWith({
    LiveMembersStatus? status,
    List<LiveMemberModel>? data,
    bool? hasReachedMax,
    String? error,
  }) {
    return LiveMembersState(
      status: status ?? this.status,
      data: data ?? this.data,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      error: error ?? this.error,
    );
  }

  @override
  String toString() =>
      'LiveMembersState(stats: $status, data: $data, hasReachedMax: $hasReachedMax, error: $error)';

  @override
  List<Object?> get props => [status, data, hasReachedMax, error];
}
