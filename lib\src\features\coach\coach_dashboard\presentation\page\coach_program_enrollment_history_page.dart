import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_income_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/bloc/coach_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/widget/coach_program_enrollments_list_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

class CoachProgramsEnrollmentHistoryPage extends StatefulWidget {
  const CoachProgramsEnrollmentHistoryPage(
      {super.key, required this.coachEntity});
  final CoachEntity coachEntity;

  @override
  State<CoachProgramsEnrollmentHistoryPage> createState() =>
      _CoachProgramsEnrollmentHistoryPageState();
}

class _CoachProgramsEnrollmentHistoryPageState
    extends State<CoachProgramsEnrollmentHistoryPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(), //Text('Enrollment History'),

      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.symmetric(
                vertical: Values.v20, horizontal: Values.v20),
            child: Column(
              children: [
                _buildProgramTotalEnrollmentSection(),
                _buildProgramEnrollmentListSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _buildProgramTotalEnrollmentSection() {
    return CoachProgramEnrollmentOverviewWidget(
      coachId: widget.coachEntity.coachId!,
    );
  }

  _buildProgramEnrollmentListSection() {
    return CoachProgramEnrollmentsListWidget(
      coachId: widget.coachEntity.coachId!,
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        'Enrollment History',
        style: TextStyle(
          color: UIColors.primaryGreen950,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: true,
      elevation: 0,
    );
  }
}

class CoachProgramEnrollmentOverviewWidget extends StatefulWidget {
  const CoachProgramEnrollmentOverviewWidget({
    super.key,
    required this.coachId,
  });
  final String coachId;

  @override
  State<CoachProgramEnrollmentOverviewWidget> createState() =>
      _CoachProgramEnrollmentOverviewWidgetState();
}

class _CoachProgramEnrollmentOverviewWidgetState
    extends State<CoachProgramEnrollmentOverviewWidget> {
  CoachIncomeEntity? coachIncomeEntity;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _isLoading = true;
    BlocProvider.of<CoachDashboardBloc>(context)
        .add(GetCoachIncomeEvent(coachId: widget.coachId));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachDashboardBloc, CoachDashboardState>(
      listener: (context, state) {
        if (state is GetCoachIncomeInfoSuccess) {
          setState(() {
            coachIncomeEntity = state.coachIncomeEntity;
            _isLoading = false;
          });
        }

        if (state is GetCoachIncomeInfoFail) {
          Log.debug('coach incom info failed');
          setState(() {
            _isLoading = false;
          });
        }
      },
      child: (coachIncomeEntity == null)
          ? const SizedBox.shrink()
          : Container(
              margin: EdgeInsets.only(top: Values.v20),
              child: Column(
                children: [
                  _buildProgramSectionHeader(
                    Assets.coachEnrolledOverviewIcon,
                    'Overview',
                    showCount: false,
                    textColor: UIColors.purple500,
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  _buildEnrollmentTotalInfoSection(),
                ],
              ),
            ),
    );
  }

  _buildProgramSectionHeader(
    String icon,
    String title, {
    required bool showCount,
    Color? iconColor,
    Color? textColor,
  }) {
    return Row(
      children: [
        Container(
          margin: const EdgeInsets.only(right: Values.v4),
          decoration: BoxDecoration(
            color: (iconColor != null) ? iconColor : UIColors.primaryGreen100,
            borderRadius: BorderRadius.circular(Values.v36),
          ),
          child: Center(
            child: SvgPicture.asset(
              icon,
              height: Values.v36,
              width: Values.v36,
            ),
          ),
        ),
        Text(
          '$title',
          style: AppTypography.poppinsSemiBold20(
              color:
                  (textColor != null) ? textColor : UIColors.primaryGreen950),
        ),
      ],
    );
  }

  _buildEnrollmentTotalInfoSection() {
    return Row(
      children: [
        Expanded(flex: 1, child: _buildTotalPaymentCard()),
        SizedBox(
          width: Values.v10,
        ),
        Expanded(flex: 1, child: _buildTotalEnrollmenCard()),
      ],
    );
  }

  _buildTotalPaymentCard() {
    return Container(
      padding: EdgeInsets.all(5),
      decoration: BoxDecoration(
          color: UIColors.skyBlue50,
          border: Border.all(color: UIColors.skyBlue500),
          borderRadius: BorderRadius.circular(Values.v10)),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(10),
            child: Image.asset(
              Assets.coachProgramPaymentImg,
              height: 40,
              width: 40,
            ),
          ),
          Text(
            'BDT ${coachIncomeEntity!.currentAccountBalance}',
            style: AppTypography.poppinsSemiBold20(color: UIColors.skyBlue500),
          ),
          Text('Total Payment',
              style: AppTypography.poppinsMedium12(
                  color: UIColors.primaryGreen950)),
        ],
      ),
    );
  }

  _buildTotalEnrollmenCard() {
    return Container(
      padding: EdgeInsets.all(5),
      decoration: BoxDecoration(
          color: UIColors.orange50,
          border: Border.all(color: UIColors.orange500),
          borderRadius: BorderRadius.circular(Values.v10)),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(10),
            child: Image.asset(
              Assets.coachProgramEnrollmentImg,
              height: 40,
              width: 40,
            ),
          ),
          Text(
            '${coachIncomeEntity!.totalSubscriptions}',
            style: AppTypography.poppinsSemiBold20(color: UIColors.orange500),
          ),
          Text('Total Enrollment',
              style: AppTypography.poppinsMedium12(
                  color: UIColors.primaryGreen950)),
        ],
      ),
    );
  }
}
