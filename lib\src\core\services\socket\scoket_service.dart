import 'dart:async';

import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/services/local_storage/cache_service.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;

class SocketService {
  IO.Socket? _socket;

  SocketService._privateConstructor();

  static SocketService instance = SocketService._privateConstructor();

  static Future<void> initialize() async {
    try {
      String? token = await CacheService.instance.retrieveBearerToken();

      instance._socket = IO.io(API.devSocket, <String, dynamic>{
        'transports': ['websocket'],
        'query': 'token=Bearer $token',
        'force new connection': true,
        'autoConnect': false,
      });

      instance._socket?.onConnect((_) {
        Log.info("Socket Connected");
      });

      instance._socket?.onDisconnect((_) {
        Log.error("Socket Disconnected");
      });

      instance._socket?.onReconnect((data) {
        Log.info("Socket reconnect: $data");
      });

      instance._socket?.onError((data) {
        Log.error("Socket Error: $data");
      });
    } catch (e) {
      Log.error("Socket initialization error: $e");
    }
  }

  void connect() {
    _socket?.connect();
  }

  void disconnect() {
    _socket?.disconnect();
  }

  void dispose(){
    _socket?.dispose();
    _socket = null;
  }

  void on(String event, Function(dynamic) callback) {
    _socket?.on(event, callback);
  }

  void emit(String event, [dynamic args]) {
    _socket?.emit(event, args);
  }

  void off(String event) {
    _socket?.off(event);
  }

  bool isActive() {
    return _socket?.active ?? false;
  }

  bool isConnected() {
    return _socket?.connected ?? false;
  }
}
