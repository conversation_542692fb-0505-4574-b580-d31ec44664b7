import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/exception/network_exception.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/data/data_source/daily_task_data_source.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/data/model/daily_task_model.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/domain/entities/daily_task.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/domain/repositories/daily_task_repository.dart';

class DailyTaskRepositoryImpl extends DailyTaskRepository {
  final DailyTaskDataSource dataSource;

  DailyTaskRepositoryImpl({required this.dataSource});

  @override
  Future<Either<ErrorModel, List<DailyTask>>> getDailyTasks(
      {required int? offset,
      required int? limit,
      required String? taskTypeFilter,
      required String? taskStatusFilter}) async {
    try {
      //  var lists = List.generate(5, (index) {
      //   return testDailyTaks;
      // });

      // return Right(lists);

      final Response response = await dataSource.getDailyTasks(
          offset: offset,
          limit: limit,
          taskTypeFilter: taskTypeFilter,
          taskStatusFilter: taskStatusFilter);
      final data = response.data['data'];
      List<DailyTask> dailyTasks = data
          .map<DailyTask>(
              (taskData) => DailyTaskModel.fromJson(taskData).toEntity())
          .toList();

      return Right(dailyTasks);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }
}
