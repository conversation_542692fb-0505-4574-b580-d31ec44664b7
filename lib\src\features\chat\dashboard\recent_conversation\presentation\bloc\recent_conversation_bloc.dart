import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/domain/entity/chat_list_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/domain/use_case/recent_chat_list_use_case.dart';
import 'package:fitsomnia_app/src/features/profile/domain/entities/user_profile_entity.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'recent_conversation_event.dart';
part 'recent_conversation_state.dart';

class RecentConversationBloc
    extends Bloc<RecentConversationEvent, RecentConversationState> {
  RecentConversationBloc({required this.recentChatListUseCase})
      : super(RecentConversationInitial()) {
    on<GetRecentConversationListEvent>(_onGetRecentConversationListEvent);
    on<UpdateRecentConversationListEvent>(_onUpdateRecentConversationListEvent);
    on<DeleteAndInsertAtFirstEvent>(_onDeleteAndInsertAtFirst);
  }

  late RecentChatListUseCase recentChatListUseCase;
  List<ChatListEntity> recentConversationList = [];

  Future<void> _onGetRecentConversationListEvent(
    GetRecentConversationListEvent event,
    Emitter<RecentConversationState> emit,
  ) async {
    emit(RecentConversationLoading());
    try {
      final response = await recentChatListUseCase.call(
        limit: event.limit,
        offset: event.offset,
      );

      response.fold(
        (l) => emit(
          RecentConversationFailure(
            errorMessage: l.toString(),
          ),
        ),
        (r) => emit(RecentConversationSuccess(chatListEntity: r)),
      );
    } catch (_) {
      emit(
        const RecentConversationFailure(
          errorMessage: TextConstants.failedToLoadData,
        ),
      );
    }
  }

  Future<void> _onUpdateRecentConversationListEvent(
    UpdateRecentConversationListEvent event,
    Emitter<RecentConversationState> emit,
  ) async {
    recentConversationList.addAll(event.chatListEntities);
  }

  Future<void> _onDeleteAndInsertAtFirst(
    DeleteAndInsertAtFirstEvent event,
    Emitter<RecentConversationState> emit,
  ) async {
    int tempIndex = recentConversationList.indexWhere((element) =>
        element.fitBuddyId == event.data['senderId'] ||
        element.fitBuddyId == event.data['receiverId']);

    if (tempIndex != -1) {
      moveExistingChatToTop(tempIndex, event);
    } else {
      addNewChatToTop(event);
    }
  }

  void addNewChatToTop(DeleteAndInsertAtFirstEvent event) {
    bool decideIfTheSenderIsMe = event.data['senderId'] == event.userProfile.id;

    recentConversationList.insert(
      0,
      ChatListEntity(
        lastMessageInfo: LastMessageInfo(
          id: event.data['id'],
          senderId: event.data['senderId'],
          receiverId: event.data['receiverId'],
          type: event.data['type'],
          content: event.data['content'],
          isLastSeen: false,
          createdAt: DateTime.now(),
        ),
        fitBuddyId: decideIfTheSenderIsMe
            ? event.data['receiverId']
            : event.data['senderId'],
        name: decideIfTheSenderIsMe
            ? event.data['receiverName']
            : event.data['senderName'],
        image: decideIfTheSenderIsMe
            ? event.data['receiverImage']
            : event.data['receiverName'],
      ),
    );
  }

  void moveExistingChatToTop(int tempIndex, DeleteAndInsertAtFirstEvent event) {
    ChatListEntity tempEntity = recentConversationList.elementAt(tempIndex);
    recentConversationList.removeAt(tempIndex);

    recentConversationList.insert(
      0,
      ChatListEntity(
        lastMessageInfo: LastMessageInfo(
          id: event.data['id'],
          senderId: event.data['senderId'],
          receiverId: event.data['receiverId'],
          type: event.data['type'],
          content: event.data['content'],
          isLastSeen: false,
          createdAt: DateTime.now(),
        ),
        fitBuddyId: tempEntity.fitBuddyId,
        name: tempEntity.name,
        image: tempEntity.image ?? "",
      ),
    );
  }
}
