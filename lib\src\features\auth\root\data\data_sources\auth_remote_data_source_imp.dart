import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/config/config.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/exception/network_exception.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';
import 'package:fitsomnia_app/src/core/services/firebase/firebase_service.dart';
import 'package:fitsomnia_app/src/core/services/local_storage/cache_service.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import 'auth_data_source.dart';

class AuthRemoteDataSourceImp implements AuthRemoteDataSource {
  const AuthRemoteDataSourceImp({required this.restClient});

  final RestClient restClient;

  @override
  Future<Response> loginWithEmailAndPassword(Map<String, dynamic> data) async {
    final response = await restClient.post(
      APIType.PUBLIC,
      API.signIn,
      data,
    );

    return response;
  }

  @override
  Future<Response> registration(Map<String, dynamic> map) async {
    final resource = await restClient.post(
      APIType.PUBLIC,
      API.registration,
      map,
    );

    return resource;
  }

  @override
  Future<Response> verification(Map<String, dynamic> map) async {
    final resource = await restClient.post(
      APIType.PUBLIC,
      API.verification,
      map,
    );

    return resource;
  }

  @override
  Future<Response> loginWithGoogle() async {
    try {
      const List<String> scopes = <String>['email'];
      final googleUser = await GoogleSignIn(scopes: scopes).signIn();
      final googleAuth = await googleUser!.authentication;
      final String accessToken = googleAuth.accessToken!;
      
      ////TODO testing error
      // throw Exception('googleAccessToken: ${accessToken}');

      String? fcmToken;
      try {
        fcmToken = await CacheService.instance.retrieveFcmToken();
        if (fcmToken == null) {
          await FirebaseService().retrieveFCMToken();
          fcmToken = await CacheService.instance.retrieveFcmToken();
        }
      } catch (_) {
        Log.error('Failed to retrieve FCM token');
      }

      final requestBody = {
        'accessToken': accessToken,
        'fcmToken': fcmToken.toString()
      };

      final response = await restClient.post(
        APIType.PUBLIC,
        API.signInWithGoogle,
        requestBody,
      );

      return response;
    } catch (e, stackTrace) {
      Log.error('Google Login Failed: $e');

      Log.error(e.toString());
      Log.error(stackTrace.toString());

      throw DefaultException(
        ErrorResponseModel(
          error: ErrorModel(message: 'Google Login Failed'),
        ),
      );
    }
  }

  @override
  Future<Response> loginWithFacebook() async {
    final facebookResponse = await FacebookAuth.instance.login();
    if (facebookResponse.status == LoginStatus.success) {
      String? fcmToken;
      try {
        fcmToken = await CacheService.instance.retrieveFcmToken();
        if (fcmToken == null) {
          await FirebaseService().retrieveFCMToken();
          fcmToken = await CacheService.instance.retrieveFcmToken();
        }
      } catch (_) {
        Log.error('Failed to retrieve FCM token');
      }

      final requestBody = {
        'accessToken': facebookResponse.accessToken!.tokenString,
        'fcmToken': fcmToken.toString()
      };

      final response = await restClient.post(
        APIType.PUBLIC,
        API.signInWithFacebook,
        requestBody,
      );

      return response;
    } else {
      Log.error('Facebook Login Failed');

      throw DefaultException(
        ErrorResponseModel(
          error: ErrorModel(message: 'Facebook Login Failed'),
        ),
      );
    }
  }

  @override
  Future<Response> loginWithApple() async {
    try {
      final authorizationCredentialAppleID =
          await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        webAuthenticationOptions: WebAuthenticationOptions(
          clientId: appleClientId,
          redirectUri: Uri.parse(appleRedirectUrl),
        ),
      );
      final String accessToken = authorizationCredentialAppleID.identityToken!;
      final String? name = authorizationCredentialAppleID.givenName;

      ////TODO testing error
      // throw Exception('appleAccessToken: ${accessToken}');

      String? fcmToken;
      try {
        fcmToken = await CacheService.instance.retrieveFcmToken();
        if (fcmToken == null) {
          await FirebaseService().retrieveFCMToken();
          fcmToken = await CacheService.instance.retrieveFcmToken();
        }
      } catch (_) {
        Log.error('Failed to retrieve FCM token');
      }


      final requestBody = {
        'idToken': accessToken,
        if (name != null) "name": name,
        'fcmToken': fcmToken.toString()
      };

      final response = await restClient.post(
        APIType.PUBLIC,
        API.signInWithApple,
        requestBody,
      );

      return response;
    } catch (e) {
      Log.error('Apple Login Failed: $e');

      throw DefaultException(
        ErrorResponseModel(
          error: ErrorModel(message: 'Apple Login Failed'),
        ),
      );
    }
  }

  @override
  Future<Response> sendOTP(Map<String, dynamic> map) async {
    final resource = await restClient.post(
      APIType.PUBLIC,
      API.forgotPasswordSendOTP,
      map,
    );

    return resource;
  }

  @override
  Future<Response> identityVerification(Map<String, dynamic> map) async {
    final resource = await restClient.post(
      APIType.PUBLIC,
      API.identityVerification,
      map,
    );

    return resource;
  }

  @override
  Future<Response> resetPassword(Map<String, dynamic> map) async {
    final resource = await restClient.post(
      APIType.PUBLIC,
      API.resetPassword,
      map,
    );

    return resource;
  }
}
