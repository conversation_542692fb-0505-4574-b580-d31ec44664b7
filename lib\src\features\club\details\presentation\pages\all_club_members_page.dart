import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/services/firebase/firebase_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/bottom_loader.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/club_members/club_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/widgets/club_member_card.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AllClubMembersPage extends StatefulWidget {
  const AllClubMembersPage({
    Key? key,
  }) : super(key: key);

  @override
  State<AllClubMembersPage> createState() => _AllClubMembersPageState();
}

class _AllClubMembersPageState extends State<AllClubMembersPage> {
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: BlocBuilder<ClubMembersCubit, ClubMembersState>(
        builder: (context, state) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 32.h),
            child: CustomScrollView(
              controller: _scrollController,
              physics: const AlwaysScrollableScrollPhysics(),
              scrollBehavior: const CupertinoScrollBehavior(),
              slivers: [
                // SliverList(
                //   delegate: SliverChildBuilderDelegate(
                //     (context, index) {
                //       return index >= state.data.length
                //           ? const BottomLoader()
                //           : ClubMemberCard(
                //               id: state.data[index].userId,
                //               name: state.data[index].userName,
                //               image: state.data[index].image,
                //               status: state.data[index].relationStatus,
                //               isHorizontal: true,
                //               callback: (status) {
                //                 _updateRelationshipAtSpecificIndex(
                //                   context,
                //                   index,
                //                   status,
                //                 );
                //               },
                //             );
                //     },
                //     childCount: state.hasReachedMax
                //         ? state.data.length
                //         : state.data.length + 1,
                //   ),
                // )
                SliverGrid(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 1.0,
                    mainAxisSpacing: 10.0,
                    crossAxisSpacing: 10.0,
                  ),
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      return index >= state.data.length
                          ? const BottomLoader()
                          : ClubMemberCard(
                              id: state.data[index].userId,
                              name: state.data[index].userName,
                              image: state.data[index].image,
                              status: state.data[index].relationStatus,
                              isHorizontal: false,
                              callback: (status) {
                                _updateRelationshipAtSpecificIndex(
                                  context,
                                  index,
                                  status,
                                );
                              },
                              width: double.infinity,
                              height: double.infinity,
                            );
                    },
                    childCount: state.hasReachedMax
                        ? state.data.length
                        : state.data.length + 1,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.white,
      iconTheme: const IconThemeData(color: Colors.black),
      title: Text(
        TextConstants.peopleFromYourClub,
        style: AppTypography.regular16(color: Colors.black),
      ),
      elevation: Values.v0,
      automaticallyImplyLeading: false,
      leading: IconButton(
        onPressed: () {
          Navigator.pop(context);
        },
        icon: SvgPicture.asset(
          Assets.backButton,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<ClubMembersCubit>().getClubMembers();
      FirebaseService().logFeatureUsage('club', 'club_members', '');
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;

    return currentScroll >= (maxScroll * 0.9);
  }

  void _updateRelationshipAtSpecificIndex(
    BuildContext context,
    int index,
    String status,
  ) {
    context.read<ClubMembersCubit>().updateRelationship(
          index: index,
          relationship: status,
        );
  }
}
