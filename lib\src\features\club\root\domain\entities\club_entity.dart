class ClubEntity {
  final String id;
  final String name;
  final ClubImage image;
  final String description;
  final ClubLocation location;

  ClubEntity({
    required this.id,
    required this.name,
    required this.image,
    required this.description,
    required this.location,
  });
}

class ClubImage {
  ClubImage({
    required this.cover,
    required this.logo,
  });

  final String cover;
  final String logo;

  factory ClubImage.fromJson(Map<String, dynamic> json) => ClubImage(
        cover: json["cover"],
        logo: json["logo"],
      );

  Map<String, dynamic> toJson() => {
        "cover": cover,
        "logo": logo,
      };
}

class ClubLocation {
  ClubLocation({
    required this.type,
    required this.coordinates,
  });

  final String type;
  final List<double> coordinates;

  factory ClubLocation.fromJson(Map<String, dynamic> json) => ClubLocation(
        type: json["type"],
        coordinates:
            List<double>.from(json["coordinates"].map((x) => x.toDouble())),
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "coordinates": List<dynamic>.from(coordinates.map((x) => x)),
      };
}
