import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/entities/user.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/repositories/auth_repository.dart';

class LoginWithAppleUseCase {
  const LoginWithAppleUseCase({required this.repository});

  final AuthRepository repository;

  Future<Either<ErrorModel, User>> call() async {
    return await repository.apple();
  }
}
