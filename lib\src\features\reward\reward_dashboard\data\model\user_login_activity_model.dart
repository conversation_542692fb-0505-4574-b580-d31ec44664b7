import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/user_login_activity_entity.dart';

class UserLoginActivityModel extends UserLoginActivityEntity {
  UserLoginActivityModel({
    required super.userId,
    required super.totalLoginStreak,
    required super.loginHistory,
  });

  factory UserLoginActivityModel.fromJson(Map<String, dynamic> json) {
    return UserLoginActivityModel(
      userId: json['userId'],
      totalLoginStreak: json['currentStreak'],
      loginHistory: List<DailyLoginActivity>.from(json['streakHistory'].map(
          (jsonData) => DailyLoginActivityModel.fromJson(jsonData).toEntity())),
    );
  }

  UserLoginActivityEntity toEntity() {
    return UserLoginActivityEntity(
      userId: userId,
      totalLoginStreak: totalLoginStreak,
      loginHistory: loginHistory,
    );
  }
}

class DailyLoginActivityModel extends DailyLoginActivity {
  DailyLoginActivityModel({
    required super.date,
    required super.loginStatus,
  });

  factory DailyLoginActivityModel.fromJson(Map<String, dynamic> json) {
    return DailyLoginActivityModel(
      date: DateTime.tryParse(json['date']) ?? DateTime.now(),
      loginStatus: json["isStreak"] ?? false,
    );
  }

  DailyLoginActivity toEntity() {
    return DailyLoginActivity(date: date, loginStatus: loginStatus);
  }
}
