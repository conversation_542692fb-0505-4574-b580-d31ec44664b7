part of '../pages/login_page.dart';

class _LoginFormBuilder extends StatelessWidget {
  const _LoginFormBuilder({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    LoginBloc bloc = context.read<LoginBloc>();

    return Column(
      children: [
        _buildEmailOr<PERSON><PERSON><PERSON><PERSON>(bloc),
        _buildPassword<PERSON>ield(bloc),
      ],
    );
  }

  Widget _buildEmailOrPhoneField(LoginBloc bloc) {
    return InputForm<PERSON>ield(
      textEditingController: bloc.emailOrPhoneField,
      hintText: TextConstants.email,
      autocorrect: false,
      keyboardType: TextInputType.emailAddress,
      validator: InputValidators.email,
    );
  }

  Widget _buildPasswordField(LoginBloc bloc) {
    return InputFormField(
      textEditingController: bloc.passwordField,
      hintText: TextConstants.password,
      password: EnabledPassword(),
      keyboardType: TextInputType.visiblePassword,
      validator: InputValidators.password,
      bottomMargin: 5.h,
    );
  }
}
