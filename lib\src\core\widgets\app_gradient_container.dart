import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:flutter/material.dart';

class AppGradientContainer extends StatelessWidget {
  const AppGradientContainer(
      {Key? key,
      this.height,
      this.width,
      this.margin,
      this.padding,
      this.borderRadius = 0,
      this.begin = Alignment.bottomRight,
      this.end = Alignment.topLeft,
      this.child})
      : super(key: key);

  final double? height;
  final double? width;
  final EdgeInsets? margin;
  final EdgeInsets? padding;
  final double borderRadius;
  final AlignmentGeometry begin;
  final AlignmentGeometry end;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      margin: margin,
      padding: padding,
      decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.primaryGreen,
              AppColors.primaryGreen,
            ],
            begin: begin,
            end: end,
          ),
          borderRadius: BorderRadius.circular(borderRadius)),
      child: child,
    );
  }
}
