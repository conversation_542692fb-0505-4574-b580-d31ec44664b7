import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/extensions/extensions.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/presentation/model/chat_model.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/pages/chat_page.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/domain/entities/user_profile_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ConversationCard extends StatefulWidget {
  const ConversationCard({
    Key? key,
    required this.chat,
  }) : super(key: key);

  final ChatListViewModel chat;

  @override
  State<ConversationCard> createState() => _ConversationCardState();
}

class _ConversationCardState extends State<ConversationCard> {
  late UserProfile? userProfile;

  @override
  void initState() {
    super.initState();
    userProfile = BlocProvider.of<SharedBloc>(context).userProfile;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          widget.chat.isLastSeen = true;
        });

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ChatPage(
              title: widget.chat.name,
              fitBuddyId: widget.chat.id,
              image: widget.chat.image,
            ),
          ),
        );
      },
      child: BlocListener<SharedBloc, SharedState>(
        listener: (context, state) {
          if (state is EssentialDataLoaded) {
            userProfile = state.userProfile;
          }
        },
        child: Container(
          margin: EdgeInsets.only(top: Values.v14.h),
          padding: EdgeInsets.all(Values.v10.r),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(Values.v0_5.r),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ImageContainer.circularImage(
                image: widget.chat.image ?? '',
                radius: Values.v26,
              ),
              SizedBox(width: Values.v10.w),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 10),
                    Row(
                      children: [
                        Text(
                          widget.chat.name,
                          style: widget.chat
                                  .isMessageAlreadySeenByUser(userProfile!.id)
                              ? AppTypography.regular12(
                                  color: AppColors.darkSoftBg,
                                )
                              : AppTypography.bold14(
                                  color: AppColors.darkSoftBg,
                                ),
                        ),
                      ],
                    ),
                    widget.chat.type == 'image'
                        ? Text(
                            widget.chat.senderId == userProfile!.id
                                ? 'You sent an image'
                                : 'You received an image',
                            overflow: TextOverflow.ellipsis,
                            style: widget.chat
                                    .isMessageAlreadySeenByUser(userProfile!.id)
                                ? AppTypography.regular10(
                                    color: AppColors.silver,
                                  )
                                : AppTypography.bold12(
                                    color: AppColors.darkSoftBg,
                                  ),
                          )
                        : Text(
                            widget.chat.lastMessage,
                            overflow: TextOverflow.ellipsis,
                            style: widget.chat
                                    .isMessageAlreadySeenByUser(userProfile!.id)
                                ? AppTypography.regular10(
                                    color: AppColors.silver,
                                  )
                                : AppTypography.bold12(
                                    color: AppColors.darkSoftBg,
                                  ),
                          ),
                  ],
                ),
              ),
              const Spacer(),
              widget.chat.isMessageAlreadySeenByUser(userProfile!.id)
                  ? const SizedBox.shrink()
                  : Text(
                      "•",
                      overflow: TextOverflow.ellipsis,
                      style: AppTypography.bold24(
                        color: AppColors.primaryGreen,
                      ),
                    ),
              Text(
                widget.chat.createdAt != null
                    ? widget.chat.createdAt!.timeAgo()
                    : 'N/A',
                overflow: TextOverflow.ellipsis,
                style: widget.chat.isMessageAlreadySeenByUser(userProfile!.id)
                    ? AppTypography.regular10(
                        color: AppColors.darkSoftBg,
                      )
                    : AppTypography.bold12(
                        color: AppColors.darkSoftBg,
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
