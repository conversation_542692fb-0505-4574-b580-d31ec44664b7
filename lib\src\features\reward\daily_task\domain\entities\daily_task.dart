class DailyTask {
  final String id;
  final String title;
  final int totalRewardPoint;
  final double taskProgress;
  final bool isCompleted;

  DailyTask({
    required this.id,
    required this.title,
    required this.totalRewardPoint,
    required this.taskProgress,
    required this.isCompleted,
  });
}

final testDailyTaks = DailyTask(
  id: 'daily-task-id-01',
  title: 'Create new social post and get 50 reactions',
  totalRewardPoint: 200,
  taskProgress: 0,
  isCompleted: false,
);
