part of 'group_chat_bloc.dart';

class GroupChatEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class GroupChatHistoryEvent extends GroupChatEvent {
  final String groupId;
  int? limit;
  int? offset;

  GroupChatHistoryEvent({required this.groupId, this.limit, this.offset});
}

class UpdateGroupChatHistoryEvent extends GroupChatEvent {
  final List<GroupChatEntity> groupChatEntities;

  UpdateGroupChatHistoryEvent({required this.groupChatEntities});

  @override
  List<Object?> get props => groupChatEntities;
}

class AddGroupChatHistoryEvent extends GroupChatEvent {
  final GroupChatEntity groupChatEntity;

  AddGroupChatHistoryEvent({required this.groupChatEntity});

  @override
  List<Object?> get props => [groupChatEntity];
}

class RemoveFromGroupChatHistoryListEvent extends GroupChatEvent {
  final String messageId;

  RemoveFromGroupChatHistoryListEvent({required this.messageId});

  @override
  List<Object?> get props => [messageId];
}

class GetMoreGroup<PERSON>hatHistoryEvent extends GroupChatEvent {
  final String groupId;
  final int? limit;
  final int? offset;

  GetMoreGroupChatHistoryEvent({required this.groupId, this.limit, this.offset});
}
