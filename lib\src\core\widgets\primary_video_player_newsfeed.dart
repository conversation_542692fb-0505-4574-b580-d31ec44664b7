import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';

ValueNotifier<double> videoVolumeValueNotifier = ValueNotifier(0.0);

class PrimaryVideoPlayerNewsfeed extends StatefulWidget {
  final String videoUrl;

  const PrimaryVideoPlayerNewsfeed({required this.videoUrl});

  @override
  PrimaryVideoPlayerNewsfeedState createState() =>
      PrimaryVideoPlayerNewsfeedState();
}

class PrimaryVideoPlayerNewsfeedState
    extends State<PrimaryVideoPlayerNewsfeed> {
  late VideoPlayerController _controller;
  bool showVideoBar = false;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));

    _controller.addListener(() {
      setState(() {
        '';
      });
    });
    _controller.setLooping(false);
    _controller.setVolume(videoVolumeValueNotifier.value);
    _controller.initialize().then((_) => setState(() {
          showVideoBar = true;
        }));
    // _controller.play();
  }

  @override
  void dispose() {
    Log.debug("Video Player Dispose Called");
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: ObjectKey(_controller),
      onVisibilityChanged: (visibility) {
        if (mounted && visibility.visibleFraction < 1.0) {
          if(_controller.value.isPlaying) _controller.pause(); //pausing  functionality
        }

        if (mounted && visibility.visibleFraction == 1.0) {
          if(!_controller.value.isPlaying) _controller.play(); //pausing  functionality
        }
      },
      child: Container(
        color: AppColors.black,
        child: Stack(
          children: [
            Center(
              child: AspectRatio(
                aspectRatio: _controller.value.aspectRatio,
                // aspectRatio: 1280/720,
                child: Stack(
                  alignment: Alignment.bottomCenter,
                  children: <Widget>[
                    showVideoBar
                        ? VideoPlayer(_controller)
                        : Center(
                            child: CircularProgressIndicator(
                              color: AppColors.primaryGreen,
                            ),
                          ),
                    // showVideoBar
                    //     ? _ControlsOverlay(controller: _controller)
                    //     : const SizedBox.shrink(),
                    // showVideoBar
                    //     ? VideoProgressIndicator(
                    //         _controller,
                    //         allowScrubbing: true,
                    //         colors: VideoProgressColors(
                    //             playedColor: AppColors.primaryGreen,
                    //             bufferedColor: AppColors.white,
                    //             backgroundColor: AppColors.greyscale400),
                    //       )
                    //     : const SizedBox.shrink(),
                  ],
                ),
              ),
            ),
            // SizedBox(
            //   height: 20,
            // ),

            Align(
              alignment: Alignment.bottomCenter,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // if (_controller.value.isPlaying) _buildVideoSkipSection(),
                  showVideoBar
                      ? Padding(
                          padding: const EdgeInsets.only(bottom: 5),
                          child: VideoProgressIndicator(
                            _controller,
                            allowScrubbing: true,
                            colors: VideoProgressColors(
                                playedColor: UIColors.primaryGreen400,
                                bufferedColor: AppColors.white,
                                backgroundColor: AppColors.greyscale400),
                          ),
                        )
                      : const SizedBox.shrink(),
                ],
              ),
            ),
            GestureDetector(
              onTap: () {
                _controller.value.isPlaying
                    ? _controller.pause()
                    : _controller.play();
              },
            ),

            _buildVolumeControllWidget(),
            Align(
              alignment: Alignment.center,
              child: showVideoBar
                  ? _ControlsOverlay(controller: _controller)
                  : const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  _buildVolumeControllWidget() {
    return Align(
      alignment: Alignment.topRight,
      child: GestureDetector(
        onTap: () {
          _controller.value.volume == 0.0
              ? _controller.setVolume(1.0)
              : _controller.setVolume(0.0);

          videoVolumeValueNotifier.value = _controller.value.volume;
        },
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 50),
          reverseDuration: const Duration(milliseconds: 200),
          child: _controller.value.volume == 0.0
              ? Container(
                  padding: EdgeInsets.only(right: 8.w, top: 8.h),
                  child: Icon(
                    Icons.volume_off_rounded,
                    color: AppColors.primaryGreen,
                    size: 30.0,
                    semanticLabel: 'Mute',
                  ),
                )
              : Container(
                  padding: EdgeInsets.only(right: 8.w, top: 8.h),
                  child: Icon(
                    Icons.volume_up_rounded,
                    color: AppColors.primaryGreen,
                    size: 30.0,
                    semanticLabel: 'Volume Up',
                  ),
                ),
        ),
      ),
    );
  }

  _buildVideoSkipSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Values.v10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(Values.v100),
        gradient: AppColors.whiteToBlackBackgroundGradient(),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            onPressed: () async {
              Log.debug('video rewind');
              // _controller.seekTo(const Duration(seconds: 10));
              await _controller.seekTo(
                  (await _controller.position)! - Duration(seconds: 10));
              setState(() {
                '';
              });
            },
            icon: Icon(
              Icons.fast_rewind_outlined,
              color: AppColors.greyscale100,
            ),
            iconSize: Values.v28,
          ),
          SizedBox(
            width: Values.v20,
          ),
          IconButton(
            onPressed: () async {
              Log.debug('video fast forward');
              await _controller.seekTo(
                  (await _controller.position)! + Duration(seconds: 10));
              setState(() {
                '';
              });
            },
            icon: Icon(
              Icons.fast_forward_outlined,
              color: AppColors.greyscale100,
            ),
            iconSize: Values.v28,
          ),
        ],
      ),
    );
  }
}

class _ControlsOverlay extends StatelessWidget {
  const _ControlsOverlay({required this.controller});

  final VideoPlayerController controller;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        Align(
          alignment: Alignment.center,
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 50),
            reverseDuration: const Duration(milliseconds: 200),
            child: controller.value.isPlaying
                ? const SizedBox.shrink()
                : Container(
                    // color: Colors.black26,
                    // padding: EdgeInsets.symmetric(horizontal: Values.v10),
                    // decoration: BoxDecoration(
                    //   borderRadius: BorderRadius.circular(Values.v100),
                    //   gradient: AppColors.whiteToBlackBackgroundGradient(),
                    // ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          onPressed: () async {
                            Log.debug('video rewind');
                            // _controller.seekTo(const Duration(seconds: 10));
                            await controller.seekTo(
                                (await controller.position)! -
                                    Duration(seconds: 10));
                          },
                          // icon: Icon(
                          //   Icons.fast_rewind_outlined,
                          //   color: AppColors.greyscale100,
                          // ),
                          icon: SvgPicture.asset(Assets.videoRewindIcon),
                          iconSize: Values.v40,
                        ),
                        IconButton(
                          onPressed: () {
                            controller.play();
                          },
                          // icon: const Icon(
                          //   Icons.play_circle,
                          //   color: AppColors.white,
                          //   size: 60,
                          // ),
                          icon: SvgPicture.asset(Assets.videoPlayIcon),
                          iconSize: Values.v60,
                        ),
                        IconButton(
                          onPressed: () async {
                            Log.debug('video fast forward');
                            await controller.seekTo(
                                (await controller.position)! +
                                    Duration(seconds: 10));
                          },
                          // icon: Icon(
                          //   Icons.fast_forward_outlined,
                          //   color: AppColors.greyscale100,
                          // ),
                          icon: SvgPicture.asset(Assets.videoForwardIcon),
                          iconSize: Values.v28,
                        ),
                      ],
                    ),
                  ),
          ),
        ),

        // Align(
        //   alignment: Alignment.topRight,
        //   child: GestureDetector(
        //     onTap: () {
        //       controller.value.volume == 0.0
        //           ? controller.setVolume(1.0)
        //           : controller.setVolume(0.0);
        //     },
        //     child: AnimatedSwitcher(
        //       duration: const Duration(milliseconds: 50),
        //       reverseDuration: const Duration(milliseconds: 200),
        //       child: controller.value.volume == 0.0
        //           ? Container(
        //               padding: EdgeInsets.only(right: 8.w, top: 8.h),
        //               child: Icon(
        //                 Icons.volume_off_rounded,
        //                 color: AppColors.primaryGreen,
        //                 size: 30.0,
        //                 semanticLabel: 'Mute',
        //               ),
        //             )
        //           : Container(
        //               padding: EdgeInsets.only(right: 8.w, top: 8.h),
        //               child: Icon(
        //                 Icons.volume_up_rounded,
        //                 color: AppColors.primaryGreen,
        //                 size: 30.0,
        //                 semanticLabel: 'Volume Up',
        //               ),
        //             ),
        //     ),
        //   ),
        // )
      ],
    );
  }
}
