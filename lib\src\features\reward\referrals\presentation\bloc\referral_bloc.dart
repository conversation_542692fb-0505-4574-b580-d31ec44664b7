import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/entity/referral_code_entity.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/entity/referral_data_entity.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/domain/use_case/referral_use_case.dart';

part 'referral_event.dart';
part 'referral_state.dart';

class ReferralBloc extends Bloc<ReferralEvent, ReferralState> {
  ReferralBloc({required this.useCase}) : super(ReferralInitial()) {
    on<GetReferrals>(_onGetReferrals);
    on<GetSingleReferral>(_onGetSingleReferral);
    on<GetReferralCode>(_onGetReferralCode);
    on<UseReferralCode>(_onUseReferralCode);
  }

  final ReferralUseCase useCase;

  Future<void> _onGetReferrals(
    GetReferrals event,
    Emitter<ReferralState> emit,
  ) async {
    try {
      emit(ReferralLoading());
      final response = await useCase.getReferrals();

      response.fold((l) {
        emit(GetReferralsFail(data: l));
      }, (r) {
        emit(GetReferralsSuccess(referrals: r));
      });
    } catch (e) {
      Log.debug(e.toString());
      emit(GetReferralsFail(data: e));
    }
  }

  Future<void> _onGetSingleReferral(
    GetSingleReferral event,
    Emitter<ReferralState> emit,
  ) async {
    try {
      emit(ReferralLoading());
      final response =
          await useCase.getReferralById(referralId: event.referralId);

      response.fold((l) {
        emit(GetSingleReferralFail(data: l));
      }, (r) {
        emit(GetSingleReferralSuccess(referralDataEntity: r));
      });
    } catch (e) {
      Log.debug(e.toString());
      emit(GetSingleReferralFail(data: e));
    }
  }

  Future<void> _onGetReferralCode(
    GetReferralCode event,
    Emitter<ReferralState> emit,
  ) async {
    try {
      emit(ReferralLoading());
      final response = await useCase.getReferralCode(
          userId: event.userId, referralId: event.referralId);

      response.fold((l) {
        emit(GetReferralCodeFail(data: l));
      }, (r) {
        emit(GetReferralCodeSuccess(referralCodeEntity: r));
      });
    } catch (e) {
      Log.debug(e.toString());
      emit(GetReferralCodeFail(data: e));
    }
  }

  Future<void> _onUseReferralCode(
    UseReferralCode event,
    Emitter<ReferralState> emit,
  ) async {
    try {
      emit(ReferralLoading());
      final response = await useCase.useReferralCode(
          userId: event.userId, referralCode: event.referralCode);

      response.fold((l) {
        emit(UserReferralCodeFail(data: l));
      }, (r) {
        emit(UserReferralCodeSuccess(referralDataEntity: r));
      });
    } catch (e) {
      Log.debug(e.toString());
      emit(UserReferralCodeFail(data: e));
    }
  }
}
