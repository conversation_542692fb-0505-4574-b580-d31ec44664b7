import 'dart:math';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:flutter/material.dart';

class ProcessStepsTimelineWidget extends StatelessWidget {
  const ProcessStepsTimelineWidget(
      {super.key, required this.title, required this.steps});
  final String title;
  final List<ProcessStep> steps;

  @override
  Widget build(BuildContext context) {
    return _buildTimeline();
  }

  Widget _buildTimeline() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTimelineHeader(),
        _buildTimelineBody(),
      ],
    );
  }

  _buildTimelineHeader() {
    return Padding(
      padding: const EdgeInsets.only(top: 30, bottom: 20),
      child: Text(
        title,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        style: AppTypography.poppinsSemiBold20(color: UIColors.primary),
      ),
    );
  }

  _buildTimelineBody() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      // children: steps.map<Widget>((step) {
      //   return _buildStep(step.stepDesc);
      // }).toList(),

      children: steps
          .asMap()
          .map<int, Widget>((index, step) {
            return MapEntry(
                index,
                _buildStep(step.stepDesc,
                    isLastItem: index == (steps.length - 1)));
          })
          .values
          .toList(),
    );
  }

  _buildStep(
    String stepDesc, {
    bool isLastItem = false,
  }) {
    return Container(
      // height: 60,
      child: IntrinsicHeight(
        child: Row(
          children: [
            _buildConnectionLine(
                showBottomLine: !isLastItem),
            // const SizedBox(
            //   width: 10,
            // ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 10, top: 0, right: 10, bottom: 40),
                child: Text(
                  stepDesc,
                  style: AppTypography.poppinsMedium14(
                      color: UIColors.primaryGreen950),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  _buildConnectionLine({bool showBottomLine = true}) {
    return Stack(
      alignment: AlignmentDirectional.topCenter,
      children: [
        Column(
          
          children: [
            Expanded(child: _buildLine(showLine: showBottomLine)),
          ],
        ),
        _stepIcon(),
      ],
    );
  }

  _buildLine({bool showLine = false}) {
    return Container(
      width: 1,
      color: (showLine) ? UIColors.primaryGreen600 : UIColors.transparent,
    );
  }

  _stepIcon() {
    return Transform.rotate(
      angle: 45.0 * pi / 180.0,
      child: Icon(
        Icons.square,
        size: 20,
        color: UIColors.primaryGreen600,
      ),
    );
  }
}

class ProcessStep {
  ProcessStep({this.icon, required this.stepDesc});

  final String? icon;
  final String stepDesc;
}
