import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/entities/nearby_clubs_members_entity.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/repository/club_repository.dart';

class NearbyClubsMembersUseCase {
  const NearbyClubsMembersUseCase({
    required this.repository,
  });

  final ClubRepository repository;

  Future<Either<ErrorResponseModel, List<NearbyClubsMemberEntity>>>
      nearbyClubsMembers(
    Map<String, dynamic> map,
    String id,
    int? offset,
  ) async {
    return await repository.nearbyClubsMembers(map, id, offset);
  }
}
