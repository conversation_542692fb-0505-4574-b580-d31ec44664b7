import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/reward_point_entity.dart';

class RewardPointModel extends RewardPointEntity{
  RewardPointModel({required super.points});
  
  factory RewardPointModel.fromJson(Map<String, dynamic> json){
    return RewardPointModel(points: json['points']);
  }

  RewardPointEntity toEntity(){
    return RewardPointEntity(points: points);
  }
}