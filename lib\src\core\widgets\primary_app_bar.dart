import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/global/globals.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/firebase/firebase_service.dart';
import 'package:fitsomnia_app/src/core/services/local_notification/local_notification_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/gradient_color_effect.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:fitsomnia_app/src/features/fit_market/cart/domain/entities/cart_entity.dart';
import 'package:fitsomnia_app/src/features/fit_market/cart/presentations/bloc/cart_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/domain/entity/wishlist_entity.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/presentation/bloc/get_wishlist/get_wishlist_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/pages/dashboard/profile_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gradient_borders/gradient_borders.dart';

class PrimaryAppBar extends StatefulWidget implements PreferredSizeWidget {
  const PrimaryAppBar({
    super.key,
    this.index = 0,
  });

  @override
  State<PrimaryAppBar> createState() => _PrimaryAppBarState();

  final int index;

  @override
  Size get preferredSize => Size(double.infinity, Values.v55.h);
}

class _PrimaryAppBarState extends State<PrimaryAppBar> {
  @override
  void initState() {
    super.initState();
    context.read<CartBloc>().add(const AddOrUpdateCartEvent());
    context.read<GetWishlistBloc>().add(GetAllWishListEvent());
  }

  @override
  Widget build(BuildContext context) {
    List<ActionItem> actionItems = _getActionItems();

    return Padding(
      padding: EdgeInsets.zero,
      child: AppBar(
        backgroundColor: AppColors.white,
        elevation: Values.v0,
        // scrolledUnderElevation: 0.0,
        iconTheme: IconThemeData(
          color: AppColors.black,
          size: Values.v10.h,
        ),
        automaticallyImplyLeading: false,
        titleSpacing: 0,
        actions: [
          Visibility(
            visible: widget.index == PageType.Shop.index,
            child: _buildBackIcon(),
          ),
          Visibility(
            visible: widget.index != PageType.Shop.index,
            child: _fitsomniaLogo(context),
          ),
          // const Spacer(),
          SizedBox(width: Values.v5.w),
          _buildFitsomniaTitle(),
          SizedBox(
            width: Values.v10.w,
          ),

          // Expanded(
          //   child: _buildSearchBar(),
          // ),

          const Spacer(),
          _buildRewardIcon(),
          _buildSmallSearchBar(),

          // /// QR Code
          // Visibility(
          //   visible: widget.index != PageType.Shop.index,
          //   child: buildActionItems(actionItems[0]),
          // ),

          /// Notification
          Visibility(
            visible: widget.index != PageType.Shop.index,
            child: buildActionItems(actionItems[1], isChat: true),
          ),

          /// Chat
          Visibility(
            visible: widget.index != PageType.Shop.index,
            child: buildActionItems(actionItems[2], isNotification: true),
          ),

          /// Cart
          Visibility(
            visible: widget.index == PageType.Shop.index,
            child: buildActionItems(actionItems[3]),
          ),

          /// wishlist
          Visibility(
            visible: widget.index == PageType.Shop.index,
            child: buildActionItems(actionItems[4]),
          ),
          // Visibility(
          //   visible: widget.index == 4,
          //   child: buildActionItems(actionItems[5]),
          // ),
        ],
      ),
    );
  }

  Widget _buildFitsomniaTitle() {
    return Text(
      'Fitsomnia',
      style: TextStyle(
        color: UIColors.primary,
        fontFamily: FontConstants.poppinsFontFamily,
        fontSize: Values.v26.sp,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget buildActionItems(
    ActionItem actionItems, {
    bool isNotification = false,
    bool isChat = false,
    // bool lastIcon = false,
  }) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () => _onTapAppbarIcon(
            actionItems.routeName,
            clearNotification: isNotification,
            clearChat: isChat,
          ),
          child: Container(
            margin: EdgeInsets.only(right: Values.v16.w),
            child: Stack(
              children: [
                Center(
                  child: actionItems.icon == null
                      ? actionItems.iconData
                      : Image.asset(
                          height: Values.v24.h,
                          width: Values.v24.w,
                          actionItems.icon!,
                          color: UIColors.primaryGreen950,
                        ),
                ),
                isNotification
                    ? Positioned(
                        top: 1,
                        right: 0,
                        child: _notificationCounter(),
                      )
                    : isChat
                        ? Positioned(
                            top: 1,
                            right: 0,
                            child: _chatCounter(),
                          )
                        : actionItems.enableNotificationCount
                            ? Positioned(
                                top: 10,
                                right: 0,
                                child: _itemCounter(actionItems.actionItemName),
                              )
                            : const SizedBox.shrink(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget hamburger(BuildContext context) {
    return Padding(
      // padding: const EdgeInsets.only(right: Values.v16),
      padding: const EdgeInsets.only(left: 16.0),
      child: InkWell(
        onTap: () => Scaffold.of(context).openDrawer(),
        child: Image.asset(
          Assets.hamburgerIcon,
          height: Values.v36.h,
          width: Values.v36.w,
        ),
      ),
    );
  }

  // SvgPicture.asset(
  //             Assets.drawerLogo,
  //             height: 60.h,
  //             width: 60.h,
  //           ),

  Widget _fitsomniaLogo(BuildContext context) {
    return Padding(
      // padding: const EdgeInsets.all(4.0),
      // padding: const EdgeInsets.only(right: Values.v16),
      padding: EdgeInsets.only(left: Values.v20.w),
      //// Need to enable appDrawer in dashboard page
      // child: InkWell(
      //   onTap: () => Scaffold.of(context).openDrawer(),
      //   child: SvgPicture.asset(
      //     Assets.drawerLogo,
      //     height: Values.v60.h, // 40
      //     width: Values.v60.w, // 40
      //   ),
      // ),
      child: SvgPicture.asset(
        Assets.appLogo,
        height: Values.v32.h, // 40
        width: Values.v32.w, // 40
      ),
    );
  }

  void _onTapAppbarIcon(
    String routeName, {
    bool clearNotification = false,
    bool clearChat = false,
  }) {
    if (clearNotification) {
      notificationCount.value = 0;
      LocalNotificationService().clearNotifications();
    }

    if (clearChat) {
      chattingCount.value = 0;
      LocalNotificationService().clearNotifications();
    }

    Navigator.pushNamed(context, routeName);
  }

  Widget _buildSearchBar() {
    return GestureDetector(
      onTap: () => _navigateToSearchPage(),
      child: Container(
        // width: 180,
        margin: const EdgeInsets.only(top: Values.v10, bottom: Values.v10),
        padding: EdgeInsets.symmetric(
          horizontal: Values.v16.w,
        ),
        height: Values.v40.h,
        decoration: BoxDecoration(
          color: AppColors.softGrey,
          borderRadius: BorderRadius.circular(Values.v50),
          border: GradientBoxBorder(gradient: AppColors.cyanBlueGradient),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          children: [
            Text(
              "Search",
              style: AppTypography.poppinsRegular14(
                color: UIColors.fitGrey,
              ),
            ),
            GradientColorEffect.applyPrimaryGradientColor(
              SvgPicture.asset(
                height: Values.v17,
                width: Values.v17,
                Assets.search,
                colorFilter:
                    const ColorFilter.mode(UIColors.fitGrey, BlendMode.srcIn),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSmallSearchBar() {
    return GestureDetector(
      onTap: () => _navigateToSearchPage(),
      child: Container(
        padding: EdgeInsets.only(right: Values.v16.w),
        child: SvgPicture.asset(
          height: Values.v24.h,
          width: Values.v24.w,
          Assets.searchNewIcon,
          colorFilter:
              const ColorFilter.mode(UIColors.primaryGreen950, BlendMode.srcIn),
        ),
        // child: GradientColorEffect.applyPrimaryGradientColor(
        //   SvgPicture.asset(
        //     height: Values.v24.h,
        //     width: Values.v24.w,
        //     Assets.search,
        //     colorFilter:
        //         const ColorFilter.mode(UIColors.fitGrey, BlendMode.srcIn),
        //   ),
        // ),
      ),
    );
  }

  _buildRewardIcon() {
    return Padding(
      padding: EdgeInsets.only(right: Values.v16),
      child: GestureDetector(
        onTap: () {
          FirebaseService()
                  .logFeatureUsage('rewards', 'rewards', '');
          Navigator.of(context).pushNamed(Routes.rewardDashboardPage);
        },
        child: Container(
          padding:
              EdgeInsets.symmetric(horizontal: Values.v10, vertical: Values.v5),
          decoration: BoxDecoration(
              color: UIColors.primaryGreen50,
              // border: Border.all(color: UIColors.primary),
              border: GradientBoxBorder(gradient: LinearGradient(colors: [UIColors.yellow400, UIColors.primary], begin: Alignment.topCenter, end: Alignment.bottomCenter), width: 2),
              borderRadius: BorderRadius.circular(Values.v50)),
          child: Row(
            children: [
              SvgPicture.asset(
                Assets.rewardsIcon,
                height: Values.v16,
                width: Values.v16,
                colorFilter: const ColorFilter.mode(UIColors.primary, BlendMode.srcIn),
              ),
              const SizedBox(
                width: Values.v3,
              ),
              Text(
                '5K',
                style: AppTypography.poppinsSemiBold14(
                    color: UIColors.primaryGreen900),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _itemCounter(String icon) {
    switch (icon) {
      case TextConstants.actionItemsCart:
        return BlocBuilder<CartBloc, BaseState>(
          buildWhen: (previous, current) => current is SuccessState,
          builder: (context, state) {
            if (state is SuccessState) {
              CartEntity entity = state.data;

              return entity.items.isEmpty
                  ? Container()
                  : _buildCounter(
                      entity.items.length.toString(),
                    );
            } else {
              return const SizedBox();
            }
          },
        );

      case TextConstants.actionItemsWishlist:
        return BlocBuilder<GetWishlistBloc, GetWishlistState>(
          buildWhen: (previous, current) => current is GetWishlistSuccess,
          builder: (context, state) {
            if (state is GetWishlistSuccess) {
              WishlistEntity entity = state.wishlistEntity;

              return entity.items.isEmpty
                  ? Container()
                  : _buildCounter(
                      entity.items.length.toString(),
                    );
            } else {
              return const SizedBox();
            }
          },
        );

      default:
        return const SizedBox();
    }
  }

  Widget _buildCounter(String counterValue) {
    return CircleAvatar(
      backgroundColor: AppColors.primaryGreen,
      radius: 8.r,
      child: Center(
        child: Text(
          counterValue,
          style: AppTypography.regular10(
            color: AppColors.white,
          ),
        ),
      ),
    );
  }

  Widget _notificationCounter() {
    return ValueListenableBuilder<int>(
      valueListenable: notificationCount,
      builder: (BuildContext context, int value, Widget? child) {
        if (value == 0) {
          return const SizedBox();
        }

        if (value >= 10) {
          return CircleAvatar(
            backgroundColor: AppColors.red,
            radius: 8.r,
            child: Center(
              child: Text(
                '9+',
                style: AppTypography.regular8(
                  color: AppColors.white,
                ),
              ),
            ),
          );
        }

        return CircleAvatar(
          backgroundColor: AppColors.primaryGreen,
          radius: 8.r,
          child: Center(
            child: Text(
              value.toString(),
              style: AppTypography.regular10(
                color: AppColors.white,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _chatCounter() {
    return ValueListenableBuilder<int>(
      valueListenable: chattingCount,
      builder: (BuildContext context, int value, Widget? child) {
        if (value == 0) {
          return const SizedBox();
        }

        if (value >= 10) {
          return CircleAvatar(
            backgroundColor: AppColors.red,
            radius: 8.r,
            child: Center(
              child: Text(
                '9+',
                style: AppTypography.regular8(
                  color: AppColors.white,
                ),
              ),
            ),
          );
        }

        return CircleAvatar(
          backgroundColor: AppColors.primaryGreen,
          radius: 8.r,
          child: Center(
            child: Text(
              value.toString(),
              style: AppTypography.regular10(
                color: AppColors.white,
              ),
            ),
          ),
        );
      },
    );
  }

  List<ActionItem> _getActionItems() => [
        ActionItem(
          actionItemName: TextConstants.actionItemsQRScanner,
          icon: Assets.barCodeIcon,
          routeName: Routes.generateQR,
        ),
        ActionItem(
          actionItemName: TextConstants.actionItemsMessage,
          icon: Assets.messageIconNew,
          routeName: Routes.chatList,
        ),
        ActionItem(
          actionItemName: TextConstants.actionItemsNotification,
          icon: Assets.notificationsNewIcon,
          routeName: Routes.notification,
          enableNotificationCount: true,
        ),
        ActionItem(
          actionItemName: TextConstants.actionItemsCart,
          icon: Assets.appBarCartIcon,
          routeName: Routes.cart,
          enableNotificationCount: true,
        ),
        ActionItem(
          actionItemName: TextConstants.actionItemsWishlist,
          icon: Assets.appBarWishlistIcon,
          routeName: Routes.wishlist,
          enableNotificationCount: true,
        ),
        // ActionItem(
        //   actionItemName: TextConstants.settings,
        //   icon: Assets.delete,
        //   routeName: Routes.updateDiet,
        // ),
      ];

  Future<dynamic> _navigateToSearchPage() {
    return Navigator.pushNamed(
      context,
      Routes.search,
    );
  }

  Future<dynamic> _navigateToProfilePage(BuildContext context) {
    return Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => const ProfilePage(),
      ),
    );
  }

  _buildBackIcon() {
    return IconButton(
      onPressed: () {
        Navigator.pop(context);
      },
      icon: SvgPicture.asset(
        Assets.backButton,
      ),
    );
  }
}

class ActionItem {
  ActionItem({
    required this.actionItemName,
    this.icon,
    this.enableNotificationCount = false,
    this.iconData,
    required this.routeName,
  });

  String actionItemName;
  String? icon;
  Icon? iconData;
  String routeName;
  bool enableNotificationCount;
}
