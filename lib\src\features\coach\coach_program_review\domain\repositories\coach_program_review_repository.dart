import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/domain/entities/user_review_entity.dart';

abstract class CoachProgramReviewRepository {
  Future<Either<ErrorModel, List<UserReviewEntity>>> getCoachProgramReviews(
      {required String programId});
  Future<Either<ErrorModel, UserReviewEntity>> getCoachProgramReviewById(
      {required String reviewId});
  Future<Either<ErrorModel, UserReviewEntity>> reviewCoachProgram({
    required String programId,
    required Map<String, dynamic> data,
  });
  Future<Either<ErrorModel, UserReviewEntity>> deleteCoachProgramReview(
      {required String reviewId});
  Future<Either<ErrorModel, UserReviewEntity>> updateCoachProgramReview({
    required String reviewId,
    required Map<String, dynamic> data,
  });
  Future<Either<ErrorModel, List<UserReviewEntity>>> getCoachProfileReviews(
      {required String coachId});
  Future<Either<ErrorModel, UserReviewEntity>> getCoachProfileReviewById(
      {required String reviewId});
  Future<Either<ErrorModel, UserReviewEntity>> reviewCoachProfile({
    required String coachId,
    required Map<String, dynamic> data,
  });
  Future<Either<ErrorModel, UserReviewEntity>> deleteCoachProfileReview(
      {required String reviewId});
  Future<Either<ErrorModel, UserReviewEntity>> updateCoachProfileReview({
    required String reviewId,
    required Map<String, dynamic> data,
  });
}
