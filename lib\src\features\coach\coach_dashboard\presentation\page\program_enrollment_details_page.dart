import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/extensions/extensions.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/pages/chat_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/bloc/coach_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/widget/program_subscription_remove_by_coach_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/presentation/widgets/coach_single_rating_view_by_id_widget.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class ProgramEnrollmentDetailsPage extends StatefulWidget {
  const ProgramEnrollmentDetailsPage({super.key, required this.subscriptionId});
  final String subscriptionId;

  @override
  State<ProgramEnrollmentDetailsPage> createState() =>
      _ProgramEnrollmentDetailsPageState();
}

class _ProgramEnrollmentDetailsPageState
    extends State<ProgramEnrollmentDetailsPage> {
  CoachProgramEnrollmentEntity? _enrollmentEntity;
  bool _isLoading = false;

  String? coachId;

  @override
  void initState() {
    super.initState();
    coachId = context.read<CoachBloc>().coachId!;

    if (coachId != null) {
      BlocProvider.of<CoachDashboardBloc>(context).add(
          GetCoachSingleProgramEnrollmentDetailEvent(
              subscriptionId: widget.subscriptionId, coachId: coachId!));
    }

    // _enrollmentEntity = testProgramEnrollmentEntity;
    _isLoading = true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: BlocListener<CoachDashboardBloc, CoachDashboardState>(
        listener: (context, state) {
          if (state is GetCoachProgramSingleEnrollerSuccess) {
            setState(() {
              _enrollmentEntity = state.enrolledProgram;
              _isLoading = false;
            });
          }

          if (state is GetCoachProgramSingleEnrollerFail) {
            setState(() {
              _isLoading = false;
            });

            AppToast.showToast(message: 'Failed to get program details info');
          }

          if (state is ProgramSubscriptionCancelByCoachSuccess) {
            Log.debug('program subscription cancel success');
            setState(() {
              _enrollmentEntity = state.enrolledProgram;
            });

            AppToast.showToast(
                message: 'Program subscription canceled successfully');
          }

          if (state is ProgramSubscriptionCancelByCoachFail) {
            Log.debug(
                'program subscription cancel failed. Please try again later');
            AppToast.showToast(
              message: 'Subscrition cancel fail',
              backgroundColor: AppColors.error,
            );
          }
        },
        child: SafeArea(
          child: (_isLoading)
              ? const Center(
                  child: CircularProgressIndicator(
                    color: UIColors.primary,
                  ),
                )
              : Stack(
                  alignment: AlignmentDirectional.bottomCenter,
                  children: [
                    SingleChildScrollView(
                      child: Column(
                        children: [
                          _buildDetailsInfoSection(),
                          SizedBox(
                            height: 150,
                          ),
                        ],
                      ),
                    ),
                    if (_enrollmentEntity!.cancelStatus == null &&
                        _enrollmentEntity!.refundStatus == null)
                      _buildRemoveSubscriptionButton(),
                  ],
                ),
        ),
      ),
    );
  }

  _buildDetailsInfoSection() {
    String userImageUrl = Assets.spotMeNoImage;
    if (_enrollmentEntity != null && _enrollmentEntity!.userImage != null) {
      if (Uri.parse(_enrollmentEntity!.userImage!).hasAbsolutePath) {
        userImageUrl = _enrollmentEntity!.userImage!;
      }
    }

    return Container(
      decoration: BoxDecoration(
          color: AppColors.greyscale10,
          border: Border.all(color: AppColors.greyscale400),
          borderRadius: BorderRadius.circular(Values.v10)),
      margin: const EdgeInsets.all(Values.v20),
      padding: const EdgeInsets.all(Values.v16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildProgramCurrentStatusSection(),
          _buildEnrollerSection(
              name: _enrollmentEntity!.userName ?? '<empty>',
              imageUrl: userImageUrl),
          const SizedBox(height: 30),
          _buildInfoItem(
              title: 'Course name', text: _enrollmentEntity!.programTitle),
          const SizedBox(height: 30),
          _buildInfoItem(
              title: 'Payment status',
              text:
                  _enrollmentEntity!.isPaymentNeeded ? 'Need Payment' : 'Paid'),
          const SizedBox(height: 30),
          _buildInfoItem(
              title: 'Course price',
              text: _enrollmentEntity!.discountPrice.toString()),
          const SizedBox(height: 30),
          _buildInfoItem(
              title: 'Subscription date',
              text: DateFormat.yMMMMd()
                  .format(_enrollmentEntity!.subscriptionDate ?? DateTime.now())
                  .toString()),
          _buildCoachProfileReviewSection2(),
          _buildCoachProgramReviewSection2(),
          _buildChatWithStudentButton(),
        ],
      ),
    );
  }

  _buildInfoItem({required String title, required String text}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitle(title: title),
        _buildText(text: text),
      ],
    );
  }

  _buildTitle({required String title}) {
    return Row(
      children: [
        Wrap(
          children: [
            Text(
              title,
              style: AppTypography.poppinsSemiBold14(
                  color: AppColors.greyscale400),
            ),
          ],
        ),
      ],
    );
  }

  _buildText({required String text}) {
    return Wrap(
      children: [
        Text(
          text,
          overflow: TextOverflow.ellipsis,
          maxLines: 2,
          style:
              AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
        ),
      ],
    );
  }

  _buildEnrollerSection({required String name, required String imageUrl}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitle(title: 'Enroller'),
        _buildEnrollerInfo(name: name, imageUrl: imageUrl),
      ],
    );
  }

  _buildEnrollerInfo({required String name, required String imageUrl}) {
    return Row(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: (imageUrl == Assets.spotMeNoImage || imageUrl == '')
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(Values.v100),
                  child: SizedBox(
                    height: Values.v32,
                    width: Values.v32,
                    child: Image.asset(
                      Assets.spotMeNoImage,
                      fit: BoxFit.cover,
                    ),
                  ),
                )
              : ImageContainer.circularImage(
                  image: imageUrl,
                  radius: Values.v16,
                  showBorder: false,
                ),
        ),
        Expanded(child: _buildText(text: name))
      ],
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        'Enrollment Details',
        style: TextStyle(
          color: UIColors.primaryGreen950,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: true,
      elevation: 0,
    );
  }

  _buildRemoveSubscriptionButton() {
    return Row(
      children: [
        Button.filled(
          label: 'Remove Subscription',
          onPressed: () {
            Log.debug('cancel program subscription by coach');
            _showSubscriptionCancelDialog();
          },
          background: AppColors.white,
          borderColor: AppColors.white,
          textStyle: AppTypography.poppinsMedium20(color: AppColors.red),
        ),
      ],
    );
  }

  void _showSubscriptionCancelDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(Values.v10))),
          contentPadding: const EdgeInsets.all(1),
          // titlePadding: EdgeInsets.all(1),
          // actionsPadding: EdgeInsets.all(1),
          // buttonPadding: EdgeInsets.all(1),
          insetPadding: const EdgeInsets.all(Values.v10),
          content: SingleChildScrollView(
              child: ProgramSubscriptionRemoveByCoachWidget(
                  subscriptionId: _enrollmentEntity!.subscriptionId)),
        );
      },
      barrierDismissible: false,
    );
  }

  _buildProgramCurrentStatusSection() {
    return Wrap(
      spacing: Values.v10,
      runSpacing: Values.v5,
      children: [_buildSubscriptionCancelCard(), _buildRefundProcessCard()],
    );
  }

  _buildRefundProcessCard() {
    if (_enrollmentEntity != null && _enrollmentEntity!.refundStatus == null) {
      return const SizedBox.shrink();
    }

    CoachProgramSubscriptionRefundStatusInfo refundStatus =
        getCoachProgramRefundStatusEnumValue(_enrollmentEntity!.refundStatus!);

    return Chip(
      label: Text(
        refundStatus.description(),
      ),
      labelStyle: AppTypography.poppinsMedium12(color: UIColors.skyBlue500),
      backgroundColor: UIColors.skyBlue50,
      side: BorderSide(color: UIColors.skyBlue500),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(20))),
    );
  }

  _buildSubscriptionCancelCard() {
    if (_enrollmentEntity != null && _enrollmentEntity!.cancelStatus == null) {
      return const SizedBox.shrink();
    }

    CoachProgramSubscriptionCancelInfo cancenStatus =
        getCoachProgramCancelStatusEnumValue(_enrollmentEntity!.cancelStatus!);

    return Chip(
      label: Text(cancenStatus.description()),
      labelStyle: AppTypography.poppinsMedium12(color: UIColors.red500),
      backgroundColor: UIColors.red50,
      side: BorderSide(color: UIColors.red500),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(20))),
    );
  }

  // _buildCoachReviewSection() {
  //   return Row(
  //     children: [
  //       Chip(
  //         label: Row(
  //           children: [
  //             const Icon(
  //               Icons.star,
  //               color: UIColors.primaryGreen500,
  //             ),
  //             const SizedBox(
  //               width: Values.v3,
  //             ),
  //             Text(
  //               '5.0',
  //               style: AppTypography.poppinsSemiBold20(
  //                   color: UIColors.primaryGreen950),
  //             )
  //           ],
  //         ),
  //         labelStyle:
  //             AppTypography.poppinsMedium12(color: UIColors.primaryGreen900),
  //         // backgroundColor: UIColors.red50,
  //         side: const BorderSide(color: UIColors.primaryGreen500),
  //         shape: const RoundedRectangleBorder(
  //             borderRadius: BorderRadius.all(Radius.circular(Values.v100))),
  //       ),
  //     ],
  //   );
  // }

  _buildCoachProgramReviewSection2() {
    if (_enrollmentEntity!.programReviewId == null) {
      return SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(top: Values.v15, bottom: Values.v15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle(title: 'Program Review'),
          const SizedBox(
            height: Values.v10,
          ),
          CoachSingleRatingViewByIdWidget(
            reviewId: _enrollmentEntity!.programReviewId!,
            isProgramReview: true,
          ), // 'review-id'
        ],
      ),
    );
  }

  _buildCoachProfileReviewSection2() {
    if (_enrollmentEntity!.coachReviewId == null) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(top: Values.v15, bottom: Values.v15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle(title: 'Coach Review'),
          const SizedBox(
            height: Values.v10,
          ),
          CoachSingleRatingViewByIdWidget(
            reviewId: _enrollmentEntity!.coachReviewId!,
          ), // 'review-id
        ],
      ),
    );
  }

  _buildChatWithStudentButton() {
    return Padding(
      padding: const EdgeInsets.all(Values.v20),
      child: Button.filled(
        label: 'Chat With Student',
        disable: (_enrollmentEntity!.isCompleted),
        onPressed: () {
          Log.debug('chat with coach pressed');
          _navigateToChatPage();
          // AppToast.showToast(message: 'Chat with coach not available');
        },
      ),
    );
  }

  void _navigateToChatPage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(
          title: _enrollmentEntity!.userName!,
          fitBuddyId: _enrollmentEntity!.userId!,
          image: _enrollmentEntity!.userImage,
        ),
      ),
    );
  }
}
