part of 'reward_leaderboard_bloc.dart';

sealed class RewardLeaderboardEvent extends Equatable {
  const RewardLeaderboardEvent();

  @override
  List<Object> get props => [];
}

class GetRewardLeaderboardTopUserList extends RewardLeaderboardEvent {
  final int? limit;
  final int? offset;
  final String? filterType;

  GetRewardLeaderboardTopUserList({required this.limit, required this.offset, required this.filterType});
  
  @override
  List<Object> get props => [];
}

class GetUserCurrentRank extends RewardLeaderboardEvent {
  final String userId;

  GetUserCurrentRank({required this.userId});

  @override
  List<Object> get props => [userId];
}
