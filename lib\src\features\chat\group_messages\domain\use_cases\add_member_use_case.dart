import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/repository/group_repository.dart';

class AddMemberUseCase {
  const AddMemberUseCase({required this.groupRepository});

  final GroupRepository groupRepository;

  Future<Either<String, String>> call(
      {required String groupId, required String memberId}) async {
    return await groupRepository.addNewMember(
        groupId: groupId, memberId: memberId);
  }
}
