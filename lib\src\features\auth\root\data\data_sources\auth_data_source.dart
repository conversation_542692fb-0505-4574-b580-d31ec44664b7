import 'package:dio/dio.dart';

abstract class AuthRemoteDataSource {
  Future<Response> loginWithEmailAndPassword(Map<String, dynamic> data);

  Future<Response> registration(Map<String, dynamic> map);

  Future<Response> verification(Map<String, dynamic> map);

  Future<Response> loginWithGoogle();

  Future<Response> loginWithFacebook();

  Future<Response> loginWithApple();

  /// Send OTP to Verify Forgot Password Request
  Future<Response> sendOTP(Map<String, dynamic> map);

  Future<Response> identityVerification(Map<String, dynamic> map);

  Future<Response> resetPassword(Map<String, dynamic> map);
}
