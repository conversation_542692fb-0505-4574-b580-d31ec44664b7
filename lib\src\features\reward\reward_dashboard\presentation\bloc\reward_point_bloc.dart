import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_point_rank_entity.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/use_case/reward_leaderboard_use_case.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/reward_point_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/reward_point_history_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/user_login_activity_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/use_case/reward_point_use_case.dart';

part 'reward_point_event.dart';
part 'reward_point_state.dart';

class RewardPointBloc extends Bloc<RewardPointEvent, RewardPointState> {
  RewardPointBloc({required this.rewardPointUseCase})
      : super(RewardPointInitial()) {
    on<GetRewardPoints>(_onGetRewardPoints);
    on<GetUserWeeklyLoginActivity>(_onGetUserWeeklyLoginActivity);
    on<GetRewardPointHistory>(_onGetRewardPointHistory);
  }
  final RewardPointUseCase rewardPointUseCase;

  Future<void> _onGetRewardPoints(
      GetRewardPoints event, Emitter<RewardPointState> emit) async {
    try {
      emit(RewardPointLoading());

      final response = await rewardPointUseCase.getCurrentPoints();
      response.fold((l) {
        emit(GetRewardPointFail(data: l));
      }, (r) {
        emit(GetRewardPointSuccess(pointEntity: r));
      });
    } catch (e) {
      Log.debug(e.toString());
      emit(GetRewardPointFail(data: e));
    }
  }

  Future<void> _onGetUserWeeklyLoginActivity(
      GetUserWeeklyLoginActivity event, Emitter<RewardPointState> emit) async {
    try {
      emit(RewardPointLoading());
      final response =
          await rewardPointUseCase.getWeeklyLoginHistory(userId: event.userId);
      response.fold((l) {
        emit(GetUserWeeklyLoginActivityFail(data: l));
      }, (r) {
        emit(GetUserWeeklyLoginActivitySuccess(userLoginActivityEntity: r));
      });
    } catch (e) {
      Log.debug(e.toString());
      emit(GetUserWeeklyLoginActivityFail(data: e));
    }
  }

  Future<void> _onGetRewardPointHistory(
      GetRewardPointHistory event, Emitter<RewardPointState> emit) async {
    try {
      emit(RewardPointLoading());
      final response =
          await rewardPointUseCase.getRewardPointHistory(offset: event.offset, limit: event.limit, pointHistoryFilter: event.historyFilter);
      response.fold((l) {
        emit(GetRewardPointHistoryFail(data: l));
      }, (r) {
        emit(GetRewardPointHistorySuccess(history: r));
      });
    } catch (e) {
      Log.debug(e.toString());
      emit(GetRewardPointHistoryFail(data: e));
    }
  }
}
