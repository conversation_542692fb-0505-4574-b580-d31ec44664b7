import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class DeleteMessageDataSource {
  Future<Response> deleteMessage({required String messageId});
  Future<Response> deleteOneToOneMessage({required String messageId});
}

class DeleteMessageDataSourceImpl implements DeleteMessageDataSource {
  const DeleteMessageDataSourceImpl({required this.restClient});

  final RestClient restClient;

  @override
  Future<Response> deleteMessage({required String messageId}) async {
    final response = await restClient.delete(
      APIType.PROTECTED,
      API.deleteGroupChatMessage(messageId: messageId),
    );

    return response;
  }

  @override
  Future<Response> deleteOneToOneMessage({required String messageId}) async {
    final response = await restClient.delete(
      APIType.PROTECTED,
      API.deleteOneToOneMessage(messageId: messageId),
    );

    return response;
  }
}
