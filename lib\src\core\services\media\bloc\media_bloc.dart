import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/services/media/app_media_service.dart';
import 'package:fitsomnia_app/src/core/services/media/model/media_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:photo_manager/photo_manager.dart';

part 'media_event.dart';

part 'media_state.dart';

class MediaBloc extends Bloc<MediaEvent, MediaState> {
  MediaBloc({
    this.selectMultipleMedia = true,
    this.onCloseButtonTap,
    this.maxSelectionLimit = 0,
  }) : super(FileLoadingState()) {
    on<LoadMediaFilesEvent>(_onLoadMediaFiles);
    on<PreviewSelectedImageEvent>(_onPreviewImage);
  }

  final List<MediaModel> selectedFiles = [];

  /// max media selection limit
  final int maxSelectionLimit;

  /// enable/disable multiple media selection
  bool selectMultipleMedia;

  Function? onCloseButtonTap;

  /// keeps track of which image is being previewed
  int previewIndex = 0;

  /// currently previewed file
  Uint8List? previewedFile;
  final AppMediaService _appMediaService = AppMediaService();

  Future<void> _onLoadMediaFiles(
    LoadMediaFilesEvent event,
    Emitter<MediaState> emit,
  ) async {
    if (await _appMediaService.requestPermission()) {
      List<AssetEntity> mediaFiles = await _appMediaService.loadAlbums();
      emit(FileLoadedState(mediaFiles));
    }
  }

  void _onPreviewImage(
    PreviewSelectedImageEvent event,
    Emitter<MediaState> emit,
  ) {
    previewedFile = event.previewedFile;

    emit(SelectedImagePreviewState(event.previewedFile, event.mediaFiles));
  }
}
