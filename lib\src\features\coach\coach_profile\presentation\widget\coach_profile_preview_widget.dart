import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:flutter/material.dart';

class CoachProfilePreviewWidget extends StatefulWidget {
  const CoachProfilePreviewWidget(
      {super.key,
      required this.profileImage,
      required this.fullName,
      required this.experienceInYear});
  final String profileImage;
  final String fullName;
  final String? experienceInYear;

  @override
  State<CoachProfilePreviewWidget> createState() =>
      _CoachProfilePreviewWidgetState();
}

class _CoachProfilePreviewWidgetState extends State<CoachProfilePreviewWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 450,
      width: 360,
      color: AppColors.transparent,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 20,
                  color: UIColors.transparent,
                ),
              ),
              const Icon(
                Icons.close,
                color: AppColors.white,
              )
            ],
          ),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(Values.v12),
                      color: AppColors.white
                      // color: UIColors.black.withOpacity(0.30),
                    ),
                    padding: EdgeInsets.all(20),
                    child: _buildCoachPreviewWidget(),
                  ),
                ),
                Container(
                  width: 20,
                  color: UIColors.transparent,
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  _buildCoachPreviewWidget() {
    return Container(
      color: AppColors.transparent,
      child: Stack(
        alignment: Alignment.center,
        children: [
          _buildProfileImage(),
          _buildProfileInformation(),
        ],
      ),
    );
  }

  _buildProfileImage() {
    // return Container(
    //   child: Image.network(
    //     widget.profileImage,
    //     fit: BoxFit.fitHeight,
    //     // width: double.infinity,
    //     // height: double.infinity,
    //   ),
    // );

    return ImageContainer.rectangularImage(
      cornerRadius: Values.v20,
      image: widget.profileImage,
      width: double.infinity,
      height: double.infinity,
      fit: BoxFit.cover,
      errorWidget: Container(
        height: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Values.v20),
          image: DecorationImage(
            image: const Image(
              image: AssetImage(
                Assets.spotMeNoImage,
              ),
            ).image,
            fit: BoxFit.fill,
          ),
        ),
      ),
    );
  }

  _buildProfileInformation() {
    return Container(
      height: double.infinity,
      width: double.infinity,
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(Values.v18),
        gradient: _buildLinearGradientWhiteToBlack(),
        // color: UIColors.black.withOpacity(0.30),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.fullName,
            style: AppTypography.poppinsBold24(color: AppColors.white),
          ),
          Text(
            '${widget.experienceInYear} Years Experience',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: AppTypography.poppinsRegular18(color: AppColors.white),
          )
        ],
      ),
    );
  }

  LinearGradient _buildLinearGradientWhiteToBlack() {
    return LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        AppColors.white.withOpacity(0.4),
        UIColors.black.withOpacity(0.40),
      ],
    );
  }
}
