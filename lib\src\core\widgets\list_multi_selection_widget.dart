import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/widgets/list_single_selection_widget.dart';
import 'package:flutter/material.dart';

class ListMultiSelectionWidget extends StatefulWidget {
  const ListMultiSelectionWidget(
      {Key? key,
      required this.options,
      required this.onSelectCallback,
      required this.preSelectedOptions}): super(key: key);

  final List<ChoiceOption> options;
  final List<ChoiceOption> preSelectedOptions;
  final Function(List<ChoiceOption>) onSelectCallback;

  @override
  State<ListMultiSelectionWidget> createState() =>
      _ListMultiSelectionWidgetState();
}

class _ListMultiSelectionWidgetState extends State<ListMultiSelectionWidget> {
  List<ChoiceOption> selectedOptions = [];

  @override
  void initState() {
    super.initState();

    selectedOptions = widget.preSelectedOptions;
    Log.debug('init options: ${selectedOptions}');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(color: AppColors.greyscale50),
          borderRadius: BorderRadius.circular(10)),
      child: _buildItemList(),
    );
  }

  _buildItemList() {
    return ListView.builder(
      itemCount: widget.options.length,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        return _buildItemWidget(index: index);
      },
    );
  }

  _buildItemWidget({required int index}) {
    return GestureDetector(
      onTap: () {
        int removeIndex = -1;
        int len = selectedOptions.length;
        for(int i = 0; i < len; i++) {
          if(selectedOptions[i].name == widget.options[index].name){
            removeIndex = i;
          }
        }

        if(removeIndex == -1) {
          selectedOptions.add(widget.options[index]);
        } else {
          selectedOptions.removeAt(removeIndex);
        }

        // if (!selectedOptions.contains(widget.options[index])) {
        //   selectedOptions.add(widget.options[index]);
        //   Log.debug('not found');

        // } else {
        //   selectedOptions.remove(widget.options[index]);
        //   Log.debug('found');
        // }

        Log.debug('selected value: ${widget.options[index].label}');

        setState(() {
          selectedOptions = selectedOptions;
        });

         widget.onSelectCallback(selectedOptions);
      },
      child: Row(
        children: [
          _buildCheckBox(index: index),
          SizedBox(
            width: 5,
          ),
          _buildItemLabel(index: index),
        ],
      ),
    );
  }

  _buildItemLabel({required int index}) {
    return Text(
      widget.options[index].label.toString(),
      style: AppTypography.poppinsMedium14(color: UIColors.primaryGreen950),
    );
  }

  _buildCheckBox({required int index}) {
    bool isSelected = false;
    for(ChoiceOption option in selectedOptions){
      if(option.name == widget.options[index].name) {
        isSelected = true;
        break;
      }
    }

    return Checkbox(
      value: isSelected,
      onChanged: null,
      activeColor: UIColors.primary,
      checkColor: AppColors.white,
      fillColor:
          (isSelected) ? WidgetStatePropertyAll(AppColors.primaryGreen) : null,
      side: const BorderSide(color: UIColors.primary),
    );
  }
}
