import 'package:fitsomnia_app/src/features/reward/reward_dashboard/data/model/reward_point_model.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/reward_point_history_entity.dart';

class RewardPointHistoryModel extends RewardPointHistoryEntity {
  RewardPointHistoryModel({
    required super.userId,
    required super.userName,
    required super.userImage,
    required super.totalPoints,
    required super.pintType,
    required super.title,
    required super.createdAt,
    required super.earnedAt,
  });

  factory RewardPointHistoryModel.fromJson(Map<String, dynamic> json) {
    return RewardPointHistoryModel(
      userId: json['userId'] ?? '',
      userName: json['name'] ?? 'no-name',
      userImage: json['image'] ?? '',
      totalPoints: json['points'],
      pintType: json['type'],
      title: json['pointSourceName'] ?? 'No title',
      createdAt: (json['createdAt'] == null) ? null : DateTime.tryParse(json['createdAt']),
      earnedAt: (json['earnedAt'] == null) ? null : DateTime.tryParse(json['earnedAt']),
    );
  }
  RewardPointHistoryEntity toEntity() {
    return RewardPointHistoryEntity(
      userId: userId,
      userName: userName,
      userImage: userImage,
      totalPoints: totalPoints,
      pintType: pintType,
      title: title,
      createdAt: createdAt,
      earnedAt: earnedAt,
    );
  }
}
