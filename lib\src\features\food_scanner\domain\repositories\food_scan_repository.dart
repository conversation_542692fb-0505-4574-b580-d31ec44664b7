import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/entities/food_info_entity.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/entities/food_scan_image_request.dart';

abstract class FoodScanRepository {
  Future<Either<ErrorModel, FoodInfoEntity>> getFoodInfoFromSingleMedia({
    required FoodScanImageRequest request,
  });

  Future<Either<ErrorModel, FoodInfoEntity>> getFoodInfoFromFeedback({
    required FoodScanFeedbackRequest request,
  });

}