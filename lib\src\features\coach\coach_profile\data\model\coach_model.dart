import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';

class CoachModel extends CoachEntity {
  CoachModel({
    required super.name,
    required super.expertise,
    required super.highlights,
    required super.skillLevel,
    required super.about,
    required super.experienceInYear,
    required super.achievements,
    required super.media,
    required super.credentials,
    required super.identifications,
    super.coachId,
    super.userId,
    super.reviewStatus,
    super.reviewComment,
    super.reviewCount,
    super.subscriptionCount,
    required super.profileName,
    required super.coachCategoryId,
    required super.coachCategoryName,
    super.currentRating,
    super.isStarCoach,
    super.userEmail,
  });

  factory CoachModel.fromJson(Map<String, dynamic> json) {
    return CoachModel(
      name: json['legalName'] ?? json['userName'] ?? '<no-name>',
      // categories: (json['coachCategory'] == null)
      //     ? []
      //     : List<CoachCategory>.from(
      //         json['coachCategory']!.map((x) => CoachCategory.fromJson(x))),
      expertise: json['expertise'] ?? '',
      highlights: json['highlights'] ?? '',
      skillLevel: json['skillLevel'] ?? '',
      about: json['about'] ?? '',
      experienceInYear: json['experienceInYear'] ?? '',
      achievements: (json['achievements'] == null)
          ? []
          : List<String>.from(json['achievements']!.map((x) => x.toString())),
      media: (json['media'] == null)
          ? []
          : List<CoachMediaFile>.from(
              json['media']!.map((x) => CoachMediaFile.fromJson(x))),
      credentials: (json['credentials'] == null)
          ? []
          : List<CoachMediaFile>.from(
              json['credentials']!.map((x) => CoachMediaFile.fromJson(x))),
      identifications: (json['identifications'] == null)
          ? []
          : List<CoachMediaFile>.from(
              json['identifications']!.map((x) => CoachMediaFile.fromJson(x))),
      coachId: json['id'],
      userId: json['userId'],
      reviewStatus: json['reviewStatus'],
      reviewComment: json['reviewComment'],
      profileName: json['profileName'],
      coachCategoryId: json['coachCategoryId'],
      coachCategoryName: json['coachCategoryName'] ?? '',
      subscriptionCount: json['subscriptionCount'] ?? 0,
      reviewCount: json['reviewCount'] ?? 0,
      isStarCoach: json['isStarCoach'] ?? false,
      currentRating: (json['currentRating'] == null)
          ? 0
          : ((json['currentRating'] is double)
              ? json['currentRating']
              : json['currentRating'].toDouble()),
      userEmail: json['userEmail'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'legalName': name.toString(),
      // 'coachCategory': List<dynamic>.from(categories.map((x) => x.toJson())),
      'profileName': profileName.toString(),
      'coachCategoryId': coachCategoryId,
      'expertise': expertise.toString(),
      'highlights': highlights.toString(),
      'skillLevel': skillLevel.toString(),
      'experienceInYear': experienceInYear,
      'about': about.toString(),
      'achievements': List<dynamic>.from(achievements.map((x) => x.toString())),
      if (media != null)
        'media': List<dynamic>.from(media!.map((x) => x.toJson())),
      if (credentials != null)
        'credentials': List<dynamic>.from(credentials!.map((x) => x.toJson())),
      if (identifications != null && identifications!.isNotEmpty)
        'identifications':
            List<dynamic>.from(identifications!.map((x) => x.toJson())),
      if(userEmail != null) 'userEmail': userEmail,
    };
  }
}

class CoachCategory {
  final String categoryId;
  final String subCategoryId;

  CoachCategory({required this.categoryId, required this.subCategoryId});

  factory CoachCategory.fromJson(Map<String, dynamic> json) {
    return CoachCategory(
      categoryId: json['parentCategoryId'],
      subCategoryId: json['subCategoryId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'parentCategoryId': categoryId,
      'subCategoryId': subCategoryId,
    };
  }
}

class CoachMediaFile {
  final String url;
  final String mediaType;

  CoachMediaFile({required this.url, required this.mediaType});

  factory CoachMediaFile.fromJson(Map<String, dynamic> json) {
    return CoachMediaFile(url: json['url'], mediaType: json['mediaType']);
  }

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'mediaType': mediaType,
    };
  }
}
