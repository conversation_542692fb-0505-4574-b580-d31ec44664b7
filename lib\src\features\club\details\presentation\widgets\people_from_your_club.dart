import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/firebase/firebase_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_info_widget.dart';
import 'package:fitsomnia_app/src/core/widgets/loading_widget.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/entities/club_members_entity.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/club_members/club_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/widgets/caption_and_view.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/widgets/club_member_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PeopleFromYourClub extends StatelessWidget {
  const PeopleFromYourClub({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ClubMembersCubit, ClubMembersState>(
      builder: (context, state) {
        switch (state.status) {
          case ClubMembersStatus.initial:
          case ClubMembersStatus.loading:
            return _buildLoading();
          case ClubMembersStatus.success:
            return _buildMembersSection(context, state);
          case ClubMembersStatus.error:
            return InfoWidget(message: state.error);
        }
      },
    );
  }

  Widget _buildLoading() {
    return Column(
      children: [
        const CaptionAndViewAll(
          caption: TextConstants.peopleFromYourClub,
          onPressed: null,
        ),
        Container(
          height: 200,
          color: AppColors.grey6,
          child: const LoadingIndicator(),
        ),
      ],
    );
  }

  Widget _buildMembersSection(
    BuildContext context,
    ClubMembersState state,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CaptionAndViewAll(
          caption: TextConstants.peopleFromYourClub,
          onPressed: state.data.length <= 3
              ? null
              : () => _navigateToAllClubMembersPage(context),
        ),
        SizedBox(height: Values.v13.h),
        SizedBox(
          height: Values.v190.h,
          child: state.data.isEmpty
              ? const Center(
                  child: Text("No Members Found"),
                )
              : ListView.builder(
                  padding: EdgeInsets.zero,
                  scrollDirection: Axis.horizontal,
                  cacheExtent: 10,
                  itemCount: state.data.length >= 3 ? 3 : state.data.length,
                  itemBuilder: (BuildContext context, int index) {
                    ClubMemberEntity model = state.data[index];

                    return ClubMemberCard(
                      id: model.userId,
                      name: model.userName,
                      image: model.image,
                      status: model.relationStatus,
                      callback: (status) {
                        context.read<ClubMembersCubit>().updateRelationship(
                              index: index,
                              relationship: status,
                            );
                      },
                    );
                  },
                ),
        ),
      ],
    );
  }

  void _navigateToAllClubMembersPage(BuildContext context) {
    FirebaseService().logFeatureUsage('club', 'club_all_members', '');
    Navigator.pushNamed(
      context,
      Routes.peopleFromYourClub,
    );
  }
}
