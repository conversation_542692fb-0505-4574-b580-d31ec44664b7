import 'package:fitsomnia_app/src/core/di/injection_container.dart' as di;
import 'package:fitsomnia_app/src/features/auth/login/presentation/bloc/login_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/logout/presentation/cubit/logout_cubit.dart';
import 'package:fitsomnia_app/src/features/auth/reset_password/bloc/forgot_password/forgot_password_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/reset_password/bloc/identity_verification/identity_verification_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/reset_password/bloc/reset_password/reset_password_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/sign_up/presentation/bloc/sign_up_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/verification/presentation/bloc/verification_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/active_users/presentation/bloc/online_fit_buddy_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/presentation/bloc/create_group_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/presentation/bloc/group_list_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/presentation/bloc/all_users_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/recent_conversation/presentation/bloc/recent_conversation_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/chat_history/chat_history_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/delete_message/delete_message_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/image_picker/image_picker_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/blocs/image_upload/image_upload_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/add_member_bloc/add_member_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/group_chat/group_chat_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/group_members/group_members_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/blocs/leave_group/leave_group_bloc.dart';
import 'package:fitsomnia_app/src/features/club/dashboard/presentation/bloc/club_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/club/club_bloc.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/club_members/club_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/live_members/live_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/nearby_clubs_members/nearby_clubs_members_cubit.dart';
import 'package:fitsomnia_app/src/features/club/details/presentation/bloc/spot_not/spot_not_cubit.dart';
import 'package:fitsomnia_app/src/features/club/nearby_clubs/presentation/bloc/nearby_clubs_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/bloc/coach_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/bloc/coach_program_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/bloc/coach_profile_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/presentation/bloc/coach_program_review_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_withdraw_payment/presentation/bloc/withdraw_payment_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/student_dashboard/presentation/bloc/student_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/bloc/dashboard_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/presentation/bloc/activity_level/activity_level_bloc.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/presentation/bloc/diet_plan/diet_plan_bloc.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/presentation/bloc/ideal_calories/ideal_calories_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/presentation/bloc/weight_unit/weight_unit_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/bloc/delete_consumed_food/delete_consumed_food_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/bloc/food_consumption/food_consumption_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/track_diet/presentation/bloc/diet_history/diet_history_bloc.dart';
import 'package:fitsomnia_app/src/features/diet/track_diet/presentation/bloc/water_consumption/water_cubit.dart';
import 'package:fitsomnia_app/src/features/event/presentation/bloc/event_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/add_new_address/presentation/bloc/add_billing_address_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/add_new_address/presentation/bloc/add_shipping_address_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/select_address/bloc/billing_address_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/select_address/bloc/select_shipping_address_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/update_address/bloc/update_billing_address_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/address/update_address/bloc/update_shipping_address_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/cart/presentations/bloc/cart_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/confirm/presentation/bloc/order_summary_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/confirm/presentation/bloc/payment_methods_cubit.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/presentation/bloc/fit_market_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/presentation/bloc/add_wishlist/add_to_wishlist_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/presentation/bloc/get_wishlist/get_wishlist_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/fit_market_dashboard/wishlist/presentation/bloc/remove_from_wishlist/remove_from_wishlist_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/order/presentation/bloc/create_order/create_order_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/order/presentation/bloc/order_details/order_details_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/order/presentation/bloc/order_history/order_history_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/payment/presentation/bloc/make_payment/make_payment_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/product_details/presentation/bloc/product_details_bloc.dart';
import 'package:fitsomnia_app/src/features/fit_market/review/presentation/bloc/create_review/create_review_bloc.dart';
import 'package:fitsomnia_app/src/features/fitbot/fitbot_chat/presentation/bloc/fitbot_chat_bloc.dart';
import 'package:fitsomnia_app/src/features/food_scanner/presentation/bloc/bloc/food_scan_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/challenge_details/presentation/bloc/upload_challenge_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/dashboard/presentation/bloc/accept_challenge/accept_challenge_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/dashboard/presentation/bloc/points/points_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/history/points_history/presentation/bloc/points_history_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/leaderboard/presentation/bloc/leaderboard_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/monthly_challenges/presentation/bloc/challenge_history/challenge_history_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/monthly_challenges/presentation/bloc/monthly_challenge_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/comment_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/create_post/create_post_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/search_location/search_location_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/bloc/edit_post_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/feed_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/likers_info_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/story_bloc.dart';
import 'package:fitsomnia_app/src/features/home/<USER>/presentation/bloc/story_viewers_bloc.dart';
import 'package:fitsomnia_app/src/features/notification/presentation/bloc/notification_bloc.dart';
import 'package:fitsomnia_app/src/features/notification/presentation/cubit/update_fcm_token_cubit.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:fitsomnia_app/src/features/planner/presentation/bloc/planner_bloc.dart';
import 'package:fitsomnia_app/src/features/polls/presentation/bloc/team_poll_bloc/bloc/team_poll_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/bench_squat_unit/bench_squat_unit_cubit.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/block_fit_buddy/block_fit_buddy_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/feed_video/feed_video_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/fit_buddies/fit_buddies_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/like_unlike_post/like_or_unlike_cubit.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/post_comments/post_comments_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/post_details/post_details_cubit.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/spot_back/spot_back_cubit.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/timeline/timeline_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/unfriend_fit_buddy/unfriend_fit_buddy_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/bloc/user_profile/user_profile_bloc.dart';
import 'package:fitsomnia_app/src/features/qr_code/scan_qr_code/presentation/bloc/scan_qr_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/daily_task/presentation/bloc/daily_task_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/presentation/bloc/reward_leaderboard_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/referrals/presentation/bloc/referral_bloc.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/presentation/bloc/reward_point_bloc.dart';
import 'package:fitsomnia_app/src/features/search/presentation/bloc/club/club_search_cubit.dart';
import 'package:fitsomnia_app/src/features/search/presentation/bloc/people/people_search_cubit.dart';
import 'package:fitsomnia_app/src/features/search/presentation/bloc/product/product_search_cubit.dart';
import 'package:fitsomnia_app/src/features/search/presentation/bloc/search/search_view_cubit.dart';
import 'package:fitsomnia_app/src/features/settings/notifications/presentation/bloc/notification_bloc.dart';
import 'package:fitsomnia_app/src/features/settings/privacy/block_list/presentation/bloc/block_bloc.dart';
import 'package:fitsomnia_app/src/features/settings/privacy/profile_visibility/bloc/profile_visibility_bloc.dart';
import 'package:fitsomnia_app/src/features/settings/security/delete_account/presentation/bloc/delete_account_cubit.dart';
import 'package:fitsomnia_app/src/features/settings/security/email/presentation/bloc/change_email_bloc.dart';
import 'package:fitsomnia_app/src/features/settings/security/password/presentation/bloc/change_password_bloc.dart';
import 'package:fitsomnia_app/src/features/spot_not/delete_profile/presentation/bloc/delete_spot_profile_cubit.dart';
import 'package:fitsomnia_app/src/features/spot_not/near_by_user_profile/presentation/bloc/near_by_user_profile_bloc.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/presentation/bloc/spot_bloc/spot_bloc.dart';
import 'package:fitsomnia_app/src/features/spot_not/root/presentation/bloc/spot_not_bloc.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_me/presentation/bloc/spot_me_bloc.dart';
import 'package:fitsomnia_app/src/features/spot_not/spot_not_filter/presentation/bloc/spot_not_filter_bloc.dart';
import 'package:fitsomnia_app/src/features/training/body_building_program_details/presentation/bloc/intermediate_training_bloc.dart';
import 'package:fitsomnia_app/src/features/training/body_building_program_selection/presentation/bloc/body_building_bloc.dart';
import 'package:fitsomnia_app/src/features/training/exercise_category/presentation/bloc/exercise_category_bloc.dart';
import 'package:fitsomnia_app/src/features/training/exercise_list/presentation/bloc/exercise_list_bloc.dart';
import 'package:fitsomnia_app/src/features/training/muscle_group_selection/presentation/bloc/muscle_group_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class GlobalBlocProviders {
  dynamic providers = [
    BlocProvider(create: (_) => di.sl<LoginBloc>()),
    BlocProvider(create: (_) => di.sl<SignUpBloc>()),
    BlocProvider(create: (_) => di.sl<VerificationBloc>()),
    BlocProvider(create: (_) => di.sl<ForgotPasswordBloc>()),
    BlocProvider(create: (_) => di.sl<IdentityVerificationBloc>()),
    BlocProvider(create: (_) => di.sl<ResetPasswordBloc>()),
    BlocProvider(create: (_) => di.sl<NotificationBloc>()),

    ///Story
    BlocProvider(create: (_) => di.sl<StoryBloc>()),
    BlocProvider(create: (_) => di.sl<StoryViewersBloc>()),

    BlocProvider(create: (_) => di.sl<UserProfileBloc>()),
    BlocProvider(create: (_) => di.sl<SpotNotFilterBloc>()),
    BlocProvider(create: (_) => di.sl<PlannerBloc>()),
    BlocProvider(create: (_) => di.sl<SpotNotBloc>()),
    BlocProvider(create: (_) => di.sl<NearByUserProfileBloc>()),
    BlocProvider(create: (_) => di.sl<SharedBloc>()),
    BlocProvider(create: (_) => di.sl<CreatePostBloc>()),
    BlocProvider(create: (_) => di.sl<FeedBloc>()),
    BlocProvider(create: (_) => di.sl<CreatePostBloc>()),
    BlocProvider(create: (_) => di.sl<EditPostBloc>()),

    BlocProvider(create: (_) => di.sl<ScanQrBloc>()),
    BlocProvider(create: (_) => di.sl<SearchLocationBloc>()),
    BlocProvider(create: (_) => di.sl<CommentBloc>()),
    BlocProvider(create: (_) => di.sl<AllUsersBloc>()),
    BlocProvider(create: (_) => di.sl<RecentConversationBloc>()),
    BlocProvider(create: (_) => di.sl<CreateGroupBloc>()),
    BlocProvider(create: (_) => di.sl<GroupListBloc>()),
    BlocProvider(create: (_) => di.sl<GroupChatBloc>()),
    BlocProvider(create: (_) => di.sl<DeleteMessageBloc>()),
    BlocProvider(create: (_) => di.sl<GroupMembersBloc>()),
    BlocProvider(create: (_) => di.sl<LeaveGroupBloc>()),
    BlocProvider(create: (_) => di.sl<ChatHistoryBloc>()),
    BlocProvider(create: (_) => di.sl<AddMemberBloc>()),
    BlocProvider(create: (_) => di.sl<OnlineFitBuddyBloc>()),
    BlocProvider(create: (_) => di.sl<ImagePickerBloc>()),
    BlocProvider(create: (_) => di.sl<ImageUploadBloc>()),
    BlocProvider(create: (_) => di.sl<ExerciseListBloc>()),
    BlocProvider(create: (_) => di.sl<ExerciseCategoryBloc>()),
    BlocProvider(create: (_) => di.sl<ChangePasswordBloc>()),
    BlocProvider(create: (_) => di.sl<ProfileVisibilityBloc>()),
    BlocProvider(create: (_) => di.sl<UpdateProfileVisibilityBloc>()),
    BlocProvider(create: (_) => di.sl<GetNotificationSettingsBloc>()),
    BlocProvider(
      create: (_) => di.sl<UpdateNotificationSettingsBloc>(),
    ),
    BlocProvider(create: (_) => di.sl<ChangeEmailBloc>()),
    BlocProvider(create: (_) => di.sl<MonthlyChallengeBloc>()),
    BlocProvider(create: (_) => di.sl<AcceptChallengeBloc>()),
    BlocProvider(create: (_) => di.sl<ChallengeHistoryBloc>()),
    BlocProvider(create: (_) => di.sl<PointsBloc>()),
    BlocProvider(create: (_) => di.sl<UploadChallengeBloc>()),
    BlocProvider(create: (_) => di.sl<LeaderboardBloc>()),
    BlocProvider(create: (_) => di.sl<PointsHistoryBloc>()),
    BlocProvider(create: (_) => di.sl<MuscleGroupBloc>()),
    BlocProvider(create: (_) => di.sl<BodyBuildingBloc>()),
    BlocProvider(create: (_) => di.sl<IntermediateTrainingBloc>()),
    BlocProvider(create: (_) => di.sl<FeedVideoBloc>()),
    BlocProvider(create: (_) => di.sl<FitBuddiesBloc>()),
    BlocProvider(create: (_) => di.sl<BlockFitBuddyBloc>()),
    BlocProvider(create: (_) => di.sl<TimelineBloc>()),
    BlocProvider(create: (_) => di.sl<PostCommentsBloc>()),
    BlocProvider(create: (_) => di.sl<FitMarketDashboardBloc>()),
    BlocProvider(create: (_) => di.sl<ProductDetailsBloc>()),
    BlocProvider(create: (_) => di.sl<CartBloc>()),
    BlocProvider(create: (_) => di.sl<SelectShippingAddressBloc>()),
    BlocProvider(create: (_) => di.sl<AddShippingAddressBloc>()),
    BlocProvider(create: (_) => di.sl<UpdateShippingAddressBloc>()),
    BlocProvider(create: (_) => di.sl<UnfriendFitBuddyBloc>()),
    BlocProvider(create: (_) => di.sl<SpotMeBloc>()),
    BlocProvider(create: (_) => di.sl<AddToWishlistBloc>()),
    BlocProvider(create: (_) => di.sl<RemoveFromWishlistBloc>()),
    BlocProvider(create: (_) => di.sl<GetWishlistBloc>()),
    BlocProvider(create: (_) => di.sl<BillingAddressBloc>()),
    BlocProvider(create: (_) => di.sl<AddBillingAddressBloc>()),
    BlocProvider(create: (_) => di.sl<UpdateBillingAddressBloc>()),
    BlocProvider(create: (_) => di.sl<OrderHistoryBloc>()),
    BlocProvider(create: (_) => di.sl<CreateOrderBloc>()),
    BlocProvider(create: (_) => di.sl<OrderDetailsBloc>()),
    BlocProvider(create: (_) => di.sl<OrderSummaryBloc>()),
    BlocProvider(create: (_) => di.sl<SpotBloc>()),
    BlocProvider(create: (_) => di.sl<DashboardCubit>()),
    BlocProvider(create: (_) => di.sl<PaymentMethodsCubit>()),
    BlocProvider(create: (_) => di.sl<SpotBackCubit>()),
    BlocProvider(create: (_) => di.sl<PostDetailsCubit>()),
    BlocProvider(create: (_) => di.sl<UpdateFcmTokenCubit>()),
    BlocProvider(create: (_) => di.sl<LogoutCubit>()),
    BlocProvider(create: (_) => di.sl<DeleteSpotProfileCubit>()),

    /// Diet Module
    BlocProvider(create: (_) => di.sl<DietPlanBloc>()),
    BlocProvider(create: (_) => di.sl<IdealCaloriesCubit>()),
    BlocProvider(create: (_) => di.sl<FoodConsumptionCubit>()),
    BlocProvider(create: (_) => di.sl<DietHistoryBloc>()),
    BlocProvider(create: (_) => di.sl<WaterCubit>()),
    BlocProvider(create: (_) => di.sl<ActivityLevelBloc>()),
    BlocProvider(create: (_) => di.sl<DeleteConsumedFoodCubit>()),
    BlocProvider(create: (_) => di.sl<WeightUnitCubit>()),

    /// Search - Global
    BlocProvider(create: (_) => di.sl<SearchViewCubit>()),
    BlocProvider(create: (_) => di.sl<PeopleSearchCubit>()),
    BlocProvider(create: (_) => di.sl<ClubSearchCubit>()),
    BlocProvider(create: (_) => di.sl<ProductSearchCubit>()),

    /// Club
    BlocProvider(create: (_) => di.sl<ClubDashboardBloc>()),
    BlocProvider(create: (_) => di.sl<ClubBloc>()),
    BlocProvider(create: (_) => di.sl<NearbyClubsBloc>()),
    BlocProvider(create: (_) => di.sl<PeopleSearchCubit>()),
    BlocProvider(create: (_) => di.sl<ClubSearchCubit>()),
    BlocProvider(create: (_) => di.sl<ProductSearchCubit>()),
    BlocProvider(create: (_) => di.sl<LiveMembersCubit>()),
    BlocProvider(create: (_) => di.sl<ClubMembersCubit>()),
    BlocProvider(create: (_) => di.sl<NearbyClubsMembersCubit>()),
    
    /// Payment
    BlocProvider(create: (context) => di.sl<MakePaymentBloc>()),

    /// Settings
    BlocProvider(create: (_) => di.sl<BlockUserBloc>()),

    /// Review
    BlocProvider(create: (_) => di.sl<CreateReviewBloc>()),

    /// Spot Not Request Management
    BlocProvider(create: (_) => di.sl<SpotNotCubit>()),

    BlocProvider(create: (_) => di.sl<LikeOrUnlikeCubit>()),

    /// Likers Info
    BlocProvider(create: (_) => di.sl<LikersInfoBloc>()),

    /// Delete Account
    BlocProvider(create: (_) => di.sl<DeleteAccountCubit>()),

    // Profile
    BlocProvider(create: (_) => di.sl<BenchSquatUnitCubit>()),

    // Fitbot
    BlocProvider(create: (_) => di.sl<FitbotChatBloc>()),

    //poll
    BlocProvider(create: (_) => di.sl<TeamPollBloc>()),

    /// Event
    BlocProvider(create: (_) => di.sl<EventBloc>()),

    /// Coach
    BlocProvider(create: (_) => di.sl<CoachBloc>()),
    BlocProvider(create: (_) => di.sl<CoachProfileBloc>()),
    BlocProvider(create: (_) => di.sl<CoachProgramBloc>()),
    BlocProvider(create: (_) => di.sl<CoachDashboardBloc>()),
    BlocProvider(create: (_) => di.sl<StudentDashboardBloc>()),
    BlocProvider(create: (_) => di.sl<CoachNewsfeedBloc>()),
    BlocProvider(create: (_) => di.sl<CoachProgramReviewBloc>()),
    BlocProvider(create: (_) => di.sl<WithdrawPaymentBloc>()),

    //rewards & referral
    BlocProvider(create: (_) => di.sl<RewardPointBloc>()),
    BlocProvider(create: (_) => di.sl<RewardLeaderboardBloc>()),
    BlocProvider(create: (_) => di.sl<ReferralBloc>()),
    BlocProvider(create: (_) => di.sl<DailyTaskBloc>()),
    BlocProvider(create: (_) => di.sl<FoodScanBloc>()),

  ];
}
