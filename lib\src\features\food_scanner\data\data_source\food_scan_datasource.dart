import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/entities/food_scan_image_request.dart';
import 'package:http_parser/http_parser.dart';

abstract class FoodScanDatasource {
  Future<Response> getFoodInfoFromSingleMedia({
    required FoodScanImageRequest request,
  });

  Future<Response> getFoodInfoFromFeedback({
    required FoodScanImageRequest request,
  });
}

class FoodScanDatasourceImpl extends FoodScanDatasource {
  final RestClient restClient;

  FoodScanDatasourceImpl({required this.restClient});

  @override
  Future<Response> getFoodInfoFromFeedback(
      {required FoodScanImageRequest request}) {
    // TODO: implement getFoodInfoFromFeedback
    throw UnimplementedError();
  }

  @override
  Future<Response> getFoodInfoFromSingleMedia(
      {required FoodScanImageRequest request}) async {
    final response = await restClient.postFormData(
      APIType.PROTECTED,
      API.foodScannerAnalyze,
      {
        'file': await MultipartFile.fromFile(
          request.filePath,
          contentType: MediaType(request.mediaType!, request.fileExtention!),
        ),
        // 'featureName': 'food-scanner'
      },
    );

    return response;
  }
}
