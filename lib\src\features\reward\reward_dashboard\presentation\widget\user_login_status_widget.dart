import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/extensions/date_time.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/user_login_activity_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/presentation/bloc/reward_point_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';

class UserLoginStatusWidget extends StatefulWidget {
  const UserLoginStatusWidget({
    super.key,
    required this.userId,
  });
  final String userId;

  @override
  State<UserLoginStatusWidget> createState() => _UserLoginStatusWidgetState();
}

class _UserLoginStatusWidgetState extends State<UserLoginStatusWidget> {
  UserLoginActivityEntity? _userLoginData;
  bool _isLoading = false;

  @override
  void initState() {
    //TODO: get user daily login data
    BlocProvider.of<RewardPointBloc>(context)
        .add(GetUserWeeklyLoginActivity(userId: widget.userId));
    _isLoading = true;

    // _createTestData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RewardPointBloc, RewardPointState>(
      listener: (context, state) {
        if (state is GetUserWeeklyLoginActivitySuccess) {
          setState(() {
            _userLoginData = state.userLoginActivityEntity;
            _isLoading = false;
          });
        }

        if (state is GetUserWeeklyLoginActivityFail) {

          var errorModel = state.data as ErrorModel;
          AppToast.showToast(message: errorModel.message ?? 'Unknown Error', backgroundColor: AppColors.error, gravity: ToastGravity.BOTTOM);

          setState(() {
            _userLoginData = null;
            _isLoading == false;
          });
        }
      },
      child: (_isLoading)
          ? const Center(
              child: CircularProgressIndicator(
                color: UIColors.primary,
              ),
            )
          : (_userLoginData == null)
              ? const SizedBox(
                  height: 200,
                  child: Center(
                    child: Text('No login history found'),
                  ),
                )
              : _buildDayList(),
    );
  }

  _createTestData() {
    // userLoginData = List.generate(7, (itemPosition) {
    //   DateTime date = DateTime.now().add(Duration(days: itemPosition - 4));
    //   Log.debug('${itemPosition} : ${date.toString()}');

    //   bool isUpcomming = false;
    //   bool isPresent = false;
    //   bool isAbsent = true;
    //   if (date.compareTo(DateTime.now()) > 0) {
    //     isUpcomming = true;
    //     isPresent = false;
    //     isAbsent = false;
    //   } else {
    //     isPresent = (itemPosition < 2);
    //     isAbsent = !isPresent;
    //   }

    //   return UserLoginStatusEntity(
    //       date: date.toString(),
    //       isPresent: isPresent,
    //       isAbsent: isAbsent,
    //       isUpcomming: isUpcomming);
    // });
    Log.debug('create test data');
  }

  Widget _buildDayList() {
    return Container(
      height: 80,
      child: ListView.separated(
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        itemCount: _userLoginData!.loginHistory.length,
        itemBuilder: (context, index) {
          return SingleDayLoginStatusWidget(
            loginStatusEntity: _userLoginData!.loginHistory[index],
          );
        },
        separatorBuilder: (context, index) {
          return SizedBox(
            width: Values.v8,
          );
        },
      ),
    );
  }
}

class UserLoginStatusEntity {
  final String date;
  final bool isPresent;
  final bool isAbsent;
  final bool isUpcomming;

  UserLoginStatusEntity({
    required this.date,
    required this.isPresent,
    required this.isAbsent,
    required this.isUpcomming,
  });
}

class SingleDayLoginStatusWidget extends StatelessWidget {
  SingleDayLoginStatusWidget({super.key, required this.loginStatusEntity});
  final DailyLoginActivity loginStatusEntity;
  bool _isToday = false;

  @override
  Widget build(BuildContext context) {
    bool isPresent = loginStatusEntity.loginStatusType ==
        UserDailyLoginActivityStatus.LOGIN_SUCCEDD;
    bool isAbsetn = loginStatusEntity.loginStatusType ==
        UserDailyLoginActivityStatus.LOGIN_FAIL;
    bool isUpcomming = loginStatusEntity.loginStatusType ==
        UserDailyLoginActivityStatus.UPCOMMING;

    _isToday = loginStatusEntity.date.isSameDate(DateTime.now());

    if (isUpcomming) {
      return _buildUpcomming();
    } else if (isAbsetn) {
      return _buildAbsent();
    } else if (isPresent) {
      return _buildPresent();
    } else {
      return SizedBox.shrink();
    }
  }

  _buildPresent() {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
              color: UIColors.greenDeep,
              borderRadius: BorderRadius.circular(Values.v100)),
          height: 40,
          width: 40,
          child: Center(
            child: Text(
              '${loginStatusEntity.weekDayName}',
              style: AppTypography.poppinsMedium12(color: AppColors.white),
            ),
          ),
        ),
        if (_isToday)
          Image.asset(
            Assets.downIconImg,
            height: 20,
            width: 20,
          ),
      ],
    );
  }

  _buildAbsent() {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
              color: UIColors.red500,
              borderRadius: BorderRadius.circular(Values.v100)),
          height: 40,
          width:40,
          child: Center(
            child: Text('${loginStatusEntity.weekDayName}',
                style: AppTypography.poppinsMedium12(color: AppColors.white)),
          ),
        ),
        if (_isToday)
          Image.asset(
            Assets.downIconImg,
            height: 20,
            width: 20,
          ),
      ],
    );
  }

  _buildUpcomming() {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
              color: UIColors.primaryGreen50,
              borderRadius: BorderRadius.circular(Values.v100)),
          height: 40,
          width: 40,
          child: Center(
            child: Text('${loginStatusEntity.weekDayName}',
                style: AppTypography.poppinsMedium12(
                    color: AppColors.primaryGreen900)),
          ),
        ),
        if (_isToday)
          Image.asset(
            Assets.downIconImg,
            height: 20,
            width: 20,
          ),
      ],
    );
  }
}
