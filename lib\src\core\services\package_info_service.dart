import 'package:package_info_plus/package_info_plus.dart';

class PackageInfoService {
  factory PackageInfoService() {
    if (_instance == null) {
      init();
    }

    return _instance!;
  }

  static PackageInfoService? _instance;
  String? appName;
  String? packageName;
  String? version;
  String? buildNumber;

  PackageInfoService._internal() {
    PackageInfo.fromPlatform().then((packageInfo) => retrieveInfo(packageInfo));
  }

  static init() {
    _instance = PackageInfoService._internal();
  }

  static PackageInfoService get instance {
    if (_instance == null) {
      init();
    }

    return _instance!;
  }

  void retrieveInfo(PackageInfo packageInfo) {
    appName = packageInfo.appName;
    packageName = packageInfo.packageName;
    version = packageInfo.version;
    buildNumber = packageInfo.buildNumber;
  }
}
