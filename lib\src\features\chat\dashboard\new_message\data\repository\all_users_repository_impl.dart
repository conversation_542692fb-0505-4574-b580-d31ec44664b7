import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/data/data_source/all_users_data_source.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/data/model/all_users_model.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/domain/entity/all_users_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/domain/repository/all_users_repository.dart';

class AllUsersRepositoryImpl implements AllUsersRepository {
  const AllUsersRepositoryImpl({required this.allUsersDataSource});

  final AllUsersDataSource allUsersDataSource;

  @override
  Future<Either<String, List<AllUsersEntity>>> getAllUsers(
      {String name = ""}) async {
    try {
      final Response response = await allUsersDataSource.getAllData(name: name);
      final data = response.data['data'];

      List<AllUsersModel> models = data
          .map<AllUsersModel>((users) => AllUsersModel.fromJson(users))
          .toList();

      return Right(models);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left(e.toString());
    }
  }
}
