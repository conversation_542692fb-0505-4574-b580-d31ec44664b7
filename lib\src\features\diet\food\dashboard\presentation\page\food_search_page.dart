import 'dart:async';

import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/core/widgets/loading_widget.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/bloc/recent_foods/recent_foods_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/widgets/food_card.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/widgets/search_food_bar.dart';
import 'package:fitsomnia_app/src/features/diet/food/root/data/model/food_response_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fitsomnia_app/src/core/di/injection_container.dart' as di;
import 'package:flutter_svg/svg.dart';

class FoodSearchPage extends StatelessWidget {
  const FoodSearchPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: BlocProvider(
        create: (context) => di.sl<VendorFoodsCubit>(),
        child: _FoodSearchView(),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: Text(
        'Search Food',
        style: AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen900),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: true,
      elevation: 0,
    );
  }
}

class _FoodSearchView extends StatefulWidget {
  const _FoodSearchView({Key? key}) : super(key: key);

  @override
  State<_FoodSearchView> createState() => __FoodSearchViewState();
}

class __FoodSearchViewState extends State<_FoodSearchView> {
  
  @override
  void initState(){
    super.initState();

    // context.read<VendorFoodsCubit>().foods.clear();
    // context.read<VendorFoodsCubit>().searchField.text = 'vegetables';
    // context.read<VendorFoodsCubit>().fetchVendorFoods();
  }
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.symmetric(horizontal: Values.v16, vertical: Values.v16),
      child: Column(
        children: [
          _buildSearchFoodBar(),
          Expanded(child: _buildFoodList()),
        ],
      ),
    );
  }

  Widget _buildSearchFoodBar() {
    return _FoodSearchBar();
  }

  Widget _buildFoodList() {
    return _SearchFoodList();
  }
}

class _FoodSearchBar extends StatefulWidget {
  const _FoodSearchBar({super.key});

  @override
  State<_FoodSearchBar> createState() => _FoodSearchBarState();
}

class _FoodSearchBarState extends State<_FoodSearchBar> {
  Timer? _timer;
  final Duration _debounceDuration = const Duration(milliseconds: 1000);

  @override
  Widget build(BuildContext context) {
    VendorFoodsCubit bloc = BlocProvider.of<VendorFoodsCubit>(context);

    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(horizontal: 6),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: _boxDecoration().copyWith(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: TextFormField(
              onChanged: (_) {
                _onChanged();
              },
              controller: bloc.searchField,
              decoration: InputDecoration(
                contentPadding:
                    const EdgeInsets.fromLTRB(12.0, 0.0, 12.0, 12.0),
                border: InputBorder.none,
                hintText: TextConstants.search,
                hintStyle: AppTypography.poppinsRegular18(
                  color: UIColors.fitGrey,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Visibility(
            visible: bloc.searchField.text.isNotEmpty,
            child: InkWell(
              onTap: bloc.reset,
              child: const Icon(
                Icons.close,
                color: AppColors.black54,
              ),
            ),
          ),
          const Icon(
            Icons.search,
            color: UIColors.fitGrey,
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _onChanged() {
    if (_timer?.isActive ?? false) {
      _timer?.cancel();
    }
    _timer = Timer(_debounceDuration, () {
      _executeSearch();
    });
  }

  void _executeSearch() {
    if(context.read<VendorFoodsCubit>().searchField.text == '') {
      return;
    }
    context.read<VendorFoodsCubit>().foods.clear();
    context.read<VendorFoodsCubit>().fetchVendorFoods();
  }

  BoxDecoration _boxDecoration() {
    return BoxDecoration(
      color: AppColors.grey6,
      border: Border.all(color: AppColors.grey6),
      borderRadius: BorderRadius.circular(8),
    );
  }
}

class _SearchFoodList extends StatefulWidget {
  const _SearchFoodList({super.key});

  @override
  State<_SearchFoodList> createState() => __SearchFoodListState();
}

class __SearchFoodListState extends State<_SearchFoodList> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VendorFoodsCubit, BaseState>(builder: (context, state) {
      if (state is LoadingState) {
        return Container(
          alignment: Alignment.center,
          child: CircularProgressIndicator(color: UIColors.primary,),
        );
      } else if (state is SuccessState) {
        bool hasFoodItems =
            BlocProvider.of<VendorFoodsCubit>(context).foods.isNotEmpty;
        if (hasFoodItems) {
          return _buildFoodsList();
        } else {
          return Center(child: Container(child: Text('No Food Data', style: AppTypography.poppinsSemiBold20(color: AppColors.greyscale400),),),);
        }
      }

      return Center(child: Container(child: Text('Search Your Food !', style: AppTypography.poppinsSemiBold20(color: AppColors.greyscale400),),),);
    });
  }

  Widget _buildFoodsList() {
    var foods = BlocProvider.of<VendorFoodsCubit>(context).foods;

    return Padding(
      padding: EdgeInsets.only(top: Values.v20),
      child: ListView.separated(
        shrinkWrap: true,
        itemCount: foods.length,
        itemBuilder: (context, index) {
          return FoodItemCard(
            food: foods[index],
          );
        },
        separatorBuilder: (BuildContext context, int index) {
          return Divider(
            height: 10,
            thickness: 1,
            color: AppColors.greyscale100,
          );
        },
      ),
    );
  }
}

class FoodItemCard extends StatelessWidget {
  const FoodItemCard({required this.food, super.key});

  final FoodModel food;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Log.debug('navigate to food details page');
        Navigator.of(context).pushNamed(Routes.searchFoodDetailsPage, arguments: [food]);
      },
      child: Container(
        // margin: EdgeInsets.only(
        //   left: Values.v20,
        // ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFoodImage(),
            _buildFoodInfo(),
          ],
        ),
      ),
    );
  }

  _buildFoodImage() {
    if (food.foodImage == null) {
      return Container(
        decoration: BoxDecoration(
            border: Border.all(color: AppColors.greyscale100),
            borderRadius: BorderRadius.circular(Values.v8)),
        child: Image.asset(
          Assets.noFoodImage,
          height: Values.v80,
          width: Values.v80,
        ),
      );
    }

    return ImageContainer.rectangularImage(
      cornerRadius: Values.v10,
      image: food.foodImage,
      width: Values.v80,
      height: Values.v80,
      fit: BoxFit.fill,
      useOriginal: true,
      hideLoadingIndicator: true,
      errorWidget: Container(
        height: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Values.v10),
          image: DecorationImage(
            image: const Image(
              image: AssetImage(
                Assets.noFoodImage,
              ),
            ).image,
            fit: BoxFit.fill,
          ),
        ),
      ),
    );
  }

  _buildFoodInfo() {
    return Flexible(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              food.name,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: AppTypography.poppinsSemiBold16(
                  color: UIColors.primaryGreen950),
            ),
            Text(
              '${food.description}',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style:
                  AppTypography.poppinsMedium16(color: AppColors.greyscale400),
            ),
          ],
        ),
      ),
    );
  }
}
