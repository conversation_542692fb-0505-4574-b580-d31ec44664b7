part of 'delete_message_bloc.dart';

class DeleteMessageEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class DeleteGroupChatMessageEvent extends DeleteMessageEvent {
  final String messageId;

  DeleteGroupChatMessageEvent({required this.messageId});
}

class DeleteOneToOneChatMessageEvent extends DeleteMessageEvent {
  final String messageId;

  DeleteOneToOneChatMessageEvent({required this.messageId});
}
