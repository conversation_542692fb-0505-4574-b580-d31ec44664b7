import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

class SupportPage extends StatefulWidget {
  const SupportPage({super.key});

  @override
  State<SupportPage> createState() => _SupportPageState();
}

class _SupportPageState extends State<SupportPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Center(
        child: _buildBody(),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: Text(
        TextConstants.support,
        style: AppTypography.poppinsBold20(color: UIColors.primaryGreen900),
      ),
      centerTitle: true,
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Values.v20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildSupportImage(),
          SizedBox(
            height: 10,
          ),
          _buildTextSection(),
          SizedBox(
            height: 10,
          ),
          _buildSupportedContactSection()
        ],
      ),
    );
  }

  _buildSupportImage() {
    return Container(
      child: Image.asset(
        Assets.supportImg,
        width: 300,
        height: 300,
      ),
    );
  }

  _buildTextSection() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Need support? We are here.',
          style:
              AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
        ),
        SizedBox(
          height: 10,
        ),
        Text('Let us know what support you need, Reach us please.',
            style: AppTypography.poppinsRegular14(
                color: UIColors.primaryGreen950)),
      ],
    );
  }

  _buildSupportedContactSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildFacebookButton(),
        _buildGmailButton(),
        // _buildInstagramButton(),
      ],
    );
  }

  _buildFacebookButton() {
    return IconButton(
      color: UIColors.primaryGreen600,
      iconSize: Values.v48,
        onPressed: () {
          Log.debug('share button pressed');
          Uri url = Uri.parse('https://www.facebook.com/p/Fitsomnia-Bangladesh-61557884841503/');
          launchUrl(url, mode: LaunchMode.externalApplication);
        },
        icon: Image.asset(Assets.facebookIconNew));
  }

  _buildGmailButton() {
    String userId = BlocProvider.of<SharedBloc>(context).userProfile!.id;
    
    return IconButton(
      color: UIColors.primaryGreen500,
      iconSize: Values.v48,
        onPressed: () {
          Log.debug('share button pressed');
          openMailApp(email: '<EMAIL>', subject: 'FITSOMNIA_SUPPORT_REQUEST(ID: ${userId})'); 
        },
        icon: Image.asset(Assets.emailIcon));
  }


  void openMailApp({
    required String email,
    required String subject,
  }) async {
    final Uri emailLaunchUri = Uri(
      scheme: 'mailto',
      path: email,
      query: encodeQueryParameters(<String, String>{
        'subject': subject,
      }),
    );


    try {
      if( await canLaunchUrl(emailLaunchUri)) {
        await launchUrl(emailLaunchUri);
      } else {
        Log.debug('can not open url');
      }
    } catch (e) {
      Log.debug('send mail error: ${e.toString()}');
      AppToast.showToast(message: 'Mail sending failed');
    }
    
  }

  String? encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((MapEntry<String, String> e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }
}
