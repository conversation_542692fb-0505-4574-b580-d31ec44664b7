import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/club/dashboard/domain/use_case/membersip_status_use_case.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/entities/club_entity.dart';

part 'club_dashboard_event.dart';
part 'club_dashboard_state.dart';

class ClubDashboardBloc extends Bloc<ClubDashboardEvent, BaseState> {
  ClubDashboardBloc({
    required this.useCase,
  }) : super(InitialState()) {
    on<ClubMembershipCheckEvent>(_onMembershipCheckEvent);
  }

  late MembershipStatusUseCase useCase;

  Future<void> _onMembershipCheckEvent(
    ClubMembershipCheckEvent event,
    Emitter<BaseState> emit,
  ) async {
    emit(const LoadingState());

    try {
      final response = await useCase.call();

      response.fold(
        (l) {
          Log.error(l.toString());
          if (l.error!.code == "YOU_ARE_NOT_A_MEMBER_OF_ANY_CLUB" ||
              l.error!.code == "CAN_NOT_GET_MY_CLUB") {
            emit(ClubMembershipNotFoundState());
          } else {
            emit(ErrorState(data: l.error!.message!));
          }
        },
        (r) => emit(ClubMembershipFoundState(club: r)),
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());
      emit(const ErrorState(data: TextConstants.pleaseTryAgain));
    }
  }
}
