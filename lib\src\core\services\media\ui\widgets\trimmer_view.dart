import 'dart:io';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/services/media/ui/widgets/preview_and_upload_story.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_trimmer/video_trimmer.dart';

class TrimmerView extends StatefulWidget {
  final File file;

  const TrimmerView(this.file);

  @override
  _TrimmerViewState createState() => _TrimmerViewState();
}

class _TrimmerViewState extends State<TrimmerView> {
  List<String?> filePath = [];
  final Trimmer _trimmer = Trimmer();

  double _startValue = 0.0;
  double _endValue = 0.0;

  bool _isPlaying = false;
  bool _progressVisibility = false;
  bool _showPreviewButton = false;

  @override
  void initState() {
    super.initState();
    deleteAllTrimmingFilesFromDirectory();
    _loadVideo();
  }

  Future<String?> _trimVideo() async {
    setState(() {
      _progressVisibility = true;
    });

    await _trimmer
        .saveTrimmedVideo(
            startValue: _startValue,
            endValue: _endValue,
            onSave: (String? outputPath) {
              setState(() {
                if (outputPath != null) {
                  _showPreviewButton = true;
                  filePath.add(outputPath);
                  // _navigateToPreviewAndUploadPage();
                }
              });
            })
        .then(
          (value) => setState(() {
            _progressVisibility = false;
          }),
        );
  }

  void _loadVideo() {
    _trimmer.loadVideo(videoFile: widget.file);
  }

  void deleteAllTrimmingFilesFromDirectory() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final String dirPath = '${directory.path}/Trimmer';

      final dir = Directory(dirPath);
      dir.deleteSync(recursive: true);
    } catch (e) {
      Log.info(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Container(
          padding: const EdgeInsets.only(bottom: 30.0),
          color: Colors.black,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: <Widget>[
              _buildTrimAndPreviewButton(),
              Expanded(
                flex: 1,
                child: VideoViewer(trimmer: _trimmer),
              ),
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TrimViewer(
                    trimmer: _trimmer,
                    viewerHeight: 50.0,
                    viewerWidth: MediaQuery.of(context).size.width,
                    durationStyle: DurationStyle.FORMAT_MM_SS,
                    maxVideoLength: const Duration(seconds: 10),
                    editorProperties: TrimEditorProperties(
                      borderPaintColor: AppColors.primaryGreen.withOpacity(.5),
                      borderWidth: 2,
                      borderRadius: 2,
                      circlePaintColor: AppColors.primaryGreen,
                    ),
                    areaProperties: TrimAreaProperties.edgeBlur(
                      thumbnailQuality: 10,
                    ),
                    onChangeStart: (value) => _startValue = value,
                    onChangeEnd: (value) => _endValue = value,
                    onChangePlaybackState: (value) =>
                        setState(() => _isPlaying = value),
                  ),
                ),
              ),
              _buildPlayButton()
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlayButton() {
    return TextButton(
      child: _isPlaying
          ? Icon(
              Icons.pause,
              size: 60.r,
              color: Colors.white,
            )
          : Icon(
              Icons.play_arrow,
              size: 60.r,
              color: Colors.white,
            ),
      onPressed: () async {
        bool playbackState = await _trimmer.videoPlaybackControl(
          startValue: _startValue,
          endValue: _endValue,
        );
        setState(() {
          _isPlaying = playbackState;
        });
      },
    );
  }

  Widget _buildTrimAndPreviewButton() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 10.h,
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: const Icon(
              Icons.arrow_back_ios,
              color: AppColors.white,
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(child: _buildTrimButton()),
          SizedBox(width: 16.w),
          Expanded(child: _buildPreviewButton()),
        ],
      ),
    );
  }

  Widget _buildTrimButton() {
    return ElevatedButton(
      onPressed: _progressVisibility
          ? null
          : () async {
              _trimVideo();
              if (_trimmer.videoPlayerController!.value.isPlaying) {
                _trimmer.videoPlayerController?.pause();
              }
            },
      style: ElevatedButton.styleFrom(
        minimumSize: Size(double.infinity, 50.h),
      ),
      child: _progressVisibility
          ? const LoadingWidget(
              message: 'Trimming...',
              loaderColor: AppColors.white,
              scale: .5,
            )
          : Text(
              'Trim Video',
              style: AppTypography.semiBold16(
                color: AppColors.white,
              ),
            ),
    );
  }

  Widget _buildPreviewButton() {
    return ElevatedButton(
      onPressed: _showPreviewButton
          ? () {
              _trimmer.videoPlayerController?.pause();
              _navigateToPreviewAndUploadPage();
            }
          : null,
      style: ElevatedButton.styleFrom(
        minimumSize: Size(double.infinity, 50.h),
        disabledBackgroundColor: AppColors.grey,
      ),
      child: Text(
        'Preview',
        style: AppTypography.semiBold16(
          color: AppColors.white,
        ),
      ),
    );
  }

  void _navigateToPreviewAndUploadPage() async{
    bool? isTrimFileNotEmpty = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PreviewAndUploadStory(filePath: filePath),
      ),
    );

    if (isTrimFileNotEmpty != null && !isTrimFileNotEmpty) {
      setState(() {
        _showPreviewButton = false;
      });
    }
  }
}
