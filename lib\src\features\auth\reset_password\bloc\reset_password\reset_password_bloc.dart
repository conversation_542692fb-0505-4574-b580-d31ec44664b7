import 'dart:async';
import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/auth/root/domain/use_cases/reset_password_use_case.dart';

part 'reset_password_event.dart';

class ResetPasswordBloc extends Bloc<ResetPasswordEvent, BaseState> {
  ResetPasswordBloc({
    required this.useCase,
  }) : super(InitialState()) {
    on<ResetPasswordWithEmailAndPasswordEvent>(_onResetPasswordEvent);
  }

  final ResetPasswordUseCase useCase;

  FutureOr<void> _onResetPasswordEvent(event, emit) async {
    emit(const LoadingState());

    Map<String, dynamic> map = {
      if (event.email != null) "email": event.email,
      if (event.phone != null)
        "phone": event.phone.toString().startsWith('+')
            ? event.phone
            : '+${event.phone}',
      "password": passwordField.text,
    };

    try {
      final response = await useCase.call(map);

      return response.fold(
        (l) => emit(ErrorState(data: l.message)),
        (r) {
          passwordField.clear();

          return emit(const SuccessState());
        },
      );
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      emit(ErrorState(data: ErrorModel()));
    }
  }

  final TextEditingController passwordField = TextEditingController();
  final TextEditingController confirmPasswordField = TextEditingController();
}
