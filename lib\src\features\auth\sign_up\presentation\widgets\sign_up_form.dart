part of '../pages/sign_up_page.dart';

class _SignUpForm extends StatefulWidget {
  const _SignUpForm({Key? key}) : super(key: key);

  @override
  State<_SignUpForm> createState() => _SignUpFormState();
}

class _SignUpFormState extends State<_SignUpForm> {
  @override
  Widget build(BuildContext context) {
    SignUpBloc bloc = context.watch<SignUpBloc>();

    return BlocListener<SignUpBloc, BaseState>(
      listenWhen: (previous, current) => current != previous,
      listener: (context, state) {
        Log.debug(state.toString());

        if (state is SuccessState) {
          _navigateToVerificationPage(context);
        } else if (state is SocialMediaSignUpSuccessState) {
          _onSocialSignUpSuccessState(context);
        } else if (state is ErrorState) {
          AppToast.showToast(
            message: state.data ?? 'An error occurred. Please try again.',
            backgroundColor: AppColors.error,
            gravity: ToastGravity.BOTTOM,
          );
        }
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          InputFormField(
            textEditingController: bloc.emailField,
            hintText: TextConstants.email,
            autocorrect: false,
            keyboardType: TextInputType.emailAddress,
            validator: InputValidators.email,
          ),
          InputFormField(
            textEditingController: bloc.passwordField,
            hintText: TextConstants.password,
            password: EnabledPassword(),
            validator: InputValidators.password,
          ),
          InputFormField(
            textEditingController: bloc.confirmPassword,
            hintText: TextConstants.confirmPassword,
            password: EnabledPassword(),
            validator: (String? value) {
              if (value != bloc.passwordField.text) {
                return TextConstants.passwordDoNotMatch;
              }

              return null;
            },
          ),
          _buildTermsAndConditionsChecker(context),
        ],
      ),
    );
  }

  Widget _buildTermsAndConditionsChecker(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCheckBox(),
        _buildText(context),
      ],
    );
  }

  Widget _buildText(BuildContext context) {
    return Expanded(
      child: RichText(
        //textAlign: TextAlign.justify,
        text: TextSpan(
          text: 'I Agree with Fitsomnia\'s ',
          style: AppTypography.poppinsRegular14(),
          children: <TextSpan>[
            TextSpan(
              text: "Terms & Conditions",
              style: AppTypography.poppinsRegular14(
                color: AppColors.primaryGreen,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Navigator.pushNamed(
                    context,
                    Routes.webView,
                    arguments: 'terms-of-service',
                  );
                },
            ),
            TextSpan(
              text: " and ",
              style: AppTypography.poppinsRegular14(),
            ),
            TextSpan(
              text: " Privacy Policies",
              style: AppTypography.poppinsRegular14(
                color: AppColors.primaryGreen,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Navigator.pushNamed(
                    context,
                    Routes.webView,
                    arguments: 'privacy-policy',
                  );
                },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCheckBox() {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: SizedBox(
        width: 24,
        height: 24,
        child: Checkbox(
          value: agreedWithTerms.value,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          activeColor: AppColors.primaryGreen,
          checkColor: AppColors.white,
          // fillColor: WidgetStateProperty.all(
          //   AppColors.primaryGreen,
          // ),
          side: WidgetStateBorderSide.resolveWith(
            (states) {
              return const BorderSide(
                color: AppColors.grey,
              );
            },
          ),
          onChanged: (value) {
            setState(() {
              agreedWithTerms.value = !agreedWithTerms.value;
            });
          },
        ),
      ),
    );
  }

  void _onSocialSignUpSuccessState(BuildContext context) {
    context.read<SharedBloc>().add(LoadEssentialDataEvent());
    context.read<SignUpBloc>().clearControllers();
    _navigateToLoginPage(context);
  }

  void _navigateToLoginPage(BuildContext context) {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const LoginPage()),
      (Route<dynamic> route) => false,
    );
  }

  void _navigateToVerificationPage(BuildContext context) {
    Navigator.pushReplacementNamed(
      context,
      Routes.verification,
      arguments: {
        'email': context.read<SignUpBloc>().emailField.text,
      },
    );
  }
}
