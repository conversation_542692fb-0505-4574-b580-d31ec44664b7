import 'dart:async';
import 'dart:io';

import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/extensions/extensions.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/firebase/firebase_service.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/bloc/dashboard_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/bloc/food_consumption/food_consumption_cubit.dart';
import 'package:fitsomnia_app/src/features/diet/food/dashboard/presentation/page/search_food_details_page.dart';
import 'package:fitsomnia_app/src/features/diet/track_diet/presentation/bloc/diet_history/diet_history_bloc.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/entities/food_info_entity.dart';
import 'package:fitsomnia_app/src/features/food_scanner/presentation/bloc/bloc/food_scan_bloc.dart';
import 'package:fitsomnia_app/src/features/food_scanner/presentation/pages/food_scanner_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';

class ScanFoodDetailsPage extends StatefulWidget {
  const ScanFoodDetailsPage({super.key, required this.foodImageFilePath});
  final String foodImageFilePath;

  @override
  State<ScanFoodDetailsPage> createState() => _ScanFoodDetailsPageState();
}

class _ScanFoodDetailsPageState extends State<ScanFoodDetailsPage> {
  late ValueNotifier<int> servingSizeNotifier;

  @override
  void initState() {
    super.initState();
    servingSizeNotifier = ValueNotifier(1);

    // Trigger food scan when page loads
    context.read<FoodScanBloc>().add(
          SendFoodScanImageEvent(imagePath: widget.foodImageFilePath),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Background Image
            _buildBackgroundImage(),

            // Bottom Sheet with Food Details
            _buildScanResultSection(),

            // Back Button
            Align(
              alignment: Alignment.topLeft,
              // top: MediaQuery.of(context).padding.top + 10,
              // left: 0,
              // child: IconButton(
              //   icon: Icon(Icons.arrow_back, color: Colors.white),
              //   onPressed: () => Navigator.pop(context),
              // ),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                child: Row(
                  children: [
                    IconButton(
                      icon: Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () => Navigator.pop(context),
                    ),
                    SizedBox(width: Values.v100),
                    Text(
                      'Scan Results',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            blurRadius: 4,
                            color: Colors.black.withOpacity(0.3),
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  _buildBackgroundImage() {
    return BlocBuilder<FoodScanBloc, FoodScanState>(
      builder: (context, state) {
        if (state is FoodScanLoading || state is FoodScanInitial) {
          return _buildLoadingImageSection();
        }

        return _buildNormalImageSection();
      },
    );
  }

  _buildScanResultSection() {
    return BlocBuilder<FoodScanBloc, FoodScanState>(builder: (context, state) {
      if (state is FoodScanLoading || state is FoodScanInitial) {
        return const SizedBox.shrink();
      } else {
        return Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: (state is FoodScanSuccess)
                ? _buildFoodDetails(state.foodInfo)
                : (state is FoodScanFailure)
                    ? _buildErrorState(state.data.message ??
                        'An error occurred while scanning the food.')
                    : SizedBox.shrink(),
          ),
        );
      }
    });
  }

  _buildNormalImageSection() {
    return Stack(
      fit: StackFit.expand,
      children: [
        Image.file(
          File(widget.foodImageFilePath),
          fit: BoxFit.fill,
        ),

        // Gradient overlay for better text visibility
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.black.withOpacity(0.7),
                Colors.transparent,
              ],
            ),
          ),
        ),
      ],
    );
  }

  _buildLoadingImageSection() {
    return Stack(
      fit: StackFit.expand,
      children: [
        Image.file(
          File(widget.foodImageFilePath),
          fit: BoxFit.fill,
          colorBlendMode: BlendMode.darken,
        ),

        // Gradient overlay for better text visibility
        // Container(
        //   decoration: BoxDecoration(
        //     gradient: LinearGradient(
        //       begin: Alignment.topCenter,
        //       end: Alignment.bottomCenter,
        //       colors: [
        //         UIColors.primaryGreen600.withOpacity(0.7),
        //         Colors.transparent,
        //       ],
        //     ),
        //   ),
        // ),
        Container(
          // height: double.infinity,
          // width: double.infinity,
          color: UIColors.black.withOpacity(0.8),
        ),
        Positioned(
        top: 150,
        left: 100,
            child: Text(
          'Analyzing your photo...',
          style: AppTypography.poppinsSemiBold18(color: UIColors.white),
        )),

        _buildScaningProgressIndicatior(),
      ],
    );
  }

  Widget _buildScaningProgressIndicatior() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          height: 300,
          width: 300,
          // child: CircularProgressIndicator(
          //   color: UIColors.primary,
          //   strokeWidth: 4,
          // ),
          child: CirculatDeterministicProgressIndicator(key: UniqueKey(),),
        ),
        ClipRRect(
          borderRadius: BorderRadius.circular(200),
          child: Image.file(
            File(widget.foodImageFilePath),
            fit: BoxFit.fill,
            height: 290,
            width: 290,
            colorBlendMode: BlendMode.darken,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(String message) {
    return Container(
      padding: EdgeInsets.all(16),
      // height: Values.v400,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildMealTypeText(),
          SizedBox(
            height: Values.v16,
          ),
          Text(
            'Can\'t detect the Food !',
            textAlign: TextAlign.center,
            style: AppTypography.poppinsSemiBold20(),
          ),
          SizedBox(height: 16),
          Image.asset(
            Assets.mealFoodDetectionErrorImg,
            height: 125,
            width: 90,
          ),
          SizedBox(height: 16),
          _takePhotoAgainButto(context),
        ],
      ),
    );
  }

  // ignore: long-method
  Widget _buildFoodDetails(FoodInfoEntity foodInfo) {
    return BlocConsumer<FoodConsumptionCubit, BaseState>(
      listener: (context, state) {
        Log.debug('FoodConsumptionCubit state listen: ${state.toString()}');
        if (state is ErrorState) {
          AppToast.showToast(
            message: state.data,
            gravity: ToastGravity.BOTTOM,
          );
        }
        if (state is SuccessState) {
          Log.debug('add ai food in diet history success');
          AppToast.showToast(
            message: "Food added successfully",
            gravity: ToastGravity.BOTTOM,
            toastLength: Toast.LENGTH_LONG,
            backgroundColor: AppColors.primaryGreen,
          );

          // Navigator.popUntil(
          //     context, (route) => route.settings.name == Routes.dietDashboard);

          // context.read<DietHistoryBloc>().add(
          //       const RefreshDietHistoryEvent(),
          //     );
          _navigateToDietFeatureDashboard(context);
        }
      },
      builder: (context, state) {
        return Stack(
          alignment: Alignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  _buildMealTypeText(),
                  SizedBox(
                    height: Values.v5,
                  ),
                  Text(foodInfo.name, style: AppTypography.poppinsSemiBold20()),
                  SizedBox(
                    height: Values.v5,
                  ),
                  ServingSizeWidget(
                      servingSizeValueNotifier: servingSizeNotifier),
                  const SizedBox(height: 16),
                  Wrap(
                    spacing: Values.v5,
                    runSpacing: 5,
                    children: [
                      _buildNutritionInfo(
                        'Calories',
                        foodInfo.calories,
                        Assets.mealCalorie,
                        'kcal',
                      ),
                      _buildNutritionInfo(
                        'Carbs',
                        foodInfo.carbs,
                        Assets.mealCarbs,
                        'g',
                      ),
                      _buildNutritionInfo(
                        'Fat',
                        foodInfo.fats,
                        Assets.mealFats,
                        'g',
                      ),
                      _buildNutritionInfo(
                        'Protein',
                        foodInfo.protein,
                        Assets.mealProtein,
                        'g',
                      ),
                    ],
                  ),
                  Divider(
                    color: AppColors.greyscale100,
                    thickness: 1,
                    height: Values.v40,
                  ),
                  if (foodInfo.ingredients != null)
                    ..._buildIngredients(foodInfo),
                  // const SizedBox(height: Values.v16),
                  _addToMealButton(foodInfo),
                  const SizedBox(height: Values.v16),
                ],
              ),
            ),
            if (state is LoadingState)
              Center(
                child: CircularProgressIndicator(
                  color: UIColors.primary,
                ),
              ),
          ],
        );
      },
    );
  }

  _buildIngredients(FoodInfoEntity foodInfo) {
    return [
      Text(
        'Breakdown',
        style: AppTypography.poppinsRegular14(
          color: AppColors.greyscale400,
        ),
      ),
      SizedBox(height: Values.v8),
      _buildBreakdownItems(foodInfo.ingredients!),
      Divider(
        color: AppColors.greyscale100,
        thickness: 1,
        height: Values.v40,
      ),
    ];
  }

  Widget _buildNutritionInfo(
      String label, String value, String mealSvgImg, String unit) {
    return Container(
      padding: const EdgeInsets.all(Values.v8),
      // height: Values.v100,
      width: Values.v90,
      decoration: BoxDecoration(
        // color: Colors.grey[200],
        borderRadius: BorderRadius.circular(Values.v8),
        border: Border.all(color: AppColors.greyscale100, width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(Values.v5),
            child: SvgPicture.asset(
              mealSvgImg,
              width: 24,
              height: 24,
            ),
          ),
          Text(
            '$value $unit',
            style: AppTypography.poppinsSemiBold14(
                color: UIColors.primaryGreen950),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
          const SizedBox(height: Values.v4),
          Text(
            label,
            style:
                AppTypography.poppinsRegular12(color: AppColors.greyscale400),
          ),
        ],
      ),
    );
  }

  Widget _addToMealButton(FoodInfoEntity foodInfo) {
    return BlocBuilder<FoodConsumptionCubit, BaseState>(
      builder: (context, foodConsumptionState) {
        return ElevatedButton(
          onPressed: () {
            Log.debug('Add AI food info to database');
            FirebaseService().logFeatureUsage('diet', 'diet_ai_food_scan', '');

            if (foodConsumptionState is LoadingState) {
              return;
            }
            //TODO send food, meal information to server
            _addFoodToDietHistory(foodInfo);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: UIColors.primaryGreen600,
            padding: const EdgeInsets.symmetric(
                horizontal: Values.v24, vertical: Values.v12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Stack(
                children: [
                  Text(
                    'Add To ${_mealTypeName()}',
                    style: AppTypography.poppinsMedium16(color: UIColors.white),
                  ),
                  // if (foodConsumptionState is LoadingState)
                  //   const Center(
                  //     child: CircularProgressIndicator(
                  //       color: AppColors.white,
                  //     ),
                  //   ),
                ],
              )
            ],
          ),
        );
      },
    );
  }

  Widget _takePhotoAgainButto(BuildContext context) {
    return ElevatedButton(
      onPressed: () {
        Log.debug('Add AI food info to database');
        //TODO send food, meal information to server
        Navigator.of(context).pop();
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: UIColors.primaryGreen600,
        padding: const EdgeInsets.symmetric(
            horizontal: Values.v24, vertical: Values.v12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Take Photo Again',
            style: AppTypography.poppinsMedium16(color: UIColors.white),
          )
        ],
      ),
    );
  }

  _buildBreakdownItems(List<String> list) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Wrap(
        spacing: Values.v8,
        runSpacing: Values.v12,
        children: list.map((item) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: Values.v4),
            child: Container(
              padding: const EdgeInsets.all(Values.v10),
              decoration: BoxDecoration(
                // color: Colors.grey[200],
                borderRadius: BorderRadius.circular(Values.v8),
                border: Border.all(color: AppColors.greyscale100, width: 1),
              ),
              child: Text(
                item.toCapitalFirst(),
                style: AppTypography.poppinsRegular14(
                  color: UIColors.primaryGreen950,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  void _addFoodToDietHistory(FoodInfoEntity foodInfo) {
    Log.debug('add food to diet history');
    final selectedIndex = context.read<FoodConsumptionCubit>().selectedTabIndex;
    context.read<FoodConsumptionCubit>().addAiFoodToDietHistory(
          foodEntity: foodInfo,
          servingSize: servingSizeNotifier.value,
          type: selectedIndex.value,
          date: context.read<DietHistoryBloc>().currentDate,
          source: 'ai',
        );
  }

  _buildMealTypeText() {
    String mealName = _mealTypeName();

    return Text(
      '${mealName.toCapitalFirst()}',
      style: AppTypography.poppinsRegular14(color: AppColors.greyscale400),
    );
  }

  String _mealTypeName() {
    int mealTypeIndex =
        context.read<FoodConsumptionCubit>().selectedTabIndex.value;

    switch (mealTypeIndex) {
      case 0:
        return 'Breakfast';
      case 1:
        return 'Lunch';
      case 2:
        return 'Dinner';
      default:
        return 'Snacks';
    }
  }

  void _navigateToDietFeatureDashboard(BuildContext context) {
    // move to diet feature page
    // Navigator.of(context).popUntil((route) => route.isFirst);
    // navigatorKey?.currentState?.pushAndRemoveUntil(
    //   MaterialPageRoute(
    //       builder: (_) => DashboardPage(
    //             selectedIndex: 3,
    //           )),
    //   (route) => false,
    // );

    navigatorKey?.currentState?.popUntil(
      (route) => route is MaterialPageRoute &&
          route.settings.name == Routes.dashboard,
    );
    context
        .read<DashboardCubit>()
        .changePage(3); // diet feature position is 3 in nav bar
  }
}

class CirculatDeterministicProgressIndicator extends StatefulWidget {
  const CirculatDeterministicProgressIndicator({Key? key}) : super(key: key);

  @override
  State<CirculatDeterministicProgressIndicator> createState() => _CirculatDeterministicProgressIndicatorState();
}

class _CirculatDeterministicProgressIndicatorState extends State<CirculatDeterministicProgressIndicator> {
  double _progress = 0;
  double maxValue = 10;

  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(milliseconds: 500), (timer) async {
      if (_progress >= maxValue) {
        timer.cancel();
      } else {
        setState(() {
          _progress += 0.5;
        });
      }
    });
    
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      width: 300,
      child: CircularProgressIndicator(
        color: UIColors.primary,
        strokeWidth: 4,
        
        value: _progress / maxValue,
      ),
    );
  }
}
