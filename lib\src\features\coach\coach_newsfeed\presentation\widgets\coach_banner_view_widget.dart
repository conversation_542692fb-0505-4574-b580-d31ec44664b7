import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/root/domain/entities/coach_program_category_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CoachBannerViewWidget extends StatefulWidget {
  const CoachBannerViewWidget({super.key, required this.categoryEntity});
  final CoachProgramCategoryEntity categoryEntity;

  @override
  State<CoachBannerViewWidget> createState() =>
      _CoachSubcategoryViewWidgetState();
}

class _CoachSubcategoryViewWidgetState extends State<CoachBannerViewWidget> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Log.debug('subcategory selected: ${widget.categoryEntity.id}');
        // Navigator.of(context).pushNamed(Routes.coachSearchListPage, arguments: [
        //   null,
        //   widget.categoryEntity.id,
        //   CoachProfileFilterType.CATEGORY,
        // ]);
      },
      child: Container(
        height: Values.v200.h,
        width: double.maxFinite,
        color: AppColors.transparent,
        child: Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(Values.v10),
              color: AppColors.white
              // color: UIColors.black.withOpacity(0.30),
              ),
          padding: EdgeInsets.only(left: 5, right: 5),
          child: _buildCoachPreviewWidget(),
        ),
      ),
    );
  }

  _buildCoachPreviewWidget() {
    return Container(
      color: AppColors.transparent,
      child: Stack(
        alignment: Alignment.center,
        children: [
          _buildProfileImage(),
          _buildProfileInformation(),
        ],
      ),
    );
  }

  _buildProfileImage() {
    // return Container(
    //   child: Image.network(
    //     widget.profileImage,
    //     fit: BoxFit.fitHeight,
    //     // width: double.infinity,
    //     // height: double.infinity,
    //   ),
    // );

    if (widget.categoryEntity.mediaList == null ||
        widget.categoryEntity.mediaList!.isEmpty ||
        widget.categoryEntity.mediaList!.first.urlPath == null) {
      return Image.asset(
        Assets.spotMeNoImage,
        height: double.infinity,
        width: double.infinity,
      );
    }

    if (Uri.parse(widget.categoryEntity.mediaList!.first.urlPath!)
        .hasAbsolutePath) {
      return ImageContainer.rectangularImage(
        cornerRadius: Values.v10,
        image: widget.categoryEntity.mediaList!.first.urlPath,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        useMedium: true,
        hideLoadingIndicator: true,
        errorWidget: Container(
          height: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(Values.v10),
            image: DecorationImage(
              image: const Image(
                image: AssetImage(
                  Assets.spotMeNoImage,
                ),
              ).image,
              fit: BoxFit.cover,
            ),
          ),
        ),
      );
    } else {
      if (widget.categoryEntity.mediaList!.first.urlPath!.contains('assets/')) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(Values.v10),
          child: Image.asset(widget.categoryEntity.mediaList!.first.urlPath!,
              width: double.maxFinite,
              height: double.maxFinite,
              fit: BoxFit.cover),
        );
      }
    }
  }

  _buildProfileInformation() {
    return Container(
      height: double.infinity,
      width: double.infinity,
      padding: const EdgeInsets.all(Values.v20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(Values.v10),
        gradient: _buildLinearGradientWhiteToBlack(),
        // color: UIColors.black.withOpacity(0.30),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSubTitle1(),
          _buildSubTitle2(),
          _buildDescText(),
          // Text(
          //   '${widget.subcategoryEnrity.totalCoach} Coaches',
          //   maxLines: 1,
          //   overflow: TextOverflow.ellipsis,
          //   style: AppTypography.poppinsRegular14(
          //       color: AppColors.primaryGreen400),
          // ),
          _buildButton(),
        ],
      ),
    );
  }

  _buildButton() {
    return InkWell(
      onTap: () {
        Log.debug('button pressed');
        Navigator.of(context).pushNamed(
          Routes.coachSearchListPage,
          arguments: [
            null,
            widget.categoryEntity.id,
            CoachProfileFilterType.CATEGORY,
          ],
        );
      },
      child: Container(
        margin: EdgeInsets.only(top: Values.v10),
        padding: EdgeInsets.symmetric(
          horizontal: Values.v16,
          vertical: Values.v5,
        ),
        decoration: BoxDecoration(
            color: UIColors.primaryGreen600,
            borderRadius: BorderRadius.circular(Values.v100)),
        child: Text(
          widget.categoryEntity.buttonText ?? 'Coach List',
          style: AppTypography.poppinsMedium14(color: AppColors.white),
        ),
      ),
    );
  }

  LinearGradient _buildLinearGradientWhiteToBlack() {
    return LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        AppColors.transparent,
        UIColors.black.withOpacity(0.3),
      ],
    );
  }

  _buildSubTitle1() {
    return Text(
      widget.categoryEntity.subTitle ?? '',
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: const TextStyle(
        color: AppColors.white,
        fontSize: FontSize.s40,
        fontFamily: FontConstants.poppinsFontFamily,
        fontWeight: FontWeightManager.bold,
        height: 0.8,
        letterSpacing: -2,
      ),
    );
  }

  _buildSubTitle2() {
    return Text(
      widget.categoryEntity.subTitle2 ?? '',
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: AppColors.primaryGreen400,
        fontSize: FontSize.s40,
        fontFamily: FontConstants.poppinsFontFamily,
        fontWeight: FontWeightManager.bold,
        height: 0.8,
        letterSpacing: -2,
      ),
    );
  }

  _buildDescText() {
    return Text(
      widget.categoryEntity.desc ?? '',
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: AppTypography.poppinsMedium14(color: AppColors.white),
    );
  }
}
