import 'dart:ffi';
import 'dart:ui';

import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/validators/input_validators.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/core/widgets/show_snack_bar_message.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/bloc/coach_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/bloc/coach_profile_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/widget/coach_profile_preview_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/widget/coach_profile_file_upload_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/widget/dropdown_selection_menu.dart';
import 'package:fitsomnia_app/src/features/coach/root/domain/entities/coach_program_category_entity.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/bloc/dashboard_cubit.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:fitsomnia_app/src/features/diet/dashboard/presentation/widgets/custom_dropdown_menu.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:input_form_field/input_form_field.dart';

class CreateCoachProfilePage extends StatefulWidget {
  const CreateCoachProfilePage(
      {super.key, this.coachId, this.isUpdateCoachProfile = false});
  final bool isUpdateCoachProfile;
  final String? coachId;

  @override
  State<CreateCoachProfilePage> createState() => _CreateCoachProfilePageState();
}

class _CreateCoachProfilePageState extends State<CreateCoachProfilePage> {
  List<CoachProgramCategoryEntity> _categoryMap = [];
  Map<String, List<CoachProgramCategoryEntity>> categoryIdToSubcategoryMap = {};
  List<MenuItem> categories = [];
  List<MenuItem> subCategories = [];

  ValueNotifier<MenuItem> selectedSkillLevelNotifier =
      ValueNotifier(MenuItem(name: '', label: ''));
  ValueNotifier<MenuItem> selectedExperienceLevelNotifier =
      ValueNotifier(MenuItem(name: '', label: ''));

  ValueNotifier<MenuItem> selectedCategoryNotifier =
      ValueNotifier(MenuItem(name: '', label: ''));

  ValueNotifier<MenuItem> selectedSubCategoryNotifier =
      ValueNotifier(MenuItem(name: '', label: ''));

  late TextEditingController _fullNameController;
  late TextEditingController _profileNameController;
  late TextEditingController _expertiesController;
  late TextEditingController _profileTitleController;
  late TextEditingController _aboutTextController;
  late TextEditingController _titleOwnController;
  late TextEditingController _emailController;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  CoachEntity? _coachEntity;
  bool _isLoading = false;
  bool _isCoachRegistrationOngoing = false;
  bool _isUpdateCoachProfile = false;

  @override
  void initState() {
    super.initState();

    // is create profile or update profile
    _isUpdateCoachProfile = (widget.coachId != null);

    _fullNameController = TextEditingController(text: '');
    _profileNameController = TextEditingController(text: '');

    _expertiesController = TextEditingController();
    _profileTitleController = TextEditingController();
    _aboutTextController = TextEditingController();
    _titleOwnController = TextEditingController();
    _emailController = TextEditingController();

    // selectedCategoryNotifier.addListener(() {
    //   Log.debug('selected category id: ${selectedCategoryNotifier.value.text}');
    //   selectedCategoryId.value = _categoryIdController.text;
    //   _subCategoryTitleController.text = '';
    //   _subCategoryIdController.text = '';
    // });

    // selectedSkillLevelNotifier.addListener(() {
    //   Log.debug('skill level: ${selectedSkillLevelNotifier.value.label}');
    // });
    _initCategory();
    // BlocProvider.of<CoachBloc>(context).add(GetCoachCategoryMap());

    if (_isUpdateCoachProfile) {
      Log.debug('get my coach profile');
      BlocProvider.of<CoachDashboardBloc>(context)
          .add(GetCoachOwnProfileEvent(coachId: widget.coachId!));
      _isLoading = true;
    }
  }

  _initCategory() {
    List<CoachProgramCategoryEntity> categoryList =
        context.read<CoachBloc>().categoryList;
    categoryIdToSubcategoryMap =
        context.read<CoachBloc>().subcategoriesByCategoryId;

    categories = categoryList
        .map<MenuItem>((item) => MenuItem(name: item.id, label: item.title))
        .toList();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _profileNameController.dispose();
    _expertiesController.dispose();
    _profileTitleController.dispose();
    _aboutTextController.dispose();
    _titleOwnController.dispose();
    _emailController.dispose();

    super.dispose();
  }

  /// set coach previous approved profile data for updating
  _setCoachPreviousData() {
    Log.debug('set coach previous data : ${_coachEntity!.name}');
    _fullNameController.text = _coachEntity!.name;
    _profileNameController.text = _coachEntity!.profileName;
    _aboutTextController.text = _coachEntity!.about;
    if (_coachEntity!.achievements.isNotEmpty)
      _titleOwnController.text = _coachEntity!.achievements.first;
    _expertiesController.text = _coachEntity!.expertise;
    _profileTitleController.text = _coachEntity!.highlights;

    MenuItem selectedSkill = skillLevels
        .firstWhere((item) => (item.name == _coachEntity!.skillLevel));
    Log.debug('setup skill: ${selectedSkill.name}');
    selectedSkillLevelNotifier.value = selectedSkill;

    MenuItem selectedExperience = experienceLevels.first;
    if (_coachEntity != null) {
      selectedExperience = experienceLevels.firstWhere((experience) =>
          (experience.name == _coachEntity!.experienceInYear.toString()));
    }

    selectedExperienceLevelNotifier.value = selectedExperience;

    if (_coachEntity != null) {
      String coachCategoryId = _coachEntity!.coachCategoryId;
      selectedCategoryNotifier.value =
          categories.firstWhere((item) => (item.name == coachCategoryId));
    }
    _emailController.text = _coachEntity!.userEmail ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<CoachDashboardBloc, CoachDashboardState>(
            listener: (context, state) {
          if (state is GetCoachOwnProfileSuccess) {
            Log.debug('get coach own profile success');
            setState(() {
              _coachEntity = state.coachEntity;
              _isLoading = false;
              _setCoachPreviousData();
            });
          }

          if (state is GetCoachOwnProfileFail) {
            Log.debug('get coach own profile fail');
            // setState(() {
            //   _isLoading = false;
            // });
            AppToast.showToast(message: 'Failed to get coach profile data');
          }
        }),
        // BlocListener<CoachBloc, CoachState>(
        //   listener: (context, state) {
        //     if (state is GetCoachCategoryMapSuccess) {
        //       List<MenuItem> updatedCategories = [];
        //       for (CoachProgramCategoryEntity category in state.categories) {
        //         Log.debug('category: ${category.id}');
        //         updatedCategories
        //             .add(MenuItem(name: category.id, label: category.title));
        //         categoryIdToSubcategoryMap[category.id] =
        //             category.subcategories ?? [];
        //       }
        //       setState(() {
        //         _categoryMap = state.categories;
        //         categories = updatedCategories;
        //       });
        //     }
        //   },
        // ),
        BlocListener<CoachProfileBloc, CoachProfileState>(
            listener: (context, state) {
          if (state is CoachRegistrationSuccess) {
            setState(() {
              _isCoachRegistrationOngoing = false;
            });
            Log.debug(
                'coach registration id: ${state.registrationEntity.coachId} status: ${state.registrationEntity.reviewStatus}');
            AppToast.showToast(
                message:
                    'Coach registration application submitted successfully.');
            // Navigator.of(context)..pop();
            // Navigator.of(context).pushNamedAndRemoveUntil(
            //     Routes.coachFeatureDashboardPage, (route) => route.isFirst);
            _navigateToCoachFeatureDashboard();
          }

          if (state is CoachRegistrationFail) {
            setState(() {
              _isCoachRegistrationOngoing = false;
            });
            // AppToast.showToast(
            //     message: 'Coach registration process failed. Try again');
            if(mounted) ShowSnackBarMessage.showErrorSnackBar(message: 'Coach registration process failed. Try again', context: context);
          }

          if(state is CoachRegistrationLoading) {
            setState(() {
              _isCoachRegistrationOngoing = true;
            });
          }

          if (state is UpdateCoachProfileSuccess) {
            Log.debug('update coach profile success');
            AppToast.showToast(message: 'Coach profile update successful');
            setState(() {
              _isCoachRegistrationOngoing = false;
            });

            // move to coach featue dashboard page
            _navigateToCoachFeatureDashboard();
          }

          if (state is UpdateCoachProfileFail) {
            Log.debug('update coach profile fail');
            AppToast.showToast(message: 'Coach profile update fail');
            // Navigator.of(context)..pop()..pushReplacementNamed(Routes.coachDashboardPage);
            setState(() {
              _isCoachRegistrationOngoing = false;
            });
          }
        }),
      ],
      child: Scaffold(
        appBar: AppBar(
          title: (!_isUpdateCoachProfile)
              ? const Text('Be A Coach')
              : const Text('Edit Coach Profile'),
          centerTitle: true,
          titleTextStyle:
              AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen900),
        ),
        body: SafeArea(
          child: (_isLoading)
              ? const Center(
                  child: CircularProgressIndicator(
                    color: UIColors.primary,
                  ),
                )
              : _buildCoachRegistrationSecton(),
        ),
      ),
    );
  }

  _buildPageHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _completeCoachProfileTitle(),
        Text(
          'Provide the information to be a coach',
          style: AppTypography.poppinsMedium14(color: AppColors.greyscale400),
        ),
        const SizedBox(
          height: 24,
        ),
      ],
    );
  }

  _buildCoachRegistrationSecton() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Stack(
        children: [
          _buildCoachRegistrationFormSection(),
          Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              _buildPreviewSaveButtonSection(),
            ],
          )
        ],
      ),
    );
  }

  _buildCoachRegistrationFormSection() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPageHeader(),
          _buildCoachRegistrationInfoForm(),
          const SizedBox(
            height: 100,
          ),
        ],
      ),
    );
  }

  Widget _completeCoachProfileTitle() {
    return RichText(
      text: TextSpan(
        style: AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
        children: [
          TextSpan(
            text: 'Hi !',
            style: AppTypography.poppinsSemiBold24(color: UIColors.primary),
          ),
          const TextSpan(text: ' Let\'s complete coach profile'),
        ],
      ),
    );
  }

  _buildPreviewSaveButtonSection() {
    return Container(
      color: UIColors.white,
      child: Row(
        children: [
          Expanded(
            child: _buildPreviewButtton(),
          ),
          const SizedBox(
            width: 10,
          ),
          Expanded(
            child: (_isUpdateCoachProfile)
                ? _buildUpdateButton()
                : _buildSaveButton(),
          )
        ],
      ),
    );
  }

  _buildCoachRegistrationInfoForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          _buildLegalNameSection(),
          const SizedBox(height: 20),
          _buildProfileNameSection(),
          if (!_isUpdateCoachProfile) const SizedBox(height: 20),
          if (!_isUpdateCoachProfile) _buildUploadIdentificationProof(),
          const SizedBox(height: 20),
          _buildCoachCategorySubCategorySection(),
          const SizedBox(height: 20),
          _buildExperties(),
          const SizedBox(height: 20),
          _buildProfileTitle(),
          const SizedBox(height: 20),
          _buildSkillLevelSection(),
          const SizedBox(height: 20),
          _buildExperience(),
          const SizedBox(height: 20),
          _buildAboutSection(),
          const SizedBox(height: 20),
          _buildTitleOwn(),
          const SizedBox(height: 20),
          _buildUploadCoachProfileImageSection(),
          const SizedBox(height: 20),
          _buildUploadCoachCredentialsSection(),
          const SizedBox(height: 20),
          _buildEmailAddressField(),

        ],
      ),
    );
  }

  _buildEmailAddressField(){
    return _buildEditTextInfo(
      name: 'Email',
      text: _emailController.text,
      controller: _emailController,
      validator: InputValidators.email,
      isRequired: true,
    );
  }

  _buildLegalNameSection() {
    return _buildEditTextInfo(
      name: 'Legal Name (Full Name)',
      text: _fullNameController.text,
      controller: _fullNameController,
      validator: InputValidators.name,
      isRequired: true,
    );
  }

  _buildProfileNameSection() {
    return _buildEditTextInfo(
      name: 'Profile Name',
      text: _profileNameController.text,
      controller: _profileNameController,
      validator: null,
      isRequired: true,
    );
  }

  _buildCoachCategorySubCategorySection() {
    return Column(
      children: [
        _buildCoachCategorySection(),
        // const SizedBox(
        //   height: 30,
        // ),
        // _buildCoachSubCategorySection(),
      ],
    );
  }

  _buildCoachCategorySection() {
    return DropdownSelectionMenu(
      title: 'Select Category',
      lists: categories,
      valuteNotifier: selectedCategoryNotifier,
      initialSelectedItem: selectedCategoryNotifier.value,
    );
  }

  _buildCoachSubCategorySection() {
    return ValueListenableBuilder(
        valueListenable: selectedCategoryNotifier,
        builder: (context, categoryItem, child) {
          subCategories = [];
          final subCategoryList =
              categoryIdToSubcategoryMap[categoryItem.name] ?? [];
          for (CoachProgramCategoryEntity entity in subCategoryList) {
            subCategories.add(MenuItem(name: entity.id, label: entity.title));
          }

          return DropdownSelectionMenu(
            title: 'Select Sub-Category',
            lists: subCategories,
            valuteNotifier: selectedSubCategoryNotifier,
            initialSelectedItem: selectedSubCategoryNotifier.value,
          );
        });
  }

  _buildUploadIdentificationProof() {
    return CoachProfileFileUploadWidget(
      key: UniqueKey(),
      featureName: 'coach',
      fieldName: 'indentification',
      initialFiles: (_coachEntity == null) ? [] : _coachEntity!.identifications,
      title:
          'Upload Identification Proof (NID/Driving Licence/Birth Certificate)',
    );
  }

  _buildExperties() {
    return _buildEditTextInfo(
      name: 'Expertise(E.g: Body Building / Cricket / Football)',
      text: _expertiesController.text,
      controller: _expertiesController,
      validator: InputValidators.name,
      isRequired: true,
    );
  }

  _buildProfileTitle() {
    return _buildEditTextInfo(
      name: 'Profile Title(E.g: Mr. Bangladesh / Fitness Pro)',
      text: _profileTitleController.text,
      controller: _profileTitleController,
      validator: InputValidators.name,
      isRequired: true,
    );
  }

  _buildAboutSection() {
    return _buildEditTextInfo(
      name: 'About Your Coaching',
      text: _aboutTextController.text,
      controller: _aboutTextController,
      validator: InputValidators.name,
      maxLine: 5,
      isRequired: true,
    );
  }

  _buildSkillLevelSection() {
    return _buildDropdownSkillSelectionMenu();
  }

  _buildExperience() {
    // return _buildEditTextInfo(
    //   name: 'Experience',
    //   text: '',
    //   controller: TextEditingController(),
    // );

    return _buildExperienceSelectionMenu();
  }

  _buildTitleOwn() {
    return _buildEditTextInfo(
      name: 'Achievement(E.g: Mr. Bangladesh 2024)',
      text: _titleOwnController.text,
      validator: null,
      controller: _titleOwnController,
    );
  }

  _buildUploadCoachProfileImageSection() {
    // return _buildEditTextInfo(
    //   name: 'Upload Images for Coach Profile(Max 5)',
    //   text: '',
    //   controller: TextEditingController(),
    // );

    return CoachProfileFileUploadWidget(
      key: UniqueKey(),
      featureName: 'coach',
      fieldName: 'coach_profile',
      initialFiles: (_coachEntity == null) ? [] : _coachEntity!.media,
      title: 'Upload Image for Coach Profile (Max 5)',
    );
  }

  _buildUploadCoachCredentialsSection() {
    // return _buildEditTextInfo(
    //   name: 'Upload Credentials(Max 5)',
    //   text: '',
    //   controller: TextEditingController(),
    // );

    return CoachProfileFileUploadWidget(
      key: UniqueKey(),
      featureName: 'coach',
      fieldName: 'credential',
      initialFiles: (_coachEntity == null) ? [] : _coachEntity!.credentials,
      title: 'Upload Coach Certificate(Max 5)',
    );
  }

  _buildEditTextInfo(
      {required String name,
      required String? text,
      required TextEditingController controller,
      required String? Function(String?)? validator,
      int maxLine = 1,
      bool isRequired = false}) {
    if (text != null) controller.text = text;

    // return TextFormField(
    //     cursorColor: AppColors.black,
    //     controller: controller,
    //     maxLines: 5,
    //     minLines: 5,
    //     autofocus: true,
    //     autocorrect: false,
    //     decoration: _buildInputDecoration(),

    //   );

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        (isRequired)
            ? RichText(
                text: TextSpan(
                  text: name,
                  style: AppTypography.poppinsRegular16(
                      color: UIColors.primaryGreen950),
                  children: const <TextSpan>[
                    TextSpan(
                      text: '*',
                      style: TextStyle(color: Colors.red),
                    ),
                  ],
                ),
              )
            : Text(
                name,
                style: AppTypography.poppinsRegular16(
                    color: UIColors.primaryGreen950),
              ),
        const SizedBox(
          height: Values.v5,
        ),
        TextFormField(
          cursorColor: AppColors.black,
          controller: controller,
          maxLines: maxLine,
          minLines: maxLine,
          autofocus: false,
          autocorrect: false,
          decoration: _buildInputDecoration(),
          validator: validator,
          autovalidateMode: AutovalidateMode.onUserInteraction,
        )
      ],
    );
  }

  InputDecoration _buildInputDecoration() {
    var inputBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(Values.v10),
      borderSide: BorderSide(
          color: UIColors.primaryGreen950.withOpacity(0.2), width: Values.v2),
    );

    return InputDecoration(
      enabledBorder: inputBorder,
      focusedBorder: inputBorder,
      // hintText: 'Write a short bio?',
      hintStyle: AppTypography.regular18(
        color: AppColors.silver,
      ),
      contentPadding: const EdgeInsets.all(Values.v16),
      // filled: true,
      // fillColor: AppColors.alto.withOpacity(0.2),
    );
  }

  Widget _buildEmailField() {
    return InputFormField(
      textEditingController: _emailController,
      label: Text(
        'Email',
        style: AppTypography.poppinsRegular16(color: UIColors.primaryGreen950),
      ),
      hintText: '<EMAIL>',
      autocorrect: false,
      keyboardType: TextInputType.emailAddress,
      validator: InputValidators.email,
    );
  }

  _buildSaveButton() {
    return Button.filled(
      label: 'Save',
      onPressed: () {
        Log.debug('save button pressed');

        if (isValid()) {
          _sendCoachRegistrationRequest();
        }
      },
      // disable: _isCoachRegistrationOngoing,
      isLoading: _isCoachRegistrationOngoing,
    );
  }

  _buildUpdateButton() {
    return Button.filled(
      label: 'Update',
      onPressed: () {
        Log.debug('update button pressed');

        if (isValid()) {
          _sendCoachProfileUpdateRequest();
        }
      },
      isLoading: _isCoachRegistrationOngoing,
    );
  }

  bool isValid() {
    var bloc = BlocProvider.of<CoachProfileBloc>(context);
    if (!_formKey.currentState!.validate()) {
      _showToastMessage('Missing Information');

      return false;
    } else if (selectedCategoryNotifier.value.label == '') {
      _showToastMessage('Select a coach category');

      return false;
    }
    // else if (selectedSubCategoryNotifier.value.label == '') {
    //   _showToastMessage('Select a coach sub-category');

    //   return false;
    // }
    else if (bloc.identificationFiles.isEmpty && !_isUpdateCoachProfile) {
      _showToastMessage('Need to upload indentification document');

      return false;
    } else if (bloc.profilePictures.isEmpty) {
      _showToastMessage('Need to upload at least 1 prfile picture');

      return false;
    } else if (bloc.credentialFiles.isEmpty) {
      _showToastMessage('Need to upload coach varification document');

      return false;
    } else if (selectedSkillLevelNotifier.value.name == '') {
      _showToastMessage('Select your skill level');

      return false;
    } else if (selectedExperienceLevelNotifier.value.name == '') {
      _showToastMessage('Select years of experience');

      return false;
    } else {
      return true;
    }
  }

  _showToastMessage(String message) {
    AppToast.showToast(
      message: message,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: AppColors.warning,
    );
  }

  _sendCoachRegistrationRequest() {
    final coachProfile = CoachEntity(
      name: _fullNameController.text,
      coachCategoryId: selectedCategoryNotifier.value.name,
      profileName: (_profileNameController.text != '')
          ? _profileNameController.text
          : _fullNameController.text,
      expertise: _expertiesController.text,
      highlights: _profileTitleController.text,
      skillLevel: selectedSkillLevelNotifier.value.name,
      about: _aboutTextController.text,
      experienceInYear: selectedExperienceLevelNotifier.value.name,
      achievements: (_titleOwnController.text.isEmpty) ? [] : [_titleOwnController.text],
      media: [],
      credentials: [],
      identifications: [],
      coachId: null,
      userId: null,
      reviewStatus: null,
      reviewComment: null,
      userEmail: _emailController.text,
    );
    BlocProvider.of<CoachProfileBloc>(context)
        .add(CreateCoachProfileEvent(coachRegistrationEntity: coachProfile));
  }

  _sendCoachProfileUpdateRequest() {
    final coachProfile = CoachEntity(
      name: _fullNameController.text,
      // categories: [
      //   CoachCategory(
      //       categoryId: selectedCategoryNotifier.value.name,
      //       subCategoryId: selectedSubCategoryNotifier.value.name,)
      // ],
      coachCategoryId: selectedCategoryNotifier.value.name,
      profileName: (_profileNameController.text != '')
          ? _profileNameController.text
          : _fullNameController.text,
      expertise: _expertiesController.text,
      highlights: _profileTitleController.text,
      skillLevel: selectedSkillLevelNotifier.value.name,
      about: _aboutTextController.text,
      experienceInYear: selectedExperienceLevelNotifier.value.name,
      achievements: [_titleOwnController.text],
      media: [],
      credentials: [],
      identifications: [],
      coachId: _coachEntity!.coachId,
      userId: _coachEntity!.userId,
      reviewStatus: null,
      reviewComment: null,
      userEmail: _emailController.text,
    );

    setState(() {
      _isCoachRegistrationOngoing = true;
    });

    BlocProvider.of<CoachProfileBloc>(context).add(UpdateCoachProfileEvent(
        coachId: _coachEntity!.coachId!, coachEntity: coachProfile));
    
  }

  _buildPreviewButtton() {
    return Button.outlined(
        label: 'Preview',
        onPressed: () {
          Log.debug('preview button pressed');
          // var bloc = BlocProvider.of<CoachRegistrationBloc>(context);
          // if (bloc.profilePictures.isNotEmpty) {
          //   // _showPreview(contest: context, profilePicturePath: bloc.profilePictures.first.url);
          //   _createOverlay();
          // }

          if (isValid()) {
            _createOverlay();
          }
        });
  }

  List<MenuItem> skillLevels = [
    MenuItem(name: "beginner", label: "Beginner"),
    MenuItem(name: "mid_level", label: "Advanced"),
    MenuItem(name: "professional", label: "Professional"),
    MenuItem(name: "expert", label: "Expert"),
  ];

  _buildDropdownSkillSelectionMenu() {
    return DropdownSelectionMenu(
      key: UniqueKey(),
      title: 'Skill Level',
      lists: skillLevels,
      valuteNotifier: selectedSkillLevelNotifier,
      initialSelectedItem: selectedSkillLevelNotifier.value,
    );
  }

  List<MenuItem> experienceLevels = [
    MenuItem(name: "1-3", label: "1-3 Year"),
    MenuItem(name: "3-5", label: "3-5 Years"),
    MenuItem(name: "5-10", label: "5-10 Years"),
    MenuItem(name: "10+", label: "10+ years"),
  ];
  _buildExperienceSelectionMenu() {
    return DropdownSelectionMenu(
      lists: experienceLevels,
      title: 'Experience',
      valuteNotifier: selectedExperienceLevelNotifier,
      initialSelectedItem: selectedExperienceLevelNotifier.value,
    );
  }

  OverlayEntry? overlayEntry;
  _createOverlay() {
    removeOverly();

    assert(overlayEntry == null);

    overlayEntry = OverlayEntry(
      builder: (BuildContext context) {
        return Material(
          child: Container(
            color: Colors.black.withOpacity(0.7),
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            child: Center(
              // child: ElevatedButton(
              //   onPressed: () {
              //     removeOverly();
              //   },
              //   child: const Text('Remove Overlay'),

              // ),
              child: InkWell(
                onTap: () {
                  removeOverly();
                },
                child: _createCoachPreview(),
              ),
            ),
          ),
        );
      },
    );
    Overlay.of(context, debugRequiredFor: widget).insert(overlayEntry!);
  }

  void removeOverly() {
    overlayEntry?.remove();
    overlayEntry = null;
  }

  _createCoachPreview() {
    var bloc = BlocProvider.of<CoachProfileBloc>(context);

    return CoachProfilePreviewWidget(
        profileImage: bloc.profilePictures.first.url,
        fullName: _profileNameController.text,
        experienceInYear: selectedExperienceLevelNotifier.value.name);
  }

  void _navigateToCoachFeatureDashboard() {
    // move to coach featue page (dashboare(2)).
    // download profile-status 
    navigatorKey?.currentState?.pushAndRemoveUntil(
                              MaterialPageRoute(
                                  builder: (_) => DashboardPage(selectedIndex: 2,)),
                              (route) => false,
                            );
    
    // context
    //     .read<DashboardCubit>()
    //     .changePage(2); // coach featue position is 2 in nav bar
  }
}
