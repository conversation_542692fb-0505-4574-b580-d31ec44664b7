import 'package:fitsomnia_app/src/core/services/camera_v2/ui/camera_v2_page.dart';
import 'package:flutter/material.dart';
import 'dart:io';

class CameraV2Test extends StatefulWidget {
  const CameraV2Test({super.key});

  @override
  State<CameraV2Test> createState() => _CameraV2TestState();
}

class _CameraV2TestState extends State<CameraV2Test> {

  File? _imageFile;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Camera V2 Test'),
      ),
      body: Center(
        child: Column(
          children: [
            (_imageFile != null)
                ? Image.file(_imageFile!)
                : const Text('No image selected'),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => CameraV2Page(
                            maxRecordingTimeInSecond: 0,
                            onImageCaptured: (p0) {
                              setState(() {
                                _imageFile = p0;
                              });
                            },
                            showMediaIcon: false,
                            onlyRecord: false,
                          )),
                );
              },
              child: const Text('Select image from camera'),
            ),
          ],
        ),
      ),
    );
  }
}
