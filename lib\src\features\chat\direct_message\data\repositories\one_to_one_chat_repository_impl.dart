import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/data/model/one_to_one_chat_model.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/data/data_sources/one_to_one_chat_data_source.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/entity/chat_history_entity.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/domain/repositories/one_to_one_chat_repository.dart';

class OneToOneChatRepositoryImpl implements OneToOneChatRepository {
  final OneToOneChatDataSource oneToOneChatDataSource;

  OneToOneChatRepositoryImpl({required this.oneToOneChatDataSource});

  @override
  Future<Either<String, List<ChatHistoryEntity>>> getChatHistory(
      {int? limit, int? offset, required String userId}) async {
    try {
      final Response response = await oneToOneChatDataSource.getChatHistory(
        userId: userId,
        limit: limit,
        offset: offset,
      );
      final data = response.data['data'];

      List<ChatHistoryEntity> chatHistoryModel = data
          .map<ChatHistoryEntity>((chats) => ChatHistoryModel.fromJson(chats))
          .toList();

      List<ChatHistoryEntity> reversedList =
          List.from(chatHistoryModel.reversed);

      return Right(reversedList);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left(e.toString());
    }
  }
}
