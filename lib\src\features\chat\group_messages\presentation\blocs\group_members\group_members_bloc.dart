import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/new_message/domain/entity/all_users_entity.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/use_cases/get_group_members_use_case.dart';

part 'group_members_event.dart';
part 'group_members_state.dart';

class GroupMembersBloc extends Bloc<GroupMembersEvent, GroupMembersState> {
  GroupMembersBloc({required this.getGroupMembersUseCase})
      : super(GroupMembersInitial()) {
    on<GroupMembersEvent>(_onGroupMembersEvent);
  }

  late GetGroupMembersUseCase getGroupMembersUseCase;

  Future<void> _onGroupMembersEvent(
    GroupMembersEvent event,
    Emitter<GroupMembersState> emit,
  ) async {
    emit(GroupMembersLoading());

    try {
      final response = await getGroupMembersUseCase.call(
        groupId: event.groupId,
        limit: event.limit,
        offset: event.offset,
      );

      response.fold(
        (l) => emit(
          GroupMembersFailure(
            errorMessage: l.toString(),
          ),
        ),
        (r) => emit(GroupMembersSuccess(groupMembersList: r)),
      );
    } catch (_) {
      emit(
        GroupMembersFailure(
          errorMessage: TextConstants.failedToLoadData,
        ),
      );
    }
  }
}
