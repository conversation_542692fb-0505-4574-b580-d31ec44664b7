import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_profile_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_program_search_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/use_case/coach_newsfeed_use_case.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';

part 'coach_newsfeed_event.dart';
part 'coach_newsfeed_state.dart';

class CoachNewsfeedBloc extends Bloc<CoachNewsfeedEvent, CoachNewsfeedState> {
  CoachNewsfeedBloc({required this.coachNewsfeedUseCase})
      : super(CoachNewsfeedInitial()) {
    on<CoachNewsfeedEvent>((event, emit) {
      // TODO: implement event handler
    });

    on<GetCoachProfilesEvent>(_onGetCoachProfilesEvent);
    on<GetSingleCoachProfileEvent>(_onGetSingleCoachProfileEvent);
    on<GetBeastCoachProfilesEvent>(_onGetBeastCoachProfilesEvent);
    on<GetCoachProgramsEvent>(_onGetCoachProgramsEvent);
  }
  final CoachNewsfeedUseCase coachNewsfeedUseCase;

  Future<void> _onGetCoachProfilesEvent(
      GetCoachProfilesEvent event, Emitter<CoachNewsfeedState> emit) async {
    try {
      emit(CoachProfilesLoading());
      final response = await coachNewsfeedUseCase.getCoachProfiles(
        filterType: event.filterType,
        categoryId: event.categoryId,
        subcategoryId: event.subcategoryId,
        namePrefix: event.namePrefix,
        offset: event.offset,
        limit: event.limit,
      );

      response.fold(
        (l) {
          emit(GetCoachProfilesFail(data: l));
        },
        (r) {
          emit(GetCoachProfilesSuccess(coachProfiles: r));
        },
      );
    } catch (e) {
      emit(GetCoachProfilesFail(data: e));
    }
  }

  Future<void> _onGetSingleCoachProfileEvent(GetSingleCoachProfileEvent event,
      Emitter<CoachNewsfeedState> emit) async {
    try {
      final response = await coachNewsfeedUseCase.getCoachProfileById(
          coachId: event.coachId);
      response.fold(
        (l) {
          emit(GetSingleCoachProfileFail(data: l));
        },
        (r) {
          emit(GetSingleCoachProfileSuccess(coachProfile: r));
        },
      );
    } catch (e) {
      emit(GetSingleCoachProfileFail(data: e));
    }
  }

  Future<void> _onGetBeastCoachProfilesEvent(GetBeastCoachProfilesEvent event,
      Emitter<CoachNewsfeedState> emit) async {
    try {
      emit(CoachProfilesLoading());
      final response = await coachNewsfeedUseCase.getCoachProfiles(
        filterType: CoachProfileFilterType.BEST,
        offset: event.offset,
        limit: event.limit,
      );

      response.fold(
        (l) {
          emit(GetBestCoachProfilesFail(data: l));
        },
        (r) {
          emit(GetBestCoachProfilesSuccess(coachProfiles: r));
        },
      );
    } catch (e) {
      emit(GetBestCoachProfilesFail(data: e));
    }
  }

  Future<void> _onGetCoachProgramsEvent(
    GetCoachProgramsEvent event,
    Emitter<CoachNewsfeedState> emit,
  ) async {
    try {
      emit(CoachProgramsLoading());
      final response = await coachNewsfeedUseCase.getPrograms(
        filterType: event.filterType,
        namePrefix: event.namePrefix,
        offset: event.offset,
        limit: event.limit,
      );

      response.fold(
        (l) {
          emit(GetCoachProgramsFail(data: l));
        },
        (r) {
          emit(GetCoachProgramsSuccess(programs: r));
        },
      );
    } catch (e) {
      emit(GetCoachProgramsFail(data: e));
    }
  }
}
