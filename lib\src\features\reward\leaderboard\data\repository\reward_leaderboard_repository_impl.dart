import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/exception/network_exception.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/data/data_source/reward_leaderboard_data_source.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/data/model/reward_leaderboard_data_model.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/data/model/reward_point_rank_model.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_leaderboard_data_entity.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_point_rank_entity.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/repository/reward_leaderboard_repository.dart';

class RewardLeaderboardRepositoryImpl extends RewardLeaderboardRepository{
  final RewardLeaderboardDataSource dataSource;

  RewardLeaderboardRepositoryImpl({required this.dataSource});
  
  @override
  Future<Either<ErrorModel, List<RewardLeaderboardDataEntity>>> getTopUser({required int? offset, required int? limit, required String? filterType}) async {
    try {
      final Response response = await dataSource.getTopUser(offset: offset, limit: limit, filterType: filterType);

      final data = response.data['data'];

      List<RewardLeaderboardDataEntity> entities = data
          .map<RewardLeaderboardDataEntity>(
              (entity) => RewardLeaderboardDataModel.fromJson(entity).toEntity())
          .toList();

      return Right(entities);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel.error!);
    }
  }

  @override
  Future<Either<ErrorModel, RewardPointRankEntity>> getUserCurrentRank() async {
    try {
      final Response response = await dataSource.getUserCurrentRank();

      final data = response.data['data'];

      RewardPointRankEntity entity = RewardPointRankModel.fromJson(data).toEntity();

      return Right(entity);
    } catch (e, stackTrace) {
      Log.error(e.toString());
      Log.error(stackTrace.toString());

      return Left((e as NetworkExceptionV2).errorModel.error!);
    }
  }

}