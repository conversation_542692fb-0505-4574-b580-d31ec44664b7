import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/routes/global_navigation.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_info_widget.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/core/widgets/loading_widget.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/root/presentation/page/chat_dashboard_page.dart';
import 'package:fitsomnia_app/src/features/notification/data/models/notification.dart';
import 'package:fitsomnia_app/src/features/notification/presentation/bloc/notification_bloc.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/pages/dashboard/others_profile_page.dart';
import 'package:fitsomnia_app/src/features/profile/presentation/pages/post_details/timeline_post_details_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AllNotification extends StatefulWidget {
  const AllNotification({Key? key}) : super(key: key);

  @override
  State<AllNotification> createState() => _AllNotificationState();
}

class _AllNotificationState extends State<AllNotification> {
  @override
  void initState() {
    super.initState();
    BlocProvider.of<NotificationBloc>(context).add(LoadAllNotificationsEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
      buildWhen: (previous, current) =>
          ((current is NotificationLoadingState) ||
              (current is AllNotificationLoadedState) ||
              (current is NotificationErrorState) &&
                  current.notificationType == NotificationType.ALL),
      builder: (context, state) {
        if (state is NotificationLoadingState &&
            state.notificationType == NotificationType.ALL) {
          return const Center(
            child: LoadingWidget(textColor: AppColors.black),
          );
        } else if (state is AllNotificationLoadedState) {
          return state.notifications.isEmpty
              ? const InfoWidget(message: TextConstants.noNotificationFound)
              : ListView.builder(
                  padding: EdgeInsets.only(top: 16.h),
                  itemCount: state.notifications.length,
                  itemBuilder: (context, index) {
                    return _buildNotificationItem(state.notifications[index]);
                  },
                );
        } else if (state is MarkAllNotificationAsReadState) {
          return state.notifications.isEmpty
              ? const Center(child: Text(TextConstants.noNotificationFound))
              : ListView.builder(
                  itemCount: state.notifications.length,
                  itemBuilder: (context, index) {
                    return _buildNotificationItem(state.notifications[index]);
                  },
                );
        } else {
          return const Center(
            child: Text(TextConstants.somethingWentWrong),
          );
        }
      },
    );
  }

  //ignore: long-method
  Widget _buildNotificationItem(NotificationModel notification) {
    return GestureDetector(
      onTap: () {
        // Debug logs to help troubleshoot in-app notification navigation
        print('DEBUG: Notification tapped - Module: ${notification.module}');
        print(
            'DEBUG: Notification feature: ${notification.notification.feature}');

        if (notification.module == 'REMINDER') {
          print('DEBUG: Processing REMINDER notification');
          print(
              'DEBUG: Raw notification data: ${notification.notification.toJson()}');
          _navigateBasedOnFeature(notification.notification.feature);
        } else if (notification.module == 'POST' &&
            notification.notification.documentId != null &&
            notification.notification.documentId != '') {
          _navigateToPostDetails(notification.notification.documentId!);
        } else if (notification.module == 'SPOT' &&
            notification.notification.documentId != null &&
            notification.notification.documentId != '') {
          _navigateToOthersTimeline(notification.notification.documentId!);
        } else if (notification.module == 'ONE_TO_ONE_CHAT' ||
            notification.module == 'GROUP_CHAT') {
          _navigateToChatDashboard();
        } else {
          print('DEBUG: Unknown notification module: ${notification.module}');
        }
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        margin: EdgeInsets.only(bottom: Values.v10.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Values.v10.r),
          color: AppColors.white,
          boxShadow: [
            BoxShadow(
              color: AppColors.grey.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Row(
          children: [
            ImageContainer.circularImage(
              image: notification.notification.createdBy.avatar,
              radius: 30,
            ),
            SizedBox(width: Values.v16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    notification.notification.title,
                    style: Theme.of(context).textTheme.titleMedium!.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  Text(
                    notification.notification.content,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _getNotificationStatus(bool status) {
    return Container(
      margin: EdgeInsets.only(left: Values.v3.w),
      height: Values.v15.h,
      width: Values.v48.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(Values.v10.r),
        color: status ? AppColors.hintOfGreen : AppColors.pippin,
      ),
      child: Center(
        child: Text(
          status ? 'Read' : 'Unread',
          style: AppTypography.regular8(color: AppColors.grey),
        ),
      ),
    );
  }

  void _navigateToPostDetails(String postId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TimelinePostDetails(postId: postId),
      ),
    );
  }

  void _navigateToOthersTimeline(String userId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => OthersProfilePage(userId: userId),
      ),
    );
  }

  void _navigateToChatDashboard() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ChatDashboardPage(),
      ),
    );
  }

  void _navigateBasedOnFeature(String? feature) {
    // Dynamic navigation based on feature field from reminder API
    print('DEBUG: Navigating based on feature: "$feature"'); // Debug log
    print('DEBUG: Feature is null: ${feature == null}'); // Debug log
    print('DEBUG: Feature is empty: ${feature?.isEmpty ?? true}'); // Debug log

    // Test all possible feature values to debug
    _testFeatureParsing();

    ReminderFeatureType featureType = ReminderFeatureType.fromString(feature);
    String? route = ReminderNavigationHelper.getRouteForFeature(featureType);
    dynamic arguments =
        ReminderNavigationHelper.getArgumentsForFeature(featureType);

    print('DEBUG: Feature type: $featureType'); // Debug log
    print('DEBUG: Route: $route'); // Debug log
    print('DEBUG: Arguments: $arguments'); // Debug log

    if (route != null) {
      print('DEBUG: Attempting to navigate to route: $route'); // Debug log
      try {
        Navigator.pushNamed(
          context,
          route,
          arguments: arguments,
        );
        print('DEBUG: Navigation successful to: $route'); // Debug log
      } catch (e) {
        print('DEBUG: Navigation failed to $route: $e'); // Debug log
        print(
            'DEBUG: Falling back to diet dashboard due to navigation error'); // Debug log
        _fallbackToDietDashboard();
      }
    } else {
      print(
          'DEBUG: No route found for feature: "$feature", defaulting to diet dashboard'); // Debug log
      _fallbackToDietDashboard();
    }
  }

  void _testFeatureParsing() {
    print('DEBUG: Testing feature parsing...');
    List<String> testFeatures = [
      'rewards',
      'referrals',
      'daily-tasks',
      'challenges',
      'diet',
      'leaderboard'
    ];

    for (String testFeature in testFeatures) {
      ReminderFeatureType featureType =
          ReminderFeatureType.fromString(testFeature);
      String? route = ReminderNavigationHelper.getRouteForFeature(featureType);
      print(
          'DEBUG: Test - Feature: "$testFeature" -> Type: $featureType -> Route: $route');
    }
  }

  void _fallbackToDietDashboard() {
    print('DEBUG: Executing fallback to diet dashboard'); // Debug log
    Navigator.pushNamed(
      context,
      '/diet_dashboard',
      arguments: [false, false],
    );
  }
}
