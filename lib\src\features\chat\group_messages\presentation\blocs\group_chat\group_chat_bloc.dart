import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/entity/group_chat_entity.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/use_cases/get_group_history_use_case.dart';

part 'group_chat_event.dart';
part 'group_chat_state.dart';

class GroupChatBloc extends Bloc<GroupChatEvent, GroupChatState> {
  GroupChatBloc({required this.getGroupHistoryUseCase})
      : super(GroupChatInitial()) {
    on<GroupChatHistoryEvent>(_onGroupChatHistoryEvent);
    on<UpdateGroupChatHistoryEvent>(_onUpdateGroupChatHistoryEvent);
    on<AddGroupChatHistoryEvent>(_onAddGroupChatHistoryEvent);
    on<RemoveFromGroupChatHistoryListEvent>(_onRemoveFromGroupChatHistoryListEvent);
    on<GetMoreGroupChatHistoryEvent>(_onGetMoreGroupChatHistoryEvent);
  }

  late GetGroupHistoryUseCase getGroupHistoryUseCase;
  List<GroupChatEntity> groupConversationList = [];
  bool hasReachedMaximum = false;

  Future<void> _onGroupChatHistoryEvent(
    GroupChatHistoryEvent event,
    Emitter<GroupChatState> emit,
  ) async {
    emit(GroupChatLoading());

    try {
      final response = await getGroupHistoryUseCase.call(
        groupId: event.groupId,
        limit: event.limit,
        offset: event.offset,
      );

      if (event.offset == 0) {
        groupConversationList.clear();
        hasReachedMaximum = false;
      }

      response.fold(
        (l) => emit(
          GroupChatFailure(
            errorMessage: l.toString(),
          ),
        ),
        (r) => emit(GroupChatSuccess(groupChatEntities: r)),
      );
    } catch (_) {
      emit(
        GroupChatFailure(
          errorMessage: TextConstants.failedToLoadData,
        ),
      );
    }
  }

  Future<void> _onGetMoreGroupChatHistoryEvent(
      GetMoreGroupChatHistoryEvent event,
      Emitter<GroupChatState> emit,
      ) async {
    if (!hasReachedMaximum) {
      try {
        final response = await getGroupHistoryUseCase.call(
          groupId: event.groupId,
          offset: event.offset,
          limit: event.limit,
        );

        response.fold(
              (l) => Log.error("Error Loading Data"),
              (r) {
            if (r.isEmpty) {
              hasReachedMaximum = true;
            }

            emit(GroupChatSuccess(groupChatEntities: r));
          },
        );
      } catch (_) {
        emit(
          GroupChatFailure(
            errorMessage: TextConstants.failedToLoadData,
          ),
        );
      }
    }
  }

  Future<void> _onUpdateGroupChatHistoryEvent(
      UpdateGroupChatHistoryEvent event,
      Emitter<GroupChatState> emit,
      ) async {
    groupConversationList.insertAll(0, event.groupChatEntities);
  }

  Future<void> _onAddGroupChatHistoryEvent(
      AddGroupChatHistoryEvent event,
      Emitter<GroupChatState> emit,
      ) async {
    groupConversationList.add(event.groupChatEntity);
  }

  Future<void> _onRemoveFromGroupChatHistoryListEvent(
      RemoveFromGroupChatHistoryListEvent event,
      Emitter<GroupChatState> emit,
      ) async {
    int index = groupConversationList.indexWhere((element) => element.id == event.messageId);
    groupConversationList.removeAt(index);
  }
}
