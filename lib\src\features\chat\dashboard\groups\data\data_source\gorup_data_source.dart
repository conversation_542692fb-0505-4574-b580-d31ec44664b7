import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class GroupDataSource {
  Future<Response> createGroup({required Map<String, dynamic> mapData});

  Future<Response> getGroupList(int? limit);

  Future<Response> getGroupHistory({
    required String groupId,
    int? limit,
    int? offset,
  });

  Future<Response> getGroupMembersList({
    required String groupId,
    int? limit,
    int? offset,
  });

  Future<Response> leaveGroup({required String groupId});

  Future<Response> addNewMember(
      {required String groupId, required String memberId});
}

class GroupDataSourceImpl implements GroupDataSource {
  GroupDataSourceImpl({required this.restClient});

  final RestClient restClient;

  @override
  Future<Response> createGroup({required Map<String, dynamic> mapData}) async {
    final response = await restClient.post(
      APIType.PROTECTED,
      API.createGroupChat,
      mapData,
    );

    return response;
  }

  @override
  Future<Response> getGroupList(int? limit) async {
    final response = await restClient.get(
      APIType.PROTECTED,
      limit != null ? "${API.getGroupList}?limit=$limit" : API.getGroupList,
    );

    return response;
  }

  @override
  Future<Response> getGroupHistory(
      {required String groupId, int? limit, int? offset}) async {
    final response = await restClient.get(
        APIType.PROTECTED, API.getGroupHistory(groupId: groupId), data: {
      if (limit != null) "limit": limit,
      if (offset != null) "offset": offset
    });

    return response;
  }

  @override
  Future<Response> getGroupMembersList(
      {required String groupId, int? limit, int? offset}) async {
    final response = await restClient.get(
        APIType.PROTECTED, API.getGroupParticipants(groupId: groupId), data: {
      if (limit != null) "limit": limit,
      if (offset != null) "offset": offset
    });

    return response;
  }

  @override
  Future<Response> leaveGroup({required String groupId}) async {
    final response = await restClient.get(
      APIType.PROTECTED,
      API.leaveGroupChat(
        groupId: groupId,
      ),
    );

    return response;
  }

  @override
  Future<Response> addNewMember(
      {required String groupId, required String memberId}) async {
    final response = await restClient.post(
      APIType.PROTECTED,
      API.addNewMember(memberId: memberId, groupId: groupId),
      {},
    );

    return response;
  }
}
