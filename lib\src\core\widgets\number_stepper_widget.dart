import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:flutter/material.dart';
import 'package:im_stepper/stepper.dart';

class NumberStepperWidget extends StatelessWidget {
  const NumberStepperWidget({
    Key? key,
    this.activeStep,
    this.numbers,
  }) : super(key: key);

  final int? activeStep;
  final List<int>? numbers;

  @override
  Widget build(BuildContext context) {
    return NumberStepper(
      numbers: numbers ??
          const [
            1,
            2,
            3,
          ],
      enableStepTapping: false,
      enableNextPreviousButtons: false,
      numberStyle: AppTypography.regular24(
        color: Colors.black,
      ),
      stepColor: UIColors.secondary,
      activeStep: activeStep ?? 0,
      activeStepColor: AppColors.primaryGreen,
      activeStepBorderColor: AppColors.primaryGreen,
      activeStepBorderPadding: 2,
      activeStepBorderWidth: 2,
      lineColor: AppColors.grey,
    );
  }
}
