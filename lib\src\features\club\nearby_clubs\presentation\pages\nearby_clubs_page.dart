import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/firebase/firebase_service.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/widgets/app_info_widget.dart';
import 'package:fitsomnia_app/src/core/widgets/loading_widget.dart';
import 'package:fitsomnia_app/src/features/club/nearby_clubs/presentation/bloc/nearby_clubs_bloc.dart';
import 'package:fitsomnia_app/src/features/club/nearby_clubs/presentation/widgets/nearby_club_card.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/bloc/dashboard_cubit.dart';
import 'package:fitsomnia_app/src/features/search/presentation/bloc/search/search_view_cubit.dart';
import 'package:fitsomnia_app/src/features/settings/about/presentation/widget/webivew_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

part '../widgets/club_search_bar.dart';

class NearbyClubsPage extends StatefulWidget {
  const NearbyClubsPage({Key? key}) : super(key: key);

  @override
  State<NearbyClubsPage> createState() => _NearbyClubsPageState();
}

class _NearbyClubsPageState extends State<NearbyClubsPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<DashboardCubit, DashboardState>(
      listener: (context, state) {
        if (state.index == 4 && state.status == DashboardStatus.Success) {
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      },
      child: BlocBuilder<NearbyClubsBloc, BaseState>(
        builder: (context, state) {
          if (state is ErrorState) {
            return RefreshIndicator(
              onRefresh: () async {
                context
                    .read<NearbyClubsBloc>()
                    .add(const FindNearbyClubsEvent(pullToRefresh: true));
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: SizedBox(
                  height: .7.sh,
                  child: Center(
                    child: InfoWidget(message: state.data),
                  ),
                ),
              ),
            );
          } else if (state is NearbyClubsSuccessState) {
            return state.clubs.isEmpty
                ? const InfoWidget(message: TextConstants.noClubFoundNearYourLocation)
                : _clubBuilder(context, state);
          }

          return RefreshIndicator(
            onRefresh: () async {
              context
                  .read<NearbyClubsBloc>()
                  .add(const FindNearbyClubsEvent(pullToRefresh: true));
            },
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Container(
                margin: EdgeInsets.only(top: .35.sh),
                child: const Center(child: LoadingIndicator()),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _clubBuilder(
    BuildContext context,
    NearbyClubsSuccessState state,
  ) {
    return RefreshIndicator(
      onRefresh: () async {
        context
            .read<NearbyClubsBloc>()
            .add(const FindNearbyClubsEvent(pullToRefresh: true));
      },
      child: ListView.builder(
        controller: _scrollController,
        itemCount: state.clubs.length,
        itemBuilder: (context, index) {
          if (index == 0) {
            return Column(
              children: [
                SizedBox(height: 10.h),
                const _ClubSearchBar(),
                Padding(
                  padding: EdgeInsets.only(
                    left: 16.w,
                    right: 16.w,
                    bottom: 10.h,
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        alignment: Alignment.centerLeft,
                        child: Text(TextConstants.clubsNearYou,
                            style: AppTypography.semiBold18()),
                      ),
                      GestureDetector(
                        onTap: () {
                          _navigateToWebView();
                          FirebaseService().logFeatureUsage('club', 'club_near_me', '');
                        },
                        child: const Icon(
                          Icons.info_outline_rounded,
                          color: AppColors.blue,
                        ),
                      ),
                    ],
                  ),
                ),
                NearbyClubCard(club: state.clubs[index]),
              ],
            );
          }

          return NearbyClubCard(club: state.clubs[index]);
        },
      ),
    );
  }

  Future<void> _navigateToWebView() {
    return Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => const WebViewPage(
          url: 'https://fitsomnia.com/clubs/disclaimer',
          fullUrl: true,
        ),
      ),
    );
  }

  void _onScroll() {
    if (_isBottom) {
      context
          .read<NearbyClubsBloc>()
          .add(const FindNearbyClubsEvent(pullToRefresh: false));
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;

    return currentScroll >= (maxScroll * 0.9);
  }
}
