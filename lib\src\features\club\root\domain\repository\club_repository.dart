import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/entities/club_members_entity.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/entities/live_member_model.dart';
import 'package:fitsomnia_app/src/features/club/details/domain/entities/nearby_clubs_members_entity.dart';
import 'package:fitsomnia_app/src/features/club/root/domain/entities/club_entity.dart';

abstract class ClubRepository {
  Future<Either<ErrorResponseModel, ClubEntity>> myClub();

  Future<Either<ErrorResponseModel, List<ClubEntity>>> findNearbyClubs({
    double? lat,
    double? long,
    int? offset,
  });

  Future<Either<ErrorResponseModel, String>> joinClub(String clubId);

  Future<Either<ErrorResponseModel, String>> leaveAndJoinClub(String clubId);

  Future<Either<ErrorResponseModel, String>> leaveClub(String clubId);

  Future<Either<ErrorResponseModel, List<ClubMemberEntity>>> clubMembers(
    String id,
    int? offset,
  );

  Future<Either<ErrorResponseModel, List<NearbyClubsMemberEntity>>>
      nearbyClubsMembers(
    Map<String, dynamic> map,
    String id,
    int? offset,
  );

  Future<Either<ErrorModel, List<LiveMemberModel>>> liveMembersNearMe(
    Map<String, dynamic> query,
  );
}
