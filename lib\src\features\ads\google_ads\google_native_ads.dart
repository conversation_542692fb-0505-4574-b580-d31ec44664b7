import 'dart:io';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/ads/google_ads/ad_helper.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class GoogleNativeAdsWidget extends StatefulWidget {
  GoogleNativeAdsWidget({Key? key}) : super(key: key);

  final String adUnitId =
      kDebugMode ? AdHelperTest.nativeAd : AdHelper.nativeAd;
  final String adFactoryId = 'googleNativeAdFactory';

  //TODO ad size not accurate
  final double nativeAdHeight = Platform.isAndroid ? 500.h : 300.h;

  @override
  State<GoogleNativeAdsWidget> createState() => _GoogleNativeAdsWidgetState();
}

class _GoogleNativeAdsWidgetState extends State<GoogleNativeAdsWidget> {
  NativeAd? _nativeAd;
  bool _nativeAdIsLoaded = false;
  bool _nativeAdLoadError = false;

  @override
  Widget build(BuildContext context) {
    if (_nativeAd == null ||
        !_nativeAdIsLoaded ||
        _nativeAdLoadError) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(Values.v16),
      margin: EdgeInsets.only(top: 5.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(Values.v10.r),
        color: AppColors.white,
      ),
      child: Center(
        child: Column(
          children: [
            Stack(
              children: [
                // Container(
                //   height: widget.nativeAdHeight,
                //   width: MediaQuery.of(context).size.width.h,
                //   child: Center(
                //     child: CircularProgressIndicator(
                //       color: UIColors.primary,
                //     ),
                //   ),
                // ),
                if (_nativeAdIsLoaded && _nativeAd != null)
                  Container(
                    height: widget.nativeAdHeight,
                    width: MediaQuery.of(context).size.width.h,
                    child: AdWidget(ad: _nativeAd!),
                  ),
              ],
            ),
            // TextButton(
            //     onPressed: _loadNativeAd, child: const Text('Refresh Ads')),
            // FutureBuilder(
            //   future: MobileAds.instance.getVersionString(),
            //   builder: (context, snapshot) {
            //     var versionString = snapshot.data ?? "";

            //     return Text(versionString);
            //   },
            // ),
          ],
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();

    _loadNativeAd();
  }

  @override
  void dispose() {
    _nativeAd?.dispose();
    super.dispose();
  }

  void _loadNativeAd() {
    // setState(() {
    //   _nativeAdIsLoaded = false;
    // });

    _nativeAd = NativeAd(
      adUnitId: widget.adUnitId,
      factoryId: widget.adFactoryId,
      // factoryId: widget.adFactoryId,
      listener: NativeAdListener(
        onAdLoaded: (ad) {
          if(!mounted) {
            ad.dispose();

            return;
          }
          setState(() {
            _nativeAdIsLoaded = true;
          });
        },
        onAdFailedToLoad: (ad, error) {
          Log.debug('$NativeAd faied to load: $error');
          ad.dispose();
          setState(() {
            _nativeAdLoadError = true;
          });
        },
        // onAdOpened: (ad) {
        //   Log.debug('Ad opened.');
        // },
        // onAdClosed: (ad) {
        //   Log.debug('Ad closed.');
        // },
        // onAdImpression: (ad) {
        //   Log.debug('Ad impression.');
        // },
        // onAdClicked: (ad) {
        //   Log.debug('Ad clicked.');
        // },
      ),
      request: const AdRequest(),
    )..load();
  }
}
//// Test NativeAd using flutter NativeTemplate
// nativeTemplateStyle: NativeTemplateStyle(
//         // Required: Choose a template.
//         templateType: TemplateType.medium,
//         // Optional: Customize the ad's style.
//         mainBackgroundColor: Colors.purple,
//         cornerRadius: 10.0,
//         callToActionTextStyle: NativeTemplateTextStyle(
//             textColor: Colors.cyan,
//             backgroundColor: Colors.red,
//             style: NativeTemplateFontStyle.monospace,
//             size: 16.0),
//         primaryTextStyle: NativeTemplateTextStyle(
//             textColor: Colors.red,
//             backgroundColor: Colors.cyan,
//             style: NativeTemplateFontStyle.italic,
//             size: 16.0),
//         secondaryTextStyle: NativeTemplateTextStyle(
//             textColor: Colors.green,
//             backgroundColor: Colors.black,
//             style: NativeTemplateFontStyle.bold,
//             size: 16.0),
//         tertiaryTextStyle: NativeTemplateTextStyle(
//             textColor: Colors.brown,
//             backgroundColor: Colors.amber,
//             style: NativeTemplateFontStyle.normal,
//             size: 16.0),
//       ),
