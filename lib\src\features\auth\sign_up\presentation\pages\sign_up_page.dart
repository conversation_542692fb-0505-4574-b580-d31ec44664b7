import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/validators/input_validators.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/auth/login/presentation/pages/login_page.dart';
import 'package:fitsomnia_app/src/features/auth/root/presentations/widgets/authentication_wrapper.dart';
import 'package:fitsomnia_app/src/features/auth/root/presentations/widgets/center_text.dart';
import 'package:fitsomnia_app/src/features/auth/root/presentations/widgets/social_icon_container.dart';
import 'package:fitsomnia_app/src/features/auth/sign_up/presentation/bloc/sign_up_bloc.dart';
import 'package:fitsomnia_app/src/features/on_boarding/presentation/bloc/shared_bloc.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:input_form_field/input_form_field.dart';

part '../widgets/sign_up_form.dart';

ValueNotifier<bool> agreedWithTerms = ValueNotifier<bool>(false);
ValueNotifier<String> countryCode = ValueNotifier<String>('+1');

class SignUpPage extends StatelessWidget {
  SignUpPage({Key? key}) : super(key: key);

  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: context.watch<SignUpBloc>().state is LoadingState,
      child: ScrollableWrapper(
        // appBar: AppBar(
        //   backgroundColor: UIColors.background,
        //   // elevation: 0,
        //   // title: Text(
        //   //   'Sign Up',
        //   //   style: AppTypography.medium30(),
        //   // ),
        //   // centerTitle: true,
        // ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: Values.v8.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              // mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Center(
                  child: Image.asset(
                    Assets.workout,
                    width: Values.v70.w,
                    height: Values.v70.h,
                  ),
                ),
                CenterText(
                  text: 'Welcome to',
                  textStyle: TextStyle(
                    color: UIColors.primaryGreen900,
                    fontSize: FontSize.s32.sp,
                    fontWeight: FontWeight.w300,
                    fontFamily: FontConstants.promptFontFamily,
                    height: 1.0,
                  ),
                ),
                CenterText(
                  text: 'Fitsomnia',
                  textStyle: TextStyle(
                    color: UIColors.primaryGreen900,
                    fontSize: FontSize.s48.sp,
                    fontWeight: FontWeight.w500,
                    fontFamily: FontConstants.promptFontFamily,
                    height: 1.0,
                  ),
                ),
                SizedBox(height: Values.v80.h),
                Form(
                  key: _formKey,
                  child: const _SignUpForm(),
                ),
                SizedBox(height: Values.v20.h),
                _buildSignUpButton(context),
                SizedBox(height: Values.v60.h),

                Align(
                  alignment: Alignment.center,
                  child: Text(
                    TextConstants.orSignUpWith,
                    style: AppTypography.sfRegular16(),
                  ),
                ),
                SizedBox(height: Values.v20.h),
                _buildSocialSignUpOptions(context),
                SizedBox(height: Values.v24.h),
                _buildAlreadyHaveAnAccount(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSocialSignUpOptions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SocialIconContainer(
          onTap: () {
            context.read<SignUpBloc>().add(SignUpWithGoogleEvent());
          },
          icon: Assets.googleIcon,
        ),
        SizedBox(width: Values.v25.w),
        SocialIconContainer(
          onTap: () {
            context.read<SignUpBloc>().add(SignUpWithAppleEvent());
          },
          icon: Assets.appleIcon,
        ),
        SizedBox(width: Values.v25.w),
        SocialIconContainer(
          onTap: () {
            context.read<SignUpBloc>().add(SignUpWithFacebookEvent());
          },
          icon: Assets.facebookIcon,
        ),
      ],
    );
  }

  Widget _buildSignUpButton(BuildContext context) {
    return BlocBuilder<SignUpBloc, BaseState>(
      builder: (context1, state) {
        return ValueListenableBuilder(
          builder: (context2, value, _) {
            return Button.filled(
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  BlocProvider.of<SignUpBloc>(context1).add(
                    SignUpWithEmailAndPasswordEvent(),
                  );
                }
              },
              isLoading: state is LoadingState,
              label: TextConstants.signUpButtonText,
              disable: !agreedWithTerms.value,
            );
          },
          valueListenable: agreedWithTerms,
        );
      },
    );
  }

  Widget _buildAlreadyHaveAnAccount(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          TextConstants.alreadyHaveAnAccount,
          style: AppTypography.sfRegular16(),
        ),
        GestureDetector(
          onTap: () {
            Navigator.pushReplacementNamed(context, Routes.login);
          },
          child: Text(
            TextConstants.login,
            style: AppTypography.sfRegular16(
              color: UIColors.primary,
            ),
          ),
        ),
      ],
    );
  }
}
