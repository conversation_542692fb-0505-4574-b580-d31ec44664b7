import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/extensions/extensions.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/fonts.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/pages/coach_program_checkout_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/data/model/coach_program_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/bloc/coach_program_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/widgets/create_coach_program_feature_info_list_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/page/coach_registration_introduction_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/presentation/widget/process_steps_timeline_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/presentation/widgets/program_preview_price_list_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/presentation/widgets/coach_program_reviews_widget.dart';
import 'package:fitsomnia_app/src/features/coach/root/presentation/bloc/coach_bloc.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/bloc/dashboard_cubit.dart';
import 'package:fitsomnia_app/src/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CoachProgramPreviewPage extends StatefulWidget {
  const CoachProgramPreviewPage({
    super.key,
    required this.isCreatingCoachProgram,
    required this.isUpdateCoachProgram,
    required this.isPreviewCoachProgram,
    required this.programEntity,
  });

  final bool isCreatingCoachProgram;
  final bool isUpdateCoachProgram;
  final bool isPreviewCoachProgram;
  final CoachProgramEntity programEntity;

  @override
  State<CoachProgramPreviewPage> createState() =>
      _CoachProgramPreviewPageState();
}

class _CoachProgramPreviewPageState extends State<CoachProgramPreviewPage> {
  late CoachProgramEntity _programEntity;
  CoachEntity? _coachEntity;
  CoachProgramPaymentInfo? selectedPaymentType;
  String? coachId;

  @override
  void initState() {
    super.initState();
    coachId = context.read<CoachBloc>().coachId;
    _programEntity = widget.programEntity;

    if (_programEntity.coachId != null) {
      BlocProvider.of<CoachNewsfeedBloc>(context)
          .add(GetSingleCoachProfileEvent(coachId: _programEntity.coachId!));
    }

    if (widget.isPreviewCoachProgram) {
      BlocProvider.of<CoachBloc>(context).add(
          GetCoachOfferedSingleProgramDetail(
              programId: _programEntity.programId!));
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<CoachProgramBloc, CoachProgramState>(
          listener: (context, state) {
            if (state is CoachProgramAddSuccess) {
              AppToast.showToast(message: 'program created successfully');

              _navigateToCoachFeatureDashboard();
            }
            if (state is CoachProgramAddFail) {
              AppToast.showToast(
                  message: 'Fait to create program. Try again later');
              _navigateToCoachFeatureDashboard();
            }

            if (state is CoachProgramLoading) {
              Log.debug('in progress');
            }

            if (state is UpdateCoachProgramSuccess) {
              AppToast.showToast(message: 'program update successfully');
              // Navigator.of(context)..pop()..pop();

              _navigateToCoachFeatureDashboard();
            }

            if (state is UpdateCoachProgramFail) {
              AppToast.showToast(
                  message: 'Fail to update your progam. Try again');
              _navigateToCoachFeatureDashboard();
            }
          },
        ),
        BlocListener<CoachBloc, CoachState>(
          listener: (context, state) {
            if (state is GetCoachOfferedSingleProgramSuccess) {
              setState(() {
                _programEntity = state.programs;
              });
            }

            if (state is GetCoachOfferedSingleProgramFail) {
              Log.debug('get coach single program failed');
              AppToast.showToast(message: 'Program details not found');
            }
          },
        ),
        BlocListener<CoachNewsfeedBloc, CoachNewsfeedState>(
            listener: (context, state) {
          if (state is GetSingleCoachProfileSuccess) {
            Log.debug('get coach profile success');
            setState(() {
              _coachEntity = state.coachProfile;
            });
          }
          if (state is GetSingleCoachProfileFail) {
            Log.debug('get coach profile fail');
          }
        })
      ],
      child: Scaffold(
        appBar: AppBar(
          title: (widget.isPreviewCoachProgram)
              ? const Text('Program')
              : const Text('Preview'),
          centerTitle: true,
          titleTextStyle:
              AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Stack(
              children: [
                _createProgramDetailsInfoSection(),
                Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // _buildProgramPriceSection(),
                    _buildProgramAvailablePriceSection(),
                    if (widget.isPreviewCoachProgram)
                      _createProgramEnrollButtonSection(),
                    if (widget.isUpdateCoachProgram)
                      _updateProgramSaveButtonSection(),
                    if (widget.isCreatingCoachProgram)
                      _createProgramSaveButtonSection(),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  _createProgramDetailsInfoSection() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildProgramTitle(),
          _builCoachInfoSection(),
          _buildProgramInfoSection(),
          _buildProgramFeatureInfoSection(),
          _buildProgramProcessSection(),
          if (widget.isPreviewCoachProgram) _buildProgramReviewSection(),
          const SizedBox(
            height: 300,
          ),
        ],
      ),
    );
  }

  _builCoachInfoSection() {
    return (_coachEntity == null)
        ? const SizedBox.shrink()
        : Padding(
            padding: const EdgeInsets.symmetric(
                vertical: Values.v20, horizontal: Values.v20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _buildCoachProfileImage(),
                SizedBox(
                  width: Values.v5,
                ),
                _buildCoachTotalRatingSection(),
              ],
            ),
          );
  }

  _buildCoachTotalRatingSection() {
    ///|| _coachEntity!.reviewCount! <= 0
    if (_coachEntity!.reviewCount == null) {
      return SizedBox.shrink();
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.star,
          color: UIColors.primaryGreen400,
        ),
        SizedBox(
          width: Values.v3,
        ),
        Text(
          '${_coachEntity!.currentRating ?? 0}',
          style:
              AppTypography.poppinsSemiBold16(color: UIColors.primaryGreen950),
        ),
        Text(
          '(${_coachEntity!.reviewCount} Reviews)',
          style: AppTypography.poppinsRegular14(color: AppColors.greyscale400),
        )
      ],
    );
  }

  _buildCoachProfileImage() {
    if (_coachEntity!.media == null || _coachEntity!.media!.isEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(Values.v100),
        child: SizedBox(
          height: Values.v32,
          width: Values.v32,
          child: Image.asset(
            Assets.spotMeNoImage,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    if (_coachEntity!.media!.first.url == 'string') {
      return ClipRRect(
        borderRadius: BorderRadius.circular(Values.v100),
        child: SizedBox(
          height: Values.v32,
          width: Values.v32,
          child: Image.asset(
            Assets.spotMeNoImage,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    return ImageContainer.circularImage(
      image: _coachEntity!.media!.first.url,
      radius: Values.v16,
      showBorder: false,
    );
  }

  _buildProgramTitle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Flexible(
          child: Text(
            '${_programEntity.title}',
            maxLines: 2,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            style: AppTypography.poppinsBold24(color: UIColors.primaryGreen950),
          ),
        ),
      ],
    );
  }

  _buildProgramFeatureInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('What You\'ll Get'),
        Column(
          children: _programEntity.features.map<Widget>((feature) {
            return _buildProgramFeature(
                feature.featureTitle, feature.featureDescription);
          }).toList(),
        ),
      ],
    );
  }

  _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(
        top: 1,
      ),
      child: Text(
        title,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        style: AppTypography.poppinsSemiBold20(color: UIColors.primary),
      ),
    );
  }

  _buildProgramFeature(String featureTitle, String featureDescription) {
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // const Icon(
          //   Icons.check_circle_outline_rounded,
          //   color: UIColors.primary,
          // ),

          Padding(
            padding: const EdgeInsets.all(4.0),
            child: Image.asset(
              Assets.coachProgramOfferIcon,
              height: 20,
              width: 20,
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Expanded(
              child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFeaturetHeader(featureTitle),
              _buildFeatureBody(featureDescription),
            ],
          )),
        ],
      ),
    );
  }

  _buildFeaturetHeader(String benefitName) {
    return Text(
      benefitName,
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
      style: AppTypography.poppinsSemiBold16(color: UIColors.primaryGreen950),
    );
  }

  _buildFeatureBody(String desc) {
    return Text(
      desc,
      style: AppTypography.poppinsSemiBold14(color: AppColors.greyscale400),
    );
  }

  _buildProgramProcessSection() {
    return ProcessStepsTimelineWidget(
      title: coachProgramProcessTitle,
      steps: coachProgramSteps,
    );
  }

  _buildProgramAvailablePriceSection() {
    return ProgramPreviewPriceListWidget(
      programEntity: widget.programEntity,
      callback: (paymentType) {
        selectedPaymentType = paymentType; // student selected payment type
      },
    );
  }

  _createProgramSaveButtonSection() {
    return Container(
      color: AppColors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Button.filled(
                label: 'Save Program',
                onPressed: () {
                  Log.debug('create program');
                  BlocProvider.of<CoachProgramBloc>(context).add(
                      AddCoachProgramEvent(
                          coachId: coachId!, programEntity: _programEntity));
                }),
          ),
        ],
      ),
    );
  }

  _updateProgramSaveButtonSection() {
    return Container(
      color: AppColors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Button.filled(
                label: 'Update Program',
                onPressed: () {
                  Log.debug('update program pressed');
                  BlocProvider.of<CoachProgramBloc>(context).add(
                      UpdateCoachProgramEvent(
                          coachId: coachId!,
                          programId: _programEntity.programId!,
                          programEntity: _programEntity));
                }),
          ),
        ],
      ),
    );
  }

  _createProgramEnrollButtonSection() {
    return Container(
      color: AppColors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Button.filled(
                label: 'Enroll Now',
                onPressed: () {
                  Log.debug('student enroll program');
                  // move to programm checkout page
                  Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => CoachProgramCheckoutPage(
                            programEntity: _programEntity,
                            selectedPaymentType: selectedPaymentType!,
                          )));
                },
                disable: _programEntity.hasSubscription!,
                borderColor: AppColors.greyscale100,),
          ),
        ],
      ),
    );
  }

  _buildProgramReviewSection() {
    return (widget.isPreviewCoachProgram)
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInfoSectionHeader(Assets.spotnotProfileBioSvg, 'Ratings'),
              SizedBox(
                height: Values.v16.h,
              ),
              CoachProgramReviewsWidget(
                  programId: widget.programEntity.programId!),
            ],
          )
        : const SizedBox.shrink();
  }

  _buildInfoSectionHeader(String icon, String title) {
    return Row(
      children: [
        Container(
          height: Values.v36.h,
          width: Values.v36.w,
          decoration: BoxDecoration(
            color: UIColors.primaryGreen100,
            borderRadius: BorderRadius.circular(Values.v36.r),
          ),
          child: Center(
            child: SvgPicture.asset(
              icon,
              height: Values.v18.h,
              width: Values.v18.w,
            ),
          ),
        ),
        SizedBox(
          width: Values.v10.w,
        ),
        Text(
          title,
          style:
              AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen600),
        ),
      ],
    );
  }

  _buildProgramInfoSection() {
    List<Widget> supports = [
      _buildInfoText(
          text:
              '${_programEntity.durationCount} ${_programEntity.durationType.toCapitalFirst()}')
    ];

    for (String eachGuarantee in widget.programEntity.guarantees) {
      supports.add(_buildItemSeperator());
      supports.add(_buildInfoText(text: eachGuarantee));
    }

    return Padding(
      padding: EdgeInsets.only(
          top: Values.v10,
          bottom: Values.v40,
          left: Values.v20,
          right: Values.v20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: Wrap(
              alignment: WrapAlignment.center,
              runAlignment: WrapAlignment.center,
              crossAxisAlignment: WrapCrossAlignment.center,
              runSpacing: Values.v10,
              children: supports,
            ),
          ),
        ],
      ),
    );
  }

  _buildItemSeperator() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 5),
      height: 15,
      width: 2,
      color: UIColors.primaryGreen400,
    );
  }

  _buildInfoText({required String text}) {
    return Text(
      textAlign: TextAlign.center,
      text,
      style: AppTypography.poppinsMedium14(color: UIColors.primaryGreen950),
    );
  }

  void _navigateToCoachFeatureDashboard() {
    // move to coach featue page
    // Navigator.of(context).popUntil((route) => route.isFirst);
    // context
    //     .read<DashboardCubit>()
    //     .changePage(2); // coach featue position is 2 in nav bar

    navigatorKey?.currentState?.pushAndRemoveUntil(
                              MaterialPageRoute(
                                  builder: (_) => DashboardPage(selectedIndex: 2,)),
                              (route) => false,
                            );
  }
}

String coachProgramProcessTitle = 'How It Works?';
List<CoachRegistrationStep> coachProgramSteps = [
  CoachRegistrationStep(stepDesc: 'You enroll in a package of your choice'),
  CoachRegistrationStep(
      stepDesc:
          'You fill in your additional key details like food preferences, preferred time to contact, any medical issues etc'),
  CoachRegistrationStep(
      stepDesc: 'The Coach calls you within 24 hours at your preferred time'),
  CoachRegistrationStep(
      stepDesc:
          'Coach understands your goals, sets expectations about how this will work'),
  CoachRegistrationStep(
      stepDesc:
          'Coach evaluates and prepares the best plan that works for you'),
  CoachRegistrationStep(
      stepDesc:
          'Coach assesses your weekly progress and makes course adjustments'),
  CoachRegistrationStep(stepDesc: 'You get results, yay!'),
];
