import 'package:fitsomnia_app/main.dart';
import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/coach/root/domain/entities/coach_program_category_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CoachSubcategoryViewWidget extends StatefulWidget {
  const CoachSubcategoryViewWidget(
      {super.key, required this.subcategoryEnrity});
  final CoachProgramCategoryEntity subcategoryEnrity;

  @override
  State<CoachSubcategoryViewWidget> createState() =>
      _CoachSubcategoryViewWidgetState();
}

class _CoachSubcategoryViewWidgetState
    extends State<CoachSubcategoryViewWidget> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Log.debug('subcategory selected: ${widget.subcategoryEnrity.id}');
        Navigator.of(context).pushNamed(Routes.coachSearchListPage, arguments: [widget.subcategoryEnrity.id, null, CoachProfileFilterType.SUBCATEGORY]);
      },
      child: Container(
        height: Values.v200.h,
        width: Values.v240.w,
        color: AppColors.transparent,
        child: Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(Values.v10),
              color: AppColors.white
              // color: UIColors.black.withOpacity(0.30),
              ),
          padding: EdgeInsets.only(left: 5, right: 5),
          child: _buildCoachPreviewWidget(),
        ),
      ),
    );
  }

  _buildCoachPreviewWidget() {
    return Container(
      color: AppColors.transparent,
      child: Stack(
        alignment: Alignment.center,
        children: [
          _buildProfileImage(),
          _buildProfileInformation(),
        ],
      ),
    );
  }

  _buildProfileImage() {
    // return Container(
    //   child: Image.network(
    //     widget.profileImage,
    //     fit: BoxFit.fitHeight,
    //     // width: double.infinity,
    //     // height: double.infinity,
    //   ),
    // );

    if (widget.subcategoryEnrity.mediaList == null ||
        widget.subcategoryEnrity.mediaList!.isEmpty) {
      return Image.asset(
        Assets.spotMeNoImage,
        height: double.infinity,
        width: double.infinity,
      );
    }

    return ImageContainer.rectangularImage(
      cornerRadius: Values.v10,
      image: widget.subcategoryEnrity.mediaList!.first.urlPath,
      width: double.infinity,
      height: double.infinity,
      fit: BoxFit.cover,
      useMedium: true,
      hideLoadingIndicator: true,
      errorWidget: Container(
        height: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Values.v10),
          image: DecorationImage(
            image: const Image(
              image: AssetImage(
                Assets.spotMeNoImage,
              ),
            ).image,
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }

  _buildProfileInformation() {
    return Container(
      height: double.infinity,
      width: double.infinity,
      padding: EdgeInsets.all(Values.v10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(Values.v10),
        gradient: _buildLinearGradientWhiteToBlack(),
        // color: UIColors.black.withOpacity(0.30),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.subcategoryEnrity.title,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: AppTypography.poppinsMedium24(color: AppColors.white),
          ),
          Text(
            '${widget.subcategoryEnrity.totalCoach} Coaches',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: AppTypography.poppinsRegular14(color: AppColors.primaryGreen400),
          )
        ],
      ),
    );
  }

  LinearGradient _buildLinearGradientWhiteToBlack() {
    return LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        AppColors.transparent,
        UIColors.black.withOpacity(0.3),
      ],
    );
  }
}
