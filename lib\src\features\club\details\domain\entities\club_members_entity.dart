class ClubMemberEntity {
  ClubMemberEntity({
    required this.userId,
    required this.userName,
    required this.image,
    required this.relationStatus,
  });

  final String userId;
  final String userName;
  final String? image;
  final String relationStatus;

  ClubMemberEntity copyWith({
    String? userId,
    String? userName,
    String? image,
    String? relationStatus,
  }) {
    return ClubMemberEntity(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      image: image ?? this.image,
      relationStatus: relationStatus ?? this.relationStatus,
    );
  }
}
