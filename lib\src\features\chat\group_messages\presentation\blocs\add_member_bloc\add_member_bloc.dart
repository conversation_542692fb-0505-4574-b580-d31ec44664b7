import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/domain/use_cases/add_member_use_case.dart';

part 'add_member_event.dart';
part 'add_member_state.dart';

class AddMemberBloc extends Bloc<AddMemberEvent, AddMemberState> {
  AddMemberBloc({required this.addMemberUseCase}) : super(AddMemberInitial()) {
    on<AddMemberEvent>(_onAddMemberEvent);
  }

  late AddMemberUseCase addMemberUseCase;

  Future<void> _onAddMemberEvent(
    AddMemberEvent event,
    Emitter<AddMemberState> emit,
  ) async {
    try {
      final response = await addMemberUseCase.call(
        groupId: event.groupId,
        memberId: event.memberId,
      );

      response.fold(
        (l) => emit(
          AddMemberFailure(
            message: l,
          ),
        ),
        (r) => emit(AddMemberSuccess(message: r)),
      );
    } catch (_) {
      emit(
        AddMemberFailure(
          message: TextConstants.unexpectedError,
        ),
      );
    }
  }
}
