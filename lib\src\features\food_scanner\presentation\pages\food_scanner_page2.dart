import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/services/food_scan_camera/ui/food_camera_page.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/food_scanner/domain/entities/food_info_entity.dart';
import 'package:fitsomnia_app/src/features/food_scanner/presentation/bloc/bloc/food_scan_bloc.dart';
import 'package:flutter/material.dart';

import 'dart:io';

import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/camera_v2/ui/camera_v2_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_picker/image_picker.dart';
import 'package:screenshot/screenshot.dart';
import 'package:path_provider/path_provider.dart' as path_provider;

class FoodScannerPage2 extends StatefulWidget {
  const FoodScannerPage2({super.key});

  @override
  State<FoodScannerPage2> createState() => _FoodScannerPage2State();
}

class _FoodScannerPage2State extends State<FoodScannerPage2> {
  String? _imagePath;
  String? _compressedImagePath;
  FoodInfoEntity? _foodInfo;

  final ImagePicker _picker = ImagePicker();
  File? _image;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: AppBar(
      //   title: const Text('Food Scanner'),
      // ),
      body: BlocListener<FoodScanBloc, FoodScanState>(
        listener: (context, state) {
          if(state is FoodScanSuccess) {
            setState(() {
              _foodInfo = (state as FoodScanSuccess).foodInfo;
            });
          }

          if(state is FoodScanFeedbackSuccess) {
            setState(() {
              _foodInfo = (state as FoodScanFeedbackSuccess).foodInfo;
            });
          }
        },
        child: FoodCameraServiceView()
      ),
    );
  }
}
