import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class ReferralDataSource {
  Future<Response> getReferrals();
  Future<Response> getReferralById({required String referralId});
  Future<Response> getReferralCode(
      {required String userId, required String referralId});
  Future<Response> useReferralCode(
      {required String userId, required String referralCode});
}

class ReferralDataSourceImpl extends ReferralDataSource {
  final RestClient restClient;

  ReferralDataSourceImpl({required this.restClient});

  @override
  Future<Response> getReferralById({required String referralId}) async {
    final response = await restClient.get(
        APIType.PROTECTED, API.referralSingel + '/${referralId}');

    return response;
  }

  @override
  Future<Response> getReferralCode(
      {required String userId, required String referralId}) async {
    final response = await restClient.post(APIType.PROTECTED, API.referralCode,
        {'userId': userId, 'campaignId': referralId});

    return response;
  }

  @override
  Future<Response> getReferrals() async {
    final response =
        await restClient.get(APIType.PROTECTED, API.multipleReferrals);

    return response;
  }

  @override
  Future<Response> useReferralCode(
      {required String userId, required String referralCode}) async {
    final response = await restClient.post(APIType.PROTECTED, API.useReferralCode,
        {'refereeUserId': userId, 'referralCode': referralCode});

    return response;
  }
}
