part of 'reward_point_bloc.dart';

sealed class RewardPointState extends Equatable {
  const RewardPointState();
  
  @override
  List<Object> get props => [];
}

final class RewardPointInitial extends RewardPointState {}
final class RewardPointLoading extends RewardPointState {}

class GetRewardPointSuccess extends RewardPointState {
  final RewardPointRankEntity pointEntity;

  GetRewardPointSuccess({required this.pointEntity});

  @override
  List<Object> get props => [pointEntity];
  
}

class GetRewardPointFail<T> extends RewardPointState {
  final T? data;
  const GetRewardPointFail({required this.data});
  
}

class GetUserWeeklyLoginActivitySuccess extends RewardPointState {
  final UserLoginActivityEntity userLoginActivityEntity;

  GetUserWeeklyLoginActivitySuccess({required this.userLoginActivityEntity});

  @override
  List<Object> get props => [userLoginActivityEntity];
  
}

class GetUserWeeklyLoginActivityFail<T> extends RewardPointState {
  final T? data;
  const GetUserWeeklyLoginActivityFail({required this.data});
}

class GetRewardPointHistorySuccess extends  RewardPointState {
  final List<RewardPointHistoryEntity> history;

  GetRewardPointHistorySuccess({required this.history});

  @override
  List<Object> get props => [history];

}

class GetRewardPointHistoryFail<T> extends RewardPointState {
  final T? data;
  GetRewardPointHistoryFail({required this.data});
}