import 'dart:async';

import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/validators/input_validators.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/auth/reset_password/bloc/forgot_password/forgot_password_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/reset_password/bloc/identity_verification/identity_verification_bloc.dart';
import 'package:fitsomnia_app/src/features/auth/root/presentations/widgets/authentication_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:input_form_field/input_form_field.dart';

part '../widget/countdown_timer.dart';

class IdentityVerificationPage extends StatelessWidget {
  IdentityVerificationPage({
    Key? key,
  }) : super(key: key);

  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return ScrollableWrapper(
      appBar: AppBar(
        backgroundColor: UIColors.background,
        elevation: 0,
        automaticallyImplyLeading: false,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppColors.black,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildForgotPasswordTitle(),
            SizedBox(height: Values.v8.h),
            _buildForgotPasswordDetails(),
            SizedBox(height: Values.v8.h),
            _buildTimeCounter(),
            SizedBox(height: Values.v30.h),
            _buildOtpField(context),
            SizedBox(height: Values.v215.h),
            _buildContinueButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildForgotPasswordTitle() {
    return Align(
      alignment: Alignment.center,
      child: Text(
        TextConstants.forgotPassword,
        style: AppTypography.regular16(
          color: AppColors.black,
        ).copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildForgotPasswordDetails() {
    return Align(
      alignment: Alignment.center,
      child: Text(
        TextConstants.forgotPasswordDescription,
        style: AppTypography.regular12(
          color: AppColors.black,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTimeCounter() {
    return const Align(
      alignment: Alignment.center,
      child: _CountdownTimer(),
    );
  }

  Widget _buildOtpField(BuildContext context) {
    return InputFormField(
      textEditingController: context.read<IdentityVerificationBloc>().otpField,
      labelText: TextConstants.enterYourOTPCode,
      keyboardType: TextInputType.number,
      validator: InputValidators.otp,
    );
  }

//ignore: long-method
  Widget _buildContinueButton(BuildContext context) {
    return BlocConsumer<IdentityVerificationBloc, BaseState>(
      listener: (context, state) {
        if (state is SuccessState) {
          Navigator.pushReplacementNamed(
            context,
            Routes.resetPassword,
          );
        } else if (state is ErrorState) {
          AppToast.showToast(
            message: state.data,
            backgroundColor: Colors.red,
            gravity: ToastGravity.TOP,
          );
        }
      },
      builder: (context, state) {
        return Button.filled(
          onPressed: () {
            if (!_formKey.currentState!.validate()) return;

            BlocProvider.of<IdentityVerificationBloc>(context).add(
              IdentityVerificationWithEmailAndOtpEvent(
                email: context
                        .read<ForgotPasswordBloc>()
                        .emailOrPhoneField
                        .text
                        .contains('@')
                    ? context
                        .read<ForgotPasswordBloc>()
                        .emailOrPhoneField
                        .text
                        .trim()
                    : null,
                phone: context
                        .read<ForgotPasswordBloc>()
                        .emailOrPhoneField
                        .text
                        .contains('@')
                    ? null
                    : context
                        .read<ForgotPasswordBloc>()
                        .emailOrPhoneField
                        .text
                        .trim(),
              ),
            );
          },
          isLoading: state is LoadingState,
          label: TextConstants.continueText,
        );
      },
    );
  }
}
