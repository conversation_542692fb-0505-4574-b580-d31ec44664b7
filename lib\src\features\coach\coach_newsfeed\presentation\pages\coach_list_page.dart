import 'dart:async';

import 'package:fitsomnia_app/src/core/base/state.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/enums.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/page_app_bar.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/bloc/coach_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/domain/entities/coach_profile_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/bloc/coach_newsfeed_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_newsfeed/presentation/widgets/coach_profile_list_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/data/model/coach_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CoachListPage extends StatefulWidget {
  const CoachListPage({
    super.key,
    this.subcategoryId,
    this.categoryId,
    this.coachProfileFilterType,
  });
  final CoachProfileFilterType? coachProfileFilterType;
  final String? subcategoryId;
  final String? categoryId;

  @override
  State<CoachListPage> createState() => _CoachListPageState();
}

class _CoachListPageState extends State<CoachListPage> {
  List<CoachProfileEntity> _coachProfiles = [];

  @override
  void initState() {
    super.initState();
    if (widget.categoryId != null) {
       Log.debug('category id: ${widget.categoryId}');
      BlocProvider.of<CoachNewsfeedBloc>(context).add(GetCoachProfilesEvent(
          filterType:
              widget.coachProfileFilterType ?? CoachProfileFilterType.BEST, categoryId: widget.categoryId));
  
    } else if(widget.subcategoryId != null) {
      Log.debug('subcategory id: ${widget.subcategoryId}');
      BlocProvider.of<CoachNewsfeedBloc>(context).add(GetCoachProfilesEvent(
          filterType:
              widget.coachProfileFilterType ?? CoachProfileFilterType.BEST, subcategoryId: widget.subcategoryId));
    } else {
      BlocProvider.of<CoachNewsfeedBloc>(context).add(GetCoachProfilesEvent(
          filterType:
              widget.coachProfileFilterType ?? CoachProfileFilterType.BEST));
    }
  }

  @override
  void dispose() {
    _searchTextController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SafeArea(
          child: Column(
          children: [
            _buildPageHeader(),
            // _buildSearchSection(),
            Expanded(child: _buildCoachListSection(),)
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        'Coach List',
        style: TextStyle(
          color: UIColors.primaryGreen950,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: true,
      elevation: 0,
    );
  }

  _buildPageHeader() {
    return Padding(
      padding: const EdgeInsets.only(top: Values.v20, left: Values.v20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCoachListTitle(),
          Text(
            'Get to know your coach and subscibe for training',
            style: AppTypography.poppinsMedium14(color: AppColors.greyscale400),
          ),
          const SizedBox(
            height: 24,
          ),
        ],
      ),
    );
  }

  _buildSearchSection() {
    return _buildSearchBar();
  }

  TextEditingController _searchTextController = TextEditingController();
  Timer? _timer;
  
  _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.only(left: Values.v20, right: Values.v20),
      child: Container(
        // padding: EdgeInsets.all(Values.v20),
        decoration: BoxDecoration(border: Border.all(color: AppColors.greyscale50), borderRadius: BorderRadius.circular(Values.v10)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(width: 10,),
            Icon(Icons.search_rounded),
            // Text('Search coach', style: AppTypography.poppinsRegular16(color: AppColors.greyscale400),),
            Expanded(
            child: TextFormField(
              onChanged: (_) {
                Log.debug('searching...');
                _onChanged();
              },
              controller: _searchTextController,
              decoration: InputDecoration(
                // contentPadding:
                //     const EdgeInsets.fromLTRB(12.0, 0.0, 12.0, 12.0),
                border: InputBorder.none,
                hintText: 'Search coach',
                hintStyle: AppTypography.poppinsRegular16(color: AppColors.greyscale400),
              ),
            ),
          ),
          ],
        ),
      ),
    );
  }

  _buildCoachListSection() {
    return CoachProfileListWidget(searchText: _searchTextController.text);
  }

  Widget _buildCoachListTitle() {
    return RichText(
      text: TextSpan(
        style: AppTypography.poppinsSemiBold24(color: UIColors.primaryGreen950),
        children: [
          TextSpan(
            text: 'Visit our ',
            style: AppTypography.poppinsSemiBold24(color: UIColors.primary),
          ),
          const TextSpan(text: 'coaches ! '),
        ],
      ),
    );
  }
  
  final Duration _debounceDuration = const Duration(milliseconds: 500);
  void _onChanged() {
    Log.debug('${_searchTextController.text}');
     if (_timer?.isActive ?? false) {
      _timer?.cancel();
    }
    _timer = Timer(_debounceDuration, () {
      _executeSearch();
    });
  }

  void _executeSearch() {
    if(_searchTextController.text != '') context.read<CoachNewsfeedBloc>().add(GetCoachProfilesEvent(filterType: CoachProfileFilterType.NAME, namePrefix: _searchTextController.text));
  }
}
