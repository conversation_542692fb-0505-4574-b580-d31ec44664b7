import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/exception/network_exception.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/data/data_source/coach_program_review_data_source.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/data/model/user_review_model.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/domain/entities/user_review_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/domain/repositories/coach_program_review_repository.dart';

class CoachRatingRepositoryImpl extends CoachProgramReviewRepository {
  final CoachProgramReviewDataSource dataSource;

  CoachRatingRepositoryImpl({required this.dataSource});

  @override
  Future<Either<ErrorModel, UserReviewEntity>> deleteCoachProgramReview(
      {required String reviewId}) async {
    try {
      final response =
          await dataSource.deleteCoachProgramReview(reviewId: reviewId);
      final d = response.data['data'];
      UserReviewEntity review = UserReviewModel.fromJson(d);

      return Right(review);
    } catch (e) {
      Log.debug(e.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, UserReviewEntity>> getCoachProgramReviewById(
      {required String reviewId}) async {
    try {
      if (reviewId == 'review-id') {
        return Right(ratingSample);
      }

      final response =
          await dataSource.getCoachProgramReviewById(reviewId: reviewId);
      final d = response.data['data'];
      UserReviewEntity review = UserReviewModel.fromJson(d);

      return Right(review);
    } catch (e) {
      Log.debug(e.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, List<UserReviewEntity>>> getCoachProgramReviews(
      {required String programId}) async {
    try {
      if (programId == 'coach-id') {
        final reviews = List.generate(10, (_) => ratingSample);

        return Right(reviews);
      }

      final response =
          await dataSource.getCoachProgramReviews(programId: programId);
      final data = response.data['data'];
      List<UserReviewEntity> ratings = List<UserReviewModel>.from(
          data!.map((x) => UserReviewModel.fromJson(x)));

      return Right(ratings);
    } catch (e) {
      Log.debug(e.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, UserReviewEntity>> reviewCoachProgram(
      {required String programId, required Map<String, dynamic> data}) async {
    try {
      final response =
          await dataSource.reviewCoachProgram(programId: programId, data: data);
      final d = response.data['data'];
      UserReviewEntity review = UserReviewModel.fromJson(d);

      return Right(review);
    } catch (e) {
      Log.debug(e.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, UserReviewEntity>> updateCoachProgramReview(
      {required String reviewId, required Map<String, dynamic> data}) async {
    try {
      final response = await dataSource.updateCoachProgramReview(
          reviewId: reviewId, data: data);
      final d = response.data['data'];
      UserReviewEntity coachRating = UserReviewModel.fromJson(d);

      return Right(coachRating);
    } catch (e) {
      Log.debug(e.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, UserReviewEntity>> deleteCoachProfileReview(
      {required String reviewId}) async {
    try {
      final response =
          await dataSource.deleteCoachProfileReview(reviewId: reviewId);
      final d = response.data['data'];
      UserReviewEntity review = UserReviewModel.fromJson(d);

      return Right(review);
    } catch (e) {
      Log.debug(e.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, List<UserReviewEntity>>> getCoachProfileReviews(
      {required String coachId}) async {
    try {
      if (coachId == 'coach-id') {
        final reviews = List.generate(10, (_) => ratingSample);

        return Right(reviews);
      }

      final response =
          await dataSource.getCoachProfileReviews(coachId: coachId);
      final data = response.data['data'];
      List<UserReviewEntity> ratings = List<UserReviewModel>.from(
          data!.map((x) => UserReviewModel.fromJson(x)));

      return Right(ratings);
    } catch (e) {
      Log.debug(e.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, UserReviewEntity>> getCoachProfileReviewById(
      {required String reviewId}) async {
    try {
      if (reviewId == 'review-id') {
        return Right(ratingSample);
      }

      final response =
          await dataSource.getCoachProfileReviewById(reviewId: reviewId);
      final d = response.data['data'];
      UserReviewEntity review = UserReviewModel.fromJson(d);

      return Right(review);
    } catch (e) {
      Log.debug(e.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, UserReviewEntity>> reviewCoachProfile({
    required String coachId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final response =
          await dataSource.reviewCoachProfile(coachId: coachId, data: data);
      final d = response.data['data'];
      UserReviewEntity review = UserReviewModel.fromJson(d);

      return Right(review);
    } catch (e) {
      Log.debug(e.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }

  @override
  Future<Either<ErrorModel, UserReviewEntity>> updateCoachProfileReview({
    required String reviewId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final response = await dataSource.updateCoachProfileReview(
          reviewId: reviewId, data: data);
      final d = response.data['data'];
      UserReviewEntity coachRating = UserReviewModel.fromJson(d);

      return Right(coachRating);
    } catch (e) {
      Log.debug(e.toString());

      return (e is NetworkExceptionV2)
          ? Left(e.errorModel.error!)
          : Left(ErrorModel(message: e.toString()));
    }
  }
}
