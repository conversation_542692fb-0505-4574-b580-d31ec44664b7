import 'package:fitsomnia_app/src/features/club/details/domain/entities/club_members_entity.dart';

class ClubMembersModel extends ClubMemberEntity {
  ClubMembersModel({
    required super.userId,
    required super.userName,
    required super.image,
    required super.relationStatus,
  });

  factory ClubMembersModel.fromJson(Map<String, dynamic> json) =>
      ClubMembersModel(
        userId: json["userId"],
        userName: json["userName"],
        image: json["image"] ?? "",
        relationStatus: json["relationStatus"],
      );
}
