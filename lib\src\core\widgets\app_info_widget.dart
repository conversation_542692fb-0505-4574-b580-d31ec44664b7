import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class InfoWidget extends StatelessWidget {
  const InfoWidget({
    Key? key,
    this.message,
  }) : super(key: key);

  final String? message;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.info, color: AppColors.greyLight),
          SizedBox(width: Values.v5.w),
          Flexible(
            child: Text(
              message ?? 'No data found, please try again',
              style: AppTypography.regular16(
                color: AppColors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
