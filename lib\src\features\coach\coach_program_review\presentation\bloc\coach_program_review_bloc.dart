import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/domain/entities/user_review_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program_review/domain/use_case/coach_program_review_use_case.dart';

part 'coach_program_review_event.dart';
part 'coach_program_review_state.dart';

class CoachProgramReviewBloc
    extends Bloc<CoachProgramReviewEvent, CoachProgramReviewState> {
  CoachProgramReviewBloc({required this.coachProgramReviewUseCase})
      : super(CoachProgramReviewInitial()) {
    on<ReviewCoachProgramEvent>(_onReviewCoachProgramEvent);
    on<GetCoachProgramReviews>(_onGetCoachProgramReviews);
    on<GetCoachProgramSingleReview>(_onGetCoachProgramSingleReview);
    on<UpdateCoachProgramReview>(_onUpdateCoachProgramReview);

    on<ReviewCoachProfileEvent>(_onReviewCoachProfileEvent);
    on<GetCoachProfileReviews>(_onGetCoachProfileReviews);
    on<GetCoachProfileSingleReview>(_onGetCoachProfileSingleReview);
    on<UpdateCoachProfileReview>(_onUpdateCoachProfileReview);
  }

  final CoachProgramReviewUseCase coachProgramReviewUseCase;

  Future<void> _onReviewCoachProgramEvent(ReviewCoachProgramEvent event,
      Emitter<CoachProgramReviewState> emit) async {
    try {
      emit(CoachProgramReviewInitial());
      final response = await coachProgramReviewUseCase.raviewCoachProgram(
          programId: event.programId, data: event.reviewRequest.toJson());

      response.fold((l) {
        emit(ReviewCoachProgramFail(data: l));
      }, (r) {
        emit(ReviewCoachProgramSuccess(review: r));
      });
    } catch (e) {
      Log.debug(e.toString());

      emit(ReviewCoachProgramFail(data: e));
    }
  }

  Future<void> _onGetCoachProgramReviews(GetCoachProgramReviews event,
      Emitter<CoachProgramReviewState> emit) async {
    try {
      emit(CoachRatingLoading());
      Log.debug('get coach program rating');
      final response = await coachProgramReviewUseCase.getCoachProgramReviews(
          programId: event.programId);

      response.fold((l) {
        emit(GetCoachProgramReviewsFail(data: l));
      }, (r) {
        emit(GetCoachProgramReviewsSuccess(reviews: r));
      });
    } catch (e) {
      Log.debug(e.toString());

      emit(GetCoachProgramReviewsFail(data: e));
    }
  }

  Future<void> _onGetCoachProgramSingleReview(GetCoachProgramSingleReview event,
      Emitter<CoachProgramReviewState> emit) async {
    try {
      emit(CoachRatingLoading());
      final response = await coachProgramReviewUseCase
          .getCoachProgramReviewById(reviewId: event.reviewId);

      response.fold((l) {
        emit(GetCoachProgramSingleReviewFail(data: l));
      }, (r) {
        emit(GetCoachProgramSingleReviewSuccess(review: r));
      });
    } catch (e) {
      Log.debug(e.toString());

      emit(GetCoachProgramSingleReviewFail(data: e));
    }
  }

  Future<void> _onUpdateCoachProgramReview(UpdateCoachProgramReview event,
      Emitter<CoachProgramReviewState> emit) async {
    try {
      emit(CoachRatingLoading());
      final response = await coachProgramReviewUseCase.updateCoachReview(
          reviewId: event.reviewId, data: event.reviewRequest.toJson());

      response.fold((l) {
        emit(UpdateCoachProgramReviewFail(data: l));
      }, (r) {
        emit(UpdateCoachProgramReviewSuccess(review: r));
      });
    } catch (e) {
      Log.debug(e.toString());

      emit(UpdateCoachProgramReviewFail(data: e));
    }
  }

  Future<void> _onReviewCoachProfileEvent(ReviewCoachProfileEvent event,
      Emitter<CoachProgramReviewState> emit) async {
    try {
      emit(CoachProgramReviewInitial());
      final response = await coachProgramReviewUseCase.reviewCoachProfile(
          coachId: event.coachId, data: event.reviewRequest.toJson());

      response.fold((l) {
        emit(ReviewCoachProfileFail(data: l));
      }, (r) {
        emit(ReviewCoachProfileSuccess(review: r));
      });
    } catch (e) {
      Log.debug(e.toString());

      emit(ReviewCoachProfileFail(data: e));
    }
  }

  Future<void> _onGetCoachProfileReviews(GetCoachProfileReviews event,
      Emitter<CoachProgramReviewState> emit) async {
    try {
      emit(CoachRatingLoading());
      Log.debug('get coach rating');
      final response = await coachProgramReviewUseCase.getCoachProfileReviews(
          coachId: event.coachId);

      response.fold((l) {
        emit(GetCoachProfileReviewsFail(data: l));
      }, (r) {
        emit(GetCoachProfileReviewsSuccess(reviews: r));
      });
    } catch (e) {
      Log.debug(e.toString());

      emit(GetCoachProfileReviewsFail(data: e));
    }
  }

  Future<void> _onGetCoachProfileSingleReview(GetCoachProfileSingleReview event,
      Emitter<CoachProgramReviewState> emit) async {
    try {
      emit(CoachRatingLoading());
      final response = await coachProgramReviewUseCase
          .getCoachProgramReviewById(reviewId: event.reviewId);

      response.fold((l) {
        emit(GetCoachProfileSingleReviewFail(data: l));
      }, (r) {
        emit(GetCoachProfileSingleReviewSuccess(review: r));
      });
    } catch (e) {
      Log.debug(e.toString());

      emit(GetCoachProfileSingleReviewFail(data: e));
    }
  }

  Future<void> _onUpdateCoachProfileReview(UpdateCoachProfileReview event,
      Emitter<CoachProgramReviewState> emit) async {
    try {
      emit(CoachRatingLoading());
      final response = await coachProgramReviewUseCase.updateCoachReview(
        reviewId: event.reviewId,
        data: event.reviewRequest.toJson(),
      );

      response.fold((l) {
        emit(UpdateCoachProfileReviewFail(data: l));
      }, (r) {
        emit(UpdateCoachProfileReviewSuccess(review: r));
      });
    } catch (e) {
      Log.debug(e.toString());

      emit(UpdateCoachProfileReviewFail(data: e));
    }
  }
}
