import 'dart:io';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/services/media/bloc/media_bloc.dart';
import 'package:fitsomnia_app/src/core/services/media/ui/widgets/media_image_grid_view.dart';
import 'package:fitsomnia_app/src/core/services/media/ui/widgets/media_video_plear.dart';
import 'package:fitsomnia_app/src/core/services/media/ui/widgets/trimmer_view.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:photo_manager/photo_manager.dart';

class MediaServiceView extends StatelessWidget {
  const MediaServiceView({
    Key? key,
    this.selectMultipleMedia = true,
    this.maxSelectionLimit = 5,
    this.onCloseButtonTap,
  }) : super(key: key);

  final bool selectMultipleMedia;
  final int maxSelectionLimit;
  final Function? onCloseButtonTap;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => MediaBloc(
            selectMultipleMedia: selectMultipleMedia,
            onCloseButtonTap: onCloseButtonTap,
          ),
        ),
      ],
      child: const ShowMediaPage(),
    );
  }
}

class ShowMediaPage extends StatefulWidget {
  const ShowMediaPage({Key? key}) : super(key: key);

  @override
  State<ShowMediaPage> createState() => _ShowMediaPageState();
}

class _ShowMediaPageState extends State<ShowMediaPage> {
  @override
  void initState() {
    context.read<MediaBloc>().add(LoadMediaFilesEvent());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: BlocBuilder<MediaBloc, MediaState>(
        buildWhen: (previous, current) =>
            previous != current && current is FileLoadedState,
        builder: (context, state) {
          if (state is FileLoadingState) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is FileLoadedState) {
            return Column(
              children: [
                Expanded(flex: 1, child: _buildMediaPreview(state.mediaFiles)),
                Expanded(flex: 1, child: _buildMediaGallery(state.mediaFiles))
              ],
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: Text(
        TextConstants.media,
        style: AppTypography.regular24(color: AppColors.black),
      ),
      backgroundColor: AppColors.white,
      elevation: 0.0,
      leading: IconButton(
        icon: const Icon(
          Icons.close,
          color: AppColors.black,
        ),
        onPressed: () {
          if (context.read<MediaBloc>().onCloseButtonTap != null) {
            context.read<MediaBloc>().onCloseButtonTap!.call();
          }
        },
      ),
      actions: [
        IconButton(
          icon: const Icon(
            Icons.chevron_right,
            color: AppColors.black,
          ),
          onPressed: () {
            Navigator.pop(
              context,
              context.read<MediaBloc>().selectedFiles,
            );
          },
        ),
      ],
    );
  }

  Widget _buildMediaPreview(List<AssetEntity> mediaFiles) {

    return BlocBuilder<MediaBloc, MediaState>(
      builder: (context, state) {
        return mediaFiles.isEmpty
            ? const Center(
                child: CircularProgressIndicator(color: Colors.black),
              )
            : _previewSelectedMedia();
      },
    );
  }

  Widget _buildMediaGallery(List<AssetEntity> mediaFiles) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          height: Values.v50.h,
          width: double.infinity,
          color: Colors.black,
          child: const Align(
            alignment: Alignment.centerLeft,
            child: Text(
              TextConstants.media,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        Expanded(
          child: NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification scroll) {
              //handleScrollEvent(scroll);
              return false;
            },
            child: MediaImageGridView(mediaFiles: mediaFiles),
          ),
        ),
      ],
    );
  }

  Widget _previewSelectedMedia() {
    final MediaBloc postBloc = BlocProvider.of<MediaBloc>(context);

    return BlocBuilder<MediaBloc, MediaState>(
      buildWhen: (previous, current) => current is SelectedImagePreviewState,
      builder: (context, state) {
        if (state is SelectedImagePreviewState) {
          return state.mediaFiles[postBloc.previewIndex].type == AssetType.video
              ? FutureBuilder(
                  future: state.mediaFiles[postBloc.previewIndex].file,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.done) {
                      if (state.mediaFiles.first.duration > 10) {
                        Future.delayed(Duration.zero, (){
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => TrimmerView(
                                snapshot.data as File,
                              ),
                            ),
                          );

                        });
                      } else {
                        return MediaVideoPlayer(
                          videoPath: snapshot.data as File,
                        );
                      }
                    }

                    return const Center(
                      child: CircularProgressIndicator(color: Colors.black),
                    );
                  },
                )
              : Image.memory(postBloc.previewedFile!);
        }

        return Container();
      },
    );
  }
}
