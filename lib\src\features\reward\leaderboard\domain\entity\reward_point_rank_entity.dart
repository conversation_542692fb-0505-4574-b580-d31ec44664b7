class RewardPointRankEntity {
  final String userId;
  final String userName;
  final String userImage;
  final int totalPoints;
  final int totalAmount;
  final int totalParticipant;
  final int userRank;
  final double pointPrice;
  final int pointRedeemThreshold;

  RewardPointRankEntity({
    required this.userId,
    required this.userName,
    required this.userImage,
    required this.totalPoints,
    required this.totalAmount,
    required this.totalParticipant,
    required this.userRank,
    required this.pointPrice,
    required this.pointRedeemThreshold,
  });
}

var testPointRankEntity = RewardPointRankEntity(
  userId: 'user-id',
  userName: '<PERSON><PERSON><PERSON><PERSON>',
  userImage: "https://dev-public-cdn.fitsomnia.com/original/user/2024/12/24/397b152c-fb18-47b8-a7a4-63af3b34d08f/473224_17350187829151000000023.jpg",
  totalPoints: 0,
  totalAmount: 0,
  totalParticipant: 1,
  userRank: 1,
  pointPrice: 1,
  pointRedeemThreshold: 5000,
);
