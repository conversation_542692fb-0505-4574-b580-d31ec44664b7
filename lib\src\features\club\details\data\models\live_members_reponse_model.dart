import 'package:fitsomnia_app/src/features/club/details/domain/entities/live_member_model.dart';

class LiveMembersResponseModel {
  List<LiveMemberModel> data;

  LiveMembersResponseModel({
    required this.data,
  });

  factory LiveMembersResponseModel.fromJson(Map<String, dynamic> json) =>
      LiveMembersResponseModel(
        data: List<LiveMemberModel>.from(
          json["data"].map((x) => LiveMemberModel.fromJson(x)),
        ),
      );
}
