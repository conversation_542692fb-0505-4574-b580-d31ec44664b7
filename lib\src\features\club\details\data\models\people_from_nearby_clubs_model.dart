import 'package:fitsomnia_app/src/features/club/details/domain/entities/nearby_clubs_members_entity.dart';

class PeopleFromNearbyClubsModel extends NearbyClubsMemberEntity {
  PeopleFromNearbyClubsModel({
    required super.userId,
    required super.userName,
    required super.image,
    required super.relationStatus,
  });

  factory PeopleFromNearbyClubsModel.fromJson(Map<String, dynamic> json) =>
      PeopleFromNearbyClubsModel(
        userId: json["userId"],
        userName: json["userName"],
        image: json["image"] ?? "",
        relationStatus: json["relationStatus"],
      );
}
