import 'package:dio/dio.dart';
import 'package:fitsomnia_app/src/core/network/end_points.dart';
import 'package:fitsomnia_app/src/core/network/rest_client.dart';

abstract class AllUsersDataSource {
  Future<Response> getAllData({String name});
}

class AllUsersDataSourceImpl implements AllUsersDataSource {
  const AllUsersDataSourceImpl({required this.restClient});

  final RestClient restClient;

  @override
  Future<Response> getAllData({String name = ""}) async {
    final response = await restClient.get(APIType.PROTECTED,
        name == "" ? API.allUsersList : "${API.allUsersList}?name=$name");

    return response;
  }
}
