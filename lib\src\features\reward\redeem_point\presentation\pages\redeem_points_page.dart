import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/extensions/extensions.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/button/button.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/history/points_history/presentation/bloc/points_history_bloc.dart';
import 'package:fitsomnia_app/src/features/hall_of_weight/history/points_history/presentation/model/points_history_view_model.dart';
import 'package:fitsomnia_app/src/features/reward/leaderboard/domain/entity/reward_point_rank_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/reward_point_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/domain/entities/reward_point_history_entity.dart';
import 'package:fitsomnia_app/src/features/reward/reward_dashboard/presentation/bloc/reward_point_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';

class RedeemPointsPage extends StatefulWidget {
  const RedeemPointsPage({super.key});

  @override
  State<RedeemPointsPage> createState() => _RedeemPointsPageState();
}

class _RedeemPointsPageState extends State<RedeemPointsPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: _buildAppBar(context),
      body: SafeArea(
        child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTotalPointRedeemSection(),
                Divider(
                  height: Values.v40,
                  color: AppColors.greyscale100,
                ),
                Expanded(child: _buildPointsHistorySection()),
              ],
            ),
          ),
      ),
    );
  }

  _buildTotalPointRedeemSection() {
    return RedeemPointsWithdrawWidget();
  }

  _buildPointsHistorySection() {
    return RewardPointsHistoryWidget();
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      leading: GestureDetector(
        onTap: () => Navigator.pop(context),
        child: Icon(
          Icons.arrow_back_ios,
          color: AppColors.greyDark,
        ),
      ),
      backgroundColor: AppColors.white,
      elevation: Values.v0,
      title: const Text(
        'Redeem Points',
        style: TextStyle(
          color: AppColors.black,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
    );
  }
}

class RewardPointsHistoryWidget extends StatefulWidget {
  const RewardPointsHistoryWidget({super.key});

  @override
  State<RewardPointsHistoryWidget> createState() =>
      _RewardPointsHistoryWidgetState();
}

class _RewardPointsHistoryWidgetState extends State<RewardPointsHistoryWidget> {
  List<RewardPointHistoryEntity> _history = [];
  @override
  void initState() {
    super.initState();
    BlocProvider.of<RewardPointBloc>(context)
        .add(GetRewardPointHistory(offset: 0, limit: 50, historyFilter: 'all'));
  }

  @override
  Widget build(BuildContext context) {
    return _buildHistorySection();
  }

  _buildHistorySection() {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildProgramSectionHeader(
            Assets.coachProgramEnrolledIcon,
            'Points History',
            showCount: false,
            iconColor: UIColors.primaryGreen100,
            textColor: UIColors.primary,
          ),
          Expanded(child: _buildRewardPointHistory()),
        ],
      ),
    );
  }

  _buildProgramSectionHeader(
    String icon,
    String title, {
    required bool showCount,
    Color? iconColor,
    Color? textColor,
  }) {
    return Row(
      children: [
        Container(
          margin: const EdgeInsets.only(right: Values.v4),
          decoration: BoxDecoration(
            color: (iconColor != null) ? iconColor : UIColors.primaryGreen100,
            borderRadius: BorderRadius.circular(Values.v36),
          ),
          child: Center(
            child: SvgPicture.asset(
              icon,
              height: Values.v36,
              width: Values.v36,
            ),
          ),
        ),
        Text(
          '$title',
          style: AppTypography.poppinsSemiBold20(
              color:
                  (textColor != null) ? textColor : UIColors.primaryGreen950),
        ),
      ],
    );
  }

  _buildRewardPointHistory() {
    return Container(
      // height: 300,
      padding: const EdgeInsets.all(Values.v14),
      child: BlocListener<RewardPointBloc, RewardPointState>(
        listener: (context, state) {
          if (state is GetRewardPointHistoryFail) {
            AppToast.showToast(
              message: (state.data as ErrorModel).message ?? 'Error',
              gravity: ToastGravity.BOTTOM,
              backgroundColor: AppColors.red,
            );
          }

          if (state is GetRewardPointHistorySuccess) {
            setState(() {
              _history = state.history;
            });
          }
        },
        child: _history.isEmpty
            ? const Center(child: Text('No Points History Found!'))
            : ListView.separated(
                  shrinkWrap: true,
                  itemCount: _history.length,
                  itemBuilder: (context, index) {
                    return buildPointsHistoryCard(
                      _history[index],
                    );
                  },
                  separatorBuilder: (context, index) {
                    return Divider(height: 10, color: AppColors.greyscale100,);
                  },
                ),
      ),
    );
  }

  Widget buildPointsHistoryCard(RewardPointHistoryEntity viewModel) {
    return Container(
      // decoration: BoxDecoration(
      //   color: AppColors.white,
      //   boxShadow: [
      //     BoxShadow(
      //       color: AppColors.softGrey,
      //       offset: const Offset(0.0, 2.0),
      //       blurRadius: 5.0,
      //     ),
      //   ],
      // ),
      margin: const EdgeInsets.symmetric(vertical: Values.v4),
      padding: const EdgeInsets.all(Values.v8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  viewModel.title,
                  style: AppTypography.poppinsMedium12(
                      color: UIColors.primaryGreen950),
                ),
                const SizedBox(height: Values.v4),
                Text(
                  'Date: ' +
                      DateFormat.yMd().format(viewModel.createdAt!).toString(),
                  style: AppTypography.regular12(
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),
          ),
          points(viewModel),
        ],
      ),
    );
  }

  Widget points(RewardPointHistoryEntity viewModel) {
    // return Expanded(
    //   flex: 1,
    //   child: Column(
    //     crossAxisAlignment: CrossAxisAlignment.end,
    //     children: [
    //       Text(
    //         viewModel.totalPoints.toString(),
    //         style: AppTypography.bold14(
    //           color: AppColors.primaryGreen,
    //         ),
    //       ),
    //       const SizedBox(width: Values.v4),
    //       Text(
    //         TextConstants.pointsABBR,
    //         style: AppTypography.regular12(
    //           color: AppColors.grey,
    //         ),
    //       ),
    //     ],
    //   ),
    // );

    return Row(
      children: [
        Image.asset(
          Assets.rewardPointImg,
          height: Values.v16,
          width: Values.v16,
        ),
        SizedBox(
          width: Values.v10,
        ),
        Text('${viewModel.totalPoints.toString()} Points',
            style:
                AppTypography.poppinsSemiBold14(color: AppColors.greyscale400))
      ],
    );
  }
}

class RedeemPointsWithdrawWidget extends StatefulWidget {
  const RedeemPointsWithdrawWidget({super.key});

  @override
  State<RedeemPointsWithdrawWidget> createState() =>
      _RedeemPointsWithdrawWidgetState();
}

class _RedeemPointsWithdrawWidgetState
    extends State<RedeemPointsWithdrawWidget> {
  RewardPointRankEntity? _rewardPointEntity;
  bool _isLoading = false;

  @override
  void initState() {
    BlocProvider.of<RewardPointBloc>(context).add(GetRewardPoints());
    _isLoading = true;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RewardPointBloc, RewardPointState>(
      listener: (context, state) {
        if (state is GetRewardPointSuccess) {
          setState(() {
            _rewardPointEntity = state.pointEntity;
            _isLoading = false;
          });
        }

        if (state is GetRewardPointFail) {
          setState(() {
            _isLoading = false;
            _rewardPointEntity = testPointRankEntity;
          });

          AppToast.showToast(message: '${(state.data as ErrorModel).message}', gravity: ToastGravity.BOTTOM);
        }
      },
      child: (_isLoading)
          ? const Center(
              child: CircularProgressIndicator(
                color: UIColors.primary,
              ),
            )
          : (_rewardPointEntity == null)
              ? const SizedBox(
                  height: 200,
                  child: Center(
                    child: Text('No Points data found'),
                  ),
                )
              : Column(
                  children: [_buildPointsSection()],
                ),
    );
  }

  _buildPointsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildPointEarnSection(),
        _buildAmountSection(),
        _buildWithdrawSection(),
      ],
    );
  }

  _buildPointEarnSection() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildCoinImage(),
        SizedBox(
          width: Values.v10,
        ),
        _buildPointText()
      ],
    );
  }

  _buildAmountSection() {
    return Text('*You will get ${_rewardPointEntity!.pointPrice} taka per point',
        style: AppTypography.poppinsMedium12(color: AppColors.greyscale400));
  }

  _buildRedeemSection() {
    return Button.filled(
      textStyle: AppTypography.poppinsMedium16(color: UIColors.primaryGreen950),
      borderColor: UIColors.white,
      background: UIColors.purple950,
      label: 'Redeem',
      onPressed: () {
        Log.debug('redeem points');
        AppToast.showToast(message: 'Need Minimum 5000 Points to redeem');

        Navigator.of(context).pushNamed(Routes.rewardPointHistoryPage);
      },
      width: Values.v175,
    );
  }

  _buildCoinImage() {
    return Image.asset(
      Assets.rewardPointImg,
      width: Values.v50,
      height: Values.v50,
    );
  }

  _buildPointText() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'Total Points',
          style: AppTypography.poppinsMedium14(color: UIColors.primaryGreen950),
        ),
        Text('${_rewardPointEntity!.totalPoints}',
            style: AppTypography.poppinsSemiBold32(color: UIColors.purple500)),
      ],
    );
  }

  _buildWithdrawSection() {
    return Container(
      margin: const EdgeInsets.only(top: Values.v20),
      padding: const EdgeInsets.symmetric(
          vertical: Values.v20, horizontal: Values.v16),
      decoration: BoxDecoration(
          color: AppColors.greyscale10,
          border: Border.all(color: AppColors.greyscale100),
          borderRadius: BorderRadius.circular(Values.v10)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // _buildWithdrawRequestPendingText(),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                flex: 2,
                child: _buildAvailableAmountSection(),
              ),
              SizedBox(width: 20),
              Flexible(
                flex: 2,
                child: _buildWithdrawButton(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  _buildWithdrawRequestPendingText() {
    return Container(
      margin: const EdgeInsets.only(bottom: Values.v10),
      padding: const EdgeInsets.symmetric(
          vertical: Values.v5, horizontal: Values.v5),
      decoration: BoxDecoration(
          color: UIColors.red50,
          border: Border.all(color: UIColors.red500),
          borderRadius: BorderRadius.circular(10)),
      child: Row(
        children: [
          Icon(Icons.info_outline_rounded),
          SizedBox(
            width: Values.v5,
          ),
          Expanded(
              child: Container(
            child: Text(
              'Previous Withdraw requst is being processed',
              style: AppTypography.poppinsRegular12(
                  color: UIColors.primaryGreen950),
              overflow: TextOverflow.ellipsis,
              maxLines: 3,
            ),
          )),
        ],
      ),
    );
  }

  _buildWithdrawButton() {
    return Button.filled(
      label: 'Withdraw',
      height: Values.v34,
      textStyle: AppTypography.poppinsSemiBold14(color: AppColors.white),
      background: UIColors.orange500,
      borderColor: UIColors.orange500,
      // disable: !enableWithdraw,
      onPressed: () {
        Log.debug('withdraw payment pressed');

        if (_rewardPointEntity!.totalPoints < _rewardPointEntity!.pointRedeemThreshold) {
          AppToast.showToast(message: 'Need to have at least ${_rewardPointEntity!.pointRedeemThreshold} Points', gravity: ToastGravity.BOTTOM);

          return;
        }

      ///TODO need to enable
       _showAlert();
        
        return;

        Navigator.of(context).pushNamed(Routes.coachWithdrawPaymentPage);
      },
    );
  }

   _showAlert() {
    showDialog(
      context: context,
      builder: (context) {
        return CupertinoAlertDialog(
          title: Column(
            children: [
              Text(
                'Withdraw',
                style: AppTypography.poppinsSemiBold16(
                    color: UIColors.primaryGreen900),
              ),
            ],
          ),
          content: Text(
            "Withdraw is not available in this version. Please Update your App",
            style: AppTypography.poppinsRegular14(
              color: AppColors.greyscale400,
            ),
          ),
          actions: [
            CupertinoDialogAction(
              child: const Text('Ok'),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        );
      },
    );
  }

  _buildAvailableAmountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Available Playment to withdraw',
          style: AppTypography.poppinsMedium12(color: AppColors.greyscale400),
        ),
        SizedBox(height: Values.v10),
        Text(
          'BDT ${_rewardPointEntity!.totalAmount}',
          style:
              AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
        ),
      ],
    );
  }
}
