import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:flutter/material.dart';

class ListSingleSelectionMenuWidget extends StatefulWidget {
  const ListSingleSelectionMenuWidget({
    Key? key,
    required this.options,
    this.previousSelectedOption,
    required this.callback,
  }): super(key: key);
  final List<ChoiceOption> options;
  final ChoiceOption? previousSelectedOption;
  final Function(ChoiceOption?) callback;

  @override
  State<ListSingleSelectionMenuWidget> createState() =>
      _ListSingleSelectionMenuWidgetState();
}

class _ListSingleSelectionMenuWidgetState
    extends State<ListSingleSelectionMenuWidget> {
  ChoiceOption? selectedOption;

  @override
  void initState() {
    super.initState();

    selectedOption = widget.previousSelectedOption;
    // Log.debug('selected option: ${(selectedOption == null)}');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(color: AppColors.greyscale50),
          borderRadius: BorderRadius.circular(10)),
      child: _buildItemList(),
    );
  }

  _buildItemList() {
    return ListView.builder(
      itemCount: widget.options.length,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        return _buildItemWidget(index: index);
      },
    );
  }

  _buildItemWidget({required int index}) {
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedOption = (selectedOption == widget.options[index])
              ? null
              : widget.options[index];
        });

        widget.callback(selectedOption);
      },
      child: Row(
        children: [
          _buildCheckBox(index: index),
          const SizedBox(
            width: 5,
          ),
          _buildItemLabel(index: index),
        ],
      ),
    );
  }

  _buildItemLabel({required int index}) {
    return Text(
      widget.options[index].label.toString(),
      style: AppTypography.poppinsMedium14(color: UIColors.primaryGreen950),
    );
  }

  _buildCheckBox({required int index}) {
    bool isSelected = (selectedOption != null) &&
        (widget.options[index].name == selectedOption!.name);

    return Checkbox(
      value: isSelected,
      onChanged: null,
      activeColor: UIColors.primary,
      checkColor: AppColors.white,
      fillColor:
          (isSelected) ? WidgetStatePropertyAll(AppColors.primaryGreen) : null,
      side: const BorderSide(color: UIColors.primary),
    );
  }
}

class ChoiceOption {
  final String name;
  final String label;

  const ChoiceOption({required this.name, required this.label});
}
