import 'package:dartz/dartz.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/features/auth/logout/data/data_source/logout_data_source.dart';
import 'package:fitsomnia_app/src/features/auth/logout/domain/repository/logout_repository.dart';

class LogoutRepositoryImpl implements LogoutRepository {
  const LogoutRepositoryImpl({
    required this.dataSource,
  });

  final LogoutDataSource dataSource;

  @override
  Future<Either<bool, bool>> logout() async {
    try {
      await dataSource.logout();

      return const Right(true);
    } catch (e, stackTrace) {
      Log.info(e.toString());
      Log.info(stackTrace.toString());

      return const Left(false);
    }
  }
}
