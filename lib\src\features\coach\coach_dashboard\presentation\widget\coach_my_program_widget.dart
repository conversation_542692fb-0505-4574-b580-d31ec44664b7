import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/bloc/coach_dashboard_bloc.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/widget/coach_single_program_widget.dart';
import 'package:fitsomnia_app/src/features/coach/coach_program/domain/entities/coach_program_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_profile/domain/entities/coach_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

class CoachMyProgramWidget extends StatefulWidget {
  const CoachMyProgramWidget({Key? key, required this.coachEntity})
      : super(key: key);
  final CoachEntity coachEntity;

  @override
  State<CoachMyProgramWidget> createState() => _CoachMyProgramWidgetState();
}

class _CoachMyProgramWidgetState extends State<CoachMyProgramWidget> {
  List<CoachProgramEntity> testPrograms = List.generate(5, (index) {
    return testCoachProgram;
  });

  List<CoachProgramEntity> _coachPrograms = [];

  @override
  void initState() {
    super.initState();

    BlocProvider.of<CoachDashboardBloc>(context)
        .add(GetCoachProgramsEvent(coachId: widget.coachEntity.coachId!));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachDashboardBloc, CoachDashboardState>(
      listener: (context, state) {
        if (state is GetCoachOwnProgramsSuccess) {
          Log.debug('get coach own programs success');
          setState(() {
            _coachPrograms = state.programs;
          });
        }

        if (state is GetCoachOwnProgramsFail) {
          Log.debug('get coach own programs fail');
          _coachPrograms = [];
        }
      },
      child: _buildCoachProgramSection(),
    );
  }

  _buildCoachProgramSection() {
    return Container(
      margin: EdgeInsets.only(top: Values.v20),
      child: Column(
        children: [
          _buildProgramSectionHeader(Assets.coachProgramIcon, 'My Programs'),
          _buildCoachProgramsList(),
        ],
      ),
    );
  }

  _buildCoachProgramsList() {
    if (_coachPrograms.isEmpty) {
      return Container(
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(Values.v30),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(Assets.coachNoProgramFoundImg, height: Values.v80, width: Values.v80,),
                SizedBox(height: Values.v10,),
                Text('No program found', style: AppTypography.poppinsSemiBold14(color: AppColors.greyscale500),),
              ],
            ),
          ),
        ),
      );
    }

    return Column(
      children: _coachPrograms.map<Widget>((program) {
        return CoachSingleProgramWidget(programEntity: program);
      }).toList(),
    );
  }

  _buildProgramSectionHeader(String icon, String title) {
    return Row(
      children: [
        Container(
          margin: const EdgeInsets.only(right: Values.v4),
          decoration: BoxDecoration(
            color: UIColors.purple50,
            borderRadius: BorderRadius.circular(Values.v36),
          ),
          child: Center(
            child: SvgPicture.asset(
              icon,
              height: Values.v36,
              width: Values.v36,
            ),
          ),
        ),

        Text(
          '$title',
          style: AppTypography.poppinsSemiBold20(color: UIColors.purple500),
        ),
        // Flexible(
        //   child: Button.filled(
        //     label: 'Add Program',
        //     onPressed: () {
        //       Log.debug('add program button pressedd');
        //     },
        //     height: Values.v34,
        //     textStyle: AppTypography.poppinsSemiBold14(color: UIColors.white),
        //     background: UIColors.purple500,
        //   ),
        // ),
        const Spacer(),
        ElevatedButton(
            onPressed: () {
              Log.debug('add program button pressedd');
              Navigator.of(context).pushNamed(Routes.coachProgramCreatePage);
            },
            style: ButtonStyle(
              backgroundColor:
                  WidgetStateProperty.all<Color>(UIColors.purple500),
            ),
            child: Text(
              'Add Program',
              style: AppTypography.poppinsMedium12(color: UIColors.white),
            )),
      ],
    );
  }
}
