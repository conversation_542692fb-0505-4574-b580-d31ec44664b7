import 'dart:async';

import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/services/socket/scoket_service.dart';
import 'package:fitsomnia_app/src/core/services/socket/socket_enum.dart';
import 'package:fitsomnia_app/src/core/text_constants.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/create_group/presentation/page/create_group_chat_page.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/domain/entity/group_list_entity.dart';
import 'package:fitsomnia_app/src/features/chat/dashboard/groups/presentation/bloc/group_list_bloc.dart';
import 'package:fitsomnia_app/src/features/chat/group_messages/presentation/pages/group_chat_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Groups extends StatefulWidget {
  const Groups({Key? key}) : super(key: key);

  @override
  State<Groups> createState() => _GroupsState();
}

class _GroupsState extends State<Groups> {
  StreamController<GroupListEntity> streamController =
      StreamController<GroupListEntity>.broadcast();

  @override
  void initState() {
    super.initState();
    BlocProvider.of<GroupListBloc>(context).add(GetGroupListEvent(limit: 5));
  }

  @override
  void didChangeDependencies() {
    _initiateSocketListener();
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: Values.v16.w,
        top: Values.v5.h,
        right: Values.v16.w,
        bottom: Values.v11.h,
      ),
      decoration: BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.black.withOpacity(Values.v0_1),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(right: Values.v10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  TextConstants.groups,
                  style: AppTypography.regular14(
                    color: AppColors.black,
                  ),
                ),
                InkWell(
                  onTap: () => _navigateToGroupChatListPage(context),
                  child: Text(
                    TextConstants.seeAll,
                    style: AppTypography.regular12(
                      color: AppColors.apple,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: Values.v15.h),
          SizedBox(
            height: Values.v90.h,
            child: BlocConsumer<GroupListBloc, GroupListState>(
              listener: (context, state) {
                if (state is GroupListSuccess) {
                  BlocProvider.of<GroupListBloc>(context).add(
                    UpdateGroupListEvent(groupListEntities: state.groupList),
                  );

                  clearStreamAndAddNewValues();
                }
              },
              buildWhen: (previous, current) => previous != current,
              builder: (context, state) {
                return state is GroupListSuccess
                    ? state.groupList.isEmpty
                        ? _buildCreateANewGroupCard(context)
                        : Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildCreateANewGroupCard(context),
                              
                              StreamBuilder<GroupListEntity>(
                                stream: streamController.stream,
                                builder: (context, snapshot) {
                                  return snapshot.hasData
                                      ? ListView.builder(
                                          shrinkWrap: true,
                                          scrollDirection: Axis.horizontal,
                                          itemCount: context
                                              .read<GroupListBloc>()
                                              .groupList
                                              .length,
                                          itemBuilder: (context, index) {
                                            return _buildGroupCard(context,
                                                model: context
                                                    .read<GroupListBloc>()
                                                    .groupList[index]);
                                          },
                                        )
                                      : ListView.builder(
                                          shrinkWrap: true,
                                          scrollDirection: Axis.horizontal,
                                          itemCount: context
                                              .read<GroupListBloc>()
                                              .groupList
                                              .length,
                                          itemBuilder: (context, index) {
                                            // print('group image ${index}');

                                            return _buildGroupCard(context,
                                                model: context
                                                    .read<GroupListBloc>()
                                                    .groupList[index]);
                                          },
                                        );
                                },
                              )
                            ],
                          )
                    : const SizedBox.shrink();
              },
            ),
          )
        ],
      ),
    );
  }

  Widget _buildGroupCard(
    BuildContext context, {
    required GroupListEntity model,
  }) {
    // print('Group images ${model.image}');

    return InkWell(
      onTap: () {
        _navigateToGroupChatPage(context, model);
      },
      child: Container(
        margin: EdgeInsets.only(right: Values.v20.w),
        width: Values.v58.w,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            ImageContainer.circularImage(
              image: model.image ?? '',
              radius: Values.v26,
              isGroup: true,
            ),
            SizedBox(height: Values.v7.h),
            Text(
              model.name,
              style: AppTypography.regular10(),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // Widget _buildCreateANewGroupCard(BuildContext context) {
  //   return ListTile(
  //     onTap: () {
  //       _navigateToCreateGroupPage(context);
  //     },
  //     contentPadding: EdgeInsets.zero,
  //     leading: CircleAvatar(
  //       backgroundColor: AppColors.apple,
  //       child: const Icon(
  //         Icons.add,
  //         color: AppColors.white,
  //         size: 32,
  //       ),
  //     ),
  //     // title: const Text("Create a new group"),
  //     // trailing: const Icon(
  //     //   Icons.arrow_forward_ios,
  //     //   size: 16,
  //     // ),
  //   );
  // }
  Widget _buildCreateANewGroupCard(BuildContext context) {
    return InkWell(
      onTap: () {
        _navigateToCreateGroupPage(context);
      },
      child: Container(
        margin: EdgeInsets.only(right: Values.v5.w),
        width: Values.v78.w,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: Values.v7.h),
            CircleAvatar(
              backgroundColor: AppColors.apple,
              radius: 24.0,
              child: const Icon(
                Icons.add,
                color: AppColors.white,
                size: 33.0
              ),
            ),
            SizedBox(height: Values.v3.h),
            Text(
              'Create Group',
              style: AppTypography.regular10(),
            ),
          ],
        ),
      ),
    );
  }

  void _initiateSocketListener() {
    SocketService.instance
        .on(ServerToClient.GROUP_SERVER_TO_CLIENT_RECEIVE_MESSAGE.name, (data) {
      if (mounted) {
        BlocProvider.of<GroupListBloc>(context).add(RemoveGroupFromListEvent(
          groupListEntity: GroupListEntity(
            groupId: data['receiverId'],
            name: data['receiverName'],
            image: data['receiverImage'],
          ),
        ));

        BlocProvider.of<GroupListBloc>(context).add(
          AddGroupToListEvent(
            groupListEntity: GroupListEntity(
              groupId: data['receiverId'],
              name: data['receiverName'],
              image: data['receiverImage'],
            ),
          ),
        );

        clearStreamAndAddNewValues();
      }
    });
  }

  void clearStreamAndAddNewValues() {
    context.read<GroupListBloc>().groupList.forEach((element) {
      streamController.sink.add(element);
    });
  }

  void _navigateToGroupChatListPage(BuildContext context) {
    Navigator.pushNamed(
      context,
      Routes.groupChat,
    );
  }

  void _navigateToGroupChatPage(BuildContext context, GroupListEntity model) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GroupChatPage(
          groupId: model.groupId,
          groupName: model.name,
          groupImage: model.image ?? "",
        ),
      ),
    );
  }

  void _navigateToCreateGroupPage(BuildContext context) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) => const CreateGroup(),
      ),
    );
  }
}
