import 'package:fitsomnia_app/src/core/assets.dart';
import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/exception/error_model.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/routes.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:fitsomnia_app/src/core/values.dart';
import 'package:fitsomnia_app/src/core/widgets/app_toast.dart';
import 'package:fitsomnia_app/src/core/widgets/image_container.dart';
import 'package:fitsomnia_app/src/features/chat/direct_message/presentation/pages/chat_page.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/domain/entities/coach_program_enrollment_entity.dart';
import 'package:fitsomnia_app/src/features/coach/coach_dashboard/presentation/bloc/coach_dashboard_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';

class CoachSingleProgramEnrollmentHistory extends StatefulWidget {
  const CoachSingleProgramEnrollmentHistory({
    super.key,
    required this.coachId,
    required this.programId,
  });
  final String coachId;
  final String programId;

  @override
  State<CoachSingleProgramEnrollmentHistory> createState() =>
      _CoachSingleProgramEnrollmentHistoryState();
}

class _CoachSingleProgramEnrollmentHistoryState
    extends State<CoachSingleProgramEnrollmentHistory> {
  List<CoachProgramEnrollmentEntity> _enrolledPrograms = [];
  List<CoachProgramEnrollmentEntity> testPrograms = List.generate(5, (index) {
    return testProgramEnrollmentEntity;
  });

  @override
  void initState() {
    //TODO for testing
    // _enrolledPrograms = testPrograms;

    BlocProvider.of<CoachDashboardBloc>(context).add(
        GetCoachProgramEnrollerHistoryEvent(
            programId: widget.programId, coachId: widget.coachId));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachDashboardBloc, CoachDashboardState>(
      listener: (context, state) {
        if (state is GetCoachProgramEnrollerHistorySuccess) {
          Log.debug('get user enrolled program success');
          setState(() {
            _enrolledPrograms = state.userSubscriptionHistoryEntity;
          });
        }

        if (state is GetCoachProgramEnrollerHistoryFail) {
          Log.debug('get user enrolled program fail');
          String errorMessage = (state.data as ErrorModel).message ?? 'Error';
          AppToast.showToast(
              message: errorMessage, gravity: ToastGravity.BOTTOM);
        }
      },
      child: Scaffold(
        appBar: _buildAppBar(),
        body: SafeArea(
          child: SingleChildScrollView(
            child: Container(
              padding: EdgeInsets.symmetric(
                  vertical: Values.v20, horizontal: Values.v20),
              child: Column(
                children: [
                  // _buildProgramSectionHeader(
                  //   Assets.coachProgramEnrolledIcon,
                  //   'Enrollments',
                  //   showCount: true,
                  //   iconColor: UIColors.primaryGreen100,
                  //   textColor: UIColors.primary,
                  // ),
                  _buildStudentProgramsEnrollmentList(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  _buildProgramSectionHeader(
    String icon,
    String title, {
    required bool showCount,
    Color? iconColor,
    Color? textColor,
  }) {
    return Row(
      children: [
        Container(
          margin: const EdgeInsets.only(right: Values.v4),
          decoration: BoxDecoration(
            color: (iconColor != null) ? iconColor : UIColors.primaryGreen100,
            borderRadius: BorderRadius.circular(Values.v36),
          ),
          child: Center(
            child: SvgPicture.asset(
              icon,
              height: Values.v36,
              width: Values.v36,
            ),
          ),
        ),
        Text(
          '$title ${(showCount && _enrolledPrograms.isNotEmpty) ? '(${_enrolledPrograms.length})' : ''}',
          style: AppTypography.poppinsSemiBold20(
              color:
                  (textColor != null) ? textColor : UIColors.primaryGreen950),
        ),
      ],
    );
  }

  _buildStudentProgramsEnrollmentList() {
    // return Column(
    //   children: _enrolledPrograms.map<Widget>((programEnrollmentEntity) {
    //     return CoachSingleProgrammSingleEnrollmentInfoWidget(
    //         programEnrollmentEntity: programEnrollmentEntity);
    //   }).toList(),
    // );

    if (_enrolledPrograms.isEmpty) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Text('No Subscribers found', style: AppTypography.poppinsMedium14(color: AppColors.greyscale400),),
        ),
      );
    }

    return ListView.separated(
      shrinkWrap: true,
      itemBuilder: (context, index) {
        return CoachSingleProgrammSingleEnrollmentInfoWidget(
            programEnrollmentEntity: _enrolledPrograms[index]);
      },
      separatorBuilder: (context, index) {
        return Divider(color: AppColors.greyscale100);
      },
      itemCount: _enrolledPrograms.length,
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text(
        'Enrollers',
        style: TextStyle(
          color: UIColors.primaryGreen950,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: true,
      elevation: 0,
    );
  }
}

class CoachSingleProgrammSingleEnrollmentInfoWidget extends StatefulWidget {
  const CoachSingleProgrammSingleEnrollmentInfoWidget(
      {super.key, required this.programEnrollmentEntity});

  final CoachProgramEnrollmentEntity programEnrollmentEntity;

  @override
  State<CoachSingleProgrammSingleEnrollmentInfoWidget> createState() =>
      _CoachSingleProgrammSingleEnrollmentInfoWidgetState();
}

class _CoachSingleProgrammSingleEnrollmentInfoWidgetState
    extends State<CoachSingleProgrammSingleEnrollmentInfoWidget> {
  @override
  Widget build(BuildContext context) {
    return _buildEnrollmentInfoCard();
  }

  _buildEnrollmentInfoCard() {
    return GestureDetector(
      onTap: () {
        Log.debug('enrollment item pressed');
        Navigator.of(context).pushNamed(Routes.coachProgramEnrolledDetailsPage,
            arguments: widget.programEnrollmentEntity.subscriptionId);
      },
      child: Container(
        margin: EdgeInsets.only(top: Values.v5),
        // padding:
        //     EdgeInsets.symmetric(vertical: Values.v20, horizontal: Values.v16),
        // decoration: BoxDecoration(
        //     color: AppColors.greyscale10,
        //     border: Border.all(color: AppColors.greyscale100),
        //     borderRadius: BorderRadius.circular(Values.v10)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   crossAxisAlignment: CrossAxisAlignment.start,
            //   children: [
            //     _buildProgramTitle(
            //         title: widget.programEnrollmentEntity.programTitle),
            //   ],
            // ),
            // const SizedBox(
            //   height: Values.v20,
            // ),
            // _buildStudentEnrollmentInfoSection(
            //     enrollmentDate: DateTime.now(), // TODO need to replace
            //     payment: 3000),
            // Container(
            //   margin: EdgeInsets.symmetric(vertical: 10),
            //   height: 2,
            //   width: double.infinity,
            //   color: AppColors.greyscale100,
            // ),
            Row(
              children: [
                _buildEnrolledUserInfo(),
                Spacer(),
                // _buildChatOption(),
                IconButton(
                  onPressed: () {
                    Log.debug('move to enrollment details page');
                    _navigateToEnrollmentDetailPage();
                  },
                  icon: Icon(Icons.arrow_forward_ios_outlined),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  _buildProgramTitle({required String title}) {
    return Expanded(
      child: Text(
        title,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: AppTypography.poppinsSemiBold20(color: UIColors.primaryGreen950),
      ),
    );
  }

  _buildStudentEnrollmentInfoSection(
      {required DateTime enrollmentDate, required int payment}) {
    return Wrap(
      children: [
        // Text('Total Enrollment: ${programEntity.totalSubscription}'),
        _buildProgramInfoText(
            'Subscribed On: ',
            '${DateFormat.yMMMMd().format(enrollmentDate)}',
            AppTypography.poppinsMedium12(color: AppColors.greyscale400),
            AppTypography.poppinsMedium14(color: UIColors.primaryGreen950)),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 5),
          height: 20,
          width: 2,
          color: UIColors.primaryGreen400,
        ),
        // Text('Total Income: BDT ${350000}'),
        _buildProgramInfoText(
            '',
            'BDT ${payment}',
            AppTypography.poppinsMedium12(color: AppColors.greyscale400),
            AppTypography.poppinsMedium14(color: UIColors.primaryGreen950)),
      ],
    );
  }

  _buildProgramInfoText(String infoName, String? infoValue,
      TextStyle infoNameStyle, TextStyle infoValueStyle) {
    if (infoValue == null || infoValue == '') return SizedBox.shrink();

    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          '${(infoName.length != 0) ? '${infoName}:' : ''}',
          style: infoNameStyle,
        ),
        SizedBox(
          width: Values.v5,
        ),
        Flexible(
          child: Wrap(
            children: [
              Text(
                infoValue,
                style: infoValueStyle,
                overflow: TextOverflow.clip,
              ),
            ],
          ),
        )
      ],
    );
  }

  _buildEnrolledUserInfo() {
    String userImageUrl = Assets.spotMeNoImage;
    if (widget.programEnrollmentEntity.userImage != null) {
      if (Uri.parse(widget.programEnrollmentEntity.userImage!)
          .hasAbsolutePath) {
        userImageUrl = widget.programEnrollmentEntity.userImage!;
      }
    }

    return Container(
      child: Row(
        children: [
          _buildStudentProfileImage(userImageUrl),
          const SizedBox(
            width: 5,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.programEnrollmentEntity.userName ?? 'no-name',
                style: AppTypography.poppinsSemiBold14(
                    color: UIColors.primaryGreen950),
              ),
            ],
          )
        ],
      ),
    );
  }

  _buildStudentProfileImage(String? imagePath) {
    if (imagePath == null || imagePath == Assets.spotMeNoImage) {
      // return ImageContainer.circularImage(
      //   image: Assets.spotMeNoImage,
      //   radius: Values.v20,
      //   showBorder: false,
      // );

      return ClipRRect(
        borderRadius: BorderRadius.circular(Values.v100),
        child: SizedBox(
          height: Values.v40,
          width: Values.v40,
          child: Image.asset(
            Assets.spotMeNoImage,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    return ImageContainer.circularImage(
      image: imagePath,
      useOriginal: true,
      radius: Values.v20,
      showBorder: false,
    );
  }

  _buildChatOption() {
    return Padding(
      padding: EdgeInsets.only(left: Values.v8),
      child: IconButton(
          onPressed: () {
            Log.debug('chat with coach');
            //TODO: move to chat page
            _navigateToChatPage();
          },
          icon: CircleAvatar(
            radius: Values.v16,
            backgroundColor: UIColors.primaryGreen600,
            child: Image.asset(
              Assets.messageIconNew,
              color: UIColors.white,
              height: Values.v16,
              width: Values.v16,
            ),
          )),
    );
  }

  void _navigateToChatPage() {
    Future.delayed(const Duration(milliseconds: 300), () {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChatPage(
            title: widget.programEnrollmentEntity.userName!,
            fitBuddyId: widget.programEnrollmentEntity.userId!,
            image: widget.programEnrollmentEntity.userImage,
          ),
        ),
      );
    });
  }

  void _navigateToEnrollmentDetailPage() {
    Future.delayed(const Duration(milliseconds: 300), () {
      Navigator.of(context).pushNamed(Routes.coachProgramEnrolledDetailsPage,
          arguments: widget.programEnrollmentEntity.subscriptionId);
    });
  }
}
