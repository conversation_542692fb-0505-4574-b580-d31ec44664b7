import 'package:fitsomnia_app/src/core/colors.dart';
import 'package:fitsomnia_app/src/core/typography/style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Button extends StatelessWidget {
  const Button({
    Key? key,
    required this.label,
    required this.onPressed,
    this.background,
    this.textStyle,
    this.scale = 1,
    this.height = 50,
    this.width = double.infinity,
    this.isLoading = false,
    this.filled = true,
    this.prefix,
    this.disable = false,
    this.borderColor,
  }) : super(key: key);

  final VoidCallback onPressed;
  final String label;
  final Color? background;
  final TextStyle? textStyle;
  final double scale;
  final double height;
  final double width;
  final bool filled;
  final bool isLoading;
  final Widget? prefix;
  final bool disable;
  final Color? borderColor;

  factory Button.filled({
    required String label,
    required VoidCallback onPressed,
    Color? background,
    TextStyle? textStyle,
    double scale = 1,
    double height = 50,
    double width = double.infinity,
    bool isLoading = false,
    Widget? prefix,
    bool disable = false,
    Color? borderColor,
  }) {
    return Button(
      label: label,
      onPressed: onPressed,
      background: background,
      textStyle: textStyle,
      scale: scale,
      height: height,
      width: width,
      isLoading: isLoading,
      filled: true,
      prefix: prefix,
      disable: disable,
      borderColor: borderColor,
    );
  }

  factory Button.outlined({
    required String label,
    required VoidCallback onPressed,
    Color? background,
    TextStyle? textStyle,
    double scale = 1,
    double height = 50,
    double width = double.infinity,
    isLoading = false,
    Widget? prefix,
    bool disable = false,
  }) {
    return Button(
      label: label,
      onPressed: onPressed,
      background: background,
      textStyle: textStyle,
      scale: scale,
      height: height,
      width: width,
      isLoading: isLoading,
      filled: false,
      prefix: prefix,
      disable: disable,
    );
  }

  @override
  Widget build(BuildContext context) {
    double height = this.height;
    double width = this.width == double.infinity ? 390.w : this.width;
    double borderRadius = 100.r;

    return ElevatedButton(
      onPressed: disable
          ? null
          : isLoading
              ? null
              : onPressed,
      style: filled
          ? ElevatedButton.styleFrom(
              elevation: 0,
              side: borderColor != null
                  ? BorderSide(
                      color: borderColor!,
                      width: 1,
                    )
                  : BorderSide(
                      color: AppColors.primaryGreen,
                      width: 1,
                    ),
              fixedSize: Size(width, height),
              backgroundColor: background ?? UIColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(borderRadius),
              ),
            )
          : ElevatedButton.styleFrom(
              elevation: 0,
              fixedSize: Size(width, height),
              backgroundColor: disable
                  ? UIColors.disabled
                  : background ?? UIColors.transparent,
              side: BorderSide(
                color: isLoading ? UIColors.transparent : UIColors.primary,
                width: 1,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(borderRadius),
              ),
            ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          isLoading
              ? Transform.scale(
                  scale: scale,
                  child: const SizedBox(
                    height: 30,
                    width: 30,
                    child: CircularProgressIndicator(),
                  ),
                )
              : prefix == null
                  ? Text(
                      label,
                      style: textStyle ?? _labelTextStyle(),
                    )
                  : Row(
                      children: [
                        prefix!,
                        SizedBox(width: 10.w),
                        Text(
                          label,
                          style: textStyle ?? _labelTextStyle(),
                        ),
                      ],
                    ),
        ],
      ),
    );
  }

  TextStyle _labelTextStyle() {
    return AppTypography.bold20(
      color: filled ? UIColors.white : UIColors.fitBlack,
    );
  }
}
